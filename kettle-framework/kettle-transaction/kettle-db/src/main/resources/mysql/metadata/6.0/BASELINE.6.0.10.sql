DROP TABLE IF EXISTS KETTLE_DEV.ASSEMBLY_TAT_DATA;
CREATE TABLE KETTLE_DEV.ASSEMBLY_TAT_DATA(
ASSEMBLY_TAT_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT UNIQUE,
UNIT_ID INTEGER NOT NULL,
BUSINESS_DATE DATE NOT NULL,
TAT_TYPE VARCHAR(20) NOT NULL,
TAT_STATUS VARCHAR(20) NOT NULL,
HOT_PREP_TIME INTEGER,
COLD_PREP_TIME INTEGER,
FOOD_PREP_TIME INTEGER,
ORDER_PREP_TIME INTEGER,
DIS<PERSON>TCH_TIME INTEGER,
GENERATION_TIME DATETIME
);

DROP PROCEDURE IF EXISTS KETTLE_DEV.HOURLY_TAT_DUMP;
DELIMITER $$
CREATE PROCEDURE KETTLE_DEV.HOURLY_TAT_DUMP()
BEGIN

DECLARE CURRENT_BIZ_DATE DATE;
DECLARE LAST_BIZ_HOUR DATETIME;
DECLARE GENERATION_TIMESTAMP DATETIME;
SELECT DATE_ADD((CASE
                WHEN
                    HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) < 5
                THEN
                    SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                        1)
                ELSE CURRENT_DATE
            END),
            INTERVAL 5 HOUR) INTO CURRENT_BIZ_DATE;

SELECT ADDTIME(UTC_TIMESTAMP, '04:30:00') INTO LAST_BIZ_HOUR;
SELECT ADDTIME(UTC_TIMESTAMP, '05:30:00') INTO GENERATION_TIMESTAMP;

set sql_safe_updates=0;
UPDATE KETTLE_DEV.ASSEMBLY_TAT_DATA 
SET TAT_STATUS="IN_ACTIVE" 
WHERE TAT_STATUS="ACTIVE" and BUSINESS_DATE = CURRENT_BIZ_DATE;

INSERT INTO KETTLE_DEV.ASSEMBLY_TAT_DATA(UNIT_ID,BUSINESS_DATE,TAT_TYPE,TAT_STATUS,
HOT_PREP_TIME,COLD_PREP_TIME,FOOD_PREP_TIME,ORDER_PREP_TIME,DISPATCH_TIME,GENERATION_TIME)
SELECT A.UNIT_ID,CURRENT_BIZ_DATE,"TODAY","ACTIVE",A.HOT_PREP_TIME,A.COLD_PREP_TIME,
A.FOOD_PREP_TIME,B.ORDER_PREP_TIME, B.ORDER_DISPATCH_TIME, GENERATION_TIMESTAMP
FROM
(

SELECT 
X.UNIT_ID,
MAX(CASE WHEN X.TYPE = "Hot Beverages" then X.PROCESSING_TIME else 0 end) as HOT_PREP_TIME,
MAX(CASE WHEN X.TYPE = "Cold Beverages" then X.PROCESSING_TIME else 0 end) as COLD_PREP_TIME,
MAX(CASE WHEN X.TYPE = "Food" then X.PROCESSING_TIME else 0 end) as FOOD_PREP_TIME
FROM 
(
	select wl.UNIT_ID,wl.TYPE,
    TRUNCATE(AVG(
		(
			(CASE WHEN wl.TIME_TO_PROCESS > 0 THEN wl.TIME_TO_PROCESS ELSE 0 END) 
			+ 
			(CASE WHEN wl.TIME_TO_START > 0 THEN wl.TIME_TO_START ELSE 0 END)
		)/1000), 0) as PROCESSING_TIME 

	from KETTLE_DEV.WORKSTATION_LOG wl
	INNER JOIN KETTLE_DEV.ORDER_DETAIL od on od.ORDER_ID = wl.ORDER_ID
	where od.BILLING_SERVER_TIME > CURRENT_BIZ_DATE
	and wl.TYPE IN ("Hot Beverages","Cold Beverages","Food")
	group by wl.UNIT_ID,wl.TYPE
) as X group by X.UNIT_ID) as A

INNER JOIN

(
	SELECT UNIT_ID, 
    TRUNCATE(AVG(ORDER_PROCESSING_TIME),0) as ORDER_PREP_TIME, 
    TRUNCATE(AVG(ORDER_DISPATCH_TIME),0) as ORDER_DISPATCH_TIME FROM 
    (
		SELECT 
				ald.UNIT_ID,
				od.ORDER_ID,
				TRUNCATE(MAX(
				(			
					(CASE WHEN wl.TIME_TO_PROCESS > 0 THEN wl.TIME_TO_PROCESS ELSE 0 END) 
					+ 
					(CASE WHEN wl.TIME_TO_START > 0 THEN wl.TIME_TO_START ELSE 0 END)
				) / 1000),2) AS ORDER_PROCESSING_TIME,
                TRUNCATE((ald.TIME_TO_DISPATCH - ald.TIME_TO_PROCESS_BY_WORKSTATIONS) / 1000, 2) ORDER_DISPATCH_TIME
			FROM
				KETTLE_DEV.WORKSTATION_LOG wl
			INNER JOIN KETTLE_DEV.ASSEMBLY_LOG_DATA ald ON ald.ORDER_ID = wl.ORDER_ID
			INNER JOIN KETTLE_DEV.ORDER_DETAIL od on od.ORDER_ID = ald.ORDER_ID
			WHERE
				od.BILLING_SERVER_TIME > CURRENT_BIZ_DATE
			GROUP BY ald.ORDER_ID
	) as X GROUP BY X.UNIT_ID
    
) as B on A.UNIT_ID = B.UNIT_ID;


INSERT INTO KETTLE_DEV.ASSEMBLY_TAT_DATA(UNIT_ID,BUSINESS_DATE,TAT_TYPE,TAT_STATUS,
HOT_PREP_TIME,COLD_PREP_TIME,FOOD_PREP_TIME,ORDER_PREP_TIME,DISPATCH_TIME,GENERATION_TIME)
SELECT A.UNIT_ID,CURRENT_BIZ_DATE,"LAST_HOUR","ACTIVE",A.HOT_PREP_TIME,A.COLD_PREP_TIME,
A.FOOD_PREP_TIME,B.ORDER_PREP_TIME, B.ORDER_DISPATCH_TIME, GENERATION_TIMESTAMP
FROM
(

SELECT 
X.UNIT_ID,
MAX(CASE WHEN X.TYPE = "Hot Beverages" then X.PROCESSING_TIME else 0 end) as HOT_PREP_TIME,
MAX(CASE WHEN X.TYPE = "Cold Beverages" then X.PROCESSING_TIME else 0 end) as COLD_PREP_TIME,
MAX(CASE WHEN X.TYPE = "Food" then X.PROCESSING_TIME else 0 end) as FOOD_PREP_TIME
FROM 
(
	select wl.UNIT_ID,wl.TYPE,
    TRUNCATE(AVG(
		(
			(CASE WHEN wl.TIME_TO_PROCESS > 0 THEN wl.TIME_TO_PROCESS ELSE 0 END) 
			+ 
			(CASE WHEN wl.TIME_TO_START > 0 THEN wl.TIME_TO_START ELSE 0 END)
		)/1000), 0) as PROCESSING_TIME 

	from KETTLE_DEV.WORKSTATION_LOG wl
	INNER JOIN KETTLE_DEV.ORDER_DETAIL od on od.ORDER_ID = wl.ORDER_ID
	where od.BILLING_SERVER_TIME > LAST_BIZ_HOUR
	and wl.TYPE IN ("Hot Beverages","Cold Beverages","Food")
	group by wl.UNIT_ID,wl.TYPE
) as X group by X.UNIT_ID) as A

INNER JOIN

(
	SELECT UNIT_ID, 
    TRUNCATE(AVG(ORDER_PROCESSING_TIME),0) as ORDER_PREP_TIME, 
    TRUNCATE(AVG(ORDER_DISPATCH_TIME),0) as ORDER_DISPATCH_TIME FROM 
    (
		SELECT 
				ald.UNIT_ID,
				od.ORDER_ID,
				TRUNCATE(MAX(
				(			
					(CASE WHEN wl.TIME_TO_PROCESS > 0 THEN wl.TIME_TO_PROCESS ELSE 0 END) 
					+ 
					(CASE WHEN wl.TIME_TO_START > 0 THEN wl.TIME_TO_START ELSE 0 END)
				) / 1000),2) AS ORDER_PROCESSING_TIME,
                TRUNCATE((ald.TIME_TO_DISPATCH - ald.TIME_TO_PROCESS_BY_WORKSTATIONS) / 1000, 2) ORDER_DISPATCH_TIME
			FROM
				KETTLE_DEV.WORKSTATION_LOG wl
			INNER JOIN KETTLE_DEV.ASSEMBLY_LOG_DATA ald ON ald.ORDER_ID = wl.ORDER_ID
			INNER JOIN KETTLE_DEV.ORDER_DETAIL od on od.ORDER_ID = ald.ORDER_ID
			WHERE
				od.BILLING_SERVER_TIME > LAST_BIZ_HOUR
			GROUP BY ald.ORDER_ID
	) as X GROUP BY X.UNIT_ID
    
) as B on A.UNIT_ID = B.UNIT_ID;



END$$
DELIMITER ;