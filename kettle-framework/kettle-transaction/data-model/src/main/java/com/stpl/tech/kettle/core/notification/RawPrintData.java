/**
 * 
 */
package com.stpl.tech.kettle.core.notification;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.domain.model.OrderItem;

/**
 * <AUTHOR>
 *
 */
public class RawPrintData {

	List<OrderItem> items;
	Map<Integer, Integer> sizeMap = new HashMap<Integer, Integer>();

	public RawPrintData(List<OrderItem> items, Map<Integer, Integer> sizeMap) {
		super();
		this.items = items;
		this.sizeMap = sizeMap;
	}

	public List<OrderItem> getItems() {
		return items;
	}

	public void setItems(List<OrderItem> items) {
		this.items = items;
	}

	public Map<Integer, Integer> getSizeMap() {
		return sizeMap;
	}

	public void setSizeMap(Map<Integer, Integer> sizeMap) {
		this.sizeMap = sizeMap;
	}

}
