CREATE TABLE KETTLE_SCM_DEV.WASTAGE_ITEM_DATA(
WASTAGE_ITEM_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
WASTAGE_ID INTEGER NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
QUANTITY DECIMAL(16,6) NOT NULL,
COST DECIMAL(16,6) NULL,
PRICE DECIMAL(16,6) NULL,
SKU_ID INTEGER NULL,
COMMENT VARCHAR(450) NULL);

INSERT INTO KETTLE_SCM_DEV.WASTAGE_ITEM_DATA(WASTAGE_ID, PRODUCT_ID , QUANTITY, COST, PRICE, SKU_ID, COMMENT)
SELECT WASTAGE_ID, PRODUCT_ID , QUANTITY, COST, PRICE, SKU_ID, COMMENT from KETTLE_SCM_DEV.WASTAGE_EVENT;

ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT
DROP FOREIGN KEY WASTAGE_EVENT_ibfk_1;
ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT
DROP COLUMN PRODUCT_ID;
ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT
DROP COLUMN QUANTITY;
ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT
DROP COLUMN PRICE;
ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT
DROP COLUMN COST;
ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT
DROP COLUMN SKU_ID;
ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT
DROP COLUMN COMMENT;
ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT
ADD COLUMN EVENT_TYPE VARCHAR(15) NULL;

update KETTLE_SCM_DEV.WASTAGE_EVENT we, KETTLE_SCM_DEV.WASTAGE_ITEM_DATA wie
SET EVENT_TYPE = CASE WHEN  wie.SKU_ID IS NULL THEN 'PRODUCT' ELSE 'SKU' END
WHERE
we.WASTAGE_ID = wie.WASTAGE_ID
;

ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT
ADD COLUMN GR_REASON VARCHAR(450) NULL;
ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT
ADD COLUMN LINKED_KETTLE_ID INTEGER NULL;
ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT
ADD COLUMN LINKED_KETTLE_ID_TYPE VARCHAR(20) NULL;
ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT
ADD COLUMN KETTLE_REASON VARCHAR(1000) NULL;
