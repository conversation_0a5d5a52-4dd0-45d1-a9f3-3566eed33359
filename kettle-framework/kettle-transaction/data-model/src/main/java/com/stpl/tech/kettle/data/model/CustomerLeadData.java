package com.stpl.tech.kettle.data.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ExcelSheet(value = "Customer Upload Details")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class CustomerLeadData {

    @ExcelField
    private String email;

    @ExcelField
    private Date addTime;

    @ExcelField
    private String customerName;

    @ExcelField
    private String customerContact;

    @ExcelField
    private String acquisitionToken;
    @ExcelField
    private String acquisitionSource;

}

