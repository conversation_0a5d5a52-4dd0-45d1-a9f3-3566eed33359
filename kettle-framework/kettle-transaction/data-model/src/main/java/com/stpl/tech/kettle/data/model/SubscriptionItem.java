/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * OrderItem generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "SUBSCRIPTION_ITEM")
public class SubscriptionItem implements java.io.Serializable {

	private Integer subscriptionItemId;
	private SubscriptionDetail subscriptionDetail;
	private int productId;
	private String productName;
	private int quantity;
	private BigDecimal price;
	private String hasAddon;
	private Integer parentItemId;
	private String comboConstituent;
	private BigDecimal totalAmount;
	private BigDecimal paidAmount;
	private BigDecimal discountPercent;
	private BigDecimal discountAmount;
	private BigDecimal taxAmount;
	private Integer discountReasonId;
	private String discountReason;
	private String dimension;
	private String billType;
	private String taxCode;
	private List<SubscriptionItemAddon> subscriptionItemAddons = new ArrayList<SubscriptionItemAddon>(0);
	private List<SubscriptionItemTaxDetail> subscriptionItemTaxes = new ArrayList<SubscriptionItemTaxDetail>(0);
	private String takeAway;
	public SubscriptionItem() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SUBSCRIPTION_ITEM_ID", unique = true, nullable = false)
	public Integer getSubscriptionItemId() {
		return this.subscriptionItemId;
	}

	public void setSubscriptionItemId(Integer orderItemId) {
		this.subscriptionItemId = orderItemId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ID", nullable = false)
	public SubscriptionDetail getSubscriptionDetail() {
		return this.subscriptionDetail;
	}

	public void setSubscriptionDetail(SubscriptionDetail subscriptionDetail) {
		this.subscriptionDetail = subscriptionDetail;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return this.productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "PRODUCT_NAME", nullable = false)
	public String getProductName() {
		return this.productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	@Column(name = "QUANTITY", nullable = false)
	public int getQuantity() {
		return this.quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	@Column(name = "PRICE", nullable = false, precision = 10)
	public BigDecimal getPrice() {
		return this.price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	@Column(name = "HAS_ADDON", nullable = false, length = 1)
	public String getHasAddon() {
		return this.hasAddon;
	}

	public void setHasAddon(String hasAddon) {
		this.hasAddon = hasAddon;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = false, precision = 10)
	public BigDecimal getTotalAmount() {
		return this.totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	@Column(name = "AMOUNT_PAID", nullable = false, precision = 10)
	public BigDecimal getPaidAmount() {
		return this.paidAmount;
	}

	public void setPaidAmount(BigDecimal paidAmount) {
		this.paidAmount = paidAmount;
	}

	@Column(name = "DISCOUNT_PERCENT", precision = 10)
	public BigDecimal getDiscountPercent() {
		return this.discountPercent;
	}

	public void setDiscountPercent(BigDecimal discountPercent) {
		this.discountPercent = discountPercent;
	}

	@Column(name = "DISCOUNT_AMOUNT", precision = 10)
	public BigDecimal getDiscountAmount() {
		return this.discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	@Column(name = "DISCOUNT_REASON_ID")
	public Integer getDiscountReasonId() {
		return this.discountReasonId;
	}

	public void setDiscountReasonId(Integer discountReasonId) {
		this.discountReasonId = discountReasonId;
	}

	@Column(name = "DISCOUNT_REASON")
	public String getDiscountReason() {
		return this.discountReason;
	}

	public void setDiscountReason(String discountReason) {
		this.discountReason = discountReason;
	}

	@Column(name = "DIMENSION", length = 10)
	public String getDimension() {
		return this.dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	@Column(name = "BILL_TYPE", nullable = false, length = 10)
	public String getBillType() {
		return this.billType;
	}

	public void setBillType(String billType) {
		this.billType = billType;
	}
	
	@Column(name = "TAX_CODE", nullable = true, length = 40)
	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subscriptionItem")
	public List<SubscriptionItemAddon> getSubscriptionItemAddons() {
		return this.subscriptionItemAddons;
	}

	public void setSubscriptionItemAddons(List<SubscriptionItemAddon> subscriptionItemAddons) {
		this.subscriptionItemAddons = subscriptionItemAddons;
	}

	@Column(name = "PARENT_ITEM_ID")
	public Integer getParentItemId() {
		return parentItemId;
	}

	public void setParentItemId(Integer parentItemId) {
		this.parentItemId = parentItemId;
	}
	
	@Column(name = "COMBO_CONSTITUENT", length = 1)
	public String getComboConstituent() {
		return comboConstituent;
	}

	public void setComboConstituent(String comboConstituent) {
		this.comboConstituent = comboConstituent;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subscriptionItem")
	public List<SubscriptionItemTaxDetail> getSubscriptionItemTaxes() {
		return subscriptionItemTaxes;
	}

	public void setSubscriptionItemTaxes(List<SubscriptionItemTaxDetail> orderItemTaxes) {
		this.subscriptionItemTaxes = orderItemTaxes;
	}
	
	@Column(name = "TOTAL_TAX", precision = 10)
	public BigDecimal getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(BigDecimal taxAmount) {
		this.taxAmount = taxAmount;
	}

	@Column(name = "TAKE_AWAY", length = 1)
	public String getTakeAway() {
		return takeAway;
	}

	public void setTakeAway(String takeAway) {
		this.takeAway = takeAway;
	}
}
