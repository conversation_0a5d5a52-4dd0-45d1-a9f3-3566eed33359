/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.DeliveryDetail;
import com.stpl.tech.kettle.data.model.PartnerAttributes;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderFeedback;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.dao.AbstractDao;

import java.util.List;
import java.util.Map;

public interface DeliveryDao extends AbstractDao {

    public String getDeliveryPartnerMapping(Integer deliveryPartnerId, String mappingType);

    public List<PartnerAttributes> getObjectMappings();

    public List<UnitToDeliveryPartnerMappings> getDeliveryPartnerPriorityForUnits();

    public DeliveryResponse saveDeliveryDetails(int unitId, DeliveryResponse response);

    public String getDeliveryDetailTaskId(Integer orderId);

    public List<DeliveryDetail> getDeliveryDetail(String generatedOrderId);

    public void mergeCancellationDetails(DeliveryResponse response);

    public DeliveryDetail updateDelivery(DeliveryResponse updateObject);

    public List<PartnerAttributes> getPartnerAttributeList(int partnerId, String partnerType);

    public Map<Integer, String> getAutomatedPartnerMap();

    public boolean updateOrder(Order order) throws DataUpdationException;

    public List<OrderFeedback> getOrderListForFeedback(String deliveryPersonContact, String feedbackStatus);

    public String submitOrderDeliveryFeedback(int deliveryId, String code);
    
    public List<PartnerAttributes> defaultDeliveryPartner(String mappingType);

	void markAdditionalDetailsAsCancelled(List<DeliveryDetail> details);

	public List<Integer> getHourlyReportPartners();

    public List<DeliveryDetail> getDeliveryDetail(Integer orderId);

}
