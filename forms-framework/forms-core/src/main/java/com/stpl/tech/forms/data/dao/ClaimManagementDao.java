package com.stpl.tech.forms.data.dao;

import com.stpl.tech.expense.domain.model.ClaimFindVO;
import com.stpl.tech.expense.domain.model.ClaimStatus;
import com.stpl.tech.expense.domain.model.ClaimType;
import com.stpl.tech.forms.data.model.ClaimDetailData;
import com.stpl.tech.forms.data.model.ClaimLogDetailData;
import com.stpl.tech.forms.domain.model.ApprovalDataVO;
import com.stpl.tech.forms.domain.model.DownloadClaimsVO;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

public interface ClaimManagementDao extends AbstractDao {

    public List<ClaimDetailData> findClaims(Date startDate, Date endDate, ClaimStatus status, ClaimType type,
                                            List<String> unitIds, Integer walletId, Integer employeeId);

    List<ClaimLogDetailData> getClaimLogsByClaimId(Integer claimId);

    List<DownloadClaimsVO> bulkDownload(ClaimFindVO response);

    List<DownloadClaimsVO> downloadById(HttpServletResponse response, Integer claimId);

    void insertExpenseId(ApprovalDataVO finalObject);
    List<ClaimDetailData> getAllClaimsToAutoSettle(Integer autoSettleDays);
}
