/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.util;

import java.sql.SQLException;
import java.text.ParseException;

import javax.activation.UnsupportedDataTypeException;
import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import com.stpl.tech.kettle.report.metadata.model.ReportData;

public class UpdateQueryExecutor extends AbstractQueryExecutor {
	private static final Logger LOG = LoggerFactory.getLogger(QueryExecutorForList.class);

	public int execute(ReportData reportDefinition, DataSource dataSource)
			throws DataAccessException, UnsupportedDataTypeException, ParseException {
		NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
		try {
			LOG.info("Connected to : {}", dataSource.getConnection().getMetaData().getURL());
		} catch (SQLException e) {
		}
		return jdbcTemplate.update(reportDefinition.getContent(), getParamSource(reportDefinition));
	}
}
