package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.EmployeeAttendanceService;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingRequest;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingResponse;
import com.stpl.tech.master.domain.model.BulkEmployeeAttendanceUploadResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.EMPLOYEE_ATTENDANCE_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;
import static com.stpl.tech.master.service.core.MasterServiceConstants.USER_MANAGEMENT_SERVICES_ROOT_CONTEXT;

/**
 * REST Controller for Employee Eligibility Mapping operations
 */
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + USER_MANAGEMENT_SERVICES_ROOT_CONTEXT +  SEPARATOR + EMPLOYEE_ATTENDANCE_ROOT_CONTEXT)
public class EmployeeAttendanceResource extends AbstractResources  {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeAttendanceResource.class);

    @Autowired
    private EmployeeAttendanceService employeeAttendanceService;


    @PostMapping(value = "/mappings",consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public EmployeeEligibilityMappingResponse saveMapping(@Valid @RequestBody EmployeeEligibilityMappingRequest request,
            HttpServletRequest httpRequest) throws DataUpdationException {
        LOG.info("Request to save employee eligibility mapping for empId: {}, type: {}",
                request.getEmpId(), request.getEligibilityType());
        return employeeAttendanceService.saveEmployeeAttendanceMapping(request , String.valueOf(getLoggedInUser(httpRequest)));
    }

    @GetMapping(value = "/mappings/{empId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    public List<EmployeeEligibilityMappingResponse> getAttendanceMappingsByEmpId(@PathVariable("empId") String empId) {
        LOG.info("Request to get mappings for empId: {}", empId);
        return employeeAttendanceService.getEligibilityAttendanceMappingsByEmpId(empId);
    }

    @PostMapping(value = "/export-mappings-excel" ,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE )
    public View downloadMappingsExcel(@RequestBody List<Long> empIds) throws Exception {
        return employeeAttendanceService.prepareEmployeeMappingExcel(empIds);
    }

    @GetMapping("/download-template")
    public View downloadBulkUploadTemplate() throws Exception {
        LOG.info("Request to download bulk upload template");
        return employeeAttendanceService.downloadBulkUploadTemplate();
    }

    @PostMapping(value = "/validate-bulk-upload", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BulkEmployeeAttendanceUploadResponse validateBulkUpload(@Valid @RequestBody List<EmployeeEligibilityMappingRequest> bulkData) throws Exception {
        LOG.info("Request to validate bulk upload with {} records", bulkData.size());
        return employeeAttendanceService.validateBulkUpload(bulkData);
    }

    @PostMapping(value = "/bulk-upload", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BulkEmployeeAttendanceUploadResponse processBulkUpload(@Valid @RequestBody List<EmployeeEligibilityMappingRequest> bulkData,
                                                                  HttpServletRequest httpRequest) throws Exception {
        LOG.info("Request to process bulk upload with {} records", bulkData.size());
        return employeeAttendanceService.processBulkUpload(bulkData, String.valueOf(getLoggedInUser(httpRequest)));
    }

    @PostMapping(value = "/mappings/batch", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BulkEmployeeAttendanceUploadResponse saveMappingsBatch(@Valid @RequestBody List<EmployeeEligibilityMappingRequest> mappingData,
                                                                  HttpServletRequest httpRequest) throws DataUpdationException {
        LOG.info("Request to save batch mappings with {} records", mappingData.size());

        BulkEmployeeAttendanceUploadResponse response = new BulkEmployeeAttendanceUploadResponse();
        response.setTotalRecords(mappingData.size());
        response.setValidationErrors(new ArrayList<>());

        try {
            int successCount = employeeAttendanceService.saveEmployeeAttendanceMappingList(mappingData, String.valueOf(getLoggedInUser(httpRequest)));

            response.setSuccessfullyProcessed(successCount);
            response.setValidRecords(successCount);
            response.setInvalidRecords(mappingData.size() - successCount);
            response.setSuccess(successCount > 0);

            if (successCount == mappingData.size()) {
                response.setMessage("All mappings saved successfully!");
            } else if (successCount > 0) {
                response.setMessage(String.format("Partially saved: %d successful, %d failed.", successCount, mappingData.size() - successCount));
            } else {
                response.setMessage("Failed to save any mappings.");
                response.setSuccess(false);
            }

            LOG.info("Batch save completed: {} successful out of {} total", successCount, mappingData.size());

        } catch (Exception e) {
            LOG.error("Error in batch save operation", e);
            response.setSuccess(false);
            response.setMessage("Error occurred during batch save: " + e.getMessage());
            response.setSuccessfullyProcessed(0);
            response.setValidRecords(0);
            response.setInvalidRecords(mappingData.size());
        }

        return response;
    }

}

