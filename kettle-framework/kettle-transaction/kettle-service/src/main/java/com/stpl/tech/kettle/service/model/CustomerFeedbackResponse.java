package com.stpl.tech.kettle.service.model;

import com.stpl.tech.kettle.domain.model.OrderNPS;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class CustomerFeedbackResponse implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 8635835534869969268L;
	int customerId;
    List<OrderNPS> feedbacks;

    public CustomerFeedbackResponse() {
    }

    public CustomerFeedbackResponse(int customerId, List<OrderNPS> feedbacks) {
        this.customerId = customerId;
        this.feedbacks = feedbacks;
    }

    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    public List<OrderNPS> getFeedbacks() {
    	if(feedbacks == null) {
    		feedbacks = new ArrayList<OrderNPS>();
    	}
        return feedbacks;
    }

    public void setFeedbacks(List<OrderNPS> feedbacks) {
        this.feedbacks = feedbacks;
    }
}
