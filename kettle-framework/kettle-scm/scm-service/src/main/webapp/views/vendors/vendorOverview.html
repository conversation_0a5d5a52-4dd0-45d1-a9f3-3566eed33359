<style>
    #vendor-overview li.tab {
        color: #fff !important;
        text-align: center;
        padding: 0 !important;
    }

    #vendor-overview li.tab a {
        background-color: #e84a17;
        color: #fff;
        display: block;
        padding: 0.75rem;
        text-transform: uppercase;
        border: 1px solid #e84a17;
        border-left: 1px solid #fff;
        border-right: 1px solid #fff;
    }

    #vendor-overview li.tab a.active {
        color: #e84a17;
        font-weight: 700;
        background-color: #fff;
        border: 1px solid #e84a17;
    }

    .bold {
        font-weight: 700;
    }

    #vendor-overview #basic .col {
        padding: 0 0.75rem;
    }

    #vendor-overview .locationDiv {
        background-color: #f6f6f6;
        padding: 1rem;
    }

    .uppercase-text {
        font-size: 12px;
        word-break: break-all;
        text-transform: uppercase;
    }
</style>
<div id="vendor-overview" class="row white z-depth-3" data-ng-init="init()">
    <div class="col s12">
        <h3 class="left"><span data-ng-if="!editMode">View</span><span data-ng-if="editMode">Edit</span> Vendor Details
        </h3>
        <button class="btn right margin-top-20" data-ng-click="goBack()">Back</button>
    </div>
    <div data-ng-class="right" data-ng-show="mode=='Edit' && approvalData!=null">
        <button class="btn right margin-top-20" data-ng-click="approveDetails()">Approve Details</button>
    </div>
    <div class="col s12">
        <h5 class="left">{{basicDetail.entityName}} ({{basicDetail.type}}) &nbsp; <span class="chip">{{basicDetail.status}}</span>
        </h5>
        <div class="right" data-ng-if="showApproveBtn">
            <input style="margin-top:25px; max-width: 150px; display: inline;"
                   data-ng-change="changeCreditDays(creditDays)" type="number" placeholder="Enter Credit Days"
                   data-ng-model="creditDays" min="0"/>
            <button class="btn" data-ng-click="approve(creditDays)">Approve</button>
        </div>
    </div>
    <h6 data-ng-show="mode=='Edit'" style="margin-left: 20px" class="left red-text">* Please Check the Columns that Vendor should Change </h6>
    <div class="row center margin-top-20" data-ng-if="basicDetail==null">
        <h5 class="alert"> No record found. Please verify if vendor has been added correctly.</h5>
    </div>
    <div class="row" data-ng-if="basicDetail!=null">
        <div class="row">
            <div class="col s12">
                <ul tabs reload="allTabContentLoaded">
                    <li class="tab col s3"><a class="active" href="#basic">Basic Details</a></li>
                    <li class="tab col s3"><a href="#company">Company Details</a></li>
                    <li class="tab col s3"><a href="#account">Account Details</a></li>
                    <li class="tab col s3"><a href="#locations">Dispatch Locations</a></li>
                </ul>
            </div>
        </div>
        <div class="row">
            <div id="basic" class="col s12">
                <div class="row">
                    <div class="col s6"
                         data-ng-repeat="(key,value) in basicDetail | hideFilter:'companyDetails,accountDetails,dispatchLocations,registrationId,link,vendorEditedData'">
                        <div class="row">
                            <div class="col s4">
                                <span class="bold" data-change-form data-ng-model="key"
                                      ng-style="isEdited(key) && {'background-color': 'yellow'}"></span> :
                            </div>
                            <div class="col s4">
                               <span class="uppercase-text" ng-switch="key">
                                    <span ng-switch-when="requestedBy">
                                        {{value.name}}
                                    </span>
                                    <span ng-switch-when="updatedBy">
                                        {{value.name}}
                                    </span>
                                    <span ng-switch-when="updateTime">
                                        {{value | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                    <span ng-switch-when="vendorAddress" create-address data-ng-model="value"></span>
                                    <span ng-switch-default>
                                        {{value ? value : "-"}}
                                    </span>
                                </span>
                            </div>
                            <div class="col s4" data-ng-show="mode=='Edit' && requestForChange(key) " >
                                <input type="checkbox" id="basicDetails_{{key}}"
                                       checked="{{basicDetailCheckBoxes[key].changesRequired}}"
                                       data-ng-model="basicDetailCheckBoxes[key].changesRequired"
                                       data-ng-change="onCheckboxClicked(key)"/>
                                <label for="basicDetails_{{key}}"></label>
                            </div>
                        </div>
                        <div class="row" data-ng-show="mode=='Edit'" style="margin-right: 20px"
                             data-ng-if="basicDetailCheckBoxes[key].changesRequired">
                            <h6>* Comment for changes in {{key}}: </h6>
                            <input type="text" data-ng-model="basicDetailCheckBoxes[key].comment"/>
                        </div>
                    </div>
                </div>
                <div data-ng-hide="mode=='Edit'">
                    <button class="btn btn-primary"
                            data-ng-if="basicDetail.status == 'ACTIVE'" type="button"
                            ng-click="partialdeactivateVendor(basicDetail.vendorId)"
                            style="float: right;margin-top: 15px;margin-right: 20px;">Deactivate Vendor
                    </button>
                    <button class="btn btn-primary"
                            data-ng-if="basicDetail.status == 'PENDING_APPROVAL_FOR_DEACTIVATION'" type="button"
                            ng-click="reactivateVendor(basicDetail.vendorId)"
                            style="float: right;margin-top: 15px;margin-right: 50px;">REACTIVATE
                    </button>
                    <button class="btn btn-primary"
                            data-ng-if="basicDetail.status == 'PENDING_APPROVAL_FOR_DEACTIVATION'" type="button"
                            ng-click="fullydeactivateVendor(basicDetail.vendorId)"
                            style="float: right;margin-top: 15px;margin-right: 20px;">DEACTIVATE
                    </button>
                </div>
            </div>
        </div>

        <div id="company" class="col s12">
            <div class="row" data-ng-if="basicDetail.companyDetails!=null">
                <div class="col s6"
                     data-ng-repeat="(key,value) in basicDetail.companyDetails | hideFilter:'basicDetail,companyId,updatedBy,vendorDetail'">
                    <div class="row" data-ng-if="value!=null">
                        <div class="col s4">
                            <span class="bold" data-change-form data-ng-model="key"
                                  ng-style="isEdited(key) && {'background-color': 'yellow'}"></span> :
                        </div>
                        <div class="col s4">
                               <span class="uppercase-text" ng-switch="key">
                                    <span ng-switch-when="panDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-when="arcDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-when="cinDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-when="cstDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-when="serviceTaxDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-when="vatDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                   <span ng-switch-when="msmeDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                   <span ng-switch-when="msmeExpirationDate">
                                        {{value | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>

                                    <span ng-switch-when="updateTime">
                                        {{value | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                    <span ng-switch-when="companyAddress" create-address data-ng-model="value"></span>
                                    <span ng-switch-default>
                                        {{value ? value : "-"}}
                                    </span>
                                </span>
                        </div>
                        <div class="col s4" data-ng-show="mode=='Edit' && requestForChange(key)">
                            <input type="checkbox" id="companyDetails_{{key}}"
                                   checked="{{basicDetailCheckBoxes.companyDetails[key].changesRequired}}"
                                   data-ng-model="basicDetailCheckBoxes.companyDetails[key].changesRequired"
                                   data-ng-change="onCompanyDetailsCheckboxClicked(key)"/>
                            <label for="companyDetails_{{key}}"></label>
                        </div>
                    </div>
                    <div class="row" data-ng-show="mode=='Edit'" style="margin-right: 20px"
                         data-ng-if="basicDetailCheckBoxes.companyDetails[key].changesRequired">
                        <h6>* Comment for changes in {{key}}: </h6>
                        <input type="text" data-ng-model="basicDetailCheckBoxes.companyDetails[key].comment"/>
                    </div>
                </div>
            </div>
            <div class="row center" data-ng-if="basicDetail.companyDetails==null">
                No Company Details found for this Vendor
            </div>
        </div>
        <div id="account" class="col s12">
            <div class="row" data-ng-if="basicDetail.accountDetails!=null">
                <div class="col s6"
                     data-ng-repeat="(key,value) in basicDetail.accountDetails | hideFilter:'basicDetail,accountId,paymentCycle,updatedBy,vendorDetail'">
                    <div class="row">
                        <div class="col s4">
                            <span class="bold" data-change-form data-ng-model="key"
                                  ng-style="isEdited(key) && {'background-color': 'yellow'}"></span> :
                        </div>
                        <div class="col s4">
                               <span class="uppercase-text" ng-switch="key">
                                    <span ng-switch-when="cancelledCheque">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-when="updateTime">
                                        {{value | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                    <span ng-switch-default>
                                        {{value ? value : "-"}}
                                    </span>
                                </span>
                        </div>
                        <div class="col s4" data-ng-show="mode=='Edit' && requestForChange(key)">
                            <input type="checkbox" id="accountDetails_{{key}}"
                                   checked="{{basicDetailCheckBoxes.accountDetails[key].changesRequired}}"
                                   data-ng-model="basicDetailCheckBoxes.accountDetails[key].changesRequired"
                                   data-ng-change="onAccountDetailsCheckboxClicked(key)"/>
                            <label for="accountDetails_{{key}}"></label>
                        </div>
                    </div>
                    <div class="row" data-ng-show="mode=='Edit'" style="margin-right: 20px"
                         data-ng-if="basicDetailCheckBoxes.accountDetails[key].changesRequired">
                        <h6>* Comment for changes in {{key}}: </h6>
                        <input type="text" data-ng-model="basicDetailCheckBoxes.accountDetails[key].comment"/>
                    </div>
                </div>
                <input type="button" data-ng-hide="mode=='Edit'" class="btn" value="Block Payments" acl-action="VNBLKP"
                       data-ng-if="!basicDetail.accountDetails.paymentBlocked"
                       data-ng-click="blockPayments(basicDetail)"/>
                <input type="button" data-ng-hide="mode=='Edit'" class="btn" value="Un Block Payments"
                       acl-action="VNUBLP"
                       data-ng-if="basicDetail.accountDetails.paymentBlocked"
                       data-ng-click="unBlockPayments(basicDetail)"/>
            </div>
            <div class="row center" data-ng-if="basicDetail.accountDetails==null">
                No Account Details found for this Vendor
            </div>
        </div>
        <div id="locations" class="col s12">
            <div class="row locationDiv" data-ng-if="basicDetail.dispatchLocations.length!=0"
                 data-ng-repeat="location in basicDetail.dispatchLocations" data-ng-init="parentIndex = $index">
                <h4>{{location.locationName}}</h4>
                <div data-ng-hide="mode=='Edit'">
                    <button class="btn btn-primary" type="button">Deactive Vendor</button>
                </div>
                <hr>
                <div class="col s6"
                     data-ng-repeat="(key,value) in location | hideFilter:'vendorDetail,locationId,locationName,updatedBy'">
                    <div class="row">
                        <div class="col s4">
                            <span class="bold" data-change-form data-ng-model="key"
                                  ng-style="isEdited(key) && {'background-color': 'yellow'}"></span> :
                        </div>
                        <div class="col s4">
                               <span class="uppercase-text" ng-switch="key">
                                    <span ng-switch-when="address" create-address data-ng-model="value"></span>
                                    <span ng-switch-when="notificationType">
                                        <span data-ng-repeat="notification in value"
                                              class="chip">{{notification}}</span>
                                    </span>
                                    <span ng-switch-when="updateTime">
                                        {{value | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                    <span ng-switch-when="applyTax">
                                        {{value ? "YES" : "NO"}}
                                    </span>
                                    <span ng-switch-when="gstinDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-default>
                                        {{value ? value : "-"}}
                                    </span>
                                </span>
                        </div>
                        <div class="col s4" data-ng-show="mode=='Edit' && requestForChange(key)" >
                            <input type="checkbox" id="dispatchLocations_{{location.dispatchId}}_{{key}}"
                                   checked="{{basicDetailCheckBoxes.dispatchLocations[parentIndex][key].changesRequired}}"
                                   data-ng-model="basicDetailCheckBoxes.dispatchLocations[parentIndex][key].changesRequired"
                                   data-ng-change="onDispatchLocationsCheckboxClicked(key,value,parentIndex)"/>
                            <label for="dispatchLocations_{{location.dispatchId}}_{{key}}"></label>
                        </div>
                    </div>
                    <div class="row" data-ng-show="mode=='Edit'" style="margin-right: 20px"
                         data-ng-if="basicDetailCheckBoxes.dispatchLocations[parentIndex][key].changesRequired">
                        <h6>* Comment for changes in {{key}}: </h6>
                        <input type="text"
                               data-ng-model="basicDetailCheckBoxes.dispatchLocations[parentIndex][key].comment"/>
                    </div>
                </div>
            </div>
            <div class="row center" data-ng-if="basicDetail.dispatchLocations.length==0">
                No Dispatch Locations found for this Vendor
            </div>
            <div class="row">
                <button class="btn " data-ng-show="mode=='Edit'" data-ng-click="onSubmitEditChanges()">REQUEST CHANGES</button>
            </div>
        </div>
    </div>
</div>
</div>

<script type="text/ng-template" id="requestedChanges.html">

    <div class="modal-header red-text" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Your Requested Changes</h3>
        <hr>
    </div>

    <div id="basicChanges" class="col s12  margin-bottom-10">
        <h5 class="red-text">Basic Details</h5>
        <ul class="collection">
            <li class="collection-item list-head">
                <div class="row">
                    <div class="col s3">Key</div>
                    <div class="col s3">Default Value</div>
                    <div class="col s5">Comment</div>
                </div>
            </li>
            <div data-ng-repeat="(key,value) in basicDetailCheckBoxes | hideFilter:'companyDetails,accountDetails,dispatchLocations,registrationId,link,vendorEditedData'">
                <li class="collection-item" data-ng-show="value.changesRequired==true">
                    <div class="row" style="margin-bottom: 0;">
                        <div class="col s3 bold" data-change-form data-ng-model="key">{{key}}</div>
                        <div class="col s3">
                            <span class="uppercase-text" ng-switch="key">
                                    <span ng-switch-when="requestedBy">
                                        {{basicDetail[key].name}}
                                    </span>
                                    <span ng-switch-when="updatedBy">
                                        {{basicDetail[key].name}}
                                    </span>
                                    <span ng-switch-when="updateTime">
                                        {{basicDetail[key] | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                    <span ng-switch-default>
                                        {{basicDetail[key] ? basicDetail[key] : "-"}}
                                    </span>
                                </span>
                        </div>
                        <div class="col s5">
                            {{value.comment ? value.comment : "-"}}
                        </div>
                    </div>
                </li>
            </div>
        </ul>
    </div>

    <div id="companyChanges" class="col s12 margin-bottom-10">
        <h5 class="red-text">Company Details</h5>
        <ul class="collection">
            <li class="collection-item list-head">
                <div class="row">
                    <div class="col s3">Key</div>
                    <div class="col s3">Default Value</div>
                    <div class="col s5">Comment</div>
                </div>
            </li>
            <div data-ng-repeat="(key,value) in basicDetailCheckBoxes.companyDetails | hideFilter:'basicDetail,companyId,updatedBy,vendorDetail'">
                <li class="collection-item" data-ng-show="value.changesRequired==true">
                    <div class="row" style="margin-bottom: 0;">
                        <div class="col s3 bold" data-change-form data-ng-model="key">{{key}}</div>
                        <div class="col s3">
                                <span class="uppercase-text" ng-switch="key">
                                    <span ng-switch-when="panDocument">
                                        {{basicDetail.companyDetails[key].documentLink}}
                                    </span>
                                    <span ng-switch-when="arcDocument">
                                        {{basicDetail.companyDetails[key].documentLink}}
                                    </span>
                                    <span ng-switch-when="cinDocument">
                                        {{basicDetail.companyDetails[key].documentLink}}
                                    </span>
                                    <span ng-switch-when="cstDocument">
                                        {{basicDetail.companyDetails[key].documentLink}}
                                    </span>
                                    <span ng-switch-when="serviceTaxDocument">
                                        {{basicDetail.companyDetails[key].documentLink}}
                                    </span>
                                    <span ng-switch-when="vatDocument">
                                        {{basicDetail.companyDetails[key].documentLink}}
                                    </span>
                                    <span ng-switch-when="msmeDocument">
                                        {{basicDetail.companyDetails[key].documentLink}}
                                    </span>
                                    <span ng-switch-when="msmeExpirationDate">
                                        {{basicDetail.companyDetails[key] | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                    <span ng-switch-when="updateTime">
                                        {{basicDetail.companyDetails[key] | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                    <span ng-switch-default>
                                        {{basicDetail.companyDetails[key] ? basicDetail.companyDetails[key] : "-"}}
                                    </span>
                                </span>
                        </div>
                        <div class="col s5">
                            {{value.comment ? value.comment : "-"}}
                        </div>
                    </div>
                </li>
            </div>
        </ul>
    </div>

    <div id="accountChanges" class="col s12 margin-bottom-10">
        <h5 class="red-text">Account Details</h5>
        <ul class="collection">
            <li class="collection-item list-head">
                <div class="row">
                    <div class="col s3">Key</div>
                    <div class="col s3">Default Value</div>
                    <div class="col s5">Comment</div>
                </div>
            </li>
            <div data-ng-repeat="(key,value) in basicDetailCheckBoxes.accountDetails | hideFilter:'basicDetail,accountId,paymentCycle,updatedBy,vendorDetail'">
                <li class="collection-item" data-ng-show="value.changesRequired==true">
                    <div class="row" style="margin-bottom: 0;">
                        <div class="col s3 bold" data-change-form data-ng-model="key">{{key}}</div>
                        <div class="col s3">
                                <span class="uppercase-text" ng-switch="key">
                                    <span ng-switch-when="cancelledCheque">
                                        {{basicDetail.accountDetails[key].documentLink}}
                                    </span>
                                    <span ng-switch-when="updateTime">
                                        {{basicDetail.accountDetails[key] | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                    <span ng-switch-default>
                                        {{basicDetail.accountDetails[key] ? basicDetail.accountDetails[key] : "-"}}
                                    </span>
                                </span>
                        </div>
                        <div class="col s5">
                            {{value.comment ? value.comment : "-"}}
                        </div>
                    </div>
                </li>
            </div>
        </ul>
    </div>

    <div id="dispatchLocationChanges" class="col s12 margin-bottom-10">
        <h5 class="red-text">Dispatch Location Details</h5>

        <div data-ng-repeat="location in basicDetailCheckBoxes.dispatchLocations" data-ng-init="parentIndex = $index">
            <h6 class="orange-text margin-bottom-5">{{basicDetail.dispatchLocations[parentIndex].locationName}}</h6>
            <ul class="collection">
                <li class="collection-item list-head">
                    <div class="row">
                        <div class="col s3">Key</div>
                        <div class="col s3">Default Value</div>
                        <div class="col s5">Comment</div>
                    </div>
                </li>
                <div data-ng-repeat="(key,value) in location | hideFilter:'vendorDetail,locationId,locationName,updatedBy'">
                    <li class="collection-item" data-ng-show="value.changesRequired==true">
                        <div class="row" style="margin-bottom: 0;">

                            <div class="col s3 bold" data-change-form data-ng-model="key">{{key}}</div>
                            <div class="col s3">
                                <span class="uppercase-text" ng-switch="key">
                                    <span ng-switch-when="notificationType" class="chip">
                                        {{basicDetail.dispatchLocations[parentIndex][key].notification}}
                                    </span>
                                    <span ng-switch-when="updateTime">
                                        {{basicDetail.dispatchLocations[parentIndex][key] | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                    <span ng-switch-when="applyTax">
                                        {{basicDetail.dispatchLocations[parentIndex][key] ? "YES" : "NO"}}
                                    </span>
                                    <span ng-switch-when="gstinDocument">
                                        {{basicDetail.dispatchLocations[parentIndex][key].documentLink}}
                                    </span>
                                    <span ng-switch-default>
                                        {{basicDetail.dispatchLocations[parentIndex][key] ? basicDetail.dispatchLocations[parentIndex][key] : "-"}}
                                    </span>
                                </span>
                            </div>
                            <div class="col s5">
                                {{value.comment ? value.comment : "-"}}
                            </div>
                        </div>
                    </li>
                </div>
            </ul>
        </div>
    </div>

    <hr>
    <div class="row">
        <button class="btn right " data-ng-click="submit()">SUBMIT</button>
    </div>

</script>
