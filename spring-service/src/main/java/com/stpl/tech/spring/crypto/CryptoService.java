package com.stpl.tech.spring.crypto;

import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.KeyPair;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;

public interface CryptoService {

	KeySpec generatePasswordBasedKeySpec(String password, String salt, int iterations, int keyLength);

	SecureRandom getsecureRandom(byte[] IV);

	Secret<PERSON>ey generateAESSecretKey(KeySpec spec) throws NoSuchAlgorithmException, InvalidKeySpecException;

	Cipher getAESECBCipher(String mode, SecretKey secretKey, boolean refresh, Cipher cipher)
			throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException,
			InvalidAlgorithmParameterException;

	String decrypt(String message, Cipher cipher)
			throws IllegalBlockSizeException, BadPaddingException;

	String encrypt(String message, Cipher cipher)
			throws IllegalBlockSizeException, BadPaddingException;

	Cipher getRSACipher(String mode, Key key, boolean refresh, Cipher cipher)
			throws InvalidKeyException, NoSuchAlgorithmException, NoSuchPaddingException;

	KeyPair getRSAKeyPair(int bits) throws NoSuchAlgorithmException;

}
