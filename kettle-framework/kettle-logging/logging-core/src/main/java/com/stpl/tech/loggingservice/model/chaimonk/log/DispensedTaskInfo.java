package com.stpl.tech.loggingservice.model.chaimonk.log;


import java.util.ArrayList;
import java.util.HashMap;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;


@Getter
@Setter
@Document(collection = "dispenserTaskLogs")
public class DispensedTaskInfo {

    private String dispensingId;
    private Object workTask;
    private String receivedTime;
    private String startTime;
    private String endTime;
    private Object dispensableItems = new HashMap<>();
    private ArrayList<String> unDispensableItems = new ArrayList<>();

    private Object completedDispenseItems = new HashMap<>();
    private Object failedDispenseItems = new HashMap<>();
    private String isDisconnectedMidway;

    private String bowlState;
    private Object dispenserLogs;
}

