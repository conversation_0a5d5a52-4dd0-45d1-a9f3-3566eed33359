/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
    .module('posApp')
    .service(
        'productService',
        ['$modal', 'posAPI', 'AppUtil', '$rootScope', '$cookieStore', 'trackingService', function ($modal, posAPI, AppUtil, $rootScope, $cookieStore, trackingService) {
            var itemsPerRow = 5;
            var service = {};
            service.orderItemArray = [];
            service.pointsRedeemed = 0;
            service.orderItemIndex = 1;

            service.transactionObject = {};
            service.addNewItemForCombo = AppUtil.addNewItemForCombo;
            service.orderTemplate = {
                employeeId: AppUtil.GetLoggedInUser(),
                hasParcel: false,
                status: null,
                cancelReason: null,
                orders: null,
                transactionDetail: null,
                settlementType: "DEBIT",
                source: "CAFE",
                settlements: null,
                unitId: null,
                billStartTime: null,
                billCreationSeconds: null,
                billCreationTime: null,
                billingServerTime: null,
                channelPartner: null,
                customerId: null,
                deliveryPartner: null,
                pointsRedeemed: null,
                terminalId: $rootScope.globals.currentUser.terminalId
            };

            service.metaDataList = {
                metadata: [],
                addAttribute: function (key, value) {
                    var found = false;
                    service.metaDataList.metadata.forEach(function (item) {
                        if(item.attributeName==key){
                            found = true;
                            item.attributeValue = value;
                        }
                    });
                    if(!found){
                        service.metaDataList.metadata.push({
                            attributeName: key,
                            attributeValue: value
                        });
                    }
                },
                getList: function () {
                    return service.metaDataList.metadata;
                },
                resetList: function () {
                    service.metaDataList.metadata = [];
                },
                removeAttribute: function (key) {
                    service.metaDataList.metadata.forEach(function (item,index) {
                        if(item.attributeName==key){
                            service.metaDataList.metadata.splice(index,1);
                        }
                    });
                },
                getAttributeValue: function (key) {
                	var data = null;
                    service.metaDataList.metadata.forEach(function (item,index) {
                        if(item.attributeName==key){
                        	data = item;
                        }
                    });
                    return data;
                },
                appendAttribute: function (key, value) {
                    var found = false;
                    service.metaDataList.metadata.forEach(function (item) {
                        if(item.attributeName==key){
                            found = true;
                            item.attributeValue = item.attributeValue + '::' + value;
                        }
                    });
                    if(!found){
                        service.metaDataList.metadata.push({
                            attributeName: key,
                            attributeValue: value
                        });
                    }
                },
            };

            service.customerSocketWidget = {
                detailsEntered: function () {
                  return !AppUtil.isEmptyObject(service.customerSocketWidget.renderedTemplate);
                },
                updateTemplate: function (message, callback) {
                    message = message;
                    var text = service.customerSocketWidget.templateSource[Object
                        .keys(message)[0]];
                    return service.customerSocketWidget.renderTemplate(
                        message, text, callback);
                },
                resetTemplate: function () {
                    service.customerSocketWidget.renderedTemplate = {};
                },
                renderTemplate: function (message, text, callback) {
                    var key = Object.keys(message)[0];
                    var customerObject = message[key];
                    if(["GIFT_CARD_CODE_ADDED","GIFT_CARD_ADDED","GIFT_CARD_REMOVED","GIFT_CARDS_REVALIDATE","GIFT_CARD_REMOVED_ALL",
                        "PAY_AMOUNT","CHAAYOS_WALLET_BALANCE", "SUBSCRIPTION_SAVINGS","SUBSCRIPTION_SAVINGS_CLOSE"].indexOf(key)<0){
                        AppUtil.customerSocket = customerObject;
                    }
                    if (text == undefined) {
                        return service.customerSocketWidget.renderedTemplate;
                    }

                    switch (key) {
                        case "SCREEN_OPENED":
                            if (callback != undefined) {
                                callback(undefined, undefined, key);
                            }
                            break;
                        case 'PROCESS_STARTED':
                            text = text.replace('${message}',
                                AppUtil.getCoveredCustomerContact(customerObject.contact));
                            console.log(text);
                            break;
                        case 'DETAILS_ENTERED':
                            var contactDetails = service.customerSocketWidget.renderContact(customerObject);
                            if (customerObject.email == null) {
                                service.metaDataList.addAttribute("EMAIL_REQUESTED", "Y");
                            }
                            text = text.replace('${message}', contactDetails);
                            console.log(text);
                            if (callback != undefined) {
                                callback(undefined, customerObject.newCustomer, key);
                            }
                            if(customerObject.contact != null){
                                try{trackingService.trackUser(customerObject.contact)}catch(e){}
                                try{trackingService.trackUserData({name:customerObject.name,email:customerObject.email,contact:customerObject.contact,
                                    loyaltea:customerObject.loyalityPoints, aquisitionSrc:customerObject.newCustomer?"CAFE":null,
                                    numberVerified:customerObject.contactVerified, countryCode:"+91", emailVerified:customerObject.emailVerified,
                                    addTime:customerObject.addTime, numberVerificationTime:customerObject.numberVerificationTime})}catch(e){}
                            }
                            break;
                        case 'EMAIL_ENTERED':
                        	text = text.replace('${message}',
                    				(customerObject.email == null || customerObject.email == "") ? "NO"
                                        : "YES");
                            service.metaDataList.addAttribute(key,
                                customerObject.email);
                            try{trackingService.trackEmailRegistered({email:customerObject.email})}catch(e){}
                            if (callback) {
                                callback(undefined,
                                    customerObject.newCustomer, key);
                            }
                            break;
                        case 'OTP_SENT':
                            var name = service
                                .stringEmptyCheck(customerObject.name) ? "to "
                            + customerObject.name
                                : "";
                            text = text.replace("${message}", name);
                            if (callback) {
                                callback(undefined,
                                    customerObject.newCustomer, key);
                            }
                            break;

                        case 'PAYMENT_DISABLE':
                            if (callback) {
                                callback(undefined,
                                    customerObject.newCustomer, key);
                            }
                            break;
                        case 'PAYMENT_ENABLED':
                            if (callback) {
                                callback(undefined,
                                    customerObject.newCustomer, key);
                            }
                            break;
                        case 'BIRTHDAY_OFFER':
                            if (callback) {
                                callback(undefined,
                                    customerObject.newCustomer, key);
                            }
                            break;
                        case 'OTP_RESENT':
                            var name = service
                                .stringEmptyCheck(customerObject.name) ? "to "
                            + customerObject.name
                                : "";
                            text = text.replace("${message}", name);
                            service.metaDataList.addAttribute(key, "Y");
                            if (callback) {
                                callback(undefined,
                                    customerObject.newCustomer, key);
                            }
                            try{trackingService.trackOTPResent({contact:customerObject.contact,status:"SUCCESS"})}catch(e){}
                            break;
                        case 'OTP_STATUS':
                            text = text.replace("${message}",
                                !customerObject.contactVerified ? "NO"
                                    : "YES");
                            if (callback) {
                                callback(undefined,
                                    customerObject.newCustomer, key);
                            }
                            break;
                        case 'REDEMPTION_AVAILABLE':
                            text = text.replace("${message}",
                                customerObject.loyalityPoints);
                            service.metaDataList.addAttribute(key,
                                customerObject.loyalityPoints);
                            break;
                        case 'REDEMPTION':
                            text = text.replace("${message}",
                                customerObject.chaiRedeemed);
                            if (!AppUtil.customerSocket.newCustomer) {    // && AppUtil.customerSocket.eligibleForSignupOffer
                                callback(undefined, customerObject.newCustomer, key)
                            }
                            break;
                        case 'REDEEM_LATER':
                            text = text.replace("${message}",
                                customerObject.loyalityPoints);
                            service.metaDataList.addAttribute(key, "Y");
                            break;
                        case 'NEW_CUSTOMER':
                            text = text.replace('${message}',
                                customerObject.newCustomer ? "YES"
                                    : "NO");
                            service.metaDataList.addAttribute(key,
                                customerObject.newCustomer ? "Y" : "N");
                            if (callback) {
                                callback(undefined,
                                    customerObject.newCustomer, key);
                            }
                            break;
                        case 'ELIGIBLE_FOR_SIGNUP_OFFER':
                            text = text.replace('${message}',
                                customerObject.eligibleForSignupOffer ? "YES" : "NO");
                            break;
                        /*case 'RECOMMENDATION':
                            text = text.replace('${message}',
                        	    customerObject.recommendationDetail != null ? customerObject.recommendationDetail.name : "Noting");
                            if (callback) {
                                callback(undefined,
                                    customerObject.newCustomer, key);
                            }
                            break;
                        case 'RECOMMENDATION_RESULT':
                            text = text.replace('${message}',
                        	    customerObject.recommendationDetail != null ? (customerObject.recommendationDetail.availed ? "YES" :"NO") : "NO");
                            if (callback) {
                                callback(undefined,
                                    customerObject.newCustomer, key);
                            }
                            break;*/
                        /*case 'FEEDBACK_PENDING':
                            if (callback) {
                                callback(undefined,
                                    customerObject.newCustomer, key);
                            }
                            break;
                        case 'FEEDBACK_SUBMITTED':
                            if (callback) {
                                callback(undefined,
                                    customerObject.newCustomer, key);
                            }
                            break;*/
                        case 'GIFT_CARD_CODE_ADDED':
                            if (callback) {
                                callback(undefined,
                                    undefined, key);
                            }
                            break;
                        case 'OTP_VERIFIED':
                            if (callback) {
                                callback(undefined,
                                    undefined, key);
                            }
                            break;
                        case 'SKIP_EMAIL_FORM':
                            if (callback) {
                                callback(undefined,
                                    undefined, key);
                            }
                            break;
                        case 'SKIP_EMAIL_INPUT':
                        	text = text.replace('${message}',
                    				(customerObject.email == null || customerObject.email == "") ? "NO"
                                        : "YES");
                            if (callback) {
                                callback(undefined,
                                    undefined, key);
                            }
                            break;
                        case 'EMAIL_FORM_SKIPPED_BY_CUSTOMER':
                        	text = text.replace('${message}',
                    				(customerObject.email == null || customerObject.email == "") ? "NO"
                                        : "YES");
                            service.metaDataList.addAttribute("EMAIL_SKIPPED",
                                "BY CUSTOMER");
                            if (callback) {
                                callback(undefined,
                                    undefined, key);
                            }
                            break;
                        case 'CUSTOMER_SCREEN_UPDATE':
                            bootbox.alert("Customer screen is being updated. Please watch it and take actions.");
                            break;
                        case 'FACE_LOGIN_SKIPPED':
                            service.metaDataList.addAttribute("FACE_SKIPPED", "BY CUSTOMER");
                            if(callback){
                                callback(undefined,undefined,key);
                            }
                            break;
                        case 'PAY_BY_GIFT_CARD':
                            if(callback){
                                callback(undefined,undefined,key);
                            }
                            break;
                        default:
                            text = text.replace("${message}", "");
                            if(callback){
                                callback(undefined,undefined,key);
                            }
                            break;
                    }
                    service.customerSocketWidget.renderedTemplate[key] = text;
                    return service.customerSocketWidget.renderedTemplate;
                },
                renderedTemplate: {},
                templateSource: {
                    SCREEN_OPENED: "<span><strong> Customer prompted for Contact </strong></span><br>",
                    PROCESS_STARTED: "<span>Process initiated with contact number: ${message}</span><br>",
                    DETAILS_ENTERED: "${message}",
                    EMAIL_ENTERED: "<span>Email Entered: <strong>${message}</strong></span><br>",
                    NEW_CUSTOMER: "<span>New Customer: <strong>${message}</strong></span><br>",
                    OTP_SENT: "<span>Verification Request Sent ${message}</span><br>",
                    OTP_RESENT: "<span>OTP Re-sent ${message}</span><br>",
                    OTP_SUBMITTED: "<span>Verification Submitted</span><br>",
                    OTP_STATUS: "<span>Customer verified: <strong>${message}</strong></span><br>",
                    REDEMPTION: "<span>Free chai(s) redeemed: <strong>${message}</strong> </span><br>",
                    REDEEM_LATER: "<span>Loyality points redeemed: <strong>0</strong></span><br>",
                    REDEMPTION_AVAILABLE: "<span>Loyality points available: ${message} </span><br>",
                    ELIGIBLE_FOR_SIGNUP_OFFER: "<span>Eligible for free chai: ${message} </span><br>",
                    //FEEDBACK_PENDING: "<span>Last order feedback pending.</span><br>",
                   // FEEDBACK_SUBMITTED: "<span>Last order feedback submitted.</span><br>",
                    //RECOMMENDATION: "<span>Recommendation : ${message} </span><br>",
                    //RECOMMENDATION_RESULT: "<span>Customer Accepted Recommendation : ${message}</span><br>",
                    GIFT_CARD_CODE_ADDED: "",
                    OTP_VERIFIED: "<span>OTP verified for gift card.</span><br>",
                    SKIP_EMAIL_FORM : "",
                    SKIP_EMAIL_INPUT : "<span>Email Entered: <strong>${message}</strong></span><br>",
                    EMAIL_FORM_SKIPPED_BY_CUSTOMER : "<span>Email Entered: <strong>${message}</strong></span><br>",
                    TC_VERIFICATION: "<span>True Caller Verification initiated for customer</span><br>",
                    PAYMENT_VERIFIED: "<span>Payment is complete by customer</span><br>",
                    PAYMENT_MODE_BY_CUSTOMER: "<span>Customer is making payment</span><br>",
                    TRANSACTION_FAILED_BY_CUSTOMER: "<span>Transaction failed by customer</span><br>",
                    TRANSACTION_TIMED_OUT_BY_CUSTOMER: "<span>Transaction timed out by customer</span><br>",
                    FACE_PROCESS_STARTED: "<span>Face recognition started</span><br>",
                    CUSTOMER_SCREEN_UPDATE: "",
                    FACE_LOGIN_SKIPPED: "<span>Face login skipped by customer</span><br>",
                    PAY_BY_GIFT_CARD:"",
                    PAYMENT_DISABLE:"",
                    PAYMENT_ENABLED:"",
                    BIRTHDAY_OFFER:""
                },
                renderContact: function (customerObject) {
                    var text = "";
                    var name = customerObject.name;
                    var contact = customerObject.contact;
                    var email = customerObject.email;

                    text += service.stringEmptyCheck(name) ? "<span>Process initiated with contact number: "+ AppUtil.getCoveredCustomerContact(customerObject.contact) +"<br/> Name: "
                    + name + "</span><br>"
                        : "";
                   /* text += service.stringEmptyCheck(contact) ? "<span>" +
                    //"Contact: " + AppUtil.getCoveredCustomerContact(contact) + "</span>" +
                    "<br>"
                        : "";*/
                    /*text += service.stringEmptyCheck(email) ? "<span>Email: "
                    + email + "</span><br>"
                        : "";*/

                    return text;
                }
            };

            service.stringEmptyCheck = function (stringText) {
                return (stringText != undefined && stringText != null
                && stringText != "null" && stringText.trim().length != 0);
            };

            function isSpecialOrder(orderType){
        	    return orderType == "employee-meal" || orderType == "complimentary-order" || orderType == "unsatisfied-customer-order" || orderType == "wastage-order";
            }

            function addNewProductToExistingOrderItemArray(productItem, quantity,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping) {
                if(service.orderItemArray.length > 0){
                    var subTypeCount=0;
                    for(var order in service.orderItemArray){
                        if(service.orderItemArray[order].productDetails.subType == AppUtil.getTransactionMetadata().subscriptionProductId){
                            subTypeCount=subTypeCount+1;
                        }
                    }
                    if (subTypeCount>1){
                        bootbox.alert("Chaayos Subscription can be ordered once only!!");
                        return;
                    }
                    if(subTypeCount>0 && productItem.subType ==AppUtil.getTransactionMetadata().subscriptionProductId){
                        bootbox.alert("Chaayos Subscription can be ordered once only!!");
                        return;
                    }
                }
                if(AppUtil.getTransactionMetadata().subscriptionProductId)
                if(typeof(quantity) == "undefined" || quantity == null){
                    quantity = 1;
                }
                var orderItem = null;
                if (productItem.prices.length > 0) {
                	if(isSpecialOrder($rootScope.orderType)){
                    	    	if($rootScope.complimentaryReasonId == null){
                    	    	    $rootScope.complimentaryReasonId = $rootScope.defaultComplimentaryCode.id;
                    	    	    $rootScope.complimentaryReasonCode = $rootScope.defaultComplimentaryCode.name;
                    	    	}
                		orderItem = AppUtil.addNewItem(
                                productItem, quantity, false,service.orderItemIndex,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping);
                		orderItem.orderDetails.complimentaryDetail = {
                                isComplimentary: true,
                                reasonCode: $rootScope.complimentaryReasonId,
                                reason: null
                            };
                        service.orderItemArray.push(orderItem);
                	}else {
                	service.orderItemIndex = setNextIndex(service.orderItemArray);
                        orderItem = AppUtil.addNewItem(productItem, quantity, false, service.orderItemIndex,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping);
                        service.orderItemArray.push(orderItem);
                    }
                    service.orderItemIndex++;
                    return orderItem;
                }
            }

            function setNextIndex(orderItemArray){

        	var maxIndex =0;
        	for(var i=0;i<orderItemArray.length;i++){
        	    if(maxIndex <= orderItemArray[i].orderDetails.itemId){
        		maxIndex=orderItemArray[i].orderDetails.itemId;
        	    }
        	}
        	return maxIndex + 1;
            }
            service.getTransactionObject = function () {

                var transactionDetails = {
                    totalAmount: 0,
                    taxableAmount: 0,
                    savings: 0,
                    discountDetail: {
                        discount: {
                            percentage: 0,
                            value: 0,
                            wasValueSet: false
                        },
                        discountReason: null,
                        discountCode: null,
                        totalDiscount: 0
                    },
                    paidAmount: 0,
                    roundOffValue: 0,
                    tax : 0,
                    taxes : []
                };
                // //console.log(AppUtil.discountObj);
                if (AppUtil.discountObj.percentage != 0) {
                    transactionDetails.discountDetail.discount.percentage = AppUtil.discountObj.percentage;
                    transactionDetails.discountDetail.discountCode = AppUtil.discountObj.discountCode;
                    transactionDetails.discountDetail.discountReason = AppUtil.discountObj.discountReason;
                }

                service.transactionObject = transactionDetails;
                return service.transactionObject;
            };

            service.changeDimension = function (orderItem, dimension) {
                var dimensionObj = orderItem.productDetails.prices[0];
                for(var i in orderItem.productDetails.prices){
                    if(orderItem.productDetails.prices[i].dimension == dimension){
                        dimensionObj = orderItem.productDetails.prices[i];
                        break;
                    }
                }
                orderItem.orderDetails.price = dimensionObj.price;
                orderItem.orderDetails.dimension = dimensionObj.dimension;
            };

            service.addNewProductToOrderItemArray = function (productItem, qty,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping) {
                var orderItem = null;
                if (service.orderItemArray.length > 0) {
                    var last_element = service.orderItemArray[service.orderItemArray.length - 1];
                    if(last_element.orderDetails.productId == productItem.id
                        && last_element.productDetails.subType == AppUtil.getTransactionMetadata().subscriptionProductId){
                        bootbox.alert("Chaayos Select can be ordered once only!!");
                        return;
                    }
                    if (last_element.orderDetails.productId == productItem.id && (last_element.isModified == undefined || !last_element.isModified)
                        && last_element.orderDetails.dimension == productItem.prices[0].dimension
                        && last_element.orderDetails.complimentaryDetail.isComplimentary == false
                        && (last_element.productDetails.parentProductId == undefined
                            || last_element.productDetails.parentProductId == null)
                        && productItem.taxCode != "GIFT_CARD" && productItem.taxCode != "COMBO" && ( isDefaultFavChaiSelected===last_element.isDefaultFavChaiSelected)) {

                        last_element.orderDetails.quantity += qty!=undefined ? qty : 1;
                        last_element.orderDetails.amount = (last_element.orderDetails.quantity)
                                                                * (last_element.orderDetails.price);

                        orderItem = last_element;
                    }else if (!last_element.chaiSavedFromDineIn && isDefaultFavChaiSelected !=undefined && isDefaultFavChaiSelected && last_element.isDefaultFavChaiSelected!=undefined
                        && last_element.isDefaultFavChaiSelected && last_element.orderDetails.complimentaryDetail.isComplimentary == false && ((customerFavChaiMapping !=undefined && customerFavChaiMapping.sourceId !=21) || customerFavChaiMapping==undefined)){
                        last_element.orderDetails.quantity += qty!=undefined ? qty : 1;
                        last_element.orderDetails.amount = (last_element.orderDetails.quantity)
                            * (last_element.orderDetails.price);

                        orderItem = last_element;
                    }else if (last_element.chaiSavedFromDineIn !=undefined && last_element.chaiSavedFromDineIn &&  last_element.orderDetails.productId ==productItem.id && customerFavChaiMapping !=undefined && customerFavChaiMapping !=null && customerFavChaiMapping.tagType!=undefined && customerFavChaiMapping.tagType !=null &&  last_element.tagType == customerFavChaiMapping.tagType ){
                        last_element.orderDetails.quantity += qty!=undefined ? qty : 1;
                        last_element.orderDetails.amount = (last_element.orderDetails.quantity)
                            * (last_element.orderDetails.price);

                        orderItem = last_element;
                    } else {
                        orderItem = addNewProductToExistingOrderItemArray(productItem,qty,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping);
                    }
                } else {
                    orderItem = addNewProductToExistingOrderItemArray(productItem,qty,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping);
                }
                return orderItem;
            };

            service.addNewProductToOrderItemArrayByProductId = function (productId, parentItemId, parentProductName, quantity) {
                var orderItem = null;
                var productItem = null;
                for (var i = 0; i < AppUtil.getUnitDetails().products.length; i++) {
                    var productObj = angular.copy(AppUtil.getUnitDetails().products[i]);
                    if(productObj.id==productId){
                        productItem = productObj;
                        productItem.parentProductId = parentItemId;
                        productItem.parentProductName = parentProductName;
                    }
                }
                if(productItem!=null){
                    if (AppUtil.isPaidEmployeeMeal()) {
                        var empMealPrice = AppUtil.getUnitDetails().empMealPrices[productItem.productId + '_' + productItem.prices[0].dimension];
                        if(empMealPrice != null){
                            orderItem = addNewProductToExistingOrderItemArray(productItem, quantity);
                        }
                    }else{
                        orderItem = addNewProductToExistingOrderItemArray(productItem, quantity);
                    }
                }
                return orderItem;
            };

            service.addNewProductviaRedeemedPoints = function (productItem, quantity,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping) {
                var orderItem = AppUtil.addNewItem(productItem,
                    quantity, true,service.orderItemArray.length+1,customizationList,isDefaultFavChaiSelected);
                orderItem.orderDetails.complimentaryDetail = {
                    isComplimentary: true,
                    reasonCode: 2101,
                    reason: null
                };
                orderItem.orderDetails.discountDetail.promotionalOffer =  orderItem.orderDetails.amount;
                if(orderItem.isFavChaiMarked !=undefined && orderItem.isFavChaiMarked !=null && orderItem.isFavChaiMarked){
                    if (orderItem.recipeDetails!=null && orderItem.recipeDetails.options != null && orderItem.recipeDetails.options.length > 0) {
                        for (var i in orderItem.recipeDetails.options) {
                            if (orderItem.recipeDetails.options[i].selected &&
                                orderItem.recipeDetails.options[i].type == "PRODUCT") {
                                service.addNewProductToOrderItemArrayByProductId(orderItem.recipeDetails.options[i].id,
                                    orderItem.orderDetails.itemId, orderItem.productDetails.name, orderItem.orderDetails.quantity);
                            }
                        }
                    }
                }
                if(customerFavChaiMapping!=undefined && customerFavChaiMapping !=null){
                    if(customerFavChaiMapping.productId == orderItem.orderDetails.productId){
                        if(customerFavChaiMapping.sourceId!=undefined && customerFavChaiMapping.sourceId !=null && customerFavChaiMapping.sourceId !=AppUtil.cafePartnerId){
                            orderItem.chaiSavedFromDineIn = true ;
                        }
                    }
                }

                service.orderItemArray.push(orderItem);
                service.orderItemIndex++;
                return service.orderItemArray;
            };

           /* service.addNewProductViaRecommendation = function (productItem, quantity, recommendation) {
                var orderItem = AppUtil.addNewItem(productItem,
                    quantity, false,service.orderItemArray.length+1);
                orderItem.orderDetails.recommended = true;
                if(!$rootScope.offerApplied && recommendation.offer){
                    service.applyRecommendationOffer(orderItem, recommendation);
                }
                service.orderItemArray.push(orderItem);
                return service.orderItemArray;
            };*/

            /*service.applyRecommendationOffer = function(orderItem, recommendation){
        	if(recommendation.skipRecommendationOfferData){
        	    return;
        	}
        	if(recommendation.offer){
                     var payableAmount = orderItem.orderDetails.price * orderItem.orderDetails.quantity;
                     var recommendationOffer = (payableAmount * recommendation.discount / 100).toFixed(2);
                       	 orderItem.orderDetails.discountDetail = service.getDefaultDiscount();
                         orderItem.orderDetails.discountDetail.promotionalOffer = recommendationOffer;
                         orderItem.orderDetails.discountDetail.discountReason = recommendation.couponCode;
                         orderItem.orderDetails.complimentaryDetail = {
                                 isComplimentary: false,
                                 reasonCode: null,
                                 reason: null
                             };
                     }
            }*/

            /*service.clearRecommendationOffer = function(couponCode, orderItem, recommendation){
    	    	orderItem.orderDetails.discountDetail = service.getDefaultDiscount();
                orderItem.orderDetails.complimentaryDetail = {
                        isComplimentary: false,
                        reasonCode: null,
                        reason: null
                    };
            };*/
            service.removeRedeemedProducts = function () {
                /*for(var i=(service.orderItemArray.length-1);i>=0;i--){
                    if(service.orderItemArray[i].orderDetails.hasBeenRedeemed){
                        service.orderItemArray.splice(i,1);
                    }
                }*/
                service.orderItemArray.forEach(function (item, index) {
                    if(item.orderDetails.hasBeenRedeemed){
                        item.orderDetails.hasBeenRedeemed = false;
                        item.orderDetails.complimentaryDetail = {
                            isComplimentary: false,
                            reasonCode: null,
                            reason: null
                        };
                        item.orderDetails.discountDetail.promotionalOffer =  0;
                    }
                });
            };

            service.removeRedeemedProduct = function (productId) {
                service.orderItemArray.forEach(function (item, index) {
                    if(item.orderDetails.hasBeenRedeemed && item.orderDetails.productId==productId){
                        service.orderItemArray.splice(index,1);
                    }
                });
            };

            service.clearOffer = function (orderItems, offerCode, transactionObj) {
                offerCode = "";
                service.checkAndRemoveFreeItems(orderItems);
                orderItems.forEach(function (orderItem) {
                    orderItem.orderDetails.discountDetail = service
                        .getDefaultDiscount();
                });
                AppUtil.discountObj = service.getDefaultDiscount();
                transactionObj.discountDetail = service.getDefaultDiscount();
                transactionObj.discountDetail.wasValueSet = false;
            };

            service.checkAndRemoveFreeItems = function (orderItems) {
                var freeItemIndexes = [];
                for(var i in orderItems){
                    if(orderItems[i].freeOfferItem){
                        freeItemIndexes.push(i);
                    }
                }

                for(var i in freeItemIndexes){
                    orderItems.splice(freeItemIndexes[i],1);
                }
            };

            service.getDefaultDiscount = function () {
                var defaultDiscount = {
                    discountCode: null,
                    discountReason: null,
                    promotionalOffer: 0,
                    discount: {
                        percentage: 0,
                        value: 0
                    },
                    totalDiscount: 0
                };
                return defaultDiscount;
            };

            service.clearOrderArray = function () {
                service.orderItemArray = [];
                return service.orderItemArray;
            };

            service.getOrderArray = function () {
                return service.orderItemArray;
            };

            service.setOrderArray = function (orderItemArray) {
            	service.orderItemArray = orderItemArray;
                return service.orderItemArray;
            };

            service.getPointsRedeemed = function () {
                return service.pointsRedeemed;
            };

            service.getOrder = function () {
                return service.orderTemplate;
            };

            service.buildOrderItemsForOrder = function (orderItemArray) {
                var orderItems = [];
                ////console.log('Final Order Item Arrar ', orderItemArray);
                for (var i = 0; i < orderItemArray.length; i++) {
                    orderItems.push(orderItemArray[i].orderDetails);
                }
                return orderItems;
            };
            service.prepareOrder = function (orderItemArray,
                                             transactionObject) {
                service.orderTemplate.customerId = AppUtil.customerSocket.id;
                service.orderTemplate.transactionDetail = transactionObject;
                service.orderTemplate.orders = service.buildOrderItemsForOrder(orderItemArray); // getting just the orderDetails
                service.orderTemplate.unitId = AppUtil.getUnitDetails().id;
                if (AppUtil.isCOD()) {
                    service.orderTemplate.status = 'CREATED';
                    service.orderTemplate.source = 'COD';
                    // //console.log(AppUtil.outlet);
                    if (AppUtil.outlet.selectedId == 1) {
                        service.orderTemplate.unitId = AppUtil.outlet.pri_unitId;
                    } else if (AppUtil.outlet.selectedId == 2) {
                        service.orderTemplate.unitId = AppUtil.outlet.sec_unitId;
                    } else {
                        service.orderTemplate.unitId = AppUtil.outlet.ter_unitId;
                    }
                    service.orderTemplate.sourceId = $cookieStore.get('globals').currentUser.unitId;
                    service.orderTemplate.deliveryAddress = AppUtil.customerDeliveryAddress;
                    service.orderTemplate.customerId = AppUtil.CSObj.id;
                }
                ////console.log("Customer obj",
                //		service.orderTemplate.customerId,
                //		AppUtil.CSObj.id, AppUtil.customerSocket.id);
                ////console.log("orderTemplate", service.orderTemplate);
                return service.orderTemplate;
            };

            service.getProductArrayForSubTypeCode = function (subTypeCode) {
                // //console.log("inside product array for sub type :::
                // ",subTypeCode);
                var productArray = [];
                for (var i = 0; i < AppUtil.getUnitDetails().products.length; i++) {
                    var productObj = AppUtil.getUnitDetails().products[i];
                    if(productObj.type == 43) {
                        productObj.type = 8;
                        productObj.subType = 801;
                    }
                    if (productObj.subType == subTypeCode && [1043,1044].indexOf(productObj.id) < 0) {
                    	if($rootScope.orderType == "employee-meal"){
                    		if(productObj.employeeMealComponent){
                        		productArray.push(productObj);
                    		}
                    	} else if (AppUtil.isPaidEmployeeMeal()) {
                    		if(productObj.type != 8 && productObj.type != 9 && productObj.type != 12){
								for (var j = 0; j < AppUtil.getUnitDetails().employeeMealProducts.length; j++) {
									var empProductObj = AppUtil.getUnitDetails().employeeMealProducts[j];
									if (productObj.id == empProductObj.id) {
										productArray.push(productObj);
									}
								}
							 }
                    	} else if ($rootScope.orderType == "complimentary-order" || $rootScope.orderType == "unsatisfied-customer-order"|| $rootScope.orderType == "wastage-order"){
                    	    if(productObj.type != 8){
                    		productArray.push(productObj);
                    	    }
                    	} else {
                    		productArray.push(productObj);
                    	}

                    }
                }
                // //console.log(productArray);
                return productArray;
            };

            service.subArrayfromSubCategory = function (productsForSubCategory, rowNo, length) {
                var itemsArray = [];
                var indexOfitemsAlreadyShown = (rowNo * itemsPerRow);
                var indexOfLastItem = indexOfitemsAlreadyShown + length;
                itemsArray = productsForSubCategory.slice(
                    indexOfitemsAlreadyShown, indexOfLastItem);
                return itemsArray;
            };

            service.getOrderItemForBillType = function (orderItemArray,
                                                        billType) {
                var orderItemsForBillType = [];
                if (orderItemArray.length > 0) {
                    for (var i = 0; i < orderItemArray.length; i++) {
                        if (orderItemArray[i].productDetails.billType == billType) {
                            orderItemsForBillType
                                .push(orderItemArray[i]);
                        }
                    }
                }
                return orderItemsForBillType;
            };

            /*function getRecipe(orderItemArray, itemId){
                for(var index in orderItemArray){
                    if(orderItemArray[index].orderDetails.itemId == itemId){
                        return orderItemArray[index].recipeDetails;
                    }
                }
            }*/

            function getMatchedOrderItem(orderItemArray, itemId){
                for(var index = 0; index < orderItemArray.length; index ++){
                    if(orderItemArray[index].orderDetails.itemId == itemId){
                        return orderItemArray[index];
                    }
                }
            }

            service.setOrderItemArray = function (orderItemArray,
                                                  orderItems) {
                var redeemMap = makeMapOfRedeemed(service.orderItemArray);
                service.orderItemArray = []; // clearing the
                // service's
                // orderItemArray
                for (var index = 0; index < orderItems.length; index ++) {
                    var productIdObj = {};
                    var orderItem = orderItems[index];
                    var matchedItem = getMatchedOrderItem(orderItemArray,orderItem.itemId);
                    productIdObj = matchedItem;
                    productIdObj.orderDetails.discountDetail = orderItem.discountDetail;
                    /*productIdObj.productDetails = AppUtil.getProductForId(orderItem.productId);
                    productIdObj.recipeDetails = matchedItem.recipeDetails;
                    productIdObj.orderDetails.hasBeenRedeemed = redeemMap.get(makeKeyForOrderItem(matchedItem));*/
                    service.orderItemArray.push(productIdObj);
                }
                console.log(service.orderItemArray);
                return service.orderItemArray;
            };

            function makeMapOfRedeemed(orderItemArray) {
                var redeemMap = {};
                for (var index in orderItemArray) {
                    var orderItem = orderItemArray[index].orderDetails;
                    redeemMap[makeKeyForOrderItem(orderItemArray[index])] = orderItem.hasBeenRedeemed;
                }
                // //console.log(redeemMap);
                return redeemMap;
            }

            function makeKeyForOrderItem(orderItem) {
                var redeemFlag = orderItem.orderDetails.hasBeenRedeemed ? 'Y' : 'N';
                var addOnString = AppUtil.getCustomizationAbb(orderItem);
                // this is subject to error until the order item array is fixed
                var key = orderItem.orderDetails.productId + '[' + addOnString + ']' + '[' + redeemFlag + ']';
                return key;
            }

            service.modifyTransactionObject = function (transactionObj) {
                service.transactionObject = transactionObj;
                AppUtil.discountObj.percentage = transactionObj.discountDetail.discount.percentage;
                AppUtil.discountObj.discountCode = transactionObj.discountDetail.discountCode;
                AppUtil.discountObj.discountReason = transactionObj.discountDetail.discountReason;
                // //console.log(service.transactionObject);
                return service.transactionObject;
            };

            service.redeem = function (callback) {
                var requestObj = $rootScope.globals.currentUser;
                posAPI.allUrl('/',AppUtil.restUrls.order.orderSession)
                    .post(requestObj)
                    .then(
                        function (response) {
                            var unitDetailSession = response
                                .plain();
                            if (unitDetailSession.customer == null) {
                                // //console.log("if
                                // statement",unitDetailSession);
                                AppUtil
                                    .myAlert("No Active Customer session detected");
                            } else {
                                // //console.log("else
                                // statement",unitDetailSession);
                                callback(unitDetailSession);
                            }
                        },
                        function (err) {
                            AppUtil
                                .myAlert(err.data.errorMessage);
                        });
            };

            service.addRedemptionToOrderWithAllProducts = function (count,redemptionCount,
                    callback, customerFavChaiMappings, redemptionChaiProductIds) {
					////console.log("Product id is :::: ", productId);
                    var customerObj ={};
                    AppUtil.getCustomerBasicInfo(function(info){
                        customerObj= info ;
                    });
					var modalInstance = $modal.open({
						animation: true,
						templateUrl: window.version + "views/redemptionSelectionModal.html",
						controller: 'redemptionSelectionCtrl',
						backdrop: 'static',
						size: "lg",
						resolve: {
						redemptionCount: function () {
						return redemptionCount + 1;
					},
                    customerFavChaiMappings: function (){
                        return customerFavChaiMappings;
                    },

                    redemptionChaiProductIds: function(){
                            return redemptionChaiProductIds;
                    },
                    customerObj:function(){
                            return customerObj;
                    }
					}
					});
					modalInstance.result.then(function (result) {
					var productId = result;
					AppUtil.getUnitDetails().products
						.forEach(function (product) {
						if (product.id == productId) {
						if (callback) {
						var pointsRedeemed = (60 * count);
						callback(pointsRedeemed, undefined,
						   "REDEMPTION");
						}
                            var customizationList = [];
                            var currentObj = {};
                            var isDefaultFavChaiSelected = false;
                            if (AppUtil.isFavChaiRedeemed) {
                                /*for (var i in customerFavChaiMappings) {
                                    var customerFavChaiMapping = customerFavChaiMappings[i];
                                    if (customerFavChaiMapping.productId == productId) {
                                        if (customerFavChaiMapping.favChaiCustomizationDetailList != null && customerFavChaiMapping.favChaiCustomizationDetailList != undefined && customerFavChaiMapping.favChaiCustomizationDetailList.length > 0) {
                                            customizationList = [];
                                            var list = customerFavChaiMapping.favChaiCustomizationDetailList;
                                            for (var i in list) {
                                                customizationList.push(list[i].name)
                                            }
                                        }
                                    }
                                }*/
                                currentObj = AppUtil.currentCustomerFavChaiMapping;
                                if (currentObj.productId == productId) {
                                    if (currentObj.favChaiCustomizationDetailList != null && currentObj.favChaiCustomizationDetailList != undefined && currentObj.favChaiCustomizationDetailList.length > 0) {
                                        customizationList = [];
                                        var list = currentObj.favChaiCustomizationDetailList;
                                        for (var i in list) {
                                            customizationList.push(list[i].name)
                                        }
                                    }
                                }
                                isDefaultFavChaiSelected = true;
                            }
					var newOrderArray = service
						.addNewProductviaRedeemedPoints(
						   product, count,customizationList,isDefaultFavChaiSelected,currentObj);
						console
					.log(
					   "order array after function call ::::: ",
					   newOrderArray);
					return true;
					}
					});
					}, function () {
					//console.log("modal instance dismissed");
					});
			};

            service.addRedemptionToOrder = function (count,productId,
                                                     callback) {
                	AppUtil.getUnitDetails().products
                    .forEach(function (product) {
                        if (product.id == productId) {
                            if (callback) {
                                var pointsRedeemed = (60 * count);
                                callback(pointsRedeemed, undefined,
                                    "REDEMPTION");
                            }
                            var newOrderArray = service
                                .addNewProductviaRedeemedPoints(
                                    product, count);
                            console
                                .log(
                                    "order array after function call ::::: ",
                                    newOrderArray);
                            return true;
                        }
                    });
            };


            service.checkGiftCardInOrder = function(){
              var zeroTaxItemList = service.orderItemArray.filter(function(orderItem){
                    return orderItem.productDetails.taxCode == "GIFT_CARD";
              });
              return zeroTaxItemList.length>0;
            };

            service.resetorderItemIndex = function () {
                service.orderItemIndex = 1;
            };

            return service;
        }]);
