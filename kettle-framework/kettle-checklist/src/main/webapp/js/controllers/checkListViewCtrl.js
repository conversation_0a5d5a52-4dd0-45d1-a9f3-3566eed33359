angular.module('listapp').controller("checkListViewCtrl",
		function($rootScope,$scope, $location, $http, AppUtil) {
	$scope.init = function () {
			if($scope.currentStation == null){
				$location.path("dashboard")
			}
	 	}
	 	$scope.checkStatus = function(){
	 	console.log($scope.currentInstance);
	 	}
		$scope.currentInstance = $rootScope.currentInstance;
		$scope.submitChecklist = function(){
			var flag=true;
			$scope.currentInstance.userId 	= $scope.user.id
			$scope.currentInstance.userName = $scope.user.name
			console.log("completeObj=",$rootScope.currentInstance);
			for(var index in $rootScope.currentInstance.checkList.items)
				{
				if($rootScope.currentInstance.checkList.items[index].responseType=="Yes/No"){
				//console.log("FF=",$rootScope.currentInstance.checkList.items[index])
				if($rootScope.currentInstance.checkList.items[index].comment==null && $rootScope.currentInstance.checkList.items[index].response==null)
						{
						var flag=false;
							alert($rootScope.currentInstance.checkList.items[index].action+" comment or checkbox is empty");
							break;
						}
				}
				else 
					{
					if($rootScope.currentInstance.checkList.items[index].responseType=="text"){
					// console.log($rootScope.currentInstance.checkList.items[index]);
					 if($rootScope.currentInstance.checkList.items[index].response=="" || $rootScope.currentInstance.checkList.items[index].response==null)
						{
						 var flag=false;
						 alert($rootScope.currentInstance.checkList.items[index].action+" text is empty");
							break;
						}
				
				}
				}
				}
			
			//console.log("flagValue=",flag)
			//console.log("testing111=",$scope.currentInstance)
			if(flag){
			console.log("testing222=",$scope.currentInstance)
			$rootScope.showFullScreenLoader = true;
			$http({
				method : 'POST',
				url : AppUtil.restUrls.checkList.submit,
				data : $scope.currentInstance
			})
			.then(
			function success(response) {
				//alert("Submitted Successfully");
				//$("#btnSubmit").show();
				console.log("res",response);
				if(response.status==200)
					{
					$rootScope.showFullScreenLoader = false;
					alert("Check List Successfully Submited");
					window.location.reload();
					}
				else if(response.status==500)
					{
					$rootScope.showFullScreenLoader = false;
					alert("Failed due to "+response.data.errorMessage);
					}
				
				$scope.allCheckList.forEach(function(value)
				{
					//console.log("value=",value.id);
					//console.log("response=",response.data.id);
					if(value.id === response.data.id){
						value = response.data;
					}
				});
				$rootScope.$broadcast("showCheckList", {checkListView:$scope.allCheckList});
				$scope.$parent.showStationList($scope.currentInstance.checkList.station);
				//window.location.reload();
				
				//console.log("fraheem11",$scope.currentInstance.checkList.station);
				if(response.data.submissionTime!=null || undefined)
					{
					 var stringToTimestamp = new Date().getTime();
					//console.log(stringToTimestamp.getTime());​
					 console.log("timeStamp=",stringToTimestamp);
					 timeDifference(stringToTimestamp,response.data.submissionTime);
					}
			}, function error(response) {
				alert("Failed");
				$("#btnSubmit").show();
				console.log("error:" + response);
			});
			}
		};
		
		/*$scope.getChecked = function(status){
			if (status === 'true') {
		        return true;
		    } else if (status === 'false') {
		        return false;
		    } else {
		        return '';
		    }
		};*/
		$scope.minutesScreen=[];
		 function  timeDifference(date1,date2) {
	        var difference = date1 - date2;
	        var daysDifference = Math.floor(difference/1000/60/60/24);
	        difference -= daysDifference*1000*60*60*24
	        var hoursDifference = Math.floor(difference/1000/60/60);
	        difference -= hoursDifference*1000*60*60
	        var minutesDifference = Math.floor(difference/1000/60);
	        difference -= minutesDifference*1000*60
	        var secondsDifference = Math.floor(difference/1000);
	        console.log('difference = ' + daysDifference + ' day/s ' + hoursDifference + ' hour/s ' + minutesDifference + ' minute/s ' + secondsDifference + ' second/s ');
	        $scope.minutesScreen	=	minutesDifference;
	};
		});
/*angular.module('listapp').directive('stringToTimestamp', function() {
    return {
      // require: '',
        link: function(scope, ele, attr) {
            // view to model
            ngModel.$parsers.push(function(value) {
                return Date.parse(value);
            });
        }
    }
});*/