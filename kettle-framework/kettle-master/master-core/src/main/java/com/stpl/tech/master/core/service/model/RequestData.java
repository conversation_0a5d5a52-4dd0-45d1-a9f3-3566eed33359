/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service.model;

import java.io.Serializable;

public class RequestData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2672861260775262924L;

	private UserSessionDetail session;

	private String data;

	public RequestData() {
		super();
	}

	public RequestData(UserSessionDetail session, String requestData) {
		super();
		this.session = session;
		this.data = requestData;
	}

	public UserSessionDetail getSession() {
		return session;
	}

	public void setSession(UserSessionDetail session) {
		this.session = session;
	}

	public String getData() {
		return data;
	}

	public void setData(String requestData) {
		this.data = requestData;
	}

}
