/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller(
  "UnitDataController",
  function (
    $rootScope,
    $scope,
    $window,
    $http,
    $location,
    $stateParams,
    AppUtil,
    Popeye,
    $uibModal,
    $cookieStore
  ) {
    $rootScope.showFullScreenLoader = true;
    $rootScope.testingUnitCategory = null;

    $scope.init = function () {
      $rootScope.enableScreenFilter = true;
      $scope.selectedRegionList = [];
      $scope.unitFilter = null;
      $scope.unitClosure = {};
      getRegionDetails();
      getMonkRecipeProfiles();

      if ($stateParams.category == "DELIVERY") {
        // $scope.loading = false;
        $scope.unitCat = "COD";
      } else if ($stateParams.category == "COD") {
        // $scope.loading = false;
        $scope.unitCat = "Call Center";
      } else if ($stateParams.category == "categoryList") {
        $scope.unitCat = "categoryList";
      } else if ($stateParams.category == "TAKE_AWAY") {
        $scope.unitCat = "Takeaway";
      } else if ($stateParams.category == "KITCHEN") {
        $scope.unitCat = "KITCHEN";
      } else if ($stateParams.category == "WAREHOUSE") {
        $scope.unitCat = "WAREHOUSE";
      } else if ($stateParams.category == "OFFICE") {
        $scope.unitCat = "OFFICE";
      } else if ($stateParams.category == "CHAI_MONK") {
        $scope.unitCat = "CHAI_MONK";
      } else if($stateParams.category == "TESTING_UNIT") {
        $scope.unitCat = $stateParams.category;
        $rootScope.testingUnitCategory = $scope.unitCat;
      } else {
        $scope.unitCat = "Cafe";
      }
    };

    $scope.multiSelectSettings = {
      showEnableSearchButton: true,
      template: "<b> {{option}}</b>",
      scrollable: true,
      scrollableHeight: "200px",
    };

    function getMonkRecipeProfiles() {
      $http({
        method: "GET",
        url:
          AppUtil.restUrls.unitMetaData.getListDataByGrpAndCat +
          "?gName=MONK_RECIPE_DATA&cat=RECIPE_PROFILE",
      }).then(
        function success(response) {
          $rootScope.monkRecipeProfiles = response.data;
        },
        function error(response) {
          console.log("error:" + response);
        }
      );
    }

    function getRegionDetails() {
      $http({
        method: "GET",
        url: AppUtil.restUrls.unitMetaData.regions,
      }).then(
        function success(response) {
          $scope.regionList = response.data;
          console.log($scope.regionList);
          $rootScope.showFullScreenLoader = false;
        },
        function error(response) {
          console.log("error:" + response);
        }
      );
    }

    $scope.fetchUnits = function () {
      var url =
        AppUtil.restUrls.unitMetaData.allUnits +
        "?category=" +
        $stateParams.category;
      if ($scope.selectedRegionList != null) {
        url =
          AppUtil.restUrls.unitMetaData.allUnits +
          "?category=" +
          $stateParams.category +
          "&region=" +
          $scope.selectedRegionList;
      }
      $rootScope.showFullScreenLoader = true;
      $http({
        method: "GET",
        url: url,
      }).then(
        function success(response) {
          $scope.unitlist = response.data;
          console.log("allUnits=", $scope.unitlist);
          $rootScope.showFullScreenLoader = false;
        },
        function error(response) {
          $rootScope.showFullScreenLoader = false;
          console.log("error:" + response);
        }
      );
    };
        $scope.setUnitClosureId = function (unit) {
          if(unit.closure !=null){
            return;
          }
            $scope.unitClosure.unitId = unit.id;
            $("#unitClosure").modal("show");
        }

        $scope.initiateCafeClosure = function () {

          if($scope.unitClosure.unitId=== undefined || $scope.unitClosure.unitId=== null){
            bootbox.alert("Unit Id is not set, Please Try Again later !");
            return; 
          }
            $rootScope.showFullScreenLoader = true;
            if (new Date() >  $scope.unitClosure.operationStopDate) {
                $rootScope.showFullScreenLoader = false;
                bootbox.alert("Date Should be Greater than Current Date");
                return;
            }

            if($scope.unitClosure.operationStopDate === null  || $scope.unitClosure.message === null || $scope.unitClosure.message.length <= 0)
              {
                $rootScope.showFullScreenLoader = false;
                bootbox.alert("Opertion date and Message is required !");
                return;

                }
            $scope.unitClosure.createdBy = AppUtil.getUserValues().userId;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.unitClosure.initiateClosure,
                data : $scope.unitClosure
            }).then(function success(response) {
              $rootScope.showFullScreenLoader = false;
              if (response.status == 200) {
                    bootbox.alert("Unit Closure Initiated Successfully");
                } else {
                    bootbox.alert("Unit Closure Intiation Failed");
                }
            }, function error(response) {
              $rootScope.showFullScreenLoader = false;
                console.log("error:" + response);
            });

            $scope.unitClosure={};
        }

        $scope.fetchUnits = function(){
            var testingUnit = $rootScope.testingUnitCategory != 'TESTING_UNIT' ? true : false;
            var url = AppUtil.restUrls.unitMetaData.allUnits + '?category=' + $stateParams.category + (testingUnit ? '&testingUnit=true' : '');
            if($scope.selectedRegionList != null){
                url = AppUtil.restUrls.unitMetaData.allUnits + '?category=' + $stateParams.category + "&region=" + $scope.selectedRegionList + (testingUnit ? '&testingUnit=true' : '');
            }
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'GET',
                url: url
            }).then(function success(response) {
                $scope.unitlist = response.data;
                console.log("allUnits=", $scope.unitlist);
                $rootScope.showFullScreenLoader = false;

            }, function error(response) {
                $rootScope.showFullScreenLoader = false;
                console.log("error:" + response);
            });
        };

    $scope.fetchProducts = function (unitDetail) {
      $rootScope.showFullScreenLoader = true;
      $http({
        method: "POST",
        url: AppUtil.restUrls.unitMetaData.unitProduct,
        data: unitDetail.id,
      }).then(
        function success(response) {
          setProductDetailInUnit(unitDetail.id, response.data);
          $rootScope.showFullScreenLoader = false;
        },
        function error(response) {
          console.log("error:" + response);
          $rootScope.showFullScreenLoader = false;
        }
      );
    };

    $scope.getUnitData = function (id) {
      $scope.unitIdData = id;
      $scope.isUnitDetailLoaded(id);
      if (!$scope.loaded) {
        $rootScope.showFullScreenLoader = true;
        var flagAction = true;
        $http({
          method: "POST",
          url: AppUtil.restUrls.unitMetaData.unit,
          data: id,
        }).then(function success(response) {
          $scope.UnitResponseData = response.data;
          $http({
            method: "POST",
            url: AppUtil.restUrls.unitMetaData.viewUnitDeliveryPartners,
            data: response.data.id,
          }).then(
            function success(response) {
              console.log(response.data);
              // setDetailInUnit(id,response.data);
              $scope.unitDeliveryData = response.data;
              $scope.UnitResponseData.deliveryPartners = response.data;
              setDetailInUnit(id, $scope.UnitResponseData);
              $scope.unitsDet = $scope.UnitResponseData;
              $rootScope.showFullScreenLoader = false;
            },
            function error(response) {
              console.log("error:" + response);
            }
          );
        });
      }
    };

    function setDetailInUnit(unitId, unitDetail) {
      $scope.unitlist.forEach(function (unit, index) {
        if (unit.id === unitId) {
          $scope.unitlist[index].unitDetail = unitDetail;
        }
      });
    }

    function setProductDetailInUnit(unitId, products) {
      $scope.unitlist.forEach(function (unit, index) {
        if (unit.id === unitId) {
          $scope.unitlist[index].unitDetail.products = products;
        }
      });
    }

    $scope.isUnitDetailLoaded = function (unitId) {
      $scope.unitlist.forEach(function (unit) {
        if (unit.id === unitId) {
          if (angular.isUndefined(unit.unitDetail) || unit.unitDetail == null) {
            $scope.loaded = false;
          } else {
            $scope.loaded = true;
          }
        }
      });
    };

    $scope.activateScmUnit = function (unitId) {
      $rootScope.showFullScreenLoader = true;
      $http({
        method: "POST",
        url: AppUtil.restUrls.unitMetaData.activateScmUnit,
        data: unitId,
      }).then(
        function (response) {
          if (response.data) {
            $rootScope.showFullScreenLoader = false;
            alert("Unit activated successfully on Kettle And SuMo both!");
            $scope.unitlist.forEach(function (unit) {
              if (unit.id === unitId) {
                unit.status = "ACTIVE";
                unit.live = false;
              }
            });
          }
        },
        function (response) {
          $rootScope.showFullScreenLoader = false;
          alert("Unit activation failed. Please try again!");
          console.log("error:" + response);
        }
      );
    };

    $scope.refreshCache = function (unitId, flushInventory) {
      var aclData = $rootScope.aclData;
      var actionName = flushInventory ? "UO_C_RCAI" : "UO_C_RC";
      if (
        aclData == null ||
        aclData.action == null ||
        aclData.action[actionName] == null
      ) {
        if (flushInventory) {
          alert("Your are not authorize to refresh cache and inventory");
        } else {
          alert("Your are not authorize to refresh cache");
        }
        return;
      }
      $rootScope.showFullScreenLoader = true;
      $http({
        method: "POST",
        url:
          AppUtil.restUrls.unitMetaData.refreshUnitCache +
          "?unitId=" +
          unitId +
          "&flushInventory=" +
          flushInventory,
      }).then(
        function (response) {
          if (response.data) {
            $rootScope.showFullScreenLoader = false;
            alert("Unit cache refreshed successfully");
          }
        },
        function (response) {
          $rootScope.showFullScreenLoader = false;
          alert("Unit cache refresh failed. Please try again!");
          console.log("error:" + response);
        }
      );
    };

    $scope.activateUnit = function (unitId) {
      $rootScope.showFullScreenLoader = true;
      $http({
        method: "POST",
        url: AppUtil.restUrls.unitMetaData.activateUnit,
        data: unitId,
      }).then(
        function success(response) {
          if (response.data) {
            $scope.activateScmUnit(unitId);
          } else {
            alert(
              "Kettle unit activation failed. Please contact support team for assistance"
            );
          }
        },
        function error(response) {
          console.log("error:" + response);
        }
      );
    };

    $scope.deactivateUnit = function (unitId) {
      var aclData = $rootScope.aclData;
      var actionName = "UNTDCT";
      if (
        aclData == null ||
        aclData.action == null ||
        aclData.action[actionName] == null
      ) {
        alert("Your are not authorize to deactivate Unit");
        return;
      }
      $rootScope.showFullScreenLoader = true;
      $http({
        method: "POST",
        url: AppUtil.restUrls.unitMetaData.deactivateUnit,
        data: unitId,
      }).then(
        function success(response) {
          if (response.status == 200) {
            $rootScope.showFullScreenLoader = false;
            $scope.unitlist.forEach(function (unit, index) {
              if (unit.id === unitId) {
                if (unit.category == "CAFE") {
                  $scope.changeUnitLiveStatusInDineApp(unitId);
                  $scope.changeUnitLiveStatusInChaayos();
                }
                unit.status = "IN_ACTIVE";
                unit.live = false;
                alert("Unit deactivated successfully!");
              }
            });
          } else {
            $rootScope.showFullScreenLoader = false;
            alert(response.data.errorMessage);
          }
        },
        function error(response) {
          $rootScope.showFullScreenLoader = false;
          console.log("error:" + response);
        }
      );
    };

    $scope.resetMeterReadingForUnit = function (unitId) {
      $rootScope.showFullScreenLoader = true;

      var data = AppUtil.getUserValues();
      var payload = {
        unitId: unitId,
        userId: data.userId,
      };
      $http({
        method: "POST",
        url: AppUtil.restUrls.posMetaData.resetMeterReading,
        data: payload,
      }).then(
        function success(response) {
          if (response.status == 200 && response.data == true) {
            $rootScope.showFullScreenLoader = false;
            alert("Meter Reset Successfully!");
          }
        },
        function error(response) {
          $rootScope.showFullScreenLoader = false;
          console.log("error:" + response);
        }
      );
    };

    $scope.openDineInOrChaayosOptionStatus = function (unit, status) {
      console.log(unit);
      $scope.unitId = unit.id;
      $scope.status = status;
      $scope.unitCategory = unit.category;
      if (unit.category == "CAFE") {
        $scope.dineIn = true;
        $scope.chaayos = true;
        $scope.dineInCheck = unit.cafeAppStatus == "ACTIVE";
        $scope.chaayosCheck = unit.cafeNeoStatus == "ACTIVE";
        $("#chooseDineInOrChaayosModal").modal("show");
      } else {
        $scope.dineIn = false;
        $scope.chaayos = false;
        $scope.dineInCheck = false;
        $scope.chaayosCheck = false;
        $scope.changeUnitLiveStatusNotInDineInAndChaayos(
          $scope.unitId,
          $scope.status,
          $scope.dineIn,
          $scope.chaayos
        );
      }
    };

    var detail = {};

    $scope.cloneCRMBanner = function (unit) {
      console.log("inside clone new");
      console.log(unit.id);
      detail.code = unit.id;
      detail.name = unit.name;
      detail.type = unit.region;
      $("#cloneCRMBanner").modal("show");
    };

    $scope.setCloneCRM = function (cloneCRMUnit) {
      detail.id = cloneCRMUnit.id;
      detail.status = cloneCRMUnit.status;
      console.log(detail);
    };

    $scope.cloneApi = function () {
      var res = null;
      if (detail.code === detail.id) {
        alert("Can not clone with the same unit");
        return false;
      }
      $http({
        method: "POST",
        url: AppUtil.restUrls.posMetaData.cloneCRMBanner,
        data: detail,
      }).then(
        function success(response) {
          if (response.status == 200) {
            if (response.data == true) {
              console.log(response.data);
              alert("Cloned Successfully");
              window.location.reload();
            }
            if (response.data == false) {
              alert(
                "Cloning not done...! data of unit selected is not available!!"
              );
              window.location.reload();
            }
          }
        },
        function error(response) {
          $rootScope.showFullScreenLoader = false;
          console.log("error:" + response);
        }
      );
    };

    $scope.selectedOptionForAppAndNeoCafe = function (dineIn, chaayos) {
      $scope.changeUnitLiveStatus(
        $scope.unitId,
        $scope.status,
        dineIn,
        chaayos
      );
    };

    $scope.changeUnitLiveStatus = function (unitId, status, dineIn, chaayos) {
      var result = confirm(
        "Are you sure you want to set status in DineInApp to: " +
          dineIn +
          " and Chaayos.com to: " +
          chaayos
      );
      if (!result) {
        return;
      }
      $("#chooseDineInOrChaayosModal").modal("hide");
      $rootScope.showFullScreenLoader = true;
      $http({
        method: "GET",
        url:
          AppUtil.restUrls.unitMetaData.changeUnitLiveStatus +
          "?unitId=" +
          unitId +
          "&status=" +
          status +
          "&dineIn=" +
          dineIn +
          "&chaayos=" +
          chaayos,
      }).then(
        function success(response) {
          if (response.status == 200) {
            if (response.data == false) {
              alert(
                "Enabling Unit was Unsuccessful (FSSAI Code is not updated!!!)"
              );
              $rootScope.showFullScreenLoader = false;
            } else {
              $scope.unitlist.forEach(function (unit, index) {
                if (unit.id === unitId) {
                  if (unit.category == "CAFE") {
                    // if ($scope.dineInCheck != dineIn) {
                    $scope.changeUnitLiveStatusInDineApp(unitId);
                    // }
                    // if ($scope.chaayosCheck != chaayos) {
                    $scope.changeUnitLiveStatusInChaayos();
                    // }
                  }
                  unit.live = status;
                  alert(
                    "Unit live status changed to " + status + " successfully!"
                  );
                  // if ($scope.dineInCheck == dineIn) {
                  $rootScope.showFullScreenLoader = false;
                  // }
                }
              });
            }
          }
        },
        function error(response) {
          $rootScope.showFullScreenLoader = false;
          console.log("error:" + response);
        }
      );
    };
    $scope.changeUnitLiveStatusNotInDineInAndChaayos = function (
      unitId,
      status,
      dineIn,
      chaayos
    ) {
      $rootScope.showFullScreenLoader = true;
      $http({
        method: "GET",
        url:
          AppUtil.restUrls.unitMetaData.changeUnitLiveStatus +
          "?unitId=" +
          unitId +
          "&status=" +
          status +
          "&dineIn=" +
          dineIn +
          "&chaayos=" +
          chaayos,
      }).then(
        function success(response) {
          console.log("364");
          console.log(response);
          if (response.status == 200) {
            if (response.data == false) {
              alert(
                "Enabling Unit was Unsuccessful (FSSAI Code is not updated!!!)"
              );
              $rootScope.showFullScreenLoader = false;
            } else {
              console.log("372");
              console.log(response);
              $scope.unitlist.forEach(function (unit, index) {
                if (unit.id === unitId) {
                  unit.live = status;
                  alert(
                    "Unit live status changed to " + status + " successfully!"
                  );
                  // if ($scope.dineInCheck == dineIn) {
                  $rootScope.showFullScreenLoader = false;
                  // }
                }
              });
            }
          } else {
            alert("Not Possible");
          }
        },
        function error(response) {
          $rootScope.showFullScreenLoader = false;
          console.log("error:" + response);
        }
      );
    };

    $scope.changeUnitLiveStatusInDineApp = function (unitId) {
      $rootScope.showFullScreenLoader = true;
      $http({
        method: "GET",
        url: AppUtil.restUrls.location.loadLocation + "/1000" + "/" + unitId,
      }).then(
        function success(response) {
          if (response.status == 200) {
            console.log("unit updated in dine in");
            $rootScope.showFullScreenLoader = false;
          }
        },
        function error(response) {
          $rootScope.showFullScreenLoader = false;
          console.log("error:" + response);
        }
      );
    };

    $scope.changeUnitLiveStatusInChaayos = function () {
      $rootScope.detailLoaderMessage = "Updating Chaayos.com ...";
      $rootScope.showDetailLoader = true;
      $http({
        method: "POST",
        url: AppUtil.restUrls.neoCache.refreshUnitCache,
        data: {},
        dataType: "json",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(
        function success(response) {
          if (response.data && response.data == true) {
            console.log(response.data);
            $rootScope.showDetailLoader = false;
            alert("succesfully update");
          } else {
            console.log("error in cache refresh");
            $rootScope.showDetailLoader = false;
            alert("error updating cache");
          }
        },
        function error(response) {
          $rootScope.showDetailLoader = false;
          console.log("error:" + response);
        }
      );
    };

    // var pc = this;
    $scope.editUnitNew = function (unit) {
      var unitCopy = angular.copy(unit);
      var modalInstance = $uibModal.open({
        animation: true,
        ariaLabelledBy: "modal-title",
        ariaDescribedBy: "modal-body",
        templateUrl: "views/modal.html",
        backdrop: "static",
        controller: "modalCtrl",
        controllerAs: "ab",
        size: "lg",
        resolve: {
          unitData: function () {
            return unitCopy;
          },
          monkRecipeProfiles: function(){
            return $rootScope.monkRecipeProfiles;
          }
        },
      });

      modalInstance.result.then(
        function () {
          //alert("Result");
        },
        function () {
          //alert("Dismissal");
        }
      );
    };

    $scope.editUnit = function (unit) {
      console.log(unit);
      $rootScope.$broadcast("editUnit", {
        unitToEdit: unit,
      });
    };

    console.log("UnitID=", $rootScope.unitToEdit);

    $scope.editProductDetailModal = function (
      unitID,
      productID,
      productName,
      prices
    ) {
      // console.log(products);
      getRecipeProfilesOfProduct(productID);
      $scope.producttNamee = productName;
      $scope.unitIDD = unitID;
      $scope.productIDD = productID;
      $scope.productPrices = prices;
      for (var index in $scope.productPrices) {
        $scope.productPrices[index].newprice = "";
        $scope.productPrices[index].newProfile = "";
      }
      $scope.filteredProducts = $scope.productPrices.length;
      $scope.productName = productName;
      $("#productUnitDetailModal").modal("show");
      $scope.count = 0;
    };

    function getRecipeProfilesOfProduct(productID) {
      $http({
        url: AppUtil.restUrls.recipeManagement.recipeProfileOfProduct,
        method: "GET",
        params: { productId: productID },
      }).success(function (data) {
        // console.log(data);
        $scope.recipeProfiles = data;
      });
    }

    $scope.cancelPrice = function () {
      $("#productUnitDetailModal").hide();
    };

    $scope.inActiveProduct = function (unitID, productID) {
      var inactiveProductString = $.param({
        unitId: unitID,
        productId: productID,
      });
      $http({
        url: AppUtil.restUrls.productMetaData.deactivateProductMapping,
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        data: inactiveProductString,
      }).success(function (data) {
        console.log(data);
        alert("Status Successfully updated");
        window.location.reload();
      });
    };
    $scope.activeProduct = function (unitID, productID) {
      var activeProductString = $.param({
        unitId: unitID,
        productId: productID,
      });

      if (
        $window.confirm(
          "Are you sure, you want to activate/deactivate Product item?"
        )
      ) {
      } else {
        return false;
      }

      $http({
        url: AppUtil.restUrls.productMetaData.activateProductMapping,
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        data: activeProductString,
      }).success(function (data) {
        // console.log(data);
        alert("Status Successfully updated");
        window.location.reload();
      });
    };
    $scope.updateProductPriceUnit = function (
      unitIDD,
      productID,
      products,
      productName
    ) {
      // console.log($scope.unitsDet);
      for (var index in products) {
        var profileUpdated =
          products[index].newProfile != undefined &&
          products[index].newProfile != "";
        if (products[index].newprice != "" || profileUpdated) {
          var newprice =
            products[index].newprice != ""
              ? products[index].newprice
              : products[index].price;
          var dimensionList = products[index].dimension;
          var payload = $.param({
            unitId: unitIDD,
            productId: productID,
            price: newprice,
            dimensionCode: dimensionList,
            profile: profileUpdated
              ? products[index].newProfile
              : products[index].profile,
          });
          $http({
            url: AppUtil.restUrls.productMetaData.unitProductPriceUpdate,
            method: "POST",
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
            data: payload,
          }).success(function (data) {
            alert(productName + " " + dimensionList + " details updated");
            window.location.reload();
          });
        }
      }
    };

    $scope.goToMonkConfiguration = function () {
      $location
        .path("dashboard/monkConf")
        .search({ selectedUnit: $scope.unitIdData });
    };

    $scope.goToMonk2Configuration = function () {
      $location
        .path("dashboard/monk2Conf")
        .search({ selectedUnit: $scope.unitIdData });
    };

    $scope.clearKettle2CacheWithSource = function (type, unitId) {
      var url = AppUtil.restUrls.kettle2CacheManagement[type];
      $rootScope.showFullScreenLoader = true;

      $http({
        method: "POST",
        url: url,
        params: {
          partnerId: 1,
        },
        headers: {
          "Content-type": "application/json",
          compid: 1000,
          uid: unitId,
          src: "CAFE",
        },
      }).then(
        function success(response) {
          $rootScope.showFullScreenLoader = false;
          bootbox.alert(JSON.stringify(response));
        },
        function error(response) {
          $rootScope.showFullScreenLoader = false;
          bootbox.alert("error:" + JSON.stringify(response));
        }
      );
    };

    $scope.restrictedApplication = function (unit) {
      var mappingModal = null;
      getRestrictedApplication(unit.id).then(function (d) {
        var data = {
          unit: unit,
          applicationList: d.data,
        };
        mappingModal = Popeye.openModal({
          templateUrl: "views/restrictedApplicationModal.html",
          controller: "restrictedApplicationModalCtrl",
          resolve: {
            applicationData: function () {
              return data;
            },
          },
          click: false,
          keyboard: false,
        });
        mappingModal.closed.then(function () {});
      });
    };

    function getRestrictedApplication(unitId) {
      var promise = $http({
        url: AppUtil.restUrls.unitMetaData.restrictedApplication,
        method: "GET",
        params: { unitId: unitId },
      }).success(function (response) {
        return response.data;
      });
      return promise;
    }
  }
);

adminapp.controller(
  "restrictedApplicationModalCtrl",
  function ($scope, Popeye, applicationData, $http, AppUtil, $cookieStore) {
    $scope.unit = applicationData.unit;
    $scope.applicationList = applicationData.applicationList;

    $scope.init = function () {};

    $scope.updateRestriction = function () {
      $http({
        url: AppUtil.restUrls.unitMetaData.updateRestrictedApplication,
        method: "POST",
        data: $scope.applicationList,
      }).success(function (response) {
        if (response === true) {
          alert("Application Restriction Updated");
        } else {
          alert("Application Restriction Updation failed");
        }
        $scope.closeModal();
      });
    };

    $scope.updateStatus = function (application) {
      if (application.status == "ACTIVE") {
        application.status = "IN_ACTIVE";
      } else if (application.status == "IN_ACTIVE") {
        application.status = "ACTIVE";
      }

      var data = AppUtil.getUserValues();
      if (data != undefined && data != null) {
        application.lastUpdatedBy = { id: data.user.id, name: "" };
      }
    };

    $scope.closeModal = function () {
      Popeye.closeCurrentModal();
    };
  }
);
