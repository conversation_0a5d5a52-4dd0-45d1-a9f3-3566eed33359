package com.stpl.tech.report.service.controller;

import com.stpl.tech.report.service.service.WhatsAppDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static com.stpl.tech.report.service.core.ReportServiceConstants.API_VERSION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SEPARATOR;
import static com.stpl.tech.report.service.core.ReportServiceConstants.WHATSAPP_DATA_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION+SEPARATOR+WHATSAPP_DATA_ROOT_CONTEXT)
public class WhatsAppDataResource {

      WhatsAppDataService whatsAppDataService;

    @Autowired
    public WhatsAppDataResource(WhatsAppDataService whatsAppDataService) {
        this.whatsAppDataService = whatsAppDataService;
    }

    @PostMapping("/upload-whatsapp-data")
    void uploadWhatsAppDataLoad(@RequestParam("file")MultipartFile file,@RequestParam Boolean isTemplate) throws IOException {
            whatsAppDataService.storeWhatsAppTemplateData(file,isTemplate);
    }

}
