package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 02-07-2018.
 */
@Entity
@Table(name = "SALES_PERFORMA_INVOICE_ITEM")
public class SalesPerformaInvoiceItemData {

    private Integer itemId;
    private Integer skuId;
    private String skuName;
    private String taxCode;
    private String unitOfMeasure;
    private Integer packagingId;
    private String packagingName;
    private BigDecimal conversionRatio;
    private BigDecimal packagingQuantity;
    private BigDecimal quantity;
    private BigDecimal currentPrice;
    private BigDecimal mappedPrice;
    private BigDecimal sellingPrice;
    private BigDecimal currentAmount;
    private BigDecimal mappedAmount;
    private BigDecimal sellingAmount;
    private BigDecimal totalTax;
    private SalesPerformaDetailData invoice;
    private String alias;

    private List<SalesPerformaItemDrilldown> expiryDrilldownList;
    private List<SalesPerformaItemTaxDetail> taxDetailList;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "INVOICE_ITEM_ID", unique = true, nullable = false)
    public Integer getItemId() {
        return itemId;
    }

    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }

    @Column(name="SKU_ID",nullable = false)
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    @Column(name="SKU_NAME",nullable = false)
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    @Column(name="TAX_CODE",nullable = false)
    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    @Column(name="UOM",nullable = false)
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    @Column(name="PKG_ID",nullable = false)
    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }

    @Column(name="PKG_NAME",nullable = false)
    public String getPackagingName() {
        return packagingName;
    }

    public void setPackagingName(String packagingName) {
        this.packagingName = packagingName;
    }

    @Column(name="CONVERSION_RATIO",nullable = false)
    public BigDecimal getConversionRatio() {
        return conversionRatio;
    }

    public void setConversionRatio(BigDecimal conversionRatio) {
        this.conversionRatio = conversionRatio;
    }

    @Column(name="PKG_QTY",nullable = false)
    public BigDecimal getPackagingQuantity() {
        return packagingQuantity;
    }

    public void setPackagingQuantity(BigDecimal packagingQuantity) {
        this.packagingQuantity = packagingQuantity;
    }

    @Column(name="QTY",nullable = false)
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Column(name="CURRENT_PRICE")
    public BigDecimal getCurrentPrice() {
        return currentPrice;
    }

    public void setCurrentPrice(BigDecimal currentPrice) {
        this.currentPrice = currentPrice;
    }

    @Column(name="MAPPED_PRICE",nullable = false)
    public BigDecimal getMappedPrice() {
        return mappedPrice;
    }

    public void setMappedPrice(BigDecimal mappedPrice) {
        this.mappedPrice = mappedPrice;
    }

    @Column(name="SELLING_PRICE",nullable = false)
    public BigDecimal getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(BigDecimal sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    @Column(name="CURRENT_AMOUNT")
    public BigDecimal getCurrentAmount() {
        return currentAmount;
    }

    public void setCurrentAmount(BigDecimal currentAmount) {
        this.currentAmount = currentAmount;
    }

    @Column(name="MAPPED_AMOUNT",nullable = false)
    public BigDecimal getMappedAmount() {
        return mappedAmount;
    }

    public void setMappedAmount(BigDecimal mappedAmount) {
        this.mappedAmount = mappedAmount;
    }

    @Column(name="SELLING_AMOUNT",nullable = false)
    public BigDecimal getSellingAmount() {
        return sellingAmount;
    }

    public void setSellingAmount(BigDecimal sellingAmount) {
        this.sellingAmount = sellingAmount;
    }

    @Column(name="TOTAL_TAX",nullable = false)
    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="INVOICE_ID", nullable = false)
    public SalesPerformaDetailData getInvoice() {
        return invoice;
    }

    public void setInvoice(SalesPerformaDetailData invoice) {
        this.invoice = invoice;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "invoiceItem")
    public List<SalesPerformaItemTaxDetail> getTaxDetailList() {
        return taxDetailList;
    }

    public void setTaxDetailList(List<SalesPerformaItemTaxDetail> taxDetailList) {
        this.taxDetailList = taxDetailList;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "invoiceItem")
    public List<SalesPerformaItemDrilldown> getExpiryDrilldownList() {
        return expiryDrilldownList;
    }

    public void setExpiryDrilldownList(List<SalesPerformaItemDrilldown> drillDownList) {
        this.expiryDrilldownList = drillDownList;
    }
    @Column(name="SKU_ALIAS")
    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }
}
