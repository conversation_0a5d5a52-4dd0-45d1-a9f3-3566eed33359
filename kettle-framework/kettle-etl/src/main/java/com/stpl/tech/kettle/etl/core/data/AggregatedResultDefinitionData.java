package com.stpl.tech.kettle.etl.core.data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "AGGREGATED_RESULT_DEFINITION")
public class AggregatedResultDefinitionData {

    private Integer aggregatedResultDefinitionId;

    private String businessDate;

    private Integer unitId;

    private String unitName;

    private String referenceNumber;

    private String state;

    private String stateCode;

    private String particulars;

    private BigDecimal totalSales;

    private String debitCredit;

    private String accountCode;

    private String keyType;

    private String keyId;

    private Integer JobId;

    private Integer executionId;

    private Date executionTime;

    private String typeOfData;

    private String status;

    private Integer stepId;
    private String transactionNumber;
    private String businessCostCentre;
    private String narration;


    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "AGGREGATED_RESULT_DEFINITION_ID", unique = true, nullable = false)
    public Integer getAggregatedResultDefinitionId() {
        return aggregatedResultDefinitionId;
    }

    public void setAggregatedResultDefinitionId(Integer aggregatedResultDefinitionId) {
        this.aggregatedResultDefinitionId = aggregatedResultDefinitionId;
    }


    @Column(name = "BUSINESS_DATE")
    public String getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(String businessDate) {
        this.businessDate = businessDate;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_NAME", nullable = false, length = 255)
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "REFERENCE_NUMBER", nullable = false, length = 255)
    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    @Column(name = "STATE", nullable = false, length = 255)
    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Column(name = "STATE_CODE", nullable = false, length = 255)
    public String getStateCode() {
        return stateCode;
    }

    public void setStateCode(String stateCode) {
        this.stateCode = stateCode;
    }

    @Column(name = "PARTICULARS", nullable = false, length = 255)
    public String getParticulars() {
        return particulars;
    }

    public void setParticulars(String particulars) {
        this.particulars = particulars;
    }

    @Column(name = "TOTAL_SALES", nullable = false)
    public BigDecimal getTotalSales() {
        return totalSales;
    }

    public void setTotalSales(BigDecimal totalSales) {
        this.totalSales = totalSales;
    }

    @Column(name = "IS_DEBIT")
    public String getDebitCredit() {
        return debitCredit;
    }

    public void setDebitCredit(String debitCredit) {
        this.debitCredit = debitCredit;
    }



    @Column(name = "ACCOUNT_CODE", nullable = false, length = 255)
    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    @Column(name = "KEY_TYPE")
    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    @Column(name = "KEY_ID")
    public String getKeyId() {
        return keyId;
    }

    public void setKeyId(String keyId) {
        this.keyId = keyId;
    }

    @Column(name = "JOB_ID", nullable = false)
    public Integer getJobId() {
        return JobId;
    }

    public void setJobId(Integer jobId) {
        JobId = jobId;
    }

    @Column(name = "JOB_EXECUTION_ID", nullable = false)
    public Integer getExecutionId() {
        return executionId;
    }

    public void setExecutionId(Integer executionId) {
        this.executionId = executionId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "JOB_EXECUTION_TIME", length = 19)
    public Date getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Date executionTime) {
        this.executionTime = executionTime;
    }

    @Column(name = "TYPE_OF_DATA", nullable = false)
    public String getTypeOfData() {
        return typeOfData;
    }

    public void setTypeOfData(String typeOfData) {
        this.typeOfData = typeOfData;
    }

    @Column(name = "DATA_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "STEP_ID", nullable = false)
    public Integer getStepId() {
        return stepId;
    }

    public void setStepId(Integer stepId) {
        this.stepId = stepId;
    }

    @Column(name = "TRANSACTION_NUMBER")
    public String getTransactionNumber() {
        return transactionNumber;
    }

    public void setTransactionNumber(String transactionNumber) {
        this.transactionNumber = transactionNumber;
    }

    @Column(name = "BUSINESS_COST_CENTRE")
    public String getBusinessCostCentre() {
        return businessCostCentre;
    }

    public void setBusinessCostCentre(String businessCostCentre) {
        this.businessCostCentre = businessCostCentre;
    }

    @Column(name = "NARRATION")
    public String getNarration() {
        return narration;
    }

    public void setNarration(String narration) {
        this.narration = narration;
    }
}
