package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SkuPriceUpdateDetail {
    private List<SkuPriceUpdate> approved;
    private List<SkuPriceUpdate> rejected;
    private List<SkuPriceUpdate> approvedNew;
    private List<SkuPriceUpdate> rejectedNew;
}
