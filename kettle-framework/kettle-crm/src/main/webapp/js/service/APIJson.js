/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 11-01-2016.
 */
function getCookie(name) {
    var cookie = {};
    document.cookie.split(';').forEach(function (el) {
        var result = el.split('=');
        var k = result[0];
        var v = result[1];
        cookie[k.trim()] = v;
    })
    return cookie[name];
}
(function () {
    'use strict';

    var EnvName = getCookie("EnvName");
    angular.module('customerApp').factory('APIJson', APIJson);
    APIJson.$inject = ['ConfigService'];
    function APIJson(ConfigService) {


        var baseUrl = ConfigService.getUrls(EnvName).baseUrl;
        var SEPARATOR = "/";

        var KETTLE_SERVICE = baseUrl + SEPARATOR + "kettle-service/rest/v1" + SEPARATOR;
        var MASTER_SERVICE = baseUrl + SEPARATOR + "master-service/rest/v1" + SEPARATOR;
        var CUSTOMER_SERVICE = baseUrl + SEPARATOR + "kettle-crm/rest/v1" + SEPARATOR;

        var POS_METADATA_ROOT_CONTEXT = "pos-metadata" + SEPARATOR;
        var USER_SERVICES_ROOT_CONTEXT = "users" + SEPARATOR;
        var UNIT_METADATA_ROOT_CONTEXT = "unit-metadata" + SEPARATOR;
        var ORDER_MANAGEMENT_ROOT_CONTEXT = "order-management" + SEPARATOR;
        var CRM_SERVICES_ROOT_CONTEXT = "crm" + SEPARATOR;

        var service = {};

        service.urls = {
            posMetaData: {
                allUnits: MASTER_SERVICE + UNIT_METADATA_ROOT_CONTEXT + "all-units",
                takeawayUnits: MASTER_SERVICE + UNIT_METADATA_ROOT_CONTEXT + "takeaway-units"
            },
            users: {
                login: MASTER_SERVICE + USER_SERVICES_ROOT_CONTEXT + "login",
                changePassCode: MASTER_SERVICE + USER_SERVICES_ROOT_CONTEXT + "changePasscode",
                logout: MASTER_SERVICE + USER_SERVICES_ROOT_CONTEXT + "logout"
            },
            customer: {
                signin: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "signin",
                signup: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "signup",
                generateOTP: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "generate/otp",
                switchOTP: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "switch/otp",
                verifySignup: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "verify/signup",
                verifyOTP: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "verify/otp",
                updateName: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "update-name",
                resendAuthorizationOTP: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "resendCustomerAuthorizationOTP/otp",
                resendRedemptionOTP: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "resendRedemptionOTP/otp",
                overrideContactVerification: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "overrideContactVerification",
                submitFeedback: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "feedback/submit",
                verifyTrueCaller: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "send-truecaller-request",
                getTrueCallerProfile: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "get-truecaller-profile",
                updateTrueCallerProfile: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "update-truecaller-profile",
                uploadCustomerSheet: CUSTOMER_SERVICE + CRM_SERVICES_ROOT_CONTEXT + "/upload-customer"
            },
            unitMetaData: {
                unit: MASTER_SERVICE + UNIT_METADATA_ROOT_CONTEXT + "unit-data",
                unitData: MASTER_SERVICE + UNIT_METADATA_ROOT_CONTEXT + "unit",
            },
            orderManagement: {
                validateGiftCardsInOrder: KETTLE_SERVICE + SEPARATOR + ORDER_MANAGEMENT_ROOT_CONTEXT + "order/validateGiftCards"
            }
        };

        return service;
    }


})();
