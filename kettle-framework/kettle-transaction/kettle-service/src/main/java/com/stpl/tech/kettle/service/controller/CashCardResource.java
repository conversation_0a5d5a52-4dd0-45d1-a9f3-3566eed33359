package com.stpl.tech.kettle.service.controller;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.GIFT_CARD_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.ws.rs.core.MediaType;

import com.stpl.tech.master.core.service.AbstractExceptionHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.google.gson.Gson;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.CashCardOffer;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.GiftCardActivationRequest;
import com.stpl.tech.util.AppUtils;

/**
 * Created by Chaayos on 29-05-2017.
 */

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + GIFT_CARD_MANAGEMENT_ROOT_CONTEXT)
public class CashCardResource extends AbstractExceptionHandler {

	private static final Logger LOG = LoggerFactory.getLogger(CashCardResource.class);

	@Autowired
	private CardService cardService;

	@RequestMapping(method = RequestMethod.POST, value = "activateGiftCard", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean activateGiftCard(@RequestBody GiftCardActivationRequest request)
			throws DataNotFoundException, DataUpdationException, CardValidationException {
		LOG.info("Request to activate gift card : " + new Gson().toJson(request));
		return cardService.activateCashCard(request);
	}

	@RequestMapping(method = RequestMethod.POST, value = "card/code", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CashCardDetail getCardDetail(@RequestBody String code)
			throws DataNotFoundException, DataUpdationException, CardValidationException {
		LOG.info("Request to get gift card by code: " + code);
		return cardService.getCardDetail(code);
	}

	@RequestMapping(method = RequestMethod.POST, value = "card/serial", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CashCardDetail getCardDetailBySerial(@RequestBody String serial)
			throws DataNotFoundException, DataUpdationException, CardValidationException {
		LOG.info("Request to get gift card by serial: " + serial);
		return cardService.getCardDetailBySerial(serial);
	}

	@RequestMapping(method = RequestMethod.POST, value = "card/units", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<Integer> getUnitsWithCardOffersForToday()
			throws DataNotFoundException, DataUpdationException, CardValidationException {
		LOG.info("Request to get Units for Cash Card Offers");
		return cardService.getUnitsWithCardOffersForToday();
	}

	/**
	 * Cash Card Manager API
	 */
	@RequestMapping(method = RequestMethod.GET, value = "card/offers", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<CashCardOffer> getAllCashCardOffers()
			throws DataNotFoundException, DataUpdationException, CardValidationException {
		LOG.info("Request to get All Cash Card Offers");
		return cardService.getAllCashCardOffers(AppUtils.getBusinessDate());
	}

	@RequestMapping(method = RequestMethod.POST, value = "card/offers/add", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<CashCardOffer> addCashCardOffers(@RequestBody List<CashCardOffer> list)
			throws DataNotFoundException, CardValidationException {
		LOG.info("Request to add Cash Card Offers");
		return cardService.addCashCardOffers(list);
	}

	@RequestMapping(method = RequestMethod.POST, value = "card/offers/changeStatus", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<CashCardOffer> changeStatusCashCardOffers(@RequestBody List<CashCardOffer> list)
			throws DataNotFoundException, DataUpdationException, CardValidationException {
		LOG.info("Request to deactivate Cash Card Offers");
		return cardService.changeStatusAllCashCardOffers(list);
	}

	/**
	 * Cash Card Manager API
	 */
	@RequestMapping(method = RequestMethod.POST, value = "card/offers/date", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<CashCardOffer> getCashCardOffersForDate(@RequestParam String startDate, @RequestParam String endDate,
														@RequestBody List<Integer> partnerId) {
		LOG.info("Request to get Cash Card Offers for start date: " + startDate + " and endDate: " + endDate);
		Date start = AppUtils.parseDate(startDate, new SimpleDateFormat("yyyy-MM-dd"));
		Date end = AppUtils.parseDate(endDate, new SimpleDateFormat("yyyy-MM-dd"));
		List<CashCardOffer> offer = new ArrayList<>();
		for (Integer pId : partnerId) {
			offer.addAll(cardService.getCashCardOffersForDate(start, end, pId));
		}
		return offer;
	}
}
