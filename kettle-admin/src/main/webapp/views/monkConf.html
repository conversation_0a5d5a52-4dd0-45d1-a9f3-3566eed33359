<style>
.monkUnitAttr {
	margin-top: 10px;
	margin-bottom: 10px;
}

.monkConfBox {
	border: 3px solid green;
	margin-top: 4px;
	padding: 5px;
}

.monkPlus {
	font-size: 23px;
	margin-left: 10px;
	color: green;
}

.monkMinus {
	font-size: 23px;
	margin-left: 10px;
	color: red;
}
</style>
<div data-ng-init="init()" class="form-group"
	data-ng-form="monkConfForm">
	<div class="row">
		<div class="col-xs-12">
			<h2>Unit Name Monk Conf</h2>
			<div class="row monkUnitAttr">
				<div class="col-xs-12">
					<button type="button" class="btn btn-warning"
						data-ng-click="toggleViewMode()">Toggle View
						Configuration</button>
					<button type="button" class="btn btn-danger pull-right"
						data-ng-click="goBack()">Go To Cafe</button>
				</div>
			</div>
		</div>
	</div>
	<div data-ng-if="!viewMode">
		<div class="row">
			<div class="col-xs-12">
				<div class="row monkUnitAttr"
					data-ng-repeat="metadata in monkConfMetadata | filter:{status:'ACTIVE',scope:'UNIT'}:true">
					<div class="col-xs-6">{{metadata.label}}</div>
					<div class="col-xs-6">
						<div data-ng-switch="metadata.type">
							<div data-ng-switch-when="boolean">
								<select data-ng-model="monkConfValue[metadata.attrId].value"
									class='form-control' required="required">
									<option value="Y">Yes</option>
									<option value="N">No</option>
								</select>
							</div>
							<div data-ng-switch-default>
								<input type="{{metadata.type}}"
									data-ng-model="monkConfValue[metadata.attrId].value"
									data-ng-disabled="metadata.attr == 'NUMBER_OF_MONKS'"
									required="required" name="{{metadata.attrId}}"
									data-ng-class="{ 'form-control': metadata.attr != 'NUMBER_OF_MONKS' }">
								<span data-ng-if="metadata.attr == 'NUMBER_OF_MONKS'"> <i
									class="fa fa-plus-circle monkPlus" aria-hidden="true"
									data-ng-click="changeMonkConfValue(metadata.attr,metadata.attrId,'plus')"></i>
									<i class="fa fa-minus-circle monkMinus" aria-hidden="true"
									data-ng-click="changeMonkConfValue(metadata.attr,metadata.attrId,'minus')"></i>
								</span>
							</div>
						</div>
					</div>
				</div>

			</div>
		</div>
		<div class="row  monkConfBox" data-ng-repeat="monk in getMonkArr()">
			<div class="row">
				<div class="col-xs-12">
					<h4>Monk {{monk}} Conf</h4>
				</div>
			</div>
			<div class="col-xs-12">
				<div class="row monkUnitAttr"
					data-ng-repeat="metadata in monkConfMetadata | filter:{scope:'MONK', status:'ACTIVE'}:true">
					<div class="col-xs-6">{{metadata.label}}</div>
					<div class="col-xs-6">
						<div data-ng-switch="metadata.type">
							<div data-ng-switch-when="boolean">
								<select
									data-ng-model="monkConfValue[MONKCONSTANT+monk][metadata.attrId].value"
									class='form-control' required="required">
									<option value="Y">Yes</option>
									<option value="N">No</option>
								</select>
							</div>
							<div data-ng-switch-default>
								<input type="{{metadata.type}}"
									data-ng-model="monkConfValue[MONKCONSTANT+monk][metadata.attrId].value"
									required="required"
									name="{{MONKCONSTANT}}{{monk}}{{metadata.attrId}}"
									class='form-control'>
							</div>
						</div>
					</div>
				</div>

			</div>
		</div>
		<div class="row monkUnitAttr">
			<div class="col-xs-12">
				<button class="btn btn-primary lg"
					data-ng-click="saveUnitMonkConfiguration()"
					style="margin-bottom: 20px;"
					data-ng-disabled="monkConfForm.$invalid">Save
					Configuration</button>
			</div>
		</div>
	</div>
	<div data-ng-if="viewMode">
		<div class="row">
			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-12" ng-if="unitConfData.length > 0">
						<table class="table table-striped table-bordered"
							id="tableDataStructure" style="margin-padding: 0px;">
							<thead>
								<th>Scope</th>
								<th>Conf Name</th>
								<th>Conf Value</th>
								<!-- <th>Action</th> -->
							</thead>
							<tbody>
								<tr ng-repeat="conf in unitConfData">
									<td>{{conf.scope}}</td>
									<td>{{conf.type}}</td>
									<td>{{conf.value}}</td>
									<!-- <td> <span style="display:inline-block;margin:10px 0"> <button class="btn btn-primary pull-right"    ng-click="editCompany(companyDetailsList.companyId)">Edit</button></span></td> -->
								</tr>
							</tbody>
						</table>
					</div>
					<div class="col-lg-10" ng-if="unitConfData.length == 0">
						<h4>No results found</h4>
					</div>
				</div>

			</div>
		</div>
	</div>

</div>
