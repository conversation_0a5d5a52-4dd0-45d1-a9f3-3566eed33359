<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>$data.audit.auditForm.name</title>
    <style type="text/css">
        * {
            font-size: 14px;
        }

        .content {
            padding: 0 20px 20px 20px;
            color: #000;
        }

        .block {
            margin-bottom: 50px;
        }

        .heading {
            background: #689045;
            color: #fff;
            padding: 10px;
            margin-bottom: 20px;
        }

        .questionBlock {
            padding: 20px;
            margin-bottom: 20px;
            background: #f8f8f8;
        }

        .question {
            font-size: 16px;
            border-bottom: #ccc 1px solid;
            margin-bottom: 20px;
        }

        .table {
            width: 100%;
        }

        .table thead {
            border-bottom: #ccc 2px solid;
        }

        .table td {
            border-top: #ccc 1px solid;
            padding: 6px 2px;
        }

        .table thead td {
            font-weight: bold;
        }

        .table tr:nth-child(2n) td {
            background: #f8f8f8;
        }

        .table tr:last-child td {
            font-weight: bold;
        }
    </style>
</head>
<body>

<div class="content">
    <div class="block">
			<div style="text-align: center; padding: 10px; margin-bottom: 50px;">
				#if($data.audit.auditForm.name == "Support Visit")
				<p style="font-size: 24px;">$data.audit.auditForm.name - $data.audit.auditor.name</p> 
				#else
				<p style="font-size: 24px;">$data.audit.auditForm.name</p>
				 #end
				
			</div>
			<p class="heading">
            Submission Details
        </p>
        <div style="padding: 20px;">
            <table style="width: 100%;">
                <tr>
                    <td>Cafe Id:</td>
                    <td>$data.audit.auditUnit.id</td>
                </tr>
                <tr>
                    <td>Cafe Name:</td>
                    <td>$data.audit.auditUnit.name</td>
                </tr>
                <tr>
                    <td>Cafe Manager Name:</td>
                    <td>$data.audit.cafeManager.name</td>
                </tr>
                <tr>
                    <td>Manager on Duty:</td>
                    <td>$data.audit.managerOnDuty.name</td>
                </tr>
                <tr>
                    <td>Area Manager:</td>
                    <td>$data.audit.areaManager.name</td>
                </tr>
                <tr>
                    <td>Audit Date:</td>
                    <td>$date.format('medium', $data.audit.auditSubmissionDate)</td>
                </tr>
                <tr>
                    <td>Auditor Name:</td>
                    <td>$data.audit.auditor.name</td>
                </tr>
                <tr>
                    <td>Audit Type:</td>
                    <td>$data.audit.auditType</td>
                </tr>
            </table>
        </div>
    </div>

    #set( $x = $data.marksComp.get(3))
    #if($x.keySet().size() > 0)
    <div class="block" style="margin-bottom: 200px">
        <p class="heading">
            Audit Score Details
        </p>
        <div style="padding: 20px;">
            <table class="table">
                <thead>
                <tr>
                    <td>PARAMETERS</td>
                    <td>MAX MARKS</td>
                    <td>MARKS SCORED</td>
                    <td>PERCENTAGE</td>
                </tr>
                </thead>
                <tbody>
                #set( $qGroupMarksMap = $data.marksComp.get(3))
                #foreach( $qGroupId in $qGroupMarksMap.keySet() )
                #set( $qGroup = $data.auditFormValuesDataMap.get($qGroupId))
                #set( $scoreData = $qGroupMarksMap.get($qGroupId))
                <tr>
                    <td>$qGroup.entityLabel</td>
                    <td>$scoreData.maxScore</td>
                    <td>$scoreData.acquiredScore</td>
                    <td>$scoreData.percentage %</td>
                </tr>
                #end
                #set ($c = (($data.audit.acquiredScore/$data.audit.totalScore)*100))
                <tr>
                    <td>Total</td>
                    <td>$data.audit.totalScore</td>
                    <td>$data.audit.acquiredScore</td>
                    <td>$c %</td>
                </tr>
                #set ($c = (($data.audit.projectedAcquiredScore/$data.audit.projectedTotalScore)*100))
                <tr>
                    <td>Projected</td>
                    <td>$data.audit.projectedTotalScore</td>
                    <td>$data.audit.projectedAcquiredScore</td>
                    <td>$c %</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    #end

    #set( $s = $data.strengthsComp.get(3))
    #set( $o = $data.observationsComp.get(3))
    #if(($s && $s.size() > 0) || ($o && $o.size() > 0))
    <div class="block">
        <p class="heading">
            AOI Details
        </p>
        <div style="padding: 20px;">
            <div style="font-weight: bold">Strengths:</div>
            #set( $strengths = $data.strengthsComp.get(3))
            #if($strengths.size() > 0)
            <ul>
                #foreach( $strength in $strengths )
                <li>$strength</li>
                #end
            </ul>
            #end
            #if($strengths.size() == 0)
            <p>Not available</p>
            #end
            <div style="font-weight: bold">Opportunities:</div>
            #set( $observations = $data.observationsComp.get(3))
            #if($observations.size() > 0)
            <ul>
                #foreach( $observation in $observations )
                <li>$observation</li>
                #end
            </ul>
            #end
            #if($observations.size() == 0)
            <p>Not available</p>
            #end
        </div>
    </div>
    #end

    #set( $qGroupMarksMap1 = $data.marksComp.get(1))
    #set( $qGroupMarksMap2 = $data.marksComp.get(2))
    #set( $qGroupMarksMap3 = $data.marksComp.get(3))
    #if($qGroupMarksMap1 && $qGroupMarksMap1.values().size()>0 && $qGroupMarksMap2 &&
    $qGroupMarksMap2.values().size()>0 && $data.auditForm.showPastStats)
    <div class="block">
        <p class="heading">
            Comparison Details
        </p>
        <p style="padding: 10px; border-bottom: #689045 2px solid;">Percentage Comparison</p>
        <div style="padding: 20px;">

            <table class="table">
                <thead>
                <tr>
                    <td>PARAMETERS</td>
                    #if($qGroupMarksMap1)
                    <td>Audit I<br/>$qGroupMarksMap1.get($qGroupMarksMap1.keySet().toArray()[0]).auditDate</td>
                    #end
                    #if($qGroupMarksMap2)
                    <td>AUDIT II<br/>$qGroupMarksMap2.get($qGroupMarksMap2.keySet().toArray()[0]).auditDate</td>
                    #end
                    #if($qGroupMarksMap3)
                    <td>AUDIT III<br/>$qGroupMarksMap3.get($qGroupMarksMap3.keySet().toArray()[0]).auditDate</td>
                    #end
                </tr>
                </thead>
                <tbody>
                #foreach( $qGroupId in $qGroupMarksMap3.keySet() )
                #set( $qGroup = $data.auditFormValuesDataMap.get($qGroupId))
                #set( $scoreData1 = $qGroupMarksMap1.get($qGroupId))
                #set( $scoreData2 = $qGroupMarksMap2.get($qGroupId))
                #set( $scoreData3 = $qGroupMarksMap3.get($qGroupId))
                <tr>
                    <td>$qGroup.entityLabel</td>
                    #if($qGroupMarksMap1)
                    <td>$scoreData1.percentage %</td>
                    #end
                    #if($qGroupMarksMap2)
                    <td>$scoreData2.percentage %</td>
                    #end
                    <td>$scoreData3.percentage %</td>
                </tr>
                #end
                <tr>
                    <td>Total</td>
                    #if($qGroupMarksMap1)
                    <td>$data.auditMarksMap.get(1) %</td>
                    #end
                    #if($qGroupMarksMap2)
                    <td>$data.auditMarksMap.get(2) %</td>
                    #end
                    <td>$data.auditMarksMap.get(3) %</td>
                </tr>
                </tbody>
            </table>
        </div>
        <p style="padding: 10px; border-bottom: #689045 2px solid;">Observation Comparison</p>
        <div style="padding: 20px;">
            #if($data.observationsComp.get(1))
            <div style="width: 33%;border: #000 1px solid; float:left;">
                <div style="background: #689045; padding: 5px;">Audit I</div>
                <ul>
                    #set( $observations = $data.observationsComp.get(1))
                    #foreach( $observation in $observations )
                    <li>$observation</li>
                    #end
                </ul>
            </div>
            #end
            #if($data.observationsComp.get(2))
            <div style="width: 33%;border: #000 1px solid; float:left;">
                <div style="background: #689045; padding: 5px;">Audit II</div>
                <ul>
                    #set( $observations = $data.observationsComp.get(2))
                    #foreach( $observation in $observations )
                    <li>$observation</li>
                    #end
                </ul>
            </div>
            #end
            <div style="width: 33%;border: #000 1px solid; float:left;">
                <div style="background: #689045; padding: 5px;">Audit III</div>
                <ul>
                    #set( $observations = $data.observationsComp.get(3))
                    #foreach( $observation in $observations )
                    <li>$observation</li>
                    #end
                </ul>
            </div>
            <div style="clear: both"></div>
        </div>
    </div>
    #end

    <div class="block">
        <p class="heading">
            Questions Asked
        </p>
        <div>
            #foreach( $headingId in $data.auditValuesDataMap.keySet() )
            #set( $head = $data.auditFormValuesDataMap.get($headingId))
            <p class="heading">
                $head.entityLabel
            </p>
            #foreach( $answer in $data.auditValuesDataMap.get($headingId) )
            <div class="questionBlock">
                #set( $question = $data.auditFormValuesDataMap.get($answer.auditFormValue.id))
                <div class="question">$question.entityLabel</div>
                <div class="answerBlock">
                    #if( !$answer.questionOpted )
                    <div>NA #if($answer.option1Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option1 )
                    <div>$answer.option1 #if($answer.option1Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option2 )
                    <div>$answer.option2 #if($answer.option2Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option3 )
                    <div>$answer.option3 #if($answer.option3Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option4 )
                    <div>$answer.option4 #if($answer.option4Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option5 )
                    <div>$answer.option5 #if($answer.option5Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option6 )
                    <div>$answer.option6 #if($answer.option6Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option7 )
                    <div>$answer.option7 #if($answer.option7Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.textArea )
                    <div>
                        <pre>$answer.textArea</pre>
                    </div>
                    #end
                    #if( $answer.yesNo )
                    <div>$answer.yesNo #if($answer.maxScore && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.dateValue )
                    <div>$date.format('medium', $answer.dateValue)</div>
                    #end
                    #if( $answer.timeValue )
                    <div>$date.format('medium', $answer.timeValue)</div>
                    #end
                    #if( $answer.numberValue )
                    <div>$answer.numberValue #if($answer.maxScore && $question.showAnswerScore)<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.textValue )
                    <div>$answer.textValue #if($answer.maxScore && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.employeeId )
                    <div>Employee: $answer.employeeName [$answer.employeeId] : $answer.employeeDesignation</div>
                    #end
                    #if( $answer.unitId )
                    <div>$answer.unitName [$answer.unitId]</div>
                    #end
                    #if( $answer.productId )
                    <div>Product: $answer.productName [$answer.productId]</div>
                    #end
                    #if($answer.attachedDoc)
                    <div>
                     #if($data.auditImageFormValuesDataMap.get($answer.attachedDoc.documentId))
					<img  src="$data.auditImageFormValuesDataMap.get($answer.attachedDoc.documentId)"
						style="max-height: 300px; width: 100%;" />
					#end
                    </div>
                    #end
                </div>
            </div>
            #end
            #end

            #if($data.topLevelValues && $data.topLevelValues.size() > 0)
            <p class="heading">
                --
            </p>
            #foreach( $answer in $data.topLevelValues )
            <div class="questionBlock">
                #set( $question = $data.auditFormValuesDataMap.get($answer.auditFormValue.id))
                <div class="question">$question.entityLabel</div>
                <div class="answerBlock">
                    #if( !$answer.questionOpted )
                    <div>NA #if($answer.option1Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option1 )
                    <div>$answer.option1 #if($answer.option1Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option2 )
                    <div>$answer.option2 #if($answer.option2Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option3 )
                    <div>$answer.option3 #if($answer.option3Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option4 )
                    <div>$answer.option4 #if($answer.option4Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option5 )
                    <div>$answer.option5 #if($answer.option5Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option6 )
                    <div>$answer.option6 #if($answer.option6Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.option7 )
                    <div>$answer.option7 #if($answer.option7Marks && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.textArea )
                    <div>
                        <pre>$answer.textArea</pre>
                    </div>
                    #end
                    #if( $answer.yesNo )
                    <div>$answer.yesNo #if($answer.maxScore && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.dateValue )
                    <div>$date.format('medium', $answer.dateValue)</div>
                    #end
                    #if( $answer.timeValue )
                    <div>$date.format('medium', $answer.timeValue)</div>
                    #end
                    #if( $answer.numberValue )
                    <div>$answer.numberValue #if($answer.maxScore && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.textValue )
                    <div>$answer.textValue #if($answer.maxScore && $question.showAnswerScore=='Y')<span style="float:right;">Marks: $answer.acquiredScore/$answer.maxScore </span>#end
                    </div>
                    #end
                    #if( $answer.employeeId )
                    <div>Employee: $answer.employeeName [$answer.employeeId] : $answer.employeeDesignation</div>
                    #end
                    #if( $answer.unitId )
                    <div>$answer.unitName [$answer.unitId]</div>
                    #end
                    #if( $answer.productId )
                    <div>Product: $answer.productName [$answer.productId]</div>
                    #end
                </div>
            </div>
            #end
            #end


        </div>
    </div>
</div>
</body>
</html>