'use strict';

angular.module('formsApp')
    .controller('dashboardCtrl', ['$rootScope', '$scope', 'StorageUtil', '$location',
            function ($rootScope, $scope, StorageUtil, $location) {

                $scope.init = function () {
                    $scope.userData = StorageUtil.getUserMeta();
                    if ($location.path() == "/dashboard") {
                        $location.path("dashboard/home");
                    }
                };

                $scope.slideMenu = function (event) {
                    var nodes = event.target.parentNode.querySelectorAll(".nav");
                    for(var i=0; i<nodes.length; i++){
                        var node = nodes[i];
                        if(node.classList.contains("collapse")){
                            node.classList.remove("collapse");
                            event.target.classList.add("active");
                        }else{
                            node.classList.add("collapse");
                            event.target.classList.remove("active");
                        }
                    }
                };

                $scope.logout = function () {
                    StorageUtil.eraseCookie("auth");
                    $location.path("/login");
                };

                $scope.startAudit = function(){
                    $scope.toggleSidebar();
                    $location.path("dashboard/forms");
                };

                $scope.searchAudit = function(){
                    $scope.toggleSidebar();
                    $location.path("dashboard/searchAudit");
                };

                $scope.toggleSidebar = function(){
                    var node = document.getElementById("sidebar");
                    var backdrop = document.getElementById("backdrop");
                    if(node){
                        if(node.classList.contains("open")){
                            node.classList.remove("open");
                            backdrop.classList.remove("open");
                        }else{
                            node.classList.add("open");
                            backdrop.classList.add("open");
                        }
                    }

                }
            }
        ]
    );