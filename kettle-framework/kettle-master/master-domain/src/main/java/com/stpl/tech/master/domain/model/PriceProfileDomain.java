package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class PriceProfileDomain implements Serializable {
    private Integer priceProfileDataId;
    private String priceProfileName;

    private Integer brandId;

    private Integer channelPartnerId;
    private String status;
    private Date creationTime;
    private Integer createdBy;
    private Date updationTime;
    private Integer updateBy;

    private Integer clonePriceProfileId;

    private Integer cloneVersion;

    @Builder.Default
    private List<PriceProfileVersionsDomain> priceProfileVersions =  new ArrayList<>();
}
