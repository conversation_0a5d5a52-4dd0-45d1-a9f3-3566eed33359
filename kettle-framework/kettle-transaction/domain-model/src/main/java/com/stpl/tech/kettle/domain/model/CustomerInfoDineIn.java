package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;
import java.util.Date;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {

})
public class CustomerInfoDineIn {

    protected int customerId;

    protected int loyaltyPoints;

    protected Integer lastOrderId;
    protected Integer orderCount;
    protected Date lastOrderTime;
    protected BigDecimal chaayosCash;
    private boolean hasCard;
    private BigDecimal cardAmount;
    private String refCode;
    private Boolean offerAvailed; // sign up offer
    private Boolean signupOfferExpired;
    private Date signupOfferExpiryTime;
    private Boolean signupOfferAvailed;
    private  String lastOrderItemName;
    private Date lastUpdatedTime;
    private BigDecimal totalSpent;
    private String optWhatsapp;
    private String smsSubscriber;
    private Integer loyalteaAvailable;



    public CustomerInfoDineIn() {
    }

    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    public int getLoyaltyPoints() {
        return loyaltyPoints;
    }

    public void setLoyaltyPoints(int loyaltyPoints) {
        this.loyaltyPoints = loyaltyPoints;
    }

    public Integer getLastOrderId() {
        return lastOrderId;
    }

    public void setLastOrderId(Integer lastOrderId) {
        this.lastOrderId = lastOrderId;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public Date getLastOrderTime() {
        return lastOrderTime;
    }

    public void setLastOrderTime(Date lastOrderTime) {
        this.lastOrderTime = lastOrderTime;
    }

    public BigDecimal getChaayosCash() {
        return chaayosCash;
    }

    public void setChaayosCash(BigDecimal chaayosCash) {
        this.chaayosCash = chaayosCash;
    }

    public boolean isHasCard() {
        return hasCard;
    }

    public void setHasCard(boolean hasCard) {
        this.hasCard = hasCard;
    }

    public BigDecimal getCardAmount() {
        return cardAmount;
    }

    public void setCardAmount(BigDecimal cardAmount) {
        this.cardAmount = cardAmount;
    }

    public String getRefCode() {
        return refCode;
    }

    public void setRefCode(String refCode) {
        this.refCode = refCode;
    }

    public Boolean getOfferAvailed() {
        return offerAvailed;
    }

    public void setOfferAvailed(Boolean offerAvailed) {
        this.offerAvailed = offerAvailed;
    }

    public Boolean getSignupOfferExpired() {
        return signupOfferExpired;
    }

    public void setSignupOfferExpired(Boolean signupOfferExpired) {
        this.signupOfferExpired = signupOfferExpired;
    }

    public Date getSignupOfferExpiryTime() {
        return signupOfferExpiryTime;
    }

    public void setSignupOfferExpiryTime(Date signupOfferExpiryTime) {
        this.signupOfferExpiryTime = signupOfferExpiryTime;
    }

    public Boolean getSignupOfferAvailed() {
        return signupOfferAvailed;
    }

    public void setSignupOfferAvailed(Boolean signupOfferAvailed) {
        this.signupOfferAvailed = signupOfferAvailed;
    }

    public String getLastOrderItemName(){
        return lastOrderItemName;
    }

    public void setLastOrderItemName(String lastOrderItemName){
        this.lastOrderItemName = lastOrderItemName;
    }
    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    public BigDecimal getTotalSpent() {
        return totalSpent;
    }

    public void setTotalSpent(BigDecimal totalSpent) {
        this.totalSpent = totalSpent;
    }

    public String getOptWhatsapp() {
        return optWhatsapp;
    }

    public void setOptWhatsapp(String optWhatsapp) {
        this.optWhatsapp = optWhatsapp;
    }

    public String getSmsSubscriber() {
        return smsSubscriber;
    }

    public void setSmsSubscriber(String smsSubscriber) {
        this.smsSubscriber = smsSubscriber;
    }

    public Integer getLoyalteaAvailable() {
        return loyalteaAvailable;
    }

    public void setLoyalteaAvailable(Integer loyalteaAvailable) {
        this.loyalteaAvailable = loyalteaAvailable;
    }
}
