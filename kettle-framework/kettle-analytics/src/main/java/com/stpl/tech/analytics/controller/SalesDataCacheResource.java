
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.controller;

import java.util.Date;

import javax.jms.JMSException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.stpl.tech.analytics.service.SalesDataCacheService;
import com.stpl.tech.analytics.service.impl.AnalyticsProperties;
import com.stpl.tech.analytics.service.impl.ProjectionsDataCache;
import com.stpl.tech.analytics.service.impl.SQSMessageService;
import com.stpl.tech.util.AppUtils;

@Component
@EnableScheduling
public class SalesDataCacheResource extends AbstractSalesResource {

	@Autowired
	private SalesDataCacheService service;

	@Autowired
	private ProjectionsDataCache projections;

	@Autowired
	private AnalyticsProperties props;

	@Autowired
	private SQSMessageService sqsService;

	// runs on 5:01 just to be sure that hour is 5 AM
	@Scheduled(cron = "0 01 05 * * *", zone = "GMT+05:30")
	public void salesDataCacheRefresh() throws JMSException {
		Date businessDate = AppUtils.getPreviousBusinessDate();
		Date startTime = AppUtils.getStartOfBusinessDay(businessDate);
		Date endTime = AppUtils.getStartOfBusinessDay(AppUtils.getBusinessDate());
		super.salesDataCacheRefresh(businessDate, startTime, endTime);
	}

	@Scheduled(cron = "0 0 11 * * *", zone = "GMT+05:30")
	public void refreshProjections() {
		projectionsDataCacheRefresh();
	}

	public ProjectionsDataCache getProjections() {
		return projections;
	}

	public AnalyticsProperties getProps() {
		return props;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.analytics.controller.AbstractSalesResource#getSQSService()
	 */
	@Override
	public SQSMessageService getSQSService() {
		return sqsService;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.analytics.controller.AbstractSalesResource#
	 * getSalesDataCacheService()
	 */
	@Override
	public SalesDataCacheService getSalesDataCacheService() {
		return service;
	}
}