import React from "react";
import {connect} from "react-redux";
import {slotMachineJourneyState, slotMachineStage} from "../../actions/SlotMachineActions";
import * as UtilityActions from "../../actions/UtilityActions";
import appUtil from "../../AppUtil";
import trackUtils from "../../utils/TrackUtils";
import {styles} from './styles/DesktopSlotMachineStyles';
import * as SlotMachineAction from "../../actions/SlotMachineActions";
import * as MyOfferAction from "../../actions/MyOfferActions";

@connect((store) => {
    return {
        currentStage: store.slotMachineReducer.currentStage,
        showNoOffer: store.slotMachineReducer.showNoOffer,
        showNBOOffer: store.slotMachineReducer.showNBOOffer,
        showDNBOOffer: store.slotMachineReducer.showDNBOOffer,
        showChaayosCashOffer: store.slotMachineReducer.showChaayosCashOffer,
        showMembershipOffer: store.slotMachineReducer.showMembershipOffer,
        campaignDetail: store.slotMachineReducer.campaignDetail,
        utmData: store.slotMachineReducer.utmData,
        specialOffer: store.slotMachineReducer.specialOffer,
        isButtonLoading: store.slotMachineReducer.isButtonLoading,
        enableShowOfferButton: store.slotMachineReducer.enableShowOfferButton,
    };
})

export default class DesktopSlotMachineLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            name: null,
            email: null,
            contact: null,
            otp: null,
            otpResendCounter: 30,
            showShareOptions: false,
            flow:0,
            authToken:null,
            isContactFromUrl:false,
            spinning:false,
            revealing:false,
            optWhatsapp:true
        };
        this.initializeGame = this.initializeGame.bind(this);
        this.getOffer = this.getOffer.bind(this);
        this.setSlotMachineBackgroundImage = this.setSlotMachineBackgroundImage.bind(this);
        this.getTermAndCondition = this.getTermAndCondition.bind(this);
        this.processCheckBox = this.processCheckBox.bind(this);
        this.onPhoneInputChange = this.onPhoneInputChange.bind(this);
    }

    componentWillMount() {
        window.scrollTo(0, 0);
    }

    componentDidMount() {
        console.log("componentDidMount");
        const queryParams = new URLSearchParams(window.location.search);
        if(appUtil.checkEmpty(queryParams.get("token"))){
            this.props.dispatch(SlotMachineAction.setCampaignDetail({"campaignToken":"6F4922F45568161A8CDF4AD2299F6D23","campaignId":"6F4922F45568161A8CDF4AD2299F6D23"}));
        }else{
            this.props.dispatch(SlotMachineAction.setCampaignDetail({"campaignToken":queryParams.get("token"),"campaignId":queryParams.get("token")}))
        }
        window.addEventListener('load', this.initializeGame);
        this.setSlotMachineBackgroundImage();
        const params = this.props.props.params;
        this.setState({flow : params.flow});
        var token = this.getCookie("token");
        this.setState({authToken:token})
        var utmData={
            "utmSource":!appUtil.checkEmpty(queryParams.get("utm_source")) ? queryParams.get("utm_source") : "DEFAULT",
            "utmMedium":!appUtil.checkEmpty(queryParams.get("utm_medium")) ? queryParams.get("utm_medium") : "DEFAULT"
        }
        if(params.flow === "2"){
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(true))
            this.props.dispatch(SlotMachineAction.updateSlotMachineStage(slotMachineStage.onlySpinPage));
        }else{
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(true))
            this.props.dispatch(SlotMachineAction.updateSlotMachineStage(slotMachineStage.getBasicInfo));
        }
        this.props.dispatch(SlotMachineAction.setUtmData(utmData));
        var urlContact = queryParams.get("contact");
        if(urlContact != null && urlContact.length >= 10){
            urlContact=urlContact.substring(urlContact.length-10);
        }
        if(!appUtil.checkEmpty(urlContact) && appUtil.validContact(urlContact)){
            this.setState({contact:urlContact});
            this.setState({isContactFromUrl:true});
        }
        fbq('trackCustom', 'MachineGamePageView');
        trackUtils.trackSlotMachineGamePageView();
        this.props.dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.pageView,"desktop"))
    }

    onPhoneInputChange(){
        if(this.state.flow !== "2"){
            return;
        }
        var value =  document.getElementById("phoneNumber").value;
        this.setState({"contact":value});
        if (appUtil.validContact(value)){
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(true))
            // this.props.dispatch(SlotMachineAction.getSpecialOffer(this.props.currentStage,value,this.props.campaignDetail.campaignToken,name,this.state.email,
            //     null,this.props.utmData.utmSource,this.props.utmData.utmMedium,this.state.authToken,this.state.flow,this.showConfetti,this.state.optWhatsapp,"desktop"))
        }else if(value.length === 10){
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(false))
            this.props.dispatch(UtilityActions.showPopup("Please enter valid contact number!", "error", 3000));
        }else{
            this.props.dispatch(SlotMachineAction.updateEnableShowOfferButton(false))
        }
    }

    getOffer(){
        if(!this.props.enableShowOfferButton){
            return;
        }
        if(this.props.currentStage === slotMachineStage.onlySpinPage){
            this.spin();
            fbq('trackCustom', 'MachineGamePlayedFlow2');
            trackUtils.trackSlotMachineGamePlayedFlow2();
            this.props.dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.spinMachineFlow2,"desktop"))
            return;
        }
        if(this.props.currentStage === slotMachineStage.getBasicInfo){
            var contact = (this.state.isContactFromUrl) ? this.state.contact : document.getElementById("phoneNumber").value;
            var email = null;
            if(this.props.isButtonLoading){
                return;
            }
            if(this.state.flow === "1"){
                email = document.getElementById("emailId").value;
                if(appUtil.checkEmpty(email) || !appUtil.validEmail(email)){
                    this.props.dispatch(UtilityActions.showPopup("Please enter valid email!", "error", 3000));
                    return;
                }else{
                    this.setState({email:email});
                }
            }
            if(appUtil.checkEmpty(contact) || !appUtil.validContact(contact)){
                this.props.dispatch(UtilityActions.showPopup("Please enter valid contact number!", "error", 3000));
                return;
            }else{
                this.setState({contact:contact});
            }
            this.props.dispatch(SlotMachineAction.updateButtonLoader(true))
            if(this.state.flow === "2"){
                this.props.dispatch(SlotMachineAction.getSpecialOffer(this.props.currentStage,contact,this.props.campaignDetail.campaignToken,name,this.state.email,
                    null,this.props.utmData.utmSource,this.props.utmData.utmMedium,this.state.authToken,this.state.flow,this.showConfetti,this.state.optWhatsapp,"desktop"))
                return;
            }
            this.spin(contact,email,this.state.name,this.state.otp,this.state.authToken,this.state.flow,this.showConfetti,this.state.optWhatsapp);
        }else{
            if(this.state.revealing){
                return;
            }
            var name = document.getElementById("name").value;
            var otp = document.getElementById("otp").value;
            if(appUtil.checkEmpty(name) || !appUtil.validName(name)){
                this.props.dispatch(UtilityActions.showPopup("Please enter valid name!", "error", 3000));
                return;
            }else{
                this.setState({name:name});
            }
            if(appUtil.checkEmpty(otp) || !appUtil.validOtp(otp)){
                this.props.dispatch(UtilityActions.showPopup("Please enter valid otp!", "error", 3000));
                return;
            }else{
                this.setState({otp:otp});
            }
            this.setState({revealing:true})
            this.props.dispatch(SlotMachineAction.getSpecialOffer(this.props.currentStage,this.state.contact,this.props.campaignDetail.campaignToken,name,this.state.email,
                otp,this.props.utmData.utmSource,this.props.utmData.utmMedium,this.state.authToken,this.state.flow,this.showConfetti,this.state.optWhatsapp,"desktop"))
        }
    }

    showConfetti(){
        var times =0;
        const canvas = document.getElementById('your_custom_canvas_id')
        const jsConfetti = new JSConfetti({ canvas })
        document.querySelector("#crowdCheer").play();
        jsConfetti.addConfetti({
            confettiColors: [
                '#ff3e3e','#ff3ec7','#c53effd4','#3e94ffd4','#05b232d4','#008222'
            ],
            confettiNumber: 300,
            confettiRadius: 5,
        })
        var timeInterval = setInterval(()=>{
            jsConfetti.addConfetti({
                confettiColors: [
                    '#ff3e3e','#ff3ec7','#c53effd4','#3e94ffd4','#05b232d4','#008222'
                ],
                confettiNumber: 300,
                confettiRadius: 5,
            })
            if(times === 1){
                clearInterval(timeInterval);
            }
            times+=1;
        },2000);
        var stopCheerSound = setTimeout(()=>{
            document.querySelector("#crowdCheer").pause();
            document.querySelector("#crowdCheer").currentTime =0;
        },6000);
    }

    setSlotMachineBackgroundImage(){
        console.log("setSlotMachineBackgroundImage");
        const imageUrlMachine = "./../../../img/SlotGame/slotMachine.webp";
        const imageUrlJackpot = "./../../../img/SlotGame/jackpotImage.webp";
        const imageUrlOfferDetail = "./../../../img/SlotGame/offerDetailBanner.webp";
        const imageUrlBalloons = "./../../../img/SlotGame/desktopBalloons.webp";
        const slotGameBoxes = document.querySelector("#slotGameBoxes");
        const parentContainer = document.querySelector("#parentContainer");
        const bgElement = document.querySelector("#slotMachineContainer");
        let preloaderImgJackpot = document.createElement("img");
        let preloaderImgSlotMachine = document.createElement("img");
        let preloaderImgOfferDetail = document.createElement("img");
        // let preloaderImgBalloons = document.createElement("img");
        preloaderImgJackpot.src = imageUrlJackpot;
        preloaderImgJackpot.addEventListener('load', (event1) => {
            preloaderImgSlotMachine.src = imageUrlMachine;
            preloaderImgSlotMachine.addEventListener('load', (event2) => {
                preloaderImgOfferDetail.src = imageUrlOfferDetail;
                preloaderImgOfferDetail.addEventListener('load', (event3) => {
                    parentContainer.style.display = 'flex';
                });
            });
        });
    }

    getDateString(date){
        return new Date(date).toDateString().substring(8,10)+" "+
            new Date(date).toDateString().substring(4,7)+" "+
            new Date(date).toDateString().substring(11)
    }

    getCookie(cname) {
        let name = cname + "=";
        let decodedCookie = decodeURIComponent(document.cookie);
        let ca = decodedCookie.split(';');
        for(let i = 0; i <ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    }
    getDays(startDate, endDate){
        return (new Date(endDate) - new Date(startDate))/(1000*60*60*24);
    }

    getTermAndCondition(tncString, startDate, endDate){
        var tncList = tncList.split("#");
        var innerHtml = "valid from : "+startDate+" valid till : "+endDate;
        for(var i in tncList){
            innerHtml+="<br/>"+tncList[i];
        }
        return innerHtml;
    }

    async spin(contact,email,name,otp,token,flow,callback,opt) {
        this.props.dispatch(MyOfferAction.recordJourney(slotMachineJourneyState.spinMachine,"desktop",contact));
        console.log("contact");
        if(flow === undefined){
            flow = this.state.flow;
        }
        const doors = document.querySelectorAll(".door");
        this.initializeGame(false, 1, 4,contact, email,name,otp,token,flow,callback,opt);
        for (const door of doors) {
            const boxes = door.querySelector(".boxes");
            const duration = parseInt(boxes.style.transitionDuration);
            boxes.style.transform = "translateY(0)";
            await new Promise((resolve) => setTimeout(resolve, duration * 100));
        }
    }

     initializeGame(firstInit = true, groups = 1, duration = 1,contact,email,name,otp,token,flow,callback,opt) {
         console.log("initializeGame");
         var jackPotSlot = Math.floor(Math.random()*100)%3;
         var props=this.props;
        const doors = document.querySelectorAll(".door");
         var doorSize = doors.length;
         var doorIndex=0;
        for (const door of doors) {
            if (firstInit) {
                door.dataset.spinned = "0";
            } else if (firstInit && door.dataset.spinned !== "1") {
                return;
            }

            const boxes = door.querySelector(".boxes");
            const boxesClone = boxes.cloneNode(false);

            const pool = ["slotGame0.webp"];
            if (!firstInit) {
                pool.pop();
                const arr = [];
                for (let n = 0; n < (groups > 0 ? groups : 1); n++) {
                    arr.push(...SlotMachineAction.slotMachineItems);
                }
                var newArray=[];
                for (var i = 0; i <= 4; i++) {
                    newArray = newArray.concat(arr);
                };
                if(doorIndex !== jackPotSlot){
                    newArray.push("slotGame8.webp");
                    newArray.push("slotGame8.webp");
                    newArray.push("slotGame8.webp");
                    newArray.push("slotGame8.webp");
                }
                pool.push(...this.shuffle(newArray));

                boxesClone.addEventListener(
                    "transitionstart",
                    function () {
                        door.dataset.spinned = "1";
                        this.querySelectorAll(".box").forEach((box) => {
                            //   box.style.filter = "blur(1px)";
                        });
                        document.querySelector("#audio").play();
                    },
                    { once: true }
                );

                boxesClone.addEventListener(
                    "transitionend",
                    function () {
                        this.querySelectorAll(".box").forEach((box, index) => {
                            box.style.filter = "blur(0)";
                            if (index > 0) this.removeChild(box);
                        });
                        if(doorIndex === doorSize){
                            doorIndex=0;
                            if(flow !== "2"){
                                const noOfferBg = "./../../../img/SlotGame/noOfferPop.webp";
                                const offerFoundBg = "./../../../img/SlotGame/offerFoundBg.webp";
                                let preloaderNoOfferBg = document.createElement("img");
                                let preloaderOfferFoundBg = document.createElement("img");
                                preloaderNoOfferBg.src = noOfferBg;
                                preloaderNoOfferBg.addEventListener('load', (event5) => {
                                    preloaderOfferFoundBg.src = offerFoundBg;
                                    preloaderOfferFoundBg.addEventListener('load', (event6) => {
                                        var getOfferTimeOut = setTimeout(()=>{
                                            props.dispatch(SlotMachineAction.getSpecialOffer(props.currentStage,contact,props.campaignDetail.campaignToken,name,email,
                                                otp,props.utmData.utmSource,props.utmData.utmMedium,token,flow,callback,opt,"desktop"))
                                            clearTimeout(getOfferTimeOut);
                                        },2000)
                                    });
                                });
                            }else{
                                var getOfferTimeOut = setTimeout(()=>{
                                    props.dispatch(SlotMachineAction.updateSlotMachineStage(slotMachineStage.getBasicInfo));
                                    props.dispatch(SlotMachineAction.updateEnableShowOfferButton(false));
                                    clearTimeout(getOfferTimeOut);
                                },2000)
                            }

                        }
                        var soundStopTimer = setTimeout(()=>{
                            document.querySelector("#audio").pause();
                            document.querySelector("#audio").currentTime =0;
                            clearTimeout(soundStopTimer);
                        },600)
                    },
                    { once: true }
                );
            }
            // console.log(pool);

            for (let i = pool.length - 1; i >= 0; i--) {
                const box = document.createElement("div");
                box.classList.add("box");
                box.style.width = door.clientWidth + "px";
                box.style.height = door.clientHeight + "px";
                box.style.display = "flex";
                box.style.alignItems = "center";
                box.style.justifyContent = "center";
                const image = document.createElement("img");
                image.src="./../../../img/SlotGame/"+pool[i];
                image.style.height="45px";
                image.style.width="45px";
                box.appendChild(image);
                boxesClone.appendChild(box);
            }
            boxesClone.style.transitionDuration = `${duration > 0 ? duration : 1}s`;
            boxesClone.style.transform = `translateY(-${
                door.clientHeight * (pool.length - 1)
            }px)`;
            door.replaceChild(boxesClone, boxes);
            // console.log(door);
            doorIndex+=1;
        }
    }

    shuffle([...arr]) {
        let m = arr.length;
        while (m) {
            const i = Math.floor(Math.random() * m--);
            [arr[m], arr[i]] = [arr[i], arr[m]];
        }
        return arr;
    }

    startOTPResendWaitCounter() {
        let sec = 30;
        let interval = setInterval(() => {
            this.setState({otpResendCounter: --sec});
            //console.log("seconds:: " + sec);
            if (sec === 0) {
                clearInterval(interval);
            }
        }, 1000);
    }

    processCheckBox(){
        if(this.state.optWhatsapp){
            this.setState({optWhatsapp:false});
        }else{
            this.setState({optWhatsapp:true});
        }
    }

    showExploreButton(utmMedium){
        var show ="visible";
        if(utmMedium==="DINE_IN_APP" || utmMedium === "CRM_APP" || this.props.utmData.utmSource === "CAFE"){
            show = "hidden";
        }
        return(
            <div style={{...styles.exploreMenuButton, visibility:show}}
                 onClick={() => window.open('https://zomato.onelink.me/xqzv/5fbcb81a')}>
                <p style={{color:'white',fontSize:'12px',fontFamily:'Nunito',textAlign:'center'}}>Explore Menu</p>
            </div>
        );
    }

    getOfferTextSize(text){
        var size = text.length;
        if(size<=8){
            return '45px'
        }else if(size<=16){
            return '30px'
        }else{
            return '22px'
        }
    }

    handleOTPChange = otp => this.setState({otp});

    render() {

        return (
            <div id="parentContainer" style={styles.parentContainer}>
                <audio id="audio" src={"./../../../img/SlotGame/slotGameSound.wav"}/>
                <audio id="crowdCheer" src={"./../../../img/SlotGame/crowdCheer.mp3"}/>
                <div style={styles.leftChildContainer}>
                    <img style={{width:'80%'}} src={"./../../../img/SlotGame/jackpotImage.webp"}/>
                    <img style={{width:'80%'}} src={"./../../../img/SlotGame/offerDetailBanner.webp"}/>
                </div>
                <div style={styles.rightChildContainer}>
                    <div id="slotMachineContainer" style={styles.slotMachineContainer}>
                        <div id="slotGameBoxes" style={styles.slotBoxContainer}>
                            <div className="door" style={styles.door}>
                                <div className="boxes" style={styles.boxes}>
                                </div>
                            </div>
                            <div className="door" style={styles.door}>
                                <div className="boxes" style={styles.boxes}>
                                </div>
                            </div>
                            <div className="door" style={styles.door}>
                                <div className="boxes" style={styles.boxes}>
                                </div>
                            </div>
                        </div>
                    </div>
                    {
                        this.props.currentStage === slotMachineStage.getBasicInfo ?
                            <div style={styles.formAndButtonContainer}>
                                {
                                    this.state.flow === "2"?
                                        <p style={{fontSize: '17px', fontWeight: 'bold', marginBottom: '5%',color:'#7a0202'}}>Yay! You have won a special offer.</p>:<div/>
                                }
                                { !this.state.isContactFromUrl ?
                                    <div style={{display:'flex',flexDirection : 'column', alignItems:'flex-start', width:'65%'}}>
                                        <p style={{padding:'5px 0',color:'#0a6001',fontWeight:'bolder',fontSize:'15px'}}>MOBILE NUMBER*</p>
                                        <div style={styles.phoneNumberBox}>
                                            <div style={styles.phoneNumberBoxChildLeft}>
                                                <p style={{color:'#fff',fontSize:'14px'}}>+91</p>
                                            </div>
                                            <div style={styles.phoneNumberBoxChildRight}>
                                                <input type={'tel'} maxLength={10} id="phoneNumber" onChange={()=> this.onPhoneInputChange()} style={styles.phoneNumberTextField}/>
                                            </div>
                                        </div>
                                        <div style={{display:'flex',flexDirection:'row', justifyContent:'flex-start',margin:'5px'}}>
                                            <div style={{width:'30px'}}>
                                                <div style={{...styles.checkBox,backgroundColor:this.state.optWhatsapp ? "#008000":"#e9e9e9"}} onClick={this.processCheckBox}>
                                                    {
                                                        this.state.optWhatsapp?<img src={"./../../../img/SlotGame/tick.png"} height={'13px'} width={'13px'}/>:<div/>
                                                    }
                                                </div>
                                            </div>
                                            <p style={styles.smallGreyText}>We will send offer details through sms/whatsapp on this number.</p>
                                        </div>
                                    </div>:<div/>
                                }
                                {
                                    this.state.flow === "1"?
                                        <div style={{display:'flex',flexDirection : 'column', alignItems:'flex-start', width:'65%',marginTop:'20px'}}>
                                            <p style={{padding:'5px 0',color:'#0a6001',fontWeight:'bolder',fontSize:'15px'}}>EMAIL ID*</p>
                                            <div style={styles.phoneNumberBox}>
                                                <div style={styles.phoneNumberBoxChildLeft}>
                                                    <p style={{color:'#fff',fontSize:'18px'}}>@</p>
                                                </div>
                                                <div style={styles.phoneNumberBoxChildRight}>
                                                    <input maxLength={60} id="emailId" style={{...styles.phoneNumberTextField,textTransform: 'lower'}}/>
                                                </div>
                                            </div>
                                        </div>:<div/>
                                }
                                <div id="playButton" style={{...styles.spinButton, backgroundColor:this.props.enableShowOfferButton ? 'green':'grey',cursor: this.props.enableShowOfferButton ? 'pointer' : 'no-drop'}} onClick={()=>this.getOffer()}>
                                    {
                                        this.props.isButtonLoading ?
                                            <div class="spinner-border text-light" role="status">
                                            </div>:
                                            <p style={styles.spinButtonText}>{this.state.flow !=="2" ? "PLAY AND WIN":"GET OFFER"}</p>
                                    }
                                </div>
                            </div>:<div/>
                    }
                    {
                        this.props.currentStage === slotMachineStage.onlySpinPage ?
                            <div style={styles.formAndButtonContainer}>
                                <p style={{fontSize: '17px', fontWeight: 'bold', marginBottom: '5%', color:'#7a0202'}}>100% guaranteed prizes for everyone.</p>
                                <div style={{...styles.spinButton, backgroundColor:this.props.enableShowOfferButton  ? 'green':'grey',cursor: this.props.enableShowOfferButton  ? 'pointer' : 'no-drop'}} onClick={()=>this.getOffer()}>
                                    {
                                        this.props.isButtonLoading ?
                                            <div class="spinner-border text-light" role="status">
                                            </div>:
                                            <p style={styles.spinButtonText}>PLAY AND WIN</p>
                                    }
                                </div>
                            </div>:<div/>
                    }
                    {
                        this.props.currentStage === slotMachineStage.getOTP ?
                            <div style={styles.formAndButtonContainer}>
                                <p style={{fontSize: '17px',fontWeight: 'bold',marginBottom: '20px'}}>Enter details to reveal your offer</p>
                                <div style={{display:'flex',flexDirection : 'column', alignItems:'flex-start', width:'65%',marginBottom:'20px'}}>
                                    <p style={{marginLeft:'10px',color:'#7a7a7a',fontWeight:'bolder'}}>NAME*</p>
                                    <div style={styles.phoneNumberBox}>
                                        <div style={styles.phoneNumberBoxChildLeft}>
                                            <img width={'40%'} src={"./../../../img/SlotGame/nameIcon.svg"}/>
                                        </div>
                                        <div style={{...styles.phoneNumberBoxChildRight }}>
                                            <input maxLength={60} id="name" style={{...styles.phoneNumberTextField,textTransform: 'capitalize'}}/>
                                        </div>
                                    </div>
                                </div>
                                <div style={{display:'flex',flexDirection : 'column', alignItems:'flex-start', width:'65%'}}>
                                    <p style={{marginLeft:'10px',color:'#7a7a7a',fontWeight:'bolder'}}>OTP*</p>
                                    <div style={styles.phoneNumberBox}>
                                        <div style={styles.phoneNumberBoxChildLeft}>
                                            <p style={{color:'#fff',fontSize:'18px'}}>****</p>
                                        </div>
                                        <div style={{...styles.phoneNumberBoxChildRight }}>
                                            <input type={'tel'} maxLength={4} id="otp" style={styles.phoneNumberTextField}/>
                                        </div>
                                    </div>
                                </div>
                                <p style={styles.smallGreyText}>We will share all offers & OTPs to this number</p>
                                <div style={{...styles.spinButton, marginTop:'20px',cursor: this.props.enableShowOfferButton ? 'pointer' : 'no-drop'}} onClick={()=>this.getOffer()}>
                                    {
                                        this.state.revealing ?
                                            <div className="spinner-border text-light" role="status">
                                            </div>:
                                            <p style={styles.spinButtonText}>REVEAL OFFER</p>
                                    }
                                </div>
                            </div>:<div/>
                    }
                    {
                        this.props.currentStage === slotMachineStage.showOffer ? <div style={{height: "50%"}}/>:<div/>
                    }
                </div>
                {this.props.currentStage===slotMachineStage.showOffer && this.props.showNoOffer ?
                    <div className="modal" style={styles.modal}>
                        <div className="modalBody" style={{...styles.modalBody,backgroundImage:`url("./../../../img/SlotGame/noOfferPop.webp")`, justifyContent:'flex-end'}}>
                            <div style={{marginBottom:'20px',width:'20%'}}>
                                <h1 style={styles.downloadText}>Download the app Now</h1>
                                <div style={styles.downloadContainer}>
                                    <div style={styles.downloadStoreContainer} onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                        <img style={styles.downloadImg} src="../../../../img/downloadappstore.png"/>
                                    </div>
                                    <div style={styles.downloadStoreContainer } onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                        <img style={styles.downloadImg} src="../../../../img/downloadplaystore.png"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>:<div/>
                }
                {this.props.currentStage===slotMachineStage.showOffer && this.props.showNBOOffer ?
                    <div className="modal" style={styles.modal}>
                        <div className="modalBody" style={{...styles.modalBody, justifyContent:'space-between', backgroundImage:`url("./../../../img/SlotGame/offerFoundBg.webp")`}}>
                            <img src={"./../../../img/SlotGame/congoHeader.webp"} height={'25%'}/>
                            <p style={styles.youHaveWonText}>you've{this.props.specialOffer.existingOffer ? " already" : ""} won</p>
                            <p style={{...styles.offerTextStyle,fontSize:this.getOfferTextSize(this.props.specialOffer.text)}}>{this.props.specialOffer.text}</p>
                            <p style={{...styles.youHaveWonText,marginBottom:'20px'}}>on your next { this.props.specialOffer.maxUsage>1 ? this.props.specialOffer.maxUsage : ""} visit{this.props.specialOffer.maxUsage>1 ? "s" : ""} at Chaayos</p>
                            <div style={styles.couponCodeContainer}>
                                <div style={{width:'100%',marginRight:'20px'}}>
                                    <p style={styles.useCodeText}>Use Code</p>
                                </div>
                                <div style={styles.couponCodeBox}>
                                    <p style={styles.couponCodeText}>{this.props.specialOffer.offerCode}</p>
                                </div>
                            </div>
                            <div style={{...styles.tncContainer,marginTop:'30px'}}>
                                <p style={styles.tncHeading}>Terms and Conditions</p>
                                <p id="tnc" style={styles.tncText}></p>
                            </div>
                            <p>Take a screenshot for future use</p>
                            {this.showExploreButton(this.props.utmData.utmMedium)}
                            <div style={{marginBottom:'20px',width:'15%'}}>
                                <h1 style={styles.downloadText}>Download the app Now</h1>
                                <div style={styles.downloadContainer}>
                                    <div style={styles.downloadStoreContainer} onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                        <img style={styles.downloadImg} src="../../../../img/downloadappstore.png"/>
                                    </div>
                                    <div style={styles.downloadStoreContainer } onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                        <img style={styles.downloadImg} src="../../../../img/downloadplaystore.png"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>:<div/>
                }
                {this.props.currentStage===slotMachineStage.showOffer && this.props.showDNBOOffer  ?
                    <div className="modal" style={styles.modal}>
                        <div className="modalBody" style={{...styles.modalBody, justifyContent:'space-between', backgroundImage:`url("./../../../img/SlotGame/offerFoundBg.webp")`}}>
                            <img src={"./../../../img/SlotGame/congoHeader.webp"} height={'25%'}/>
                            <p style={styles.youHaveWonText}>you've{this.props.specialOffer.existingOffer ? " already" : ""} won</p>
                            <p style={{...styles.offerTextStyle,fontSize:this.getOfferTextSize(this.props.specialOffer.text)}}>{this.props.specialOffer.text}</p>
                            <p style={{...styles.youHaveWonText,marginBottom:'20px'}}>on your next { this.props.specialOffer.maxUsage>1 ? this.props.specialOffer.maxUsage : ""} order{this.props.specialOffer.maxUsage>1 ? "s" : ""} from Zomato</p>
                            <div style={styles.couponCodeContainer}>
                                <div style={{width:'100%',marginRight:'20px'}}>
                                    <p style={styles.useCodeText}>Use Code</p>
                                </div>
                                <div style={styles.couponCodeBox}>
                                    <p style={styles.couponCodeText}>{this.props.specialOffer.offerCode}</p>
                                </div>
                            </div>
                            <div style={{...styles.tncContainer,marginTop:'30px'}}>
                                <p style={styles.tncHeading}>Terms and Conditions</p>
                                <p id="tnc" style={styles.tncText}></p>
                            </div>
                            <p>Take a screenshot for future use</p>
                            {this.showExploreButton(this.props.utmData.utmMedium)}
                            <div style={{marginBottom:'20px',width:'15%'}}>
                                <h1 style={styles.downloadText}>Download the app Now</h1>
                                <div style={styles.downloadContainer}>
                                    <div style={styles.downloadStoreContainer} onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                        <img style={styles.downloadImg} src="../../../../img/downloadappstore.png"/>
                                    </div>
                                    <div style={styles.downloadStoreContainer } onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                        <img style={styles.downloadImg} src="../../../../img/downloadplaystore.png"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>:<div/>
                }
                {this.props.currentStage===slotMachineStage.showOffer && this.props.showMembershipOffer ?
                    <div className="modal" style={styles.modal}>
                        <div className="modalBody" style={{...styles.modalBody, justifyContent:'space-between', textAlign:'center', backgroundImage:`url("./../../../img/SlotGame/offerFoundBg.webp")`}}>
                            <img src={"./../../../img/SlotGame/congoHeader.webp"} height={'25%'}/>
                            <p style={styles.youHaveWonText}>you've{this.props.specialOffer.existingOffer ? " already" : ""} won</p>
                            <p style={{...styles.offerTextStyle,fontSize:this.getOfferTextSize(this.props.specialOffer.text)}}>Chaayos Select Membership</p>
                            <p style={{...styles.offerTextStyle,fontSize:'15px'}}>Worth 199&#x20b9;</p>
                            <p style={styles.youHaveWonText}>for your next { this.getDays(this.props.specialOffer.validityFrom,this.props.specialOffer.validityTill)} visits at Chaayos</p>
                            <img src={"./../../../img/SlotGame/twoCups.webp"} height={'10%'}/>
                            <p style={{...styles.offerTextStyle,fontSize:'15px'}}>{this.props.specialOffer.text}</p>
                            <div style={{...styles.tncContainer}}>
                                <p style={styles.tncHeading}>Terms and Conditions</p>
                                <p id="tnc" style={styles.tncText}></p>
                            </div>
                            <p>Take a screenshot for future use</p>
                            {this.showExploreButton(this.props.utmData.utmMedium)}
                            <div style={{marginBottom:'20px',width:'15%'}}>
                                <h1 style={styles.downloadText}>Download the app Now</h1>
                                <div style={styles.downloadContainer}>
                                    <div style={styles.downloadStoreContainer} onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                        <img style={styles.downloadImg} src="../../../../img/downloadappstore.png"/>
                                    </div>
                                    <div style={styles.downloadStoreContainer } onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                        <img style={styles.downloadImg} src="../../../../img/downloadplaystore.png"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>:<div/>
                }
                {this.props.currentStage===slotMachineStage.showOffer && this.props.showChaayosCashOffer ?
                    <div className="modal" style={styles.modal}>
                        <div className="modalBody" style={{...styles.modalBody, justifyContent:'space-between', backgroundImage:`url("./../../../img/SlotGame/offerFoundBg.webp")`}}>
                            <img src={"./../../../img/SlotGame/congoHeader.webp"} height={'25%'}/>
                            <p style={styles.youHaveWonText}>you've{this.props.specialOffer.existingOffer ? " already" : ""} won</p>
                            <p style={{...styles.offerTextStyle,fontSize:'50px'}}>&#x20b9; {this.props.specialOffer.chaayosCash}* </p>
                            <div style={{display:'flex',justifyContent:'center',alignItems:'center',width:'20%'}}>
                                <div style={{minHeight:'3px',minWidth:'20%',backgroundColor:'#C2541A'}}/>
                                <div style={{width:'70%'}}>
                                    <p style={{fontSize:'18px',marginLeft:'10px',marginRight:'10px',textAlign:'center'}}>Chaayos Cash</p>
                                </div>
                                <div style={{minHeight:'3px',minWidth:'20%',backgroundColor:'#C2541A'}}/>
                            </div>
                            <p style={styles.youHaveWonText}>Redeem in next visit & get free items</p>
                            <img src={"./../../../img/SlotGame/twoCups.webp"} height={'15%'}/>
                            <div style={{...styles.tncContainer,marginTop:'10px'}}>
                                <p style={styles.tncHeading}>Terms and Conditions</p>
                                <p id="tnc" style={styles.tncText}></p>
                            </div>
                            <p>Take a screenshot for future use</p>
                            {this.showExploreButton(this.props.utmData.utmMedium)}
                            <div style={{marginBottom:'10px',width:'15%'}}>
                                <h1 style={styles.downloadText}>Download the app Now</h1>
                                <div style={styles.downloadContainer}>
                                    <div style={styles.downloadStoreContainer} onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                        <img style={styles.downloadImg} src="../../../../img/downloadappstore.png"/>
                                    </div>
                                    <div style={styles.downloadStoreContainer } onClick={()=> window.open('https://chaayos.org/freechai_signup_app_download')}>
                                        <img style={styles.downloadImg} src="../../../../img/downloadplaystore.png"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>:<div/>
                }
            </div>
        );
    }
}

