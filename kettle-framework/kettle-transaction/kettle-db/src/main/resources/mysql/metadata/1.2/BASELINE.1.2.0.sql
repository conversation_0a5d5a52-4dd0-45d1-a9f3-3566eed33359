INSERT INTO MIGRATION_LOOKUP_DATA(<PERSON><PERSON><PERSON><PERSON>_TYPE,<PERSON><PERSON><PERSON>UP_TEXT,C<PERSON><PERSON>LATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, R<PERSON>ISTRATION_UNIT_ID) VALUES('<PERSON><PERSON><PERSON>', '<EMAIL>',1640,1640, '<PERSON><PERSON><PERSON>','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LO<PERSON>UP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3550,1550, 'Vimal Agarwal','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2360,1400, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1450,1330, 'atul singh','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2150,1150, 'ankit jain','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3550,930, 'Sanchit Nishesh','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1050,870, 'Arobindo Mullick','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1500,820, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',4000,820, 'pramod.mudgal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',760,760, 'Manuj Goel','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2050,750, 'Aparna Sridhar','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',660,660, 'Bharat Ahuja ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',660,660, 'KANIK YADAV','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',660,660, 'sumeet','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2160,660, 'vaibhav','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',610,610, 'Jasneet','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2580,600, 'ranjana kadam','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1380,600, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',760,580, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',580,580, 'Shruti Arora','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',570,570, 'Rahul Ghoshal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',570,570, 'rishi','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',860,560, 'Mohit','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',830,530, 'Jayesh Jani','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',890,530, 'kunal ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',770,530, 'Tushar Singla','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',520,520, 'vivek','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2240,500, 'Krishna Kumar','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',500,500, 'Ruchika Bansal','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',470,470, 'abhishek','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',490,430, 'Abhinav Jain','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',790,430, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',470,410, 'Nitin Chadha','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',410,410, 'Manvi Sobti','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',690,390, 'bhavya aggarwal','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',390,390, 'Pavan Reddy','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1100,380, 'Davinder Singh','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',370,370, 'mohit','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',710,350, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3040,340, 'Gaurav Khare ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',480,300, 'Abhishek Gulati','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',600,300, 'Manali Arora','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',280,280, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',280,280, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', 'sapnarathee @gmail.com',760,280, 'Sapna Rathee','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',270,270, 'Sonal','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1350,270, 'Prateek','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',260,260, 'Kulpreet Singh Chandoke','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',260,260, 'Rohit Thakur','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',250,250, 'Kamal','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',250,250, 'manisha','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',430,250, 'Manish Nautiyal','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',250,250, 'Harsh Vardhan Singh','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',300,240, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1020,240, 'Gurpreet Singh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',420,240, 'saurabh23','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',230,230, 'Divya','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',230,230, 'Kunal Gaba','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1250,230, 'Parit Pathak','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3530,230, 'Riecha Sharma Vohra','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',6580,220, 'Deepak Goel','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',640,220, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',220,220, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',270,210, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',210,210, 'Abbas Haider','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',210,210, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',570,210, 'Varsha Joshi','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',210,210, 'M H Shaikh','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',210,210, 'Daljeet Singh Gill','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',510,210, 'Vikas Malik','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',200,200, 'shruti','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',200,200, 'Ayush Rai','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',200,200, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',620,200, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,190, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1510,190, 'abhishek jain','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,190, 'Kumar','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1270,190, 'Vinni Malik','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2950,190, 'Neha Ahuja','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',5820,180, 'ajay','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',720,180, 'hardik gangwar','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',660,180, 'Manish Mirchandani','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3480,180, 'SameerAneja','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',180,180, 'vishal','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',470,170, 'Aditya','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1070,170, 'Nidhi Uppal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1790,170, 'Bipin Paul Bedi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',650,170, 'Prashant Joshi','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',170,170, 'rahul','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',530,170, 'Samay Mahajan','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',410,170, 'Vishal Sharma','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',170,170, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',160,160, 'chetali khera','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',520,160, 'Naina','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',520,160, 'Rajat Ratewal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',400,160, 'Sandeep Singh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',760,160, 'Abhishek Sethi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',160,160, 'Neetu Singh','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',220,160, 'Taran','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',220,160, 'vinay','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',390,150, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',4110,150, 'hemamutreja','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', 'ishpreet388 @gmail.com',330,150, 'Ishpreet Singh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',150,150, 'Kannav Goyal','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',390,150, 'Manish Bhatia','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',150,150, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',390,150, 'Ajay Mishra','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',150,150, 'Aditya Madheshiya','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1890,150, 'Narasimha Rao','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2850,150, 'Richa','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',510,150, 'Ruhi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',810,150, 'Saikot Ghosh','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',260,140, 'Arpit Kumar','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',140,140, 'Arvind Kumar','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',5540,140, 'Neha Sood','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',560,140, 'Paramjit Singh','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',320,140, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1340,140, 'Gaurav Saxena ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',560,140, 'Tushar Garg','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2300,140, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1630,130, 'ashish','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',130,130, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',370,130, 'Dimple Garg','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1510,130, 'Jatin Rishi','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,130, 'Manali Jain','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1210,130, 'Mohil Chaturvedi ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',250,130, 'Preethi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',430,130, 'Priya','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1570,130, 'priya','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1210,130, 'puneet','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',370,130, 'Vikas Tyagi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',610,130, 'Mohd Shahid','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3010,130, 'Vaibhav Dadu','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',730,130, 'vipin','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1260,120, 'Ajay','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',120,120, 'Amisha','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',180,120, 'Ankur Bhatia','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3480,120, 'Neha Jain','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',660,120, 'Jogesh Bhayana','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',300,120, 'Divas Agarwal','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',120,120, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',120,120, 'Hitesh Gupta ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',180,120, 'Sonia','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',120,120, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',600,120, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',600,120, 'Praveen Poonia','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',240,120, 'Rajeev Jindal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',900,120, 'saurabh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',720,120, 'Shiva Raman','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',300,120, 'shiraj','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',480,120, 'varuni','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,110, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',770,110, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',350,110, 'Aseim','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',290,110, 'shintu','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,110, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',530,110, 'Goonjit Singh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', 'jagadish.mba1986 @gmail.com',590,110, 'jagadish','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',170,110, 'Jagtar Singh ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1190,110, 'Kanchan Yadav','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1970,110, 'Abhishek Vig','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',950,110, 'Malika Gera','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1010,110, 'Mayank Bhadauria','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',230,110, 'Nishu','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',590,110, 'Nitin Suri','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',890,110, 'Payal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,110, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,110, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',230,110, 'Rajiv Gupta','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',530,110, 'Sanjiv Aggarwal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,110, 'Romit Kalra','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2090,110, 'Shobhit Gupta','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1790,110, 'sunil924','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',350,110, 'Niharika Manchanda','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',590,110, 'Vaibhav Jayas','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,110, 'Vikash Bhardwaj','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',100,100, 'Ajai Rana','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',100,100, 'Akansha Vishnoi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',100,100, 'Amit Sharma','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',220,100, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1000,100, 'Anuj Bhardwaj','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',340,100, 'Anuj Gupta','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',940,100, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',280,100, 'Bibhash Das','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',580,100, 'Durga Pedgaonkar','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',460,100, 'Guninder Sandhu','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',340,100, 'Sumit Gupta','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',640,100, 'Jagmeet','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',220,100, 'Krishan Kant Sharma','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',820,100, 'Kshitija','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',100,100, 'Mayank Singh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1140,100, 'Navneet Gupta','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1240,100, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',100,100, 'neha','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2800,100, 'Rahul','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',100,100, 'Kannan','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',220,100, 'Sanchit Mago','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',760,100, 'Sunny','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',880,100, 'Sonesh Bahel','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',400,100, 'Sachin','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',7060,100, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',160,100, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',150,90, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',270,90, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',810,90, 'Attamjot','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',570,90, 'Ritesh Mittal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',330,90, 'rechin','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3390,90, 'Raunak Sachdeva','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',210,90, 'Eugenia Romanova','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',210,90, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,90, 'kuldeep sharma','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',150,90, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',330,90, 'Nitin Bansal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,90, 'Amit','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',450,90, 'Sachin Kumar Tiwari','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',810,90, 'Saurabh','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,90, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,90, 'Shreya Sharma','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',210,90, 'sibashish','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,90, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',330,90, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2490,90, 'suman','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3690,90, 'Suvritti ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,90, 'Swetha Gowda','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',150,90, 'tanuj','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,90, 'Gaurav Tewari','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',5130,90, 'Vivek Verma','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',200,80, 'Abhijit Pundeer','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2060,80, 'Abhishek Sharma','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',320,80, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',260,80, 'Ankit Aggarwal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',320,80, 'Ashish Rawat','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',500,80, 'Deepti Gogia','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1160,80, 'Ashutosh Misra','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',260,80, 'Sandeep Pahuja','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2180,80, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',740,80, 'Hirak Patel','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',200,80, 'Dhiraj Jain','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',680,80, 'Kumar Aditya','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',200,80, 'Mani Kohli','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',6140,80, 'Mohit Middha','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1340,80, 'Nidhi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', 'nishu.kumar9818 @gmail.com',260,80, 'Nishu kumar','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1400,80, 'spider','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',260,80, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',560,80, 'pradeep ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',7940,80, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1880,80, 'Rakeah','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',740,80, 'rekha','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',260,80, 'gaurav singh','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',380,80, 'Shailesh Upadhyay','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',740,80, 'Vibhor Singhal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',800,80, 'sonam','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',140,80, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3740,80, 'srini','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1340,80, 'Varinder Bhasin','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',140,80, 'Varun Thakur','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,70, 'abhay','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1630,70, 'Aditya Vashishtha','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',670,70, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,70, 'Anjula Gupta ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,70, 'Ankita Duhan','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',310,70, 'Arun Naiyar','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',310,70, 'Jayshwani Dutta Ayra ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',430,70, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',130,70, 'Chaitanya Sharma','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,70, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',250,70, 'Gaurav','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',250,70, 'Gunajit Kalita','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,70, 'Gunjan Chhabra','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',370,70, 'shagun gupta','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2770,70, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,70, 'Tarun Kumar Jaiswal','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',790,70, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',250,70, 'Navdeep Sandhar','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',130,70, 'Nipun Goyal','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,70, 'Piyush Kapoor','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,70, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,70, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',310,70, 'Gurdeep','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,70, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,70, 'Deepak Sachdeva','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,70, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,70, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',130,70, 'Vandana','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',130,70, 'subhash','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,70, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',130,70, 'tarun','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,70, 'Vikrant Arora','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1390,70, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,70, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',180,60, 'varun gupta','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'Pankaj Yadav','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'Aakash','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'Abhishek Kapoor','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',240,60, 'Aman Garg','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',120,60, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'Pranjal Biswas','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'Dev Thakur','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',540,60, 'vipin','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',300,60, 'Gaurav Gaba','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'gaurav','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',240,60, 'abhi','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',600,60, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2340,60, 'jpankaj','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',540,60, 'Kapil Kalra','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',120,60, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'Rishi','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',480,60, 'Karan','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',120,60, 'Goura Chaudhary','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'Mahesh Chadha','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1260,60, 'Snigdha Tripathi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'Mayank','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',480,60, 'rishi mehtani','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'Preeti Gupta','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',120,60, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'sandy','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',4080,60, 'Rohit','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3580,60, 'Ruchir Pande','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',420,60, 'Sahil Arora','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',180,60, 'Kulvinder S Saini','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',180,60, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'Saurabh','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'saurabh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',300,60, 'Amar Sharma','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',300,60, 'Shabd EspaÃ±ol','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'Shilpa','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'somya','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, 'Sunaina','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3360,60, 'Sunil Gautam','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',600,60, 'vibha','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,60, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',180,60, 'Vimal','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',590,50, 'Abhishek','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Akhil Gupta','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,50, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,50, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Ankit Babbar','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',650,50, 'Ankit Khanna','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Banktesh Kumar','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',230,50, 'Shekhar Soni','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Chandni','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',4370,50, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',290,50, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',290,50, 'dhanu priya','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1070,50, 'Tiwari Deviprasad Amaranth','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',290,50, 'Hardik Mehta','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Jatinder Singh','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',590,50, 'kanika','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1070,50, 'Mohit','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,50, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Monica','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Neha Misra','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Rhythm Gupta','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Rohan Arora','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,50, 'Sahil Goyal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,50, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Shyam Sharad Bhaiya','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Simran Sandhu','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Sunil Bansal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,50, 'Tejinder','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',110,50, 'prabhat','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',670,50, 'Kunal Singh','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Praveen Tyagi','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',5270,50, 'varun bansal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Varun Jain ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',50,50, 'Venkatesh Mishra','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',590,50, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',40,40, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',340,40, 'Ajay Kohli','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',40,40, 'Aman Thaper ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',40,40, 'Chietra ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',40,40, 'Divya Dhingra','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',40,40, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', 'hiabhi4011 @gmail.com',40,40, 'abhishek mittal','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',160,40, 'jatinder','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',100,40, 'Bikram Singh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1060,40, 'Ruhi Arora','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',40,40, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',100,40, 'mukul','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',7480,40, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',40,40, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',160,40, 'Ratandeep Kaur','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',160,40, 'richa','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',400,40, 'Rashmie','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',400,40, 'Sakshi','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',280,40, 'Shahanshah Khan','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',520,40, 'Manu Singla','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',40,40, 'Hitesh Gupta','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',40,40, 'Hrishabh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',580,40, 'Sukanya Agarwal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',100,40, 'sundip','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',40,40, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',100,40, 'vipin sharma','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',220,40, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',160,40, 'Yasha','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Rahul Sharma','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',150,30, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,30, 'Akhil Minocha','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Ankit','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',210,30, 'anshul','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',210,30, 'Anshul','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Apurva','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,30, 'Ashish K Sharma','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Ashok','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'asif ali','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2910,30, 'astha bhalla','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',2310,30, 'Bharat Narang','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',5790,30, 'Rachit','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Chandni','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Dipen Verma','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Vikas Yadav','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,30, 'Gaurav Chadha','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',210,30, 'Gaurav Aggarwal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',390,30, 'Govind','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',150,30, 'Happy Jhamnani','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Vishal Kumar','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,30, 'kanika','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Kapil Bhagia','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Kuldeep','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Misbah Ashraf','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Mishel Arora','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',270,30, 'Mitanshu Verma','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Raj Sachdeva','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',630,30, 'prashant sharma','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'vivek','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,30, 'Ritika','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',150,30, 'Sameer','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',270,30, 'saurav gupta','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Ankur Sharma','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',90,30, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Jazz','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',630,30, 'Sorabh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Tanveer Singh Sethi ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Udham Singh','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, 'Varun Vij','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',150,30, 'Vikas Jetly','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',150,30, 'Vikas','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,30, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Rocking Rex','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'aa','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Aakash','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',80,20, 'ajit','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',260,20, 'Akanksha Chouhan','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',80,20, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',80,20, 'Amrit Dutt Gautam','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Archan Ghosh','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Abhiroop Chaudhuri','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'bharat','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',80,20, 'Deepak','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',80,20, 'Damanpreet singh','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Deepika','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Devendra ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Dev Rawat','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Dhruv Sharma','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',80,20, 'Rajneesh Gaur','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',260,20, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Haribandhu Patra','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',80,20, 'Jatin','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Jitender Gandhi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Kamlesh','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'kuldeep','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Krishna yadav','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',260,20, 'meghna','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',80,20, '<EMAIL>','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Nayyar Alam','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3260,20, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',80,20, 'Nikhil Swaminathan','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',920,20, 'Nirmit Kapoor','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Nishant Singh','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',80,20, 'piyush_patodia','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Poojauberoi','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',200,20, 'Puneet','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'rahul nanwani','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Raghav Verma','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',80,20, 'Rahul Kathuria','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Rawlins Jacob','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Richik Sinha Roy','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Rohit Agarwal','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',200,20, 'Sakshi Khandelwal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',80,20, 'Shailaja Taparia','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3140,20, 'dewesh.singh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'Sunny Verma','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'sunny sahni ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, 'vipin','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',320,20, 'Vipul Mahiman','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',20,20, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Richa Goel','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Himanshu','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Aasma','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Abad Khan','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'abh1984','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'abhay','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Gaurav Agrawal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Akash','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,10, 'Akash Kumar','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Anjali Garg','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',130,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Anudita Parihar','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'anupsinghania','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Sai Chakradhar Araveti','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'A Ravi','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Arun Naiyar','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Ashish','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Ashwin Suresh','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'abhishek','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'dsb','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Tina Kaur Kapoor','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',310,10, 'bmazumdar','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Kamal Sood','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'devesh','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'vivek verma','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Sahil Kalra','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,10, 'Deen Dayal Gaur','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Gaurav Tewari','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',130,10, 'gureesh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Gurpreet Flora','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',250,10, 'Gurpreet','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'hansraj singh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Harmeet Singh Dharni','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Sid Bhambhani','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Himanshu Verma','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Abhishek Mittal','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'vsh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,10, 'Sachin Choudhary','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Kapil','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Kanchana Vishwanath','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Kaushal Elsewhere','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Amandeep','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'bcc42prt','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Manohar','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',190,10, 'Meadhavi','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1030,10, 'Awadhesh Mishra','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'mjuneja','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Himanshu Gupta','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'jatinarora','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Naveen Prasad','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Neeraj','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',310,10, 'nirupama singh','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Tosh','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Pawanjeet Singh Arora','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Prankur Rastogi','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Praphul','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Pulkit','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Rakesh ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Ratnesh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Ravi ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Rahul Chakraborti','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Abhinav Arora ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,10, 'rahul garg','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Safoora Zrgr','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,10, 'Kanika Sakhuja','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'sameer','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'sbhasin','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'SANJHI','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Shaurya Wadhwa','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Sheetal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Siddharth Khanna','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Sudhir Mishra','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Sumeet','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Suryansh Khanna ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Umang Katyal','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'vaibhav','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10008);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10007);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',70,10, 'Vijay','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10000);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',510,10, 'Vishnu Narain Saxena','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, ' ','Y',10006);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',10,10, 'Kirti Dhingra','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'aamir hussain','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'abc','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Abhishek','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ajay Daksh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Akhil Bajaj','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Akriti Agarwal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Akshay Gurnani','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Akshit','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Aman Bhutani','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',3720,0, 'Amit Singh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ankit Bhatnagar','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1680,0, 'Ankur Munjal','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ankush','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Anuj Upadhyay','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'arpit.sahai','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Arun Dutt Mathur','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ashish Singla','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ashutosh Kashyap','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Astha','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Atul Mavinkurve','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Atulya Mahajan','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Chandra Shekhar Bhatt','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'bj','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Biswamitra Biswas','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'hemant','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ritesh Chauhan','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Chirag Bansal','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'ish chitkara','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'coolkunal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Rohit Jhawar','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Deepak Sharma','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Yishu Malik','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'devashishsharma89','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Dheeraj Kapoor','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Dipayan','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'vasu','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'divya','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Deepak Khemani','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Edwin','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Gagandeep Gulati','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Gaurav Hooda','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Gaurav Rawat','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Gaurav Dham','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Rohit Gautam','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ayush Soni','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Damayanti Mukherji','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Gourav Kumar','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',240,0, 'Manpreet Singh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Shubhendu Gupta','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Hitesh Gupta','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Abhay Singh','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Konark Revri','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Injo Navish','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'jatin kindra','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Jatin Kohli','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'kanwaljit','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'kapoor.kanchi87','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Karan','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Kulwinder','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Mandeep Gupta','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Manish Purohit','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Manmohan Gupta','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Mayank','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Mohit Bahl','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'mohit vij','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ankit Monga','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'monosarc','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ronak Gandhi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'joshi_mukesh1212','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Naveen Bhagi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Naval Dulloo','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Neeraj Bhagat','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'NiteshGangrade','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Niharika Rawat Harpalani','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Nirav','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'nisha','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'nitinsuri','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'nitin','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'nitish','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'nitish','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Nitin Saluja','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Omesh Kumar Gupta','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Raunaq Sandhu','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Parag Nahar','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'pradngpl19','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Pujya Trivedi','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Raghav Kansal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Rahul Tiwa','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ramakant Yadav','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ramesh Govindan','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ranjeet Kumar','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'richik sinha','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Ritesh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Rohit Kumar','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Rohit Ahuja','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Sachin Arora','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Sachin Joshi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Sameer Bansal','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Sandeep','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Sandeep Gupta','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Sanjeev Purohit','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Saurabh','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',60,0, 'saurabh','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Saurabha Sharma','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'saurabhsr87','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Prateek Luthra','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Sethu','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'shaily dheer','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Shant ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'shilpika','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Shubham Jain','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'siddhantlucky2002','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'vaibhav','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Vaibhav Bansal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Tarun Kumar','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Sriram Dharmarajan','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Suangna Singh','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Subinder Khurana','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10004);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Sonny Kal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Kamal','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Swati','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Siddharth','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Vineet','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'tanvish','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Tarun Gupta','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Shubham Agajcass','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Tim Square','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Akshaya','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Vaibhav Chauhan','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Varun Kapoor','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Vibhuti Khanna','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Himanshu Verma','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Virender Sharma','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'vishal gupta','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Vinod','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Arun K Sharma','Y',10002);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, 'Satyam Gupta','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,0, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',530,-10, 'Ravi','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',590,-10, 'Shobhit','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',40,-20, 'Shantanu Mathur','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',580,-20, 'Antariksh Yadav','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',450,-30, 'Poornima Gupta','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',30,-30, 'Vinod Rathore','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',450,-30, ' ','Y',10005);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',780,-60, 'mohitsharma2','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,-60, 'Sourav Gangawat','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',590,-90, 'Sailesh Raghavan','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',610,-110, 'Shubham Agarwal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',1020,-120, 'Manish Kumar Nirwal','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',0,-120, ' ','Y',10001);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',370,-170, 'Charu','Y',10003);
INSERT INTO MIGRATION_LOOKUP_DATA(LOOKUP_TYPE,LOOKUP_TEXT,CUMULATIVE_POINTS,ACQUIRED_POINTS,FIRST_NAME,IS_ACTIVE, REGISTRATION_UNIT_ID) VALUES('EMAIL', '<EMAIL>',160,-340, ' ','Y',10001);
