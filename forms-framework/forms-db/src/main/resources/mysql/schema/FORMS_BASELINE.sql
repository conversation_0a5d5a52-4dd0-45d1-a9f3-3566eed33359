CREATE TABLE KETTLE_DEV.AUDIT_FORM (
    ID INT PRIMARY KEY AUTO_INCREMENT,
    NAME VARCHAR(155) NOT NULL,
    DESCRIPTION VARCHAR(250) NULL,
    CREATION_TIME TIMESTAMP NULL,
    LAST_UPDATED TIMESTAMP NULL,
    CREATED_BY INT NOT NULL,
    LAST_UPDATED_BY INT NOT NULL,
    STATUS VARCHAR(30) NOT NULL,
    NOTIFICATION_EMAILS VARCHAR(250) NULL,
    NOTIFICATION_SLACK_CHANNELS VARCHAR(250) NULL
);

CREATE TABLE KETTLE_DEV.AUDIT_FORM_VALUES (
    ID INT PRIMARY KEY AUTO_INCREMENT,
    AUDIT_FORM_ID INT NOT NULL,
    ENTITY_LABEL VARCHAR(250) NULL,
    ENTITY_DESCRIPTION VARCHAR(250) NULL,
    ENTITY_TYPE VARCHAR(50) NOT NULL,
    ENTITY_VALUES VARCHAR(1000) NULL,
    ENTITY_METADATA VARCHAR(250) NULL,
    LINKED_DATA_TYPE VARCHAR(250) NULL,
    LINKED_ENTITY_ID INT NULL,
    ANSWER_BY VARCHAR(100) NULL,
    FOREIGN KEY (AUDIT_FORM_ID) REFERENCES KETTLE_DEV.AUDIT_FORM(ID)
);
ALTER TABLE KETTLE_DEV.AUDIT_FORM_VALUES ADD COLUMN APPEARANCE_ORDER INT NULL;
ALTER TABLE KETTLE_DEV.AUDIT_FORM_VALUES MODIFY COLUMN ENTITY_LABEL VARCHAR(250) NULL;
ALTER TABLE KETTLE_DEV.AUDIT_FORM_VALUES MODIFY COLUMN ANSWER_BY VARCHAR(100) NULL;
ALTER TABLE KETTLE_DEV.AUDIT_FORM_VALUES ADD COLUMN LINKED_API VARCHAR(250) NULL;
ALTER TABLE KETTLE_DEV.AUDIT_FORM_VALUES ADD COLUMN SCORE_COUNTED VARCHAR(1) NOT NULL DEFAULT 'N';
ALTER TABLE KETTLE_DEV.AUDIT_FORM_VALUES ADD COLUMN STATUS VARCHAR(20) NOT NULL DEFAULT 'ACTIVE';
ALTER TABLE KETTLE_DEV.AUDIT_FORM_VALUES ADD COLUMN MAX_SCORE INT NULL;
ALTER TABLE KETTLE_DEV.AUDIT_FORM_VALUES ADD COLUMN ADDITIONAL_COMMENT VARCHAR(1) NOT NULL DEFAULT "N";
ALTER TABLE KETTLE_DEV.AUDIT_FORM_VALUES ADD COLUMN ATTACH_DOC VARCHAR(1) NOT NULL DEFAULT "N";

DROP TABLE IF EXISTS KETTLE_DEV.AUDIT_DETAIL;
CREATE TABLE KETTLE_DEV.AUDIT_DETAIL(
    ID INT PRIMARY KEY AUTO_INCREMENT,
    AUDITOR_ID INT NOT NULL,
    AUDITOR_NAME VARCHAR(100) NOT NULL,
    AUDIT_FORM_ID INT NOT NULL,
    AUDIT_UNIT_ID INT NOT NULL,
    AUDIT_UNIT_NAME VARCHAR(200) NOT NULL,
    AUDIT_DATE DATE NULL,
    AUDIT_TIME TIMESTAMP NULL,
    AUDIT_SUBMIT_DATE TIMESTAMP NULL,
    CAFE_MANAGER_ID INT NOT NULL,
    CAFE_MANAGER VARCHAR(100) NOT NULL,
    MANAGER_ON_DUTY_ID INT NOT NULL,
    MANAGER_ON_DUTY VARCHAR(100) NOT NULL,
    AREA_MANAGER_ID INT NOT NULL,
    AREA_MANAGER VARCHAR(100) NOT NULL,
    AUDIT_TYPE VARCHAR(50) NOT NULL,
    TOTAL_SCORE INT NOT NULL,
    ACQUIRED_SCORE INT NOT NULL,
    STATUS VARCHAR(10) NOT NULL DEFAULT 'ACTIVE',
    FOREIGN KEY (AUDIT_FORM_ID) REFERENCES AUDIT_FORM (ID)
);

DROP TABLE IF EXISTS KETTLE_DEV.AUDIT_DETAIL_VALUES;
CREATE TABLE KETTLE_DEV.AUDIT_DETAIL_VALUES(
	ID INT PRIMARY kEY AUTO_INCREMENT,
    AUDIT_DETAIL_ID INT NOT NULL,
    FORM_VALUE_ID INT NOT NULL,
    EMPLOYEE_ID INT NULL,
    EMPLOYEE_NAME VARCHAR(100) NULL,
    EMPLOYEE_DESIGNATION VARCHAR(100) NULL,
    UNIT_ID INT NULL,
    UNIT_NAME VARCHAR(100) NULL,
    PRODUCT_ID INT NULL,
    PRODUCT_NAME VARCHAR(100) NULL,
    OPTION_1 VARCHAR(250) NULL,
    OPTION_1_MARKS INT NULL,
    OPTION_2 VARCHAR(250) NULL,
    OPTION_2_MARKS INT NULL,
    OPTION_3 VARCHAR(250) NULL,
    OPTION_3_MARKS INT NULL,
    OPTION_4 VARCHAR(250) NULL,
    OPTION_4_MARKS INT NULL,
    OPTION_5 VARCHAR(250) NULL,
    OPTION_5_MARKS INT NULL,
    OPTION_6 VARCHAR(250) NULL,
    OPTION_6_MARKS INT NULL,
    OPTION_7 VARCHAR(250) NULL,
    OPTION_7_MARKS INT NULL,
    TEXTAREA VARCHAR(1000) NULL,
    YES_NO VARCHAR(1) NULL,
    DATE_VALUE DATE NULL,
    TIME_VALUE TIMESTAMP NULL,
    NUMBER_VALUE INT NULL,
    TEXT_VALUE VARCHAR(500) NULL,
    ANSWER_COMMENT VARCHAR(500) NULL,
    ATTACHED_DOC_ID INT NULL,
    MAX_SCORE INT NULL,
    ACQUIRED_SCORE INT NULL,
    FOREIGN KEY (AUDIT_DETAIL_ID) REFERENCES AUDIT_DETAIL (ID),
    FOREIGN KEY (FORM_VALUE_ID) REFERENCES AUDIT_FORM_VALUES (ID)
);

DROP TABLE IF EXISTS KETTLE_DEV.DOCUMENT_DETAIL_DATA;
CREATE TABLE KETTLE_DEV.DOCUMENT_DETAIL_DATA (
  DOCUMENT_ID INT NOT NULL AUTO_INCREMENT ,
  FILE_TYPE VARCHAR(45) NOT NULL,
  DOCUMENT_LINK VARCHAR(200) NOT NULL,
  UPDATE_TIME DATETIME NOT NULL,
  UPDATED_BY INT NULL,
  DOCUMENT_MIME_TYPE VARCHAR(45) NOT NULL,
  DOCUMENT_UPLOAD_TYPE VARCHAR(45) NOT NULL,
  DOCUMENT_UPLOAD_TYPE_ID INT NULL,
  DOCUMENT_S3_KEY VARCHAR(180) NULL,
  DOCUMENT_S3_BUCKET VARCHAR(180) NULL,
  PRIMARY KEY (DOCUMENT_ID),
  UNIQUE INDEX DOCUMENT_ID_UNIQUE (DOCUMENT_ID ASC)
);


ALTER TABLE KETTLE_DEV.AUDIT_FORM_VALUES ADD COLUMN IS_MANDATORY VARCHAR(1) NOT NULL DEFAULT "N";

ALTER TABLE KETTLE_DEV.AUDIT_FORM_VALUES MODIFY COLUMN MAX_SCORE DECIMAL(10,2) NULL;

ALTER TABLE KETTLE_DEV.AUDIT_DETAIL MODIFY COLUMN TOTAL_SCORE DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_DEV.AUDIT_DETAIL MODIFY COLUMN ACQUIRED_SCORE DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_DEV.AUDIT_DETAIL_VALUES MODIFY COLUMN MAX_SCORE DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_DEV.AUDIT_DETAIL_VALUES MODIFY COLUMN ACQUIRED_SCORE DECIMAL(10,2) NULL;

ALTER TABLE KETTLE_DEV.AUDIT_DETAIL_VALUES MODIFY COLUMN OPTION_1_MARKS DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_DEV.AUDIT_DETAIL_VALUES MODIFY COLUMN OPTION_2_MARKS DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_DEV.AUDIT_DETAIL_VALUES MODIFY COLUMN OPTION_3_MARKS DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_DEV.AUDIT_DETAIL_VALUES MODIFY COLUMN OPTION_4_MARKS DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_DEV.AUDIT_DETAIL_VALUES MODIFY COLUMN OPTION_5_MARKS DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_DEV.AUDIT_DETAIL_VALUES MODIFY COLUMN OPTION_6_MARKS DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_DEV.AUDIT_DETAIL_VALUES MODIFY COLUMN OPTION_7_MARKS DECIMAL(10,2) NULL;

ALTER TABLE KETTLE_DEV.AUDIT_DETAIL ADD COLUMN AUDIT_REPORT_ID INTEGER NULL;
ALTER TABLE KETTLE_DEV.DOCUMENT_DETAIL_DATA ADD COLUMN DOCUMENT_URL VARCHAR(500);

ALTER TABLE KETTLE_DEV.AUDIT_FORM_VALUES MODIFY COLUMN ENTITY_DESCRIPTION VARCHAR(500) NULL;

/*Dev PQSC audit form*/
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM` (`ID`, `NAME`, `DESCRIPTION`, `CREATION_TIME`, `LAST_UPDATED`, `CREATED_BY`, `LAST_UPDATED_BY`, `STATUS`, `NOTIFICATION_EMAILS`, `NOTIFICATION_SLACK_CHANNELS`) VALUES ('1', 'PQSC Audit', 'This form is used to do audits for PQSC', '2018-01-17 12:44:10', '2018-01-17 12:44:10', '120103', '120103', 'ACTIVE', '<EMAIL>', 'pqsc');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('1', '1', 'Product Excellence', 'detail data for product excellence long para formatted', 'QUESTION_GROUP', '1', 'N', 'ACTIVE', 'N', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('2', '1', 'Hot station MILK Details', 'Check One Hot Beverage - Desi Chai Category. If Perfect Recipe, SOP, Packaging & Condiments then score 5', 'PRODUCT_EMPLOYEE_RADIO', '[{\"id\":1,\"value\":\"Perfect product, score 5\",\"marks\":5},{\"id\":2,\"value\":\"Product not perfect\",\"marks\":0}]', 'JSON', '1', 'AUDITOR', '2', 'Y', 'ACTIVE', '5.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('3', '1', 'Hot Station Non-Milk Details', 'Check One Hot Beverage - Classic/Speciality/Unchai Hot Category. If Perfect Recipe, SOP, Packaging & Condiments then score 5', 'PRODUCT_EMPLOYEE_RADIO', '[{\"id\":1,\"value\":\"Perfect product, score 5\",\"marks\":5},{\"id\":2,\"value\":\"Product not perfect\",\"marks\":0}]', 'JSON', '1', 'AUDITOR', '3', 'Y', 'ACTIVE', '5.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('4', '1', 'Cold station Details', 'Check One Cold Beverage - If Perfect Recipe, SOP, Packaging & Condiments then score 5. If not then score NO', 'PRODUCT_EMPLOYEE_RADIO', '[{\"id\":1,\"value\":\"Perfect product, score 5\",\"marks\":5},{\"id\":2,\"value\":\"Product not perfect\",\"marks\":0}]', 'JSON', '1', 'AUDITOR', '4', 'Y', 'ACTIVE', '5.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('5', '1', 'Food Station Details', 'Check One item from Food Station - If Perfect Recipe, SOP, Packaging & Condiments then score 5. If not then score NO', 'PRODUCT_EMPLOYEE_RADIO', '[{\"id\":1,\"value\":\"Perfect product, score 5\",\"marks\":5},{\"id\":2,\"value\":\"Product not perfect\",\"marks\":0}]', 'JSON', '1', 'AUDITOR', '5', 'Y', 'ACTIVE', '5.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('6', '1', 'Manager Product Details', 'Check One item made by Manager - It cane be from Hot/Cold/Food Station - If Perfect Recipe, SOP, Packaging & Condiments then score 5.', 'PRODUCT_EMPLOYEE_RADIO', '[{\"id\":1,\"value\":\"Perfect product, score 5\",\"marks\":5},{\"id\":2,\"value\":\"Product not perfect\",\"marks\":0}]', 'JSON', '1', 'AUDITOR', '6', 'Y', 'ACTIVE', '5.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('7', '1', 'PRODUCT KNOWLEDGE', 'Ask questions based on Product, questions can be based on Recipe, SOP, Packaging, Shelf Life, Storage or Description', 'QUESTION_GROUP', '7', 'N', 'ACTIVE', 'N', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('8', '1', 'Question 1', 'Ask question and give marks if answered correctly and select employee who answered question', 'TEXTAREA_EMPLOYEE_RADIO', '[{\"id\":1,\"value\":\"Answerred correctly, score 1\",\"marks\":1},{\"id\":2,\"value\":\"Answerred incorrectly, score 0\",\"marks\":0}]', 'JSON', '7', 'AUDITOR', '8', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('9', '1', 'Question 2', 'Ask question and give marks if answered correctly and select employee who answered question', 'TEXTAREA_EMPLOYEE_RADIO', '[{\"id\":1,\"value\":\"Answerred correctly, score 1\",\"marks\":1},{\"id\":2,\"value\":\"Answerred incorrectly, score 0\",\"marks\":0}]', 'JSON', '7', 'AUDITOR', '9', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('10', '1', 'Question 3', 'Ask question and give marks if answered correctly and select employee who answered question', 'TEXTAREA_EMPLOYEE_RADIO', '[{\"id\":1,\"value\":\"Answerred correctly, score 1\",\"marks\":1},{\"id\":2,\"value\":\"Answerred incorrectly, score 0\",\"marks\":0}]', 'JSON', '7', 'AUDITOR', '10', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('11', '1', 'Question 4', 'Ask question and give marks if answered correctly and select employee who answered question', 'TEXTAREA_EMPLOYEE_RADIO', '[{\"id\":1,\"value\":\"Answerred correctly, score 1\",\"marks\":1},{\"id\":2,\"value\":\"Answerred incorrectly, score 0\",\"marks\":0}]', 'JSON', '7', 'AUDITOR', '11', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('12', '1', 'Question 5', 'Ask question and give marks if answered correctly and select employee who answered question', 'TEXTAREA_EMPLOYEE_RADIO', '[{\"id\":1,\"value\":\"Answerred correctly, score 1\",\"marks\":1},{\"id\":2,\"value\":\"Answerred incorrectly, score 0\",\"marks\":0}]', 'JSON', '7', 'AUDITOR', '12', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('13', '1', 'Question 6', 'Ask question and give marks if answered correctly and select employee who answered question', 'TEXTAREA_EMPLOYEE_RADIO', '[{\"id\":1,\"value\":\"Answerred correctly, score 1\",\"marks\":1},{\"id\":2,\"value\":\"Answerred incorrectly, score 0\",\"marks\":0}]', 'JSON', '7', 'AUDITOR', '13', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('14', '1', 'Product Quality', 'Check FIFO, Approved Products, Products Availability, Condiments & Packaging, Equipment Cleaning & Maintenance', 'QUESTION_GROUP', '14', 'N', 'ACTIVE', 'N', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('15', '1', 'FIFO followed', 'Are food products being used in FIFO (First In First Out). Check Random products. One product not under FIFO, deduct 1 mark. Two or more products not under FIFO, deduct 2 marks', 'RADIO', '[{\"id\":1,\"value\":\"FIFO followed, score 2\",\"marks\":2},{\"id\":2,\"value\":\"One product not under FIFO, score 1\",\"marks\":1},{\"id\":3,\"value\":\"Two or more products not under FIFO, score 0\",\"marks\":0}]', 'JSON', '14', 'AUDITOR', '15', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('16', '1', 'Bain Marie Storage', 'Products stored in Bain Marie containers - not more than 5 packets in one container any 1 product type violating this rule, deduct 2 marks', 'RADIO', '[{\"id\":1,\"value\":\"5 or less products in all containers, score 2\",\"marks\":2},{\"id\":2,\"value\":\"More than 5 products found in containers, score 0\",\"marks\":0}]', 'JSON', '14', 'AUDITOR', '16', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('17', '1', 'Equipment Cleanliness & Maintenance', 'If cleanliness issue then deduct 1 mark, 1 equipment breakdown and no mail found then deduct 1 mark, 2 equipment breakdown and no mail found then deduct 2 marks, equipment breakdown but follow up mail available then no marks deducted (FOLLOW UP MAIL SHOULD BE LESS THAN A WEEK OLD, OR ELSE IT WONT BE VALID)', 'RADIO', '[{\"id\":1,\"value\":\"Cleanliness Issue, score 1\",\"marks\":1},{\"id\":2,\"value\":\"1 equipment breakdown, no mail found, score 1\",\"marks\":1},{\"id\":3,\"value\":\"2 equipment breakdown, no mail found, score 0\",\"marks\":0},{\"id\":4,\"value\":\"Equipment breakdown, follow up mail available, score 2\",\"marks\":2},{\"id\":5,\"value\":\"No issues found, score 2\",\"marks\":2}]', 'JSON', '14', 'AUDITOR', '17', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('18', '1', 'Condiments & Packaging', 'Check if product is served in the right packaging and with the right condiments, include COD orders too. Check 4 orders and score 1 mark for each order correctly packed with correct condiments. For e.g. 2 orders correct will score 2 out of 4', 'RADIO', '[{\"id\":1,\"value\":\"Condiments and Packaging correct for 0 orders, score 0\",\"marks\":0},{\"id\":2,\"value\":\"Condiments and Packaging correct for 1 order, score 1\",\"marks\":1},{\"id\":3,\"value\":\"Condiments and Packaging correct for 2 orders, score 2\",\"marks\":2},{\"id\":4,\"value\":\"Condiments and Packaging correct for 3 orders, score 3\",\"marks\":3},{\"id\":5,\"value\":\"Condiments and Packaging correct for 4 orders, score 4\",\"marks\":4}]', 'JSON', '14', 'AUDITOR', '18', 'Y', 'ACTIVE', '4.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('19', '1', 'Approved Products', 'Cut marks if any product found which is not approved, Check for: milk, sugar, patti, breads, poha, wraps, bhujia & Add ons, Exception is: vegetables purchased locally (area mgr approval)', 'RADIO', '[{\"id\":1,\"value\":\"All approved products, score 2\",\"marks\":2},{\"id\":2,\"value\":\"Unapproved product found, score 0\",\"marks\":0}]', 'JSON', '14', 'AUDITOR', '19', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('20', '1', 'Availability of products', 'This  will be judged product wise, not SKU wise, for e.g.  If Bun is not available, then  all products made with Bun will be unavailable, If any Desi Chai add ons or Kulhad chai are not available, then it will also result in deduction of 10 more points', 'RADIO', '[{\"id\":1,\"value\":\"If all products are available, score 4\",\"marks\":4},{\"id\":2,\"value\":\"If 1 product is unavailable, score 3\",\"marks\":3},{\"id\":3,\"value\":\"If 2 products are unavailable, score 2\",\"marks\":2},{\"id\":4,\"value\":\"If 3 or more products are unavailable, score 0\",\"marks\":0}]', 'JSON', '14', 'AUDITOR', '20', 'Y', 'ACTIVE', '4.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('21', '1', 'Service Excellence', 'Track service parameters like welcoming customers etc.', 'QUESTION_GROUP', '21', 'N', 'ACTIVE', 'N', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('22', '1', 'Welcome to Chaayos - Every customer should be greeted.', 'Minimum 1 person should greet, Maximum 2 people should greet', 'RADIO', '[{\"id\":1,\"value\":\"If compliance is between 80% or more, score 2\",\"marks\":2},{\"id\":2,\"value\":\"If compliance is between 60% to 79.99%, score 1\",\"marks\":1},{\"id\":3,\"value\":\"If compliance is below 60%, score 0\",\"marks\":0}]', 'JSON', '21', 'AUDITOR', '22', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('23', '1', 'Customer name call out - every customer name should be called out when the order is ready', 'Minimum 1 person should greet, Maximum 2 people should greet', 'RADIO', '[{\"id\":1,\"value\":\"If compliance is between 80% or more, score 2\",\"marks\":2},{\"id\":2,\"value\":\"If compliance is between 60% to 79.99%, score 1\",\"marks\":1},{\"id\":3,\"value\":\"If compliance is below 60%, score 0\",\"marks\":0}]', 'JSON', '21', 'AUDITOR', '23', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('24', '1', 'Thanked the customer - Every customer should be thanked when they are leaving from the café', 'Minimum 1 person should greet, Maximum 2 people should greet', 'RADIO', '[{\"id\":1,\"value\":\"If compliance is between 80% or more, score 2\",\"marks\":2},{\"id\":2,\"value\":\"If compliance is between 60% to 79.99%, score 1\",\"marks\":1},{\"id\":3,\"value\":\"If compliance is below 60%, score 0\",\"marks\":0}]', 'JSON', '21', 'AUDITOR', '24', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('25', '1', 'Genuine Smile - Ordertaker should always smile when speaking to the custome', 'Minimum 1 person should greet, Maximum 2 people should greet', 'RADIO', '[{\"id\":1,\"value\":\"If compliance is between 80% or more, score 2\",\"marks\":2},{\"id\":2,\"value\":\"If compliance is between 60% to 79.99%, score 1\",\"marks\":1},{\"id\":3,\"value\":\"If compliance is below 60%, score 0\",\"marks\":0}]', 'JSON', '21', 'AUDITOR', '25', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('26', '1', 'Customer Capture Rate - Ordertaker must request every customer to fill in their details on the screen', 'Minimum 1 person should greet, Maximum 2 people should greet', 'RADIO', '[{\"id\":1,\"value\":\"If compliance is between 80% or more, score 1\",\"marks\":1},{\"id\":2,\"value\":\"If compliance is between 60% to 79.99%, score 0.5\",\"marks\":\"0.5\"},{\"id\":3,\"value\":\"If compliance is below 60%, score 0\",\"marks\":0}]', 'JSON', '21', 'AUDITOR', '26', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('27', '1', 'Upselling - Ordertaker must attempt upselling to every customer', 'Minimum 1 person should greet, Maximum 2 people should greet', 'RADIO', '[{\"id\":1,\"value\":\"If compliance is between 80% or more, score 3\",\"marks\":3},{\"id\":2,\"value\":\"If compliance is between 60% to 79.99%, score 1.5\",\"marks\":1.5},{\"id\":3,\"value\":\"If compliance is below 60%, score 0\",\"marks\":0}]', 'JSON', '21', 'AUDITOR', '27', 'Y', 'ACTIVE', '3.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('28', '1', 'Service Time - KOT reaching, individual items ready, customer callout - Min 5 orders Max 10 orders', 'Minimum 1 person should greet, Maximum 2 people should greet', 'RADIO', '[{\"id\":1,\"value\":\"If the average service time was less than 4 mins, score 12\",\"marks\":12},{\"id\":2,\"value\":\"If the average service time was between 4 and 6 mins, score 8\",\"marks\":8},{\"id\":3,\"value\":\"If the average service time was between 6 and 8 mins, score 4\",\"marks\":4},{\"id\":4,\"value\":\"If the average service time was more than 8 mins, score 0\",\"marks\":0}]', 'JSON', '21', 'AUDITOR', '28', 'Y', 'ACTIVE', '12.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('29', '1', 'Service Quality', 'Check Grooming, Uniforms, AC, Music, WiFi, Lights, EDC machine', 'QUESTION_GROUP', '29', 'N', 'ACTIVE', 'N', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('30', '1', 'Uniform Max marks 2', 'Cap, T-Shirt, Shoes ', 'RADIO', '[{\"id\":1,\"value\":\"No uniform violations found, score 2\",\"marks\":2},{\"id\":2,\"value\":\"If 1 uniform violation found, score 1\",\"marks\":1},{\"id\":3,\"value\":\"If 2 or more uniform violations found, score 0\",\"marks\":0}]', 'JSON', '29', 'AUDITOR', '30', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('31', '1', 'Grooming', 'Hair, Facial hair, Side burns, Jewellery, Hygiene ', 'RADIO', '[{\"id\":1,\"value\":\"No grooming violations found, score 2\",\"marks\":2},{\"id\":2,\"value\":\"If 1 grooming violation found, score 1\",\"marks\":1},{\"id\":3,\"value\":\"If 2 or more grooming violations found, score 0\",\"marks\":0}]', 'JSON', '29', 'AUDITOR', '31', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('32', '1', 'AC Temperature', 'AC temperature should be 24 degrees, plus or minus 2 degrees, If AC temperature maintained then score 1, if not then score 0', 'RADIO', '[{\"id\":1,\"value\":\"AC at right temperature, score 1\",\"marks\":1},{\"id\":2,\"value\":\"AC not at right temperature, score 0\",\"marks\":0}]', 'JSON', '29', 'AUDITOR', '32', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('33', '1', 'Music: Radio wala working', 'Music should be playing through the Radiowala app, If YES then score 1, If NO then score 0', 'RADIO', '[{\"id\":1,\"value\":\"Radiowala working, score 1\",\"marks\":1},{\"id\":2,\"value\":\"Radiowala not working, score 0\",\"marks\":0},{\"id\":3,\"value\":\"Radiowala not working, follow up mail found, score 1\",\"marks\":1}]', 'JSON', '29', 'AUDITOR', '33', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('34', '1', 'Lights working properly', '(FOLLOW UP MAIL SHOULD BE ATLEAST ONE WEEK OLD, OR ELSE IT WONT BE VALID)', 'RADIO', '[{\"id\":1,\"value\":\"All lights working, score 1\",\"marks\":1},{\"id\":2,\"value\":\"Lights not working but follow up going on, score 1\",\"marks\":1},{\"id\":3,\"value\":\"Lights not working, score 0\",\"marks\":0}]', 'JSON', '29', 'AUDITOR', '34', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('35', '1', 'Cleaning', 'Check cleaning & maintenance of Customer Area, Outside Area, Kitchen Area and Washroom', 'QUESTION_GROUP', '35', 'N', 'ACTIVE', 'N', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('36', '1', 'Washroom', 'Cleaning of Floor, WC, taps, mirror. Smell in washroom.', 'RADIO', '[{\"id\":1,\"value\":\"Clean, Not Smelling, score 2\",\"marks\":2},{\"id\":2,\"value\":\"One item not clean/smelling, score 1\",\"marks\":1},{\"id\":3,\"value\":\"Two or more items not clean/smelling, score 0\",\"marks\":0}]', 'JSON', '35', 'AUDITOR', '36', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('37', '1', 'Toilet supplies available', 'Running water, tissues, soap. If all supplies are available the score 2, If not then score 0', 'RADIO', '[{\"id\":1,\"value\":\"Supplies available, score 2\",\"marks\":2},{\"id\":2,\"value\":\"Supplies not available, score 0\",\"marks\":0}]', 'JSON', '35', 'AUDITOR', '37', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('38', '1', 'Customer Area', 'Seating area, floor, glass, frames, top of AC, merchandise, shelves should be clean and dust free. One mark deducted for each violation.', 'RADIO', '[{\"id\":1,\"value\":\"Customer Area Clean, score 4\",\"marks\":4},{\"id\":2,\"value\":\"1 cleaning violation found, score 3\",\"marks\":3},{\"id\":3,\"value\":\"2 cleaning violations found, score 2\",\"marks\":2},{\"id\":4,\"value\":\"3 cleaning violations found, score 1\",\"marks\":1},{\"id\":5,\"value\":\"4 cleaning violations found, score 0\",\"marks\":0}]', 'JSON', '35', 'AUDITOR', '38', 'Y', 'ACTIVE', '4.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('39', '1', 'Damaged furniture', '(seating area, frames, glass), Damaged branding (Signage), Damaged assembly counter found. If no damage found then score 2, If any damage found and no mails for follow up, then score 0, FOLLOW UP MAIL SHOULD BE LESS THAN A WEEK OLD, OR ELSE IT WONT BE VALID', 'RADIO', '[{\"id\":1,\"value\":\"Damage found, score 0\",\"marks\":0},{\"id\":2,\"value\":\"No Damage found, score 2\",\"marks\":2},{\"id\":3,\"value\":\"Damage found but follow up going on, score 2\",\"marks\":2}]', 'JSON', '35', 'AUDITOR', '39', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('40', '1', 'Table cleaning', 'Tables being cleared as soon as guest leaves. If tables cleaned immediately when the team member/utility, then score 2, if not cleaned immediately even after the team member/utility was free, then score 0', 'RADIO', '[{\"id\":1,\"value\":\"Tables cleaned, score 2\",\"marks\":2},{\"id\":2,\"value\":\"Tables not cleaned, score 0\",\"marks\":0}]', 'JSON', '35', 'AUDITOR', '40', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('41', '1', 'Dustbins', 'Dustbins should not be overflowing and should be clean. If dustbin is clean and not overflowing then score 1, if Not the score 0', 'RADIO', '[{\"id\":1,\"value\":\"Dustbins clean, score 1\",\"marks\":1},{\"id\":2,\"value\":\"Dustbins not clean, score 0\",\"marks\":0}]', 'JSON', '35', 'AUDITOR', '41', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('42', '1', 'CAYG inside kiosk', 'Check for cleanliness - Stations, Equipment, Storage areas, floor, sink', 'RADIO', '[{\"id\":1,\"value\":\"CAYG done,score 4\",\"marks\":4},{\"id\":2,\"value\":\"1 CAYG violation found, score 3\",\"marks\":3},{\"id\":3,\"value\":\"2 CAYG violations found, score 2\",\"marks\":2},{\"id\":4,\"value\":\"3 CAYG violations found, score 1\",\"marks\":1},{\"id\":5,\"value\":\"4 or more CAYG violations found, score 0\",\"marks\":0}]', 'JSON', '35', 'AUDITOR', '42', 'Y', 'ACTIVE', '4.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('43', '1', 'Entry & Outside Area', 'Facade cleaning, glass cleaning, no posters torn or dirty, If outside area is clean then score 2, if not then score 0', 'RADIO', '[{\"id\":1,\"value\":\"Outside area clean,score 2\",\"marks\":2},{\"id\":2,\"value\":\"Outside area not clean, score 0\",\"marks\":0}]', 'JSON', '35', 'AUDITOR', '43', 'Y', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('44', '1', 'Signage on (only at night)', 'Marks cut if not on at night (6pm), marks cut if on during the day, Only exception is rain or dark day, Mall stores where its on throughout the day', 'RADIO', '[{\"id\":1,\"value\":\" If at night Signage is ON, score 1\",\"marks\":1},{\"id\":2,\"value\":\"If at night signage is not ON, score 0\",\"marks\":0},{\"id\":3,\"value\":\" If at day Signage is ON, score 0\",\"marks\":0},{\"id\":4,\"value\":\"If at day Signage is not ON, score 1\",\"marks\":1}]', 'JSON', '35', 'AUDITOR', '44', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('45', '1', 'Fly Kill Light working', 'If mail dropped for it, then no points will be deducted FOLLOW UP MAIL SHOULD BE LESS THAN A WEEK OLD, OR ELSE IT WONT BE VALID', 'RADIO', '[{\"id\":1,\"value\":\"Fly kill working, score 1\",\"marks\":1},{\"id\":2,\"value\":\"Fly kill not working, but follow up going on, score 1\",\"marks\":1},{\"id\":3,\"value\":\"Fly Kill not working, score 0\",\"marks\":0}]', 'JSON', '35', 'AUDITOR', '45', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('46', '1', 'Citronella Diffusers', 'For electric Diffusers check if its ON, for non electric diffusers check if its being used', 'RADIO', '[{\"id\":1,\"value\":\"If electric diffuser is ON, score 1\",\"marks\":1},{\"id\":2,\"value\":\" if non electric diffuser is in use, score 1\",\"marks\":1},{\"id\":3,\"value\":\" if no diffusers are in use, score 0\",\"marks\":0}]', 'JSON', '35', 'AUDITOR', '46', 'Y', 'ACTIVE', '1.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('47', '1', 'Critical', 'Critical errors will result in deduction of 10 points from the total score - Issues like - Pests found, Expired product found, Desi chai add ons not available, 2 or more grooming issues, etc.', 'QUESTION_GROUP', '47', 'N', 'ACTIVE', 'N', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('48', '1', 'Expired Product found ?', 'If any product found expired then minus 10', 'RADIO', '[{\"id\":1,\"value\":\"Yes\",\"marks\":-10},{\"id\":2,\"value\":\"No\",\"marks\":0}]', 'JSON', '47', 'AUDITOR', '48', 'Y', 'ACTIVE', '0.00', 'Y', 'N', 'N');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('49', '1', 'Cross Contamination found ?', '- If any issue relating to cross contamination, then deduct 1 point. Examples: Veg/Non Veg segration (BOWLS, CHOPPING BOARDS, KNIVES, SPOONS, ETC), Fungal growth, Not changing gloves, Picking something off the floor, Working without using gloves, Only serious violations', 'RADIO', '[{\"id\":1,\"value\":\"Yes\",\"marks\":-1},{\"id\":2,\"value\":\"No\",\"marks\":0}]', 'JSON', '47', 'AUDITOR', '49', 'Y', 'ACTIVE', '0.00', 'Y', 'N', 'N');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('50', '1', 'Desi Chai, Kulhad Chai, any add on combination unavailable ?', 'If any of the 12 addons, or kulhad chai unavailable then minus 10', 'RADIO', '[{\"id\":1,\"value\":\"Yes\",\"marks\":-10},{\"id\":2,\"value\":\"No\",\"marks\":0}]', 'JSON', '47', 'AUDITOR', '50', 'Y', 'ACTIVE', '0.00', 'Y', 'N', 'N');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('51', '1', 'Order marked as Ready to Dispatch when it is still under prep', 'If COD order marked as dispatched while its still being prepared then minus 10', 'RADIO', '[{\"id\":1,\"value\":\"Yes\",\"marks\":-10},{\"id\":2,\"value\":\"No\",\"marks\":0}]', 'JSON', '47', 'AUDITOR', '51', 'Y', 'ACTIVE', '0.00', 'Y', 'N', 'N');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('52', '1', '2 or more grooming issues found', '2 or more grooming issues observed then minus 10', 'RADIO', '[{\"id\":1,\"value\":\"Yes\",\"marks\":-10},{\"id\":2,\"value\":\"No\",\"marks\":0}]', 'JSON', '47', 'AUDITOR', '52', 'Y', 'ACTIVE', '0.00', 'Y', 'N', 'N');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_DESCRIPTION`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('53', '1', 'Pest/Rodent found in cafe', 'Any pests seen in the café, then minus 10', 'RADIO', '[{\"id\":1,\"value\":\"Yes\",\"marks\":-10},{\"id\":2,\"value\":\"No\",\"marks\":0}]', 'JSON', '47', 'AUDITOR', '53', 'Y', 'ACTIVE', '0.00', 'Y', 'N', 'N');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_TYPE`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('54', '1', 'Delivery audit', 'QUESTION_GROUP', '54', 'N', 'ACTIVE', 'N', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('55', '1', 'Float amount issued to rider is mentioned in Shift handover copy & Kettle', 'RADIO', '[{\"id\":1,\"value\":\"Float issued and mentioned in Shift Handover register and Kettle, then score 2\",\"marks\":2},{\"id\":2,\"value\":\"Float issued but not mentioned in Shift Handover register and Kettle, then score 0\",\"marks\":0},{\"id\":3,\"value\":\"Float not issued, then score 0\",\"marks\":0}]', 'JSON', '54', 'AUDITOR', '55', 'N', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('56', '1', 'All bikes issued to Café from HQ is physically reconciled', 'RADIO', '[{\"id\":1,\"value\":\"Bike count tallied, then score 2\",\"marks\":2},{\"id\":2,\"value\":\"Bikes count not tallied, then score 0\",\"marks\":0}]', 'JSON', '54', 'AUDITOR', '56', 'N', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('57', '1', 'All Bikes Registration Certificate copy, PUC Copy, Insurance copy, Authorization letter copy lying In safe', 'RADIO', '[{\"id\":1,\"value\":\"All documents copies lying in the safe for all bikes, then score 2\",\"marks\":2},{\"id\":2,\"value\":\"All documents copies present, but not kept in the safe, then score 1\",\"marks\":1},{\"id\":3,\"value\":\"Document copies not present for all bikes, then score 0\",\"marks\":0},{\"id\":4,\"value\":\"Document copies present for all bikes, however 1 or more documents missing, then score 0\",\"marks\":0}]', 'JSON', '54', 'AUDITOR', '57', 'N', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('58', '1', 'All Bikes Registration Certificate copy, PUC Copy, Insurance copy, lying in the bike', 'RADIO', '[{\"id\":1,\"value\":\"All documents copies lying in the bike, then score 2\",\"marks\":2},{\"id\":2,\"value\":\"Documents present, but not in the bike, then score 0\",\"marks\":0},{\"id\":3,\"value\":\"All bikes don\'t have complete documents, 1 or more documents missing, then score 0\",\"marks\":0}]', 'JSON', '54', 'AUDITOR', '58', 'N', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('59', '1', 'Is Authorization letter lying with SDP’s', 'RADIO', '[{\"id\":1,\"value\":\"If SDP has Authorization letter, then score2\",\"marks\":2},{\"id\":2,\"value\":\"If SDP doesn\'t have Authorization letter, then score 0\",\"marks\":0},{\"id\":3,\"value\":\"If Authorization letter in present in Cafe, but not with SDP, then score 0\",\"marks\":0}]', 'JSON', '54', 'AUDITOR', '59', 'N', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('60', '1', 'Do the SDP have valid driving licence', 'RADIO', '[{\"id\":1,\"value\":\"If all SDP have valid driving license, then score 2\",\"marks\":2},{\"id\":2,\"value\":\"If any SDP has invalid or expired driving license, then score 0\",\"marks\":0}]', 'JSON', '54', 'AUDITOR', '60', 'N', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('61', '1', 'All asstes - helmets, delivery bags, eys & Cash Pouches in SuMo allocated to café is reconciled with the physical quantity', 'RADIO', '[{\"id\":1,\"value\":\"If all issued Assets are tallying with physical quantities in the Cafe, then score 2\",\"marks\":2},{\"id\":2,\"value\":\"If any issued Assets are not tallying with physical quantities in the Cafe, then score 0\",\"marks\":0}]', 'JSON', '54', 'AUDITOR', '61', 'N', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
INSERT INTO `KETTLE_DEV`.`AUDIT_FORM_VALUES` (`ID`, `AUDIT_FORM_ID`, `ENTITY_LABEL`, `ENTITY_TYPE`, `ENTITY_VALUES`, `LINKED_DATA_TYPE`, `LINKED_ENTITY_ID`, `ANSWER_BY`, `APPEARANCE_ORDER`, `SCORE_COUNTED`, `STATUS`, `MAX_SCORE`, `ADDITIONAL_COMMENT`, `ATTACH_DOC`, `IS_MANDATORY`) VALUES ('62', '1', 'If fuel mentioned in fuel register is reconciled with the voucher fuel claimed', 'RADIO', '[{\"id\":1,\"value\":\"If fuel bills/vouchers are tallying with fuel register, then score 2\",\"marks\":2},{\"id\":2,\"value\":\"If fuel bills/vouchers are not tallying with fuel register, then score 0\",\"marks\":0}]', 'JSON', '54', 'AUDITOR', '62', 'N', 'ACTIVE', '2.00', 'Y', 'N', 'Y');
