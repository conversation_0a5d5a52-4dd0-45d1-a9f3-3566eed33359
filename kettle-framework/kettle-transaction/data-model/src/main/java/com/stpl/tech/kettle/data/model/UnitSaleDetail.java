/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are private by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "DAY_WISE_SALES_DATA")
public class UnitSaleDetail {

	private int dayWiseSalesDetailId;
	private int unitId;
	private String unitName;
	private String category;
	private String areaManager;
	private BigDecimal billCreationInSeconds;
	private Date businessDate;
	private BigDecimal netSales;
	private BigDecimal netDeliverySales;
	private BigDecimal netDineInSales;
	private BigDecimal netApc;
	private BigDecimal netDeliveryApc;
	private BigDecimal netDineInApc;
	private BigDecimal customerCapture;
	private BigDecimal merchandiseSale;
	private BigDecimal coldPenetration;
	private BigDecimal giftCard;
	private Integer netTickets;
	private Integer netDeliveryTickets;
	private Integer netDineInTickets;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "DAY_WISE_SALES_DATA_ID", unique = true, nullable = false)
	public int getDayWiseSalesDetailId() {
		return dayWiseSalesDetailId;
	}

	public void setDayWiseSalesDetailId(int unitExpenseDetailId) {
		this.dayWiseSalesDetailId = unitExpenseDetailId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "UNIT_NAME", nullable = false)
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "BUSINESS_DATE", nullable = false, length = 10)
	public Date getBusinessDate() {
		return this.businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Column(name = "NET_TICKETS")
	public Integer getNetTickets() {
		return netTickets;
	}

	public void setNetTickets(Integer netTickets) {
		this.netTickets = netTickets;
	}

	@Column(name = "NET_SALES", precision = 10)
	public BigDecimal getNetSales() {
		return netSales;
	}

	public void setNetSales(BigDecimal netSales) {
		this.netSales = netSales;
	}

	@Column(name = "NET_DELIVERY_SALES", precision = 10)
	public BigDecimal getNetDeliverySales() {
		return netDeliverySales;
	}

	public void setNetDeliverySales(BigDecimal netDeliverySales) {
		this.netDeliverySales = netDeliverySales;
	}

	@Column(name = "NET_DINE_IN_SALES", precision = 10)
	public BigDecimal getNetDineInSales() {
		return netDineInSales;
	}

	public void setNetDineInSales(BigDecimal netDineInSales) {
		this.netDineInSales = netDineInSales;
	}

	@Column(name = "NET_APC", precision = 10)
	public BigDecimal getNetApc() {
		return netApc;
	}

	public void setNetApc(BigDecimal netApc) {
		this.netApc = netApc;
	}

	@Column(name = "NET_DELIVERY_APC", precision = 10)
	public BigDecimal getNetDeliveryApc() {
		return netDeliveryApc;
	}

	public void setNetDeliveryApc(BigDecimal netDeliveryApc) {
		this.netDeliveryApc = netDeliveryApc;
	}

	@Column(name = "NET_DINE_IN_APC", precision = 10)
	public BigDecimal getNetDineInApc() {
		return netDineInApc;
	}

	public void setNetDineInApc(BigDecimal netDineInApc) {
		this.netDineInApc = netDineInApc;
	}

	@Column(name = "CUSTOMER_CAPTURE_PERCENT_OF_TKTS", precision = 10)
	public BigDecimal getCustomerCapture() {
		return customerCapture;
	}

	public void setCustomerCapture(BigDecimal customerCapture) {
		this.customerCapture = customerCapture;
	}

	@Column(name = "MERCHANDISE_SALES", precision = 10)
	public BigDecimal getMerchandiseSale() {
		return merchandiseSale;
	}

	public void setMerchandiseSale(BigDecimal merchandiseSale) {
		this.merchandiseSale = merchandiseSale;
	}

	@Column(name = "COLD_PENETRATION", precision = 10)
	public BigDecimal getColdPenetration() {
		return coldPenetration;
	}

	public void setColdPenetration(BigDecimal coldPenetration) {
		this.coldPenetration = coldPenetration;
	}

	@Column(name = "GIFT_CARD_AMOUNT", precision = 10)
	public BigDecimal getGiftCard() {
		return giftCard;
	}

	public void setGiftCard(BigDecimal giftCard) {
		this.giftCard = giftCard;
	}

	@Column(name = "NET_DELIVERY_TICKETS")
	public Integer getNetDeliveryTickets() {
		return netDeliveryTickets;
	}

	public void setNetDeliveryTickets(Integer netDeliveryTickets) {
		this.netDeliveryTickets = netDeliveryTickets;
	}

	@Column(name = "DINE_IN_TICKETS")
	public Integer getNetDineInTickets() {
		return netDineInTickets;
	}

	public void setNetDineInTickets(Integer netDineInTickets) {
		this.netDineInTickets = netDineInTickets;
	}

	@Column(name = "AREA_MANAGER")
	public String getAreaManager() {
		return areaManager;
	}

	public void setAreaManager(String areaManager) {
		this.areaManager = areaManager;
	}

	@Column(name = "BILL_CREATION_SECONDS", precision = 10)
	public BigDecimal getBillCreationInSeconds() {
		return billCreationInSeconds;
	}

	public void setBillCreationInSeconds(BigDecimal billCreationInSeconds) {
		this.billCreationInSeconds = billCreationInSeconds;
	}
	
	@Column(name = "UNIT_CATEGORY")
	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

}