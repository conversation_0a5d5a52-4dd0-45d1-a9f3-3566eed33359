package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.List;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "MENU_VARIANT_METADATA_GROUPS")
public class LCMMenuVariantMetadataGroup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "GROUP_NAME", nullable = false)
    private String groupName;

    @Column(name = "STATUS", nullable = false)
    private String status;



    @OneToMany(mappedBy = "group", cascade = CascadeType.ALL)
    private List<LCMMenuVariantMetadataItem> items;

}
