/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.stpl.tech.kettle.core.CalculationType;
import com.stpl.tech.kettle.core.ReportStatus;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.data.budget.vo.AdjustmentAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetKey;
import com.stpl.tech.kettle.core.data.budget.vo.CalculationStatus;
import com.stpl.tech.kettle.core.data.budget.vo.DirectCost;
import com.stpl.tech.kettle.core.data.budget.vo.ElectricityAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ElectricityStaticData.ElectricityBillType;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.ExpenseRecordCategory;
import com.stpl.tech.kettle.core.file.management.GoogleSheetLoader;
import com.stpl.tech.kettle.core.notification.DataErrorNotification;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.notification.PnLMailReceipt;
import com.stpl.tech.kettle.core.notification.PnLNotification;
import com.stpl.tech.kettle.core.service.CashManagementService;
import com.stpl.tech.kettle.core.service.ExpenseManagementService;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.kettle.core.service.PullTransferSettlementReasonService;
import com.stpl.tech.kettle.core.service.TableService;
import com.stpl.tech.kettle.core.service.UnitBudgetService;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.impl.BudgetUtils;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.model.ExpenseDetailData;
import com.stpl.tech.kettle.data.model.PnlAdjustmentDetail;
import com.stpl.tech.kettle.data.model.UnitBudgetoryDetail;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.data.model.UnitPullDetail;
import com.stpl.tech.kettle.domain.model.DayWiseOrderConsumptionItem;
import com.stpl.tech.kettle.domain.model.DayWiseOrderConsumptionRequest;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.TableStatus;
import com.stpl.tech.kettle.domain.model.UnitClosure;
import com.stpl.tech.kettle.domain.model.UnitTableMapping;
import com.stpl.tech.kettle.report.metadata.model.ExecutionType;
import com.stpl.tech.kettle.report.metadata.model.ReportCategories;
import com.stpl.tech.kettle.report.metadata.model.ReportCategory;
import com.stpl.tech.kettle.report.metadata.model.ReportData;
import com.stpl.tech.kettle.report.metadata.model.ReportParam;
import com.stpl.tech.kettle.reports.core.ReportOutput;
import com.stpl.tech.kettle.reports.dao.impl.PostClosureReportInputData;
import com.stpl.tech.kettle.reports.dao.impl.ReportFileData;
import com.stpl.tech.kettle.reports.dao.impl.RevenueDataProvider;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportInputData;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportOutputData;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportProvider;
import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.kettle.reports.process.ConsumptionReportController;
import com.stpl.tech.kettle.reports.process.PostDayClosureReportController;
import com.stpl.tech.kettle.reports.process.ReportNotification;
import com.stpl.tech.kettle.reports.process.SettlementReportController;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.WebServiceCallException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.ConsumptionData;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.notification.GenericNotification;
import com.stpl.tech.master.notification.GenericNotificationTemplate;
import com.stpl.tech.master.notification.GenericReportEmail;
import com.stpl.tech.master.tax.model.Taxation;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.master.util.QueryExecutorForList;
import com.stpl.tech.master.util.ServiceUtil;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.ExecutionEnvironment;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.JaxbUtil;
import com.stpl.tech.util.notification.AttachmentData;
import com.stpl.tech.util.notification.EmailNotification;
import com.stpl.tech.util.notification.ReportDetailData;
import com.stpl.tech.util.notification.model.ResponseData;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import javax.activation.UnsupportedDataTypeException;
import javax.sql.DataSource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

public abstract class AbstractMetadataResource extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(AbstractMetadataResource.class);

    private static final int SOCKET_TIMEOUT=60;
    private static final int CONNECTION_TIMEOUT=60;

    public boolean dayClose(int unitId, int userId, Date businessDate, String reason, EnvironmentProperties props, String pullTransferReason, List<UnitPullDetail> unitPullDetailList)
            throws DataUpdationException, DataNotFoundException, WebServiceCallException {
        Unit unit = getMasterDataCache().getUnit(unitId);
        boolean isAlreadyClosed = getMetadataService().isDayClosed(unitId, businessDate);
        if (isAlreadyClosed) {
            String msg = String.format("Tried closing a day which is already closed for unit %d by employee %d", unitId,
                    userId);
            LOG.info(msg);
            throw new DataUpdationException(msg);
        }
        Date copyDate = businessDate;
        UnitClosureDetails unitClosureDetails = getMetadataService().getLastClosureDetail(unitId);
        if (Objects.nonNull(unitClosureDetails) && AppUtils.getAbsDaysDiff(unitClosureDetails.getBusinessDate(),businessDate) > 1 ){
            businessDate = AppUtils.addDays(AppUtils.getBusinessDate(),-1);
            List<UnitHours> unitHours = getMasterDataCache().getOperationalHoursForUnit(unitId);
            int retryCount =0;
            boolean retry = true;
            while (retry && retryCount<7) {
                try {
                    UnitHours hours = unitHours.get(AppUtils.getDayOfWeek(businessDate)-1);
                    if(Objects.nonNull(hours) && hours.isIsOperational()){
                        retry = false;
                    } else {
                        businessDate = AppUtils.getPreviousBusinessDate(businessDate);
                    }
                } catch (Exception e) {
                    businessDate = AppUtils.getPreviousBusinessDate(businessDate);
                }
                retryCount++;
            }
        }
        if (Objects.nonNull(unitClosureDetails) && businessDate.equals(unitClosureDetails.getBusinessDate())){
            businessDate = copyDate;
        }
        if (unit.isTableService() && unit.getTableServiceType() > 0) {
            List<UnitTableMapping> list = getTableService().getTablesForUnit(unitId);
            for (UnitTableMapping table : list) {
                if (TableStatus.OCCUPIED.equals(table.getTableStatus())) {
                    String msg = String.format("Please Close all tables before creating day close");
                    LOG.info(msg);
                    throw new DataUpdationException(msg);
                }
            }
        }

        try {
            LOG.info("Processing Product Consumption for Unit: {}", unitId);
            Boolean checkOpeningINSCM = callWebServiceWithTimeout(Boolean.class, getEnvironmentProperties().checkOpeningURL(),
                    unitId,SOCKET_TIMEOUT,CONNECTION_TIMEOUT);
            if (checkOpeningINSCM == null) {
                String msg = String.format("Error in checking opening stock entry for unit %d in SUMO %d", unitId, userId);
                LOG.info(msg);
                throw new DataUpdationException(msg);
            }
            if (checkOpeningINSCM) {
                String msg = String.format("Stock Opening Missing For unit %d in SCM. Please enter an opening stock",
                        unitId);
                LOG.info(msg);
                throw new DataUpdationException(msg);
            }
        } catch (WebServiceCallException e) {
            throw new WebServiceCallException(e.getMessage());
        } catch (Exception e) {
            // TODO Make this into common try catch
            if(e.getClass().equals(JsonSyntaxException.class)){
                new ErrorNotification("SCM Opening Stock Check Failure",
                        "Sumo Is Down , Please Try Day Close After 5 Minutes " + unitId, e,
                        getEnvironmentProperties().getEnvironmentType()).sendEmail();
                throw new DataUpdationException("Sumo Is Down , Please Try Again After 5 Minutes....");
            }else{
                try {
                    new ErrorNotification("SCM Opening Stock Check Failure",
                            "Error Checking SCM Opening Stock for unit " + unitId, e,
                            getEnvironmentProperties().getEnvironmentType()).sendEmail();
                } catch (Exception ex) {
                    // TODO Make this into common try catch
                    LOG.error("Error Checking SCM Opening Stock for unit {}", unitId, ex);
                }
                LOG.error("Error Checking SCM Opening Stock for unit {}", unitId, e);
                throw new DataUpdationException("Error Checking SCM Opening Stock for unit " + unitId);
            }

        }
        int startOrderId = getOrderSearchService().getLastDayCloseOrderId(unitId);
        int lastOrderId = getOrderSearchService().getLastOrderDetail(unitId);
        List<Order> ordersForTheDay = getOrderSearchService().getOrderDetailsForDay(unitId, startOrderId, lastOrderId);
        BigDecimal creditCardPercentage = getUnitBudgetService().getCreditCardPercentageForCurrentMonth(unitId, AppUtils.getBusinessDate());
        try {
            SettlementReportOutputData output = generateReports(unitId, businessDate, ordersForTheDay, false, true,
                    creditCardPercentage, getChannelPartners(businessDate));
            int dayClosureId = getMetadataService().closeDay(unitId, userId, reason, startOrderId, lastOrderId,
                    businessDate);
            try {
                LOG.info("Processing Product Estimates for Unit: {}", unitId);
                getMetadataService().callProductEsimateUpdateProc(unitId, businessDate);
            } catch (Exception e) {
                new ErrorNotification("Product Estimates for Unit Failure",
                        "Error Product Estimates for Unit " + unitId, e,
                        getEnvironmentProperties().getEnvironmentType()).sendEmail();
                LOG.error("Error Product Estimates for unit {}", unitId, e);
            }
            if (dayClosureId > 0) {
                processSCMProductConsumption(unitId, userId, businessDate, unit, startOrderId, lastOrderId, ordersForTheDay, dayClosureId);
                processCashManagement(unitId, businessDate, props, unit, ordersForTheDay, dayClosureId);
                if (Objects.nonNull(output) && output.isReportGenerated()) {
                    try {
                        emailReport(output, new ReportNotification(output));
                    } catch (Exception e) {
                        LOG.error("Error sending email for the report for unit " + unitId, e);
                        output.setReportEmailed(false);
                    }
                }
            }
            if(dayClosureId > 0 && Objects.nonNull(pullTransferReason) && Objects.nonNull(unitPullDetailList) && unitPullDetailList.size() >0){
                getPullTransferSettlementReasonService().savePullTransferSettlement(unitPullDetailList,dayClosureId,pullTransferReason,AppConstants.STATUS_SUCCESSFUL,AppUtils.getBusinessDate());
            }
            getMetadataService().clearGoalsData(unitId,dayClosureId);
            return dayClosureId > 0;
        } catch (Exception e) {
            LOG.error("Error in Day Close for unit :::::::::::::", e);
            throw new DataUpdationException("Error in Day Close for unit " + unitId+", message : "+e.getMessage());
        }

    }

    public DayWiseOrderConsumptionRequest getOrderConsumptionsDayWise(DayWiseOrderConsumptionRequest dayWiseOrderConsumptionRequest) {
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        LOG.info("Got Request To re Calculate Consumption Day Wise For Unit Id : {} is : {}", dayWiseOrderConsumptionRequest.getUnitId(),
                gson.toJson(dayWiseOrderConsumptionRequest));
        List<Order> orders = getOrderSearchService().getAllOrdersOfTheDays(dayWiseOrderConsumptionRequest);
        LOG.info("Total Bulk Orders Found For Unit ID are : {} and Orders are : {}", dayWiseOrderConsumptionRequest.getUnitId(), Arrays.toString(orders.stream().mapToInt(Order::getOrderId).boxed().toArray()));
        Map<Integer, List<Order>> dayWiseOrderMap = getDayWiseOrderMap(orders, dayWiseOrderConsumptionRequest);
        LOG.info("dayWiseOrderMap For Unit ID are : {} and Orders are : {}", dayWiseOrderConsumptionRequest.getUnitId(), gson.toJson(dayWiseOrderMap));
        Map<Integer, List<Consumable>> dayCloseEventWiseConsumption = getDayCloseEventWiseConsumption(dayWiseOrderMap);
        LOG.info("dayCloseEventWiseConsumption For Unit ID are : {} and Orders are : {}", dayWiseOrderConsumptionRequest.getUnitId(), gson.toJson(dayCloseEventWiseConsumption));
        dayWiseOrderConsumptionRequest.setDayCloseEventWiseConsumption(dayCloseEventWiseConsumption);
        return dayWiseOrderConsumptionRequest;
    }

    private Map<Integer, List<Consumable>> getDayCloseEventWiseConsumption(Map<Integer, List<Order>> dayWiseOrderMap) {
        Map<Integer, List<Consumable>> result = new HashMap<>();
        for (Map.Entry<Integer, List<Order>> dayWiseOrder : dayWiseOrderMap.entrySet()) {
            if (!dayWiseOrder.getValue().isEmpty()) {
                Map<Integer, Consumable> map = new HashMap<>();
                for (Order o : dayWiseOrder.getValue()) {
                    int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(), o.getChannelPartner(), o.getBrandId(), TransactionUtils.isCODOrder(o.getSource()));
                    getItemConsumptionHelper().addConsumption(o, map, deliveryUnitId, true);
                }
                if (!map.values().isEmpty()) {
                    result.put(dayWiseOrder.getKey(), new ArrayList<>(map.values()));
                }
            }
        }
        return result;
    }

    private Map<Integer, List<Order>> getDayWiseOrderMap(List<Order> orders, DayWiseOrderConsumptionRequest dayWiseOrderConsumptionRequest) {
        Map<Integer, List<Order>> result = new HashMap<>();
        for (DayWiseOrderConsumptionItem dayWiseOrderConsumptionItem : dayWiseOrderConsumptionRequest.getDayWiseOrderConsumptionItems()) {
            if (!result.containsKey(dayWiseOrderConsumptionItem.getSumoDayCloseId())) {
                result.put(dayWiseOrderConsumptionItem.getSumoDayCloseId(), new ArrayList<>());
            }
            for (Order order : orders) {
                if (order.getOrderId() > dayWiseOrderConsumptionItem.getKettleOrderStartId() && order.getOrderId() <= dayWiseOrderConsumptionItem.getKettleOrderEndId()) {
                    result.get(dayWiseOrderConsumptionItem.getSumoDayCloseId()).add(order);
                }
            }
        }
        return result;
    }

    private void processCashManagement(int unitId, Date businessDate, EnvironmentProperties props, Unit unit, List<Order> ordersForTheDay, int dayClosureId) throws DataUpdationException {
        try {
            processCashManagement(businessDate, ordersForTheDay, unitId, props,
                    new TreeSet<>(getTaxDataCache().getSaleTaxations().get(unit.getLocation().getState().getId())));
        } catch (Exception e) {
            // TODO Make this into common try catch
            try {
                new ErrorNotification("Cash Management Failure",
                        "Error Processing Cash Management for unit:" + unitId, e,
                        getEnvironmentProperties().getEnvironmentType()).sendEmail();
            } catch (Exception ex) {
                LOG.error("Error Processing Cash Management for unit {}", unitId, ex);
            }
            LOG.error("rror Processing Cash Management for unit {}", unitId, e);
            getMetadataService().markDayCloseAsCancelled(dayClosureId);
            //getUnitBudgetService().markAsCancelled(pnlDetailId);
            throw new DataUpdationException("Error in Cash Management While Day Close for unit " + unitId);
        }
    }

    private void processSCMProductConsumption(int unitId, int userId, Date businessDate, Unit unit, int startOrderId, int lastOrderId, List<Order> ordersForTheDay, int dayClosureId) throws DataUpdationException {
        try {
            LOG.info("Processing Product Consumption for Unit: {}", unitId);
            ResponseData<ConsumptionData> result = processSCMProductConsumption(dayClosureId, unitId, userId, startOrderId,
                    lastOrderId, businessDate, ordersForTheDay, false);
            if (result == null || !result.isSuccess()) {
                getMetadataService().markDayCloseAsCancelled(dayClosureId);
                String message = "Error in sending consumption to SUMO for unit " + unit.getName() + "(" + unitId
                        + ")" + "\n Response: " + result != null ? result.getMessage() : "NO RESULT";
                List<String> toEmails = new ArrayList<>();
                if (unit.getUnitEmail() != null) {
                    toEmails.add(unit.getUnitEmail());
                }
                if (unit.getManagerEmail() != null) {
                    toEmails.add(unit.getManagerEmail());
                }
                if (unit.getCafeManager() != null) {
                    EmployeeBasicDetail e = getMasterDataCache().getEmployeeBasicDetail(unit.getCafeManager().getId());
                    if (e != null && e.getEmailId() != null && !toEmails.contains(e.getEmailId())) {
                        toEmails.add(e.getEmailId());
                    }
                }
                toEmails.add("<EMAIL>");
                new DataErrorNotification("Failure in Day Close for " + unit.getName() + " for " + businessDate,
                        message, getEnvironmentProperties().getEnvironmentType(), toEmails).sendEmail();
                throw new DataUpdationException(message);
            }
        } catch (DataUpdationException due) {
            getMetadataService().markDayCloseAsCancelled(dayClosureId);
            throw due;
        } catch (Exception e) {
            new ErrorNotification("SCM Product Consumption Report Failure",
                    "Error Processing SCM Product Consumption for unit:" + unitId, e,
                    getEnvironmentProperties().getEnvironmentType()).sendEmail();
            LOG.error("Error Processing SCM Product Consumption for unit {}", unitId, e);
            getMetadataService().markDayCloseAsCancelled(dayClosureId);
            throw new DataUpdationException(
                    "Error in sending consumption to SUMO for unit " + unitId + " \n" + e);
        }
    }

    public boolean dayCloseFromSumo(int unitId, Date businessDate, EnvironmentProperties props) throws DataUpdationException {
        boolean isAlreadyClosed = getMetadataService().isDayClosed(unitId, businessDate);
        if (isAlreadyClosed) {
            try {
                LOG.info("Restart Day Close From for Unit: {}", unitId);
                UnitClosure closure = getMetadataService().getUnitsClosure(unitId, businessDate);
                List<UnitExpenditureDetail> unitExpenditureDetails = getMetadataService().getAllPnlListForClosureId(closure.getId());
                if (unitExpenditureDetails != null) {
                    getMetadataService().markDayCloseAsCancelled(closure.getId());
                    for (UnitExpenditureDetail ucd : unitExpenditureDetails) {
                        getUnitBudgetService().markAsCancelled(ucd.getDetailId());
                    }
                    LOG.info("Retrigger Day Close after Cancelling Previous Day Close Event");
                    dayClose(unitId, closure.getEmployeeId(), businessDate, closure.getComment(), props,null,null);
                    LOG.info("Retrigger Day Close Successfully Completed");
                    return true;
                }else{
                    LOG.info("Error Finding PNL Generated For Day Close For UnitClosureId" + closure.getId());
                    return false;
                }
            } catch (Exception e) {
                LOG.error("Error Refreshing Day Close for unit" + unitId, e);
                return false;
            }
        } else {
            LOG.info("Day Close Is Not Processed From Kettle For unit" + unitId);
            return false;
        }
    }

    /**
     * @param unit
     * @param dayClosureId
     * @param businessDate
     * @return
     */
    protected BudgetKey createBudgetKey(Unit unit, int dayClosureId, Date businessDate) {
        BudgetKey key = new BudgetKey();
        key.setBusinessDate(businessDate);
        key.setUnitId(unit.getId());
        key.setUnitName(unit.getName());
        key.setDayClosureId(dayClosureId);
        key.setCalculation(CalculationType.CURRENT.name());
        key.setYear(AppUtils.getYear(businessDate));
        key.setMonth(AppUtils.getMonth(businessDate));
        key.setDay(AppUtils.getDay(businessDate));
        return key;
    }

    protected Map<String, List<ReportDetailData>> generateQueryBasedManagersReportData(int unitId, Date businessDate)
            throws UnsupportedDataTypeException, ParseException {
        ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
                getEnvironmentProperties().getBasePath() + "/reports/ManagersReport.xml");
        Map<String, List<ReportDetailData>> map = new HashMap<>();
        for (ReportCategory category : reportCategories.getCategory()) {
            LOG.info("Running automated report: {}", category.getName());
            map.put(category.getName(), new ArrayList<>());
            for (ReportData report : category.getReport()) {
                if (report.getEnvironment() == null || report.getEnvironment().trim().length() == 0) {
                    report.setEnvironment(getEnvironmentProperties().getEnvironmentType().getExecutionEnvironment().name());
                }
                for (ReportParam param : report.getParam()) {
                    if (param.getName().equals("unitId")) {
                        param.setValue(unitId + "");
                    }
                    if (param.getName().equals("businessDate")) {
                        param.setValue(AppUtils.getSQLFormattedDate(businessDate));
                    }
                    if (param.getName().equals("monthStartDate")) {
                        param.setValue(AppUtils.getSQLFormattedDate(
                                AppUtils.getStartOfMonth(businessDate.getYear(), businessDate.getMonth())));
                    }
                    if (param.getName().equals("quarterStartDate")) {
                        param.setValue(AppUtils.getSQLFormattedDate(AppUtils.getStartOfQuarter(businessDate)));
                    }
                }
                DataSource dataSource = ServiceUtil
                        .getDataSourceBean(ExecutionEnvironment.valueOf(report.getEnvironment()));
                QueryExecutorForList executor = new QueryExecutorForList();
                executor.execute(report, dataSource);
                List<List<String>> data = executor.getData();
                ReportDetailData reportData = new ReportDetailData(report.getName(), data,
                        report.isSkipInline() == null || !report.isSkipInline(), new HashSet<Integer>());
                map.get(category.getName()).add(reportData);
            }
        }
        return map;
    }

    protected SettlementReportOutputData generateReports(int unitId, Date businessDate, List<Order> ordersForTheDay,
                                                         boolean processExternalReports, boolean generatePnL, BigDecimal creditCardPercentage, Map<Integer, ChannelPartnerDetail> partners)
            throws DataNotFoundException, DataUpdationException {
        Map<String, List<ReportDetailData>> reports = null;
        SettlementReportInputData input = new SettlementReportInputData(businessDate,
                getMasterDataCache().getUnit(unitId), ordersForTheDay, reports, generatePnL, creditCardPercentage, partners,null);
        SettlementReportController reportController = new SettlementReportController(getMasterDataCache(),
                getMetadataCache(), getTaxDataCache(), getEnvironmentProperties(), getOfferManagementExternalService(), getCardService());
        SettlementReportOutputData output = reportController.execute(input);
        if (processExternalReports) {
            List<ReportOutput> externalOutputList = null;
            try {
                externalOutputList = processExternalReports(unitId, ordersForTheDay, businessDate);
                if (externalOutputList != null && Objects.nonNull(output)) {
                    externalOutputList.forEach(e -> output.getReportFiles().addAll(e.getReportFiles()));
                }
            } catch (Exception e) {
                LOG.error("Error collecting external report for unit " + unitId, e);
            }
        }
        return output;
    }

    protected BudgetDetail generatePnLReports(int unitId, Date businessDate, List<Order> ordersForTheDay,
                                              BigDecimal creditCardPercentage, Map<Integer, ChannelPartnerDetail> partners) {
        RevenueDataProvider provider = new RevenueDataProvider(getMasterDataCache(),
                getOfferManagementExternalService(), getCardService());
        for (Order order : ordersForTheDay) {
            provider.process(order, creditCardPercentage, partners);
        }
        BudgetDetail budget = new BudgetDetail();
        budget.setRevenue(provider.getRevenue());
        budget.setCommissions(provider.getSalesCommission());
        return budget;
    }

    public List<ReportOutput> processExternalReports(UnitClosure closure) throws DataNotFoundException {
        List<Order> ordersForTheDay = getOrderSearchService().getOrderDetailsForDay(closure.getUnitId(),
                closure.getStartOrderId(), closure.getLastOrderId());
        return processExternalReports(closure.getUnitId(), ordersForTheDay, closure.getBusinessDate());
    }

    protected void emailReport(SettlementReportOutputData output, EmailNotification notification)
            throws FileNotFoundException, EmailGenerationException, IOException {
        List<AttachmentData> attachments = new ArrayList<AttachmentData>();
        for (ReportFileData fileData : output.getReportFiles()) {
            if (fileData.isGenerated()) {
                File file = new File(fileData.getFilePath());
                if (file.exists()) {
                    AttachmentData reports = new AttachmentData(IOUtils.toByteArray(new FileInputStream(file)),
                            fileData.getFileName(), fileData.getMimeType());
                    attachments.add(reports);
                }
            }
        }
        notification.sendRawMail(attachments);
        output.setReportEmailed(true);
    }

    public void processCashManagement(Date businessDate, List<Order> orders, int unitId, EnvironmentProperties props,
                                      Set<Taxation> stateTaxes) throws DataNotFoundException {
        // LOG.info("Initiating Cash Management Process...");
        SettlementReportInputData data = new SettlementReportInputData(AppUtils.getCurrentDate(),
                getMasterDataCache().getUnit(unitId), orders, false, null, null,null);
        SettlementReportProvider settlementProvider = new SettlementReportProvider(getMasterDataCache(),
                getMetadataCache());
        settlementProvider.process(data, stateTaxes);
        Collection<SettlementReport> products = settlementProvider.getByPaymentType();
        getCashManagementService().saveClosurePayment(businessDate, products, unitId);
    }

    private ResponseData<ConsumptionData> processSCMProductConsumption(int clousureId, int unitId, int userId, int startOrderId,
                                                      int lastOrderId, Date businessDate, List<Order> orders, boolean update)
            throws DataNotFoundException, DataUpdationException, WebServiceCallException {

        ConsumptionData data = createConsumptionData(clousureId, unitId, userId, startOrderId, lastOrderId,
                businessDate);
        Map<Integer, Consumable> map = new HashMap<>();
        Map<String, Map<Integer, Consumable>> orderTypeMap = new HashMap<>();
        LOG.info("SCM :: Total orders to Process {}", orders.size());
        for (Order o : orders) {
            // all orders consumption
            //int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(), TransactionUtils.isPartnetOrder(getMasterDataCache().getChannelPartner(o.getChannelPartner()).getType()));
            int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(), o.getChannelPartner(), o.getBrandId(), TransactionUtils.isCODOrder(o.getSource()));
            getItemConsumptionHelper().addConsumption(o, map, deliveryUnitId, false);
            addConsumptionType(o, orderTypeMap, getConsumptionType(o), deliveryUnitId);
        }
        data.getConsumables().addAll(map.values());
        if (orderTypeMap.get(AppConstants.COD) != null) {
            data.getCodConsumable().addAll(orderTypeMap.get(AppConstants.COD).values());
        }
        if (orderTypeMap.get(AppConstants.CAFE) != null) {
            data.getCafeConsumables().addAll(orderTypeMap.get(AppConstants.CAFE).values());
        }
        if (orderTypeMap.get(AppConstants.ORDER_TYPE_EMPLOYEE_MEAL) != null) {
            data.getEmployeeMealConsumable().addAll(orderTypeMap.get(AppConstants.ORDER_TYPE_EMPLOYEE_MEAL).values());
        }

        LOG.info("SCM :: Sending Data to SCM : size {}", data.getConsumables().size());
        getItemConsumptionHelper().writeToSCMRepotFile(data, getEnvironmentProperties());
        if(!update) {
			Object obj = callWebServiceWithTimeout(Object.class, getEnvironmentProperties().addConsumptionURL(), data, SOCKET_TIMEOUT, CONNECTION_TIMEOUT);
			if (obj != null) {
				Type type = new TypeToken<ResponseData<ConsumptionData>>() {
				}.getType();
				ResponseData<ConsumptionData> response = JSONSerializer.toJSON(obj, type);
				getMenuItemConsumptionHelper().processMenuProductCogsForUnit(response, orders);
				return response;
			} else {
				throw new DataNotFoundException();
			}
        }else {
        	Object obj = callWebServiceWithTimeout(Object.class, getEnvironmentProperties().getConsumptionURL(), data, SOCKET_TIMEOUT, CONNECTION_TIMEOUT);
			if (obj != null) {
				Type type = new TypeToken<ResponseData<ConsumptionData>>() {
				}.getType();
				ResponseData<ConsumptionData> response = JSONSerializer.toJSON(obj, type);
                LOG.info("response from sumo ");
				getMenuItemConsumptionHelper().deleteMenuProductCogsForUnit(unitId, clousureId);
				getMenuItemConsumptionHelper().processMenuProductCogsForUnit(response, orders);
				return response;
			} else {
				throw new DataNotFoundException("Not able to find scm consumption data while updating menu cogs for unit "+unitId);
			}
        }

    }

    private String getConsumptionType(Order o) {
        String type = null;
        if (TransactionUtils.isEmployeeMeal(o)) {
            // employee meal
            type = AppConstants.ORDER_TYPE_EMPLOYEE_MEAL;
        } else if (AppConstants.COD.equals(o.getSource())) {
            // delivery orders
            type = AppConstants.COD;
        } else {
            // cafe, take away and others
            type = AppConstants.CAFE;
        }
        return type;
    }

    private void addConsumptionType(Order o, Map<String, Map<Integer, Consumable>> orderTypeMap, String type, Integer deliveryUnitId) {
        if (type == null) {
            return;
        }
        Map<Integer, Consumable> m = orderTypeMap.get(type);
        if (m == null) {
            m = new HashMap<Integer, Consumable>();
            orderTypeMap.put(type, m);
        }
        getItemConsumptionHelper().addConsumption(o, m, deliveryUnitId, false);
    }

    public boolean processSCMProductConsumption(UnitClosure closure, boolean update)
            throws DataNotFoundException, DataUpdationException, WebServiceCallException {
        List<Order> ordersForTheDay = getOrderSearchService().getOrderDetailsForDay(closure.getUnitId(),
                closure.getStartOrderId(), closure.getLastOrderId());
        ResponseData<ConsumptionData> response = processSCMProductConsumption(closure.getId(), closure.getUnitId(),
                closure.getEmployeeId(), closure.getStartOrderId(), closure.getLastOrderId(), closure.getBusinessDate(),
                ordersForTheDay, update);
        return response.isSuccess();
    }

    private ConsumptionData createConsumptionData(int clousureId, int unitId, int userId, int startOrderId,
                                                  int lastOrderId, Date businessDate) {
        ConsumptionData data = new ConsumptionData();
        data.setBusinessDate(businessDate);
        data.setClosureId(clousureId);
        data.setEndOrderId(lastOrderId);
        data.setStartOrderId(startOrderId);
        data.setUnitId(unitId);
        return data;
    }

    public void generateConsumptionReport(Date businessDate)
            throws DataNotFoundException, IOException, EmailGenerationException {

        Set<Integer> states = getMasterDataCache().getUnits().values().stream()
                .map(x -> x.getLocation().getState().getId()).collect(Collectors.toSet());

        for (Integer stateId : states) {
            State state = null;
            for (State s : getMasterDataCache().getAllStates().values()) {
                if (s.getId() == stateId) {
                    state = s;
                }
            }

            Set<Integer> units = getMasterDataCache().getUnits().values().stream()
                    .filter(u -> u.getLocation().getState().getId() == stateId).map(v -> v.getId())
                    .collect(Collectors.toSet());

            ConsumptionReportController consumptionReportController = new ConsumptionReportController(
                    getMasterDataCache(), getMetadataCache(), getTaxDataCache(), getEnvironmentProperties());

            SettlementReportOutputData data = consumptionReportController.processData(getMetadataService(),
                    getOrderSearchService(), businessDate, units);

            Workbook workbook = new XSSFWorkbook(data.getReportFiles().get(0).getFilePath());
            WorkbookContext workbookContext = WorkbookContextFactory.useWorkbook(workbook);
            AttachmentData attachmentData = new AttachmentData(workbookContext.toNativeBytes(),
                    data.getReportFiles().get(0).getFileName(), data.getReportFiles().get(0).getMimeType());
            List<AttachmentData> attachmentDatas = new ArrayList<>();
            attachmentDatas.add(attachmentData);

            GenericNotification notification = new GenericNotification();
            notification.setToEmails(AppUtils.isDev(getEnvironmentProperties().getEnvironmentType())
                    ? new String[]{"<EMAIL>"}
                    : new String[]{"<EMAIL>", "<EMAIL>", "<EMAIL>"});
            notification.setSubject("All Unit Consumption Report for " + state.getName() + " on "
                    + AppUtils.getDateString(businessDate));
            notification.setFromEmail("<EMAIL>");
            notification.setAttachFile(true);
            notification.setAttachmentData(attachmentDatas);
            notification.setOutputType(com.stpl.tech.kettle.report.metadata.model.ReportOutput.EXCEL);
            notification.setNeedsCompression(false);
            notification.setExecutionType(ExecutionType.JAVA);
            sendGenericNotification(notification);
        }
    }

    private void sendGenericNotification(GenericNotification mailNotification)
            throws IOException, EmailGenerationException {
        GenericNotificationTemplate template = new GenericNotificationTemplate(mailNotification);
        GenericReportEmail email = new GenericReportEmail(template, getEnvironmentProperties().getEnvironmentType());
        if (mailNotification.isAttachFile()) {
            List<AttachmentData> attachments = null;
            if (mailNotification.isNeedsCompression()) {
                attachments = new ArrayList<>();
                attachments.add(MasterUtil.compress(mailNotification.getSubject(),
                        getEnvironmentProperties().getBasePath() + "/tmp/", mailNotification.getAttachmentData()));
            } else {
                attachments = mailNotification.getAttachmentData();
            }
            email.sendRawMail(attachments);
        } else {
            email.sendEmail();
        }
    }

    /**
     * @param businessDate
     */
    protected void generatePnLReports(Date businessDate){
        List<UnitExpenditureDetail> expenditures = getUnitBudgetService().getUnitExpenditureDetail(businessDate,
                CalculationStatus.CREATED, CalculationType.CURRENT);
        if (expenditures != null && !expenditures.isEmpty()) {
            expenditures.parallelStream().forEach(e ->{
                UnitBudgetoryDetail budget = getUnitBudgetService().getUnitBudgetDetail(e.getUnitId(), e.getMonth(),
                        e.getYear());
                if (budget == null) {
                    getUnitBudgetService().markAsBudgetNotAvailable(e.getDetailId());
                } else {
                    BudgetDetail detail = new BudgetDetail();
                    List<VoucherData> voucherDataList = setExpenseData(detail, e);
                    AdjustmentAggregate currentAdjustment = createCurrentAdjustmentAggregate(e.getUnitId(), e.getMonth(),
                            e.getYear(), e.getBusinessDate());
                    detail.setAdjustment(currentAdjustment);
                    DirectCost d = getUnitBudgetService().setDirectCostData(budget, e.getDay(), e.getMonth(), e.getYear());
                    detail.setUploadCost(d);
                    getUnitBudgetService().saveKettleCalculatedExpenses(e.getDetailId(), detail);
                    try {
                        getUnitBudgetService().updatePnLInVoucherData(e.getDetailId(), businessDate, voucherDataList);
                    } catch (DataUpdationException ex) {
                        LOG.error("Exception While Updating Voucher Data {} :{}",e.getUnitId(),businessDate,ex);
                    }

                    // update gift card Offer to PNL Data
                    getUnitBudgetService().updateGiftCardOfferData(e.getDetailId(), e.getUnitId(), businessDate);
                }
            });
        }
    }


    private AdjustmentAggregate createCurrentAdjustmentAggregate(int unitId, int month, int year, Date businessDate) {
        List<PnlAdjustmentDetail> adjustmentAggregate = getExpenseService().getAdjustmentAggregate(unitId, month, year, businessDate);
        AdjustmentAggregate aggregateObject = new AdjustmentAggregate();
        for (PnlAdjustmentDetail aggregate : adjustmentAggregate) {
            if (aggregate.getAdjustmentType().equals("EARNING")) {
                aggregateObject.setRevenueAdjustment(aggregate.getAdjustmentValue());
            }
            if (aggregate.getAdjustmentType().equals("COST")) {
                aggregateObject.setCostAdjustment(aggregate.getAdjustmentValue());
            }
        }
        return aggregateObject;
    }

    private void createMTDAdjustmentAggregate(UnitExpenditureDetail mtd, int unitId, int month, int year) {

        List<PnlAdjustmentDetail> adjustmentAggregate = getExpenseService().getMTDAdjustmentAggregate(unitId, month, year);
        AdjustmentAggregate aggregateObject = new AdjustmentAggregate();
        for (PnlAdjustmentDetail aggregate : adjustmentAggregate) {
            if (aggregate.getAdjustmentType().equals("EARNING")) {
                aggregateObject.setRevenueAdjustment(aggregate.getAdjustmentValue());
            }
            if (aggregate.getAdjustmentType().equals("COST")) {
                aggregateObject.setCostAdjustment(aggregate.getAdjustmentValue());
            }
        }
        BudgetUtils.setAdjustmentAggregate(mtd, aggregateObject);
    }

    /**
     * @param businessDate
     */
    protected void generateMTDPnLReports(Date businessDate) {
        LOG.info("Running MTD for business date {}", businessDate);
        List<UnitExpenditureDetail> expenditures = getUnitBudgetService().getUnitExpenditureDetail(businessDate,
                CalculationStatus.PENDING_MTD_CALCULATION, CalculationType.CURRENT);
        if (expenditures != null && !expenditures.isEmpty()) {
            expenditures.parallelStream().forEach(e -> {
                UnitBudgetoryDetail budget = getUnitBudgetService().getUnitBudgetDetail(e.getUnitId(), e.getMonth(),
                        e.getYear());
                if (budget == null) {
                    getUnitBudgetService().markAsBudgetNotAvailable(e.getDetailId());
                } else {
                    CalculationStatus finalStatus = null;
                    UnitExpenditureDetail mtdExpense = null;
                    if (e.getDay() == 1) {
                        mtdExpense = null;
                    } else {
                        mtdExpense = getUnitBudgetService().getLatestUnitExpenditureDetail(e.getUnitId(), e.getMonth(),
                                e.getYear(), CalculationStatus.COMPLETED, CalculationType.MTD);
                        String message = null;
                        if (mtdExpense == null) {
                            message = String.format("Did Not Find MTD Calculation for Unit %s , Month %d, Year %d",
                                    e.getUnitName(), e.getMonth(), e.getYear());
                        } else if (mtdExpense.getDay() != e.getDay() - 1) {
                            message = String.format("Missing MTD Calculation for Unit %s , Month %d, Year %d, Day %d",
                                    e.getUnitName(), e.getMonth(), e.getYear(), e.getDay() - 1);
                        } else if (!mtdExpense.getStatus().equals(CalculationStatus.COMPLETED.name())) {
                            message = String.format("Last Days MTD Had Errors for Unit %s , Month %d, Year %d, Day %d",
                                    e.getUnitName(), e.getMonth(), e.getYear(), e.getDay() - 1);
                        }
                        if (message != null) {
                            finalStatus = mtdExpense == null ? CalculationStatus.COMPLETED_WITH_MISSING_DAYS
                                    : CalculationStatus.valueOf(mtdExpense.getStatus());
                            SlackNotificationService.getInstance().sendNotification(
                                    getEnvironmentProperties().getEnvironmentType(), "Kettle", null,
                                    SlackNotification.SYSTEM_ERRORS
                                            .getChannel(getEnvironmentProperties().getEnvironmentType()),
                                    message);
                        }
                    }
                    UnitExpenditureDetail mtd = null;
                    if (mtdExpense == null && e.getDay() == 1) {
                        mtd = e.copy();
                        setRentData(mtd, budget);

                    } else if (mtdExpense != null) {
                        mtd = mtdExpense.copy();
                        addToMTD(mtd, e, budget);
                    }
                    if (mtd != null) {
                        if (finalStatus != null) {
                            mtd.setStatus(finalStatus.name());
                        }
                        mtd.setCalculation(CalculationType.MTD.name());
                        calculatePnLData(mtd);
                        getUnitBudgetService().savePnL(mtd);
                        if (finalStatus == null) {
                            getUnitBudgetService().markAsCompleted(e.getDetailId());
                            getUnitBudgetService().markAsCompleted(mtd.getDetailId());
                        }
                    } else {
                        getUnitBudgetService().markAsFailed(e.getDetailId());
                        String message = "Failed To generate PnL for Unit " + e.getUnitId() + "{" + e.getUnitName() + "} For Business Date ::" + e.getBusinessDate();
                        SlackNotificationService.getInstance().sendNotification(
                                getEnvironmentProperties().getEnvironmentType(), "Kettle", null,
                                SlackNotification.PNL_NOTIFY
                                        .getChannel(getEnvironmentProperties().getEnvironmentType()),
                                message);
                    }
                }
            });
        }
    }

    /**
     * @param mtd
     */
    private void calculatePnLData(UnitExpenditureDetail mtd) {
        BudgetUtils.setCalculatedData(mtd, null);
    }


    /**
     * @param mtd
     * @param e
     */
    private void addToMTD(UnitExpenditureDetail mtd, UnitExpenditureDetail e, UnitBudgetoryDetail budget) {
        mtd.setDay(e.getDay());
        mtd.setBusinessDate(e.getBusinessDate());
        mtd.setDayClosureId(e.getDayClosureId());
        mtd.setSumoClosureId(e.getSumoClosureId());
        setRevenue(mtd, e);
        setInventoryAggregate(mtd, e);
        setCommissions(mtd, e);
        setWastageAggregate(mtd, e);
        setConsumablesAggregate(mtd, e);
        setConsumablesSubCategoryAggregate(mtd, e);
        setServiceAggregate(mtd, e);
        setRentData(mtd, budget);
        setExpenseData(mtd);
        createMTDAdjustmentAggregate(mtd, e.getUnitId(), e.getMonth(), e.getYear());
        DirectCost directCost = getUnitBudgetService().setDirectCostData(budget, e.getDay(), e.getMonth(), e.getYear());
        BudgetUtils.setDirectCost(mtd, directCost);
		/*ElectricityAggregate aggregate = getElectricityData(mtd, budget);
		BudgetUtils.setElectricityAggregate(mtd, aggregate);*/
    }


    private void setServiceAggregate(UnitExpenditureDetail mtd, UnitExpenditureDetail b) {
        mtd.setSecurityGuardCharges(b.getSecurityGuardCharges());
        mtd.setOtherServiceCharges(b.getOtherServiceCharges());
        mtd.setFuelCharges(b.getFuelCharges());
        mtd.setLogisticCharges(b.getLogisticCharges());
        mtd.setCommunicationInternet(b.getCommunicationInternet());
        mtd.setCommunicationTelephone(b.getCommunicationTelephone());
        mtd.setCommunicationILL(b.getCommunicationILL());
        mtd.setPayrollProcessingFee(b.getPayrollProcessingFee());
        mtd.setNewsPaper(b.getNewsPaper());
        mtd.setStaffWelfareExpenses(b.getStaffWelfareExpenses());
        mtd.setCourierCharges(b.getCourierCharges());
        mtd.setPrintingAndStationary(b.getPrintingAndStationary());
        mtd.setBusinessPromotion(b.getBusinessPromotion());
        mtd.setLegalCharges(b.getLegalCharges());
        mtd.setProfessionalCharges(b.getProfessionalCharges());
        mtd.setCleaningCharges(b.getCleaningCharges());
        mtd.setPestControlCharges(b.getPestControlCharges());
        mtd.setTechnologyTraining(b.getTechnologyTraining());
        mtd.setCorporateMarketingDigital(b.getCorporateMarketingDigital());
        mtd.setCorporateMarketingAdvOffline(b.getCorporateMarketingAdvOffline());
        mtd.setCorporateMarketingChannelPartner(b.getCorporateMarketingChannelPartner());
        mtd.setCorporateMarketingAdvOnline(b.getCorporateMarketingAdvOnline());
        mtd.setCorporateMarketingOutdoor(b.getCorporateMarketingOutdoor());
        mtd.setCorporateMarketingAgencyFees(b.getCorporateMarketingAgencyFees());
        mtd.setDeliveryChargesVariable(b.getDeliveryChargesVariable());
        mtd.setConveyanceMarketing(b.getConveyanceMarketing());
        mtd.setConveyanceOperations(b.getConveyanceOperations());
        mtd.setConveyanceOthers(b.getConveyanceOthers());
        mtd.setAuditFee(b.getAuditFee());
        mtd.setAuditFeeOutOfPocket(b.getAuditFeeOutOfPocket());
        mtd.setBrokerage(b.getBrokerage());
        mtd.setCharityAndDonations(b.getCharityAndDonations());
        mtd.setDomesticTicketsAndHotels(b.getDomesticTicketsAndHotels());
        mtd.setInternationalTicketsAndHotels(b.getInternationalTicketsAndHotels());
        mtd.setHouseKeepingCharges(b.getHouseKeepingCharges());
        mtd.setLateFeeCharges(b.getLateFeeCharges());
        mtd.setMarketingDataAnalysis(b.getMarketingDataAnalysis());
        mtd.setMiscellaneousExpenses(b.getMiscellaneousExpenses());
        mtd.setPenalty(b.getPenalty());
        mtd.setPhotoCopyExpenses(b.getPhotoCopyExpenses());
        mtd.setQcrExpense(b.getQcrExpense());
        mtd.setRecuritmentConsultants(b.getRecuritmentConsultants());
        mtd.setRocFees(b.getRocFees());
        mtd.setDebitCreditWrittenOff(b.getDebitCreditWrittenOff());
        mtd.setDifferenceInExchange(b.getDifferenceInExchange());
        mtd.setRnDEngineeringExpenses(b.getRnDEngineeringExpenses());
        mtd.setCapitalImprovementExpenses(b.getCapitalImprovementExpenses());
        mtd.setLeaseHoldImprovements(b.getLeaseHoldImprovements());
        mtd.setMarketingLaunch(b.getMarketingLaunch());
        mtd.setCorporateMarketingPhotography(b.getCorporateMarketingPhotography());
        mtd.setOdcRental(b.getOdcRental());
        mtd.setCogsOthers(b.getCogsOthers());
        mtd.setCommissionChange(b.getCommissionChange());

        mtd.setVehicleRegularMaintenanceHq(b.getVehicleRegularMaintenanceHq());
        mtd.setBuildingMaintenanceHq(b.getBuildingMaintenanceHq());
        mtd.setComputerItMaintenanceHq(b.getComputerItMaintenanceHq());
        mtd.setEquipmentMaintenanceHq(b.getEquipmentMaintenanceHq());
        mtd.setMarketingNpiHq(b.getMarketingNpiHq());
        mtd.setLicenseExpenses(b.getLicenseExpenses());
        mtd.setCorporateMarketingAtlRadio(b.getCorporateMarketingAtlRadio());
        mtd.setCorporateMarketingAtlTv(b.getCorporateMarketingAtlTv());
        mtd.setCorporateMarketingAtlPrintAd(b.getCorporateMarketingAtlPrintAd());
        mtd.setCorporateMarketingAtlCinema(b.getCorporateMarketingAtlCinema());
        mtd.setCorporateMarketingAtlDigital(b.getCorporateMarketingAtlDigital());
        mtd.setLogisticInterstateColdVehicle(b.getLogisticInterstateColdVehicle());
        mtd.setLogisticInterstateNonColdVehicle(b.getLogisticInterstateNonColdVehicle());
        mtd.setLogisticInterstateAir(b.getLogisticInterstateAir());
        mtd.setLogisticInterstateRoad(b.getLogisticInterstateRoad());
        mtd.setLogisticInterstateTrain(b.getLogisticInterstateTrain());
        mtd.setAirConditionerAmc(b.getAirConditionerAmc());
        mtd.setCorporateMarketingSms(b.getCorporateMarketingSms());
        mtd.setEmployeeFacilitationExpenses(b.getEmployeeFacilitationExpenses());
       mtd.setProntoAMC(b.getProntoAMC());
       mtd.setOthersMaintenance(b.getOthersMaintenance());
       mtd.setTechnologyPlatformCharges(b.getTechnologyPlatformCharges());
       mtd.setInterestOnTermLoan(b.getInterestOnTermLoan());
       mtd.setTechnologyOthers(b.getTechnologyOthers());
       mtd.setSystemRental(b.getSystemRental());
        mtd.setRoRental(b.getRoRental());
        mtd.setInsurnaceAccidental(b.getInsurnaceAccidental());
        mtd.setDgRental(b.getDgRental());
        mtd.setOthersAMC(b.getOthersAMC());
        mtd.setMusicRentals(b.getMusicRentals());
//        mtd.setVoucherTransactionCharges(b.getVoucherTransactionCharges());
        mtd.setInsuranceAssets(b.getInsuranceAssets());
        mtd.setInsuranceCGL(b.getInsuranceCGL());
        mtd.setInsurnaceMedical(b.getInsurnaceMedical());
//        mtd.setPropertyFixRent(b.getPropertyFixRent());
        mtd.setPettyCashRentals(b.getPettyCashRentals());
//        mtd.setEnergyDGRunningCafe();
        mtd.setEdcRental(b.getEdcRental());


    }

    private void setWastageAggregate(UnitExpenditureDetail d, UnitExpenditureDetail b) {

        d.setUnsatifiedCustomerCost(AppUtils.add(d.getUnsatifiedCustomerCost(), b.getUnsatifiedCustomerCost()));
        d.setExpiryWastage(AppUtils.add(d.getExpiryWastage(), b.getExpiryWastage()));
        d.setWastageOther(AppUtils.add(d.getWastageOther(), b.getWastageOther()));
        d.setMarketingAndSampling(AppUtils.add(d.getMarketingAndSampling(), b.getMarketingAndSampling()));
        d.setTrainingCogs(AppUtils.add(d.getTrainingCogs(), b.getTrainingCogs()));

        d.setUnsatifiedCustomerCostTax(AppUtils.add(d.getUnsatifiedCustomerCostTax(), b.getUnsatifiedCustomerCostTax()));
        d.setExpiryWastageTax(AppUtils.add(d.getExpiryWastageTax(), b.getExpiryWastageTax()));
        d.setWastageOtherTax(AppUtils.add(d.getWastageOtherTax(), b.getWastageOtherTax()));
        d.setMarketingAndSamplingTax(AppUtils.add(d.getMarketingAndSamplingTax(), b.getMarketingAndSamplingTax()));
        d.setTrainingCogsTax(AppUtils.add(d.getTrainingCogsTax(), b.getTrainingCogsTax()));

    }


    private void setInventoryAggregate(UnitExpenditureDetail d, UnitExpenditureDetail b) {
        d.setDineInCogs(AppUtils.add(d.getDineInCogs(), b.getDineInCogs()));
        d.setDeliveryCogs(AppUtils.add(d.getDeliveryCogs(), b.getDeliveryCogs()));
        d.setEmployeeMealCogs(AppUtils.add(d.getEmployeeMealCogs(), b.getEmployeeMealCogs()));
        d.setStockVariance(AppUtils.add(d.getStockVariance(), b.getStockVariance()));

        d.setVariancePCC(AppUtils.add(d.getVariancePCC(), b.getVariancePCC()));
        d.setVariancePCCTax(AppUtils.add(d.getVariancePCCTax(), b.getVariancePCCTax()));
        d.setVarianceYC(AppUtils.add(d.getVarianceYC(), b.getVarianceYC()));
        d.setVarianceYCTax(AppUtils.add(d.getVarianceYCTax(), b.getVarianceYCTax()));
        d.setVarianceZero(AppUtils.add(d.getVarianceZero(), b.getVarianceZero()));
        d.setVarianceZeroTax(AppUtils.add(d.getVarianceZeroTax(), b.getVarianceZeroTax()));


        d.setDineInCogsTax(AppUtils.add(d.getDineInCogsTax(), b.getDineInCogsTax()));
        d.setDeliveryCogsTax(AppUtils.add(d.getDeliveryCogsTax(), b.getDeliveryCogsTax()));
        d.setEmployeeMealCogsTax(AppUtils.add(d.getEmployeeMealCogsTax(), b.getEmployeeMealCogsTax()));
        d.setStockVarianceTax(AppUtils.add(d.getStockVarianceTax(), b.getStockVarianceTax()));
    }

    private void setConsumablesAggregate(UnitExpenditureDetail d, UnitExpenditureDetail b) {
        d.setConsumable(AppUtils.add(d.getConsumable(), b.getConsumable()));
//        d.setFixedAssets(AppUtils.add(d.getFixedAssets(), b.getFixedAssets()));

        d.setDepreciationOfFurnitureFixture(AppUtils.add(d.getDepreciationOfFurnitureFixture(), b.getDepreciationOfFurnitureFixture()));
        d.setDepreciationOfOfficeEquipment(AppUtils.add(d.getDepreciationOfOfficeEquipment(), b.getDepreciationOfOfficeEquipment()));
        d.setDepreciationOfKitchenEquipment(AppUtils.add(d.getDepreciationOfKitchenEquipment(), b.getDepreciationOfKitchenEquipment()));
        d.setDepreciationOfEquipment(AppUtils.add(d.getDepreciationOfEquipment(), b.getDepreciationOfEquipment()));
        d.setDepreciationOfIt((AppUtils.add(d.getDepreciationOfIt(), b.getDepreciationOfIt())));
        d.setDepreciationOfVehicle(AppUtils.add(d.getDepreciationOfVehicle(), b.getDepreciationOfVehicle()));
        d.setDepreciationOfOthers((AppUtils.add(d.getDepreciationOfOthers(), b.getDepreciationOfOthers())));

        d.setFixedAssetsCapex(AppUtils.add(d.getFixedAssetsCapex(), b.getFixedAssetsCapex()));
        d.setConsumableOthers(AppUtils.add(d.getConsumableOthers(), b.getConsumableOthers()));
        d.setConsumableMarketing(AppUtils.add(d.getConsumableMarketing(), b.getConsumableMarketing()));

        d.setConsumableCutlery(AppUtils.add(d.getConsumableCutlery(), b.getConsumableCutlery()));
        d.setConsumableEquipment(AppUtils.add(d.getConsumableEquipment(), b.getConsumableEquipment()));
        d.setConsumableStationary(AppUtils.add(d.getConsumableStationary(), b.getConsumableStationary()));
        d.setConsumableUniform(AppUtils.add(d.getConsumableUniform(), b.getConsumableUniform()));
        d.setConsumableUtility(AppUtils.add(d.getConsumableUtility(), b.getConsumableUtility()));

        d.setConsumableLhi(AppUtils.add(d.getConsumableLhi(), b.getConsumableLhi()));
        d.setConsumableIt(AppUtils.add(d.getConsumableIt(), b.getConsumableIt()));
        d.setConsumableMaintenance(AppUtils.add(d.getConsumableMaintenance(), b.getConsumableMaintenance()));
        d.setConsumableOfficeEquipment(AppUtils.add(d.getConsumableOfficeEquipment(), b.getConsumableOfficeEquipment()));
        d.setConsumableChaiMonk(AppUtils.add(d.getConsumableChaiMonk(), b.getConsumableChaiMonk()));
        d.setConsumableKitchenEquipment(AppUtils.add(d.getConsumableKitchenEquipment(), b.getConsumableKitchenEquipment()));


        d.setConsumableTax(AppUtils.add(d.getConsumableTax(), b.getConsumableTax()));
        d.setFixedAssetsTax(AppUtils.add(d.getFixedAssetsTax(), b.getFixedAssetsTax()));
        d.setFixedAssetsCapexTax(AppUtils.add(d.getFixedAssetsCapexTax(), b.getFixedAssetsCapexTax()));
        d.setConsumableOthersTax(AppUtils.add(d.getConsumableOthersTax(), b.getConsumableOthersTax()));
        d.setConsumableMarketingTax(AppUtils.add(d.getConsumableMarketingTax(), b.getConsumableMarketingTax()));

        // TODO Abhishek - done

        d.setFixedAssetsEquipmentTax(AppUtils.add(d.getFixedAssetsEquipmentTax(), b.getFixedAssetsEquipmentTax()));
        d.setFixedAssetFurnitureTax(AppUtils.add(d.getFixedAssetFurnitureTax(), b.getFixedAssetFurnitureTax()));
        d.setFixedAssetsItTax(AppUtils.add(d.getFixedAssetsItTax(), b.getFixedAssetsItTax()));
        d.setFixedAssetsKitchenEquipmentTax(AppUtils.add(d.getFixedAssetsKitchenEquipmentTax(), b.getFixedAssetsKitchenEquipmentTax()));
        d.setFixedAssetsOfficeEquipmentTax(AppUtils.add(d.getFixedAssetsOfficeEquipmentTax(), b.getFixedAssetsOfficeEquipmentTax()));
        d.setFixedAssetsVehicleTax(AppUtils.add(d.getFixedAssetsVehicleTax(), b.getFixedAssetsVehicleTax()));
        d.setFixedAssetsOthersSubCategoryCafeTax(AppUtils.add(d.getFixedAssetsOthersSubCategoryCafeTax(), b.getFixedAssetsOthersSubCategoryCafeTax()));
        d.setFixedAssetsEquipmentHq(AppUtils.add(d.getFixedAssetsEquipmentHq(), b.getFixedAssetsEquipmentHq()));
        d.setFixedAssetFurnitureHq(AppUtils.add(d.getFixedAssetFurnitureHq(), b.getFixedAssetFurnitureHq()));
        d.setFixedAssetsItHq(AppUtils.add(d.getFixedAssetsItHq(), b.getFixedAssetsItHq()));
        d.setFixedAssetsKitchenEquipmentHq(AppUtils.add(d.getFixedAssetsKitchenEquipmentHq(), b.getFixedAssetsKitchenEquipmentHq()));
        d.setFixedAssetsOfficeEquipmentHq(AppUtils.add(d.getFixedAssetsOfficeEquipmentHq(), b.getFixedAssetsOfficeEquipmentHq()));
        d.setFixedAssetsVehicleHq(AppUtils.add(d.getFixedAssetsVehicleHq(), b.getFixedAssetsVehicleHq()));
        d.setFixedAssetsOthersSubCategoryHq(AppUtils.add(d.getFixedAssetsOthersSubCategoryHq(), b.getFixedAssetsOthersSubCategoryHq()));
        d.setFixedAssetsEquipmentHqTax(AppUtils.add(d.getFixedAssetsEquipmentHqTax(), b.getFixedAssetsEquipmentHqTax()));
        d.setFixedAssetFurnitureHqTax(AppUtils.add(d.getFixedAssetFurnitureHqTax(), b.getFixedAssetFurnitureHqTax()));
        d.setFixedAssetsItHqTax(AppUtils.add(d.getFixedAssetsItHqTax(), b.getFixedAssetsItHqTax()));
        d.setFixedAssetsKitchenEquipmentHqTax(AppUtils.add(d.getFixedAssetsKitchenEquipmentHqTax(), b.getFixedAssetsKitchenEquipmentHqTax()));
        d.setFixedAssetsOfficeEquipmentHqTax(AppUtils.add(d.getFixedAssetsOfficeEquipmentHqTax(), b.getFixedAssetsOfficeEquipmentHqTax()));
        d.setFixedAssetsVehicleHqTax(AppUtils.add(d.getFixedAssetsVehicleHqTax(), b.getFixedAssetsVehicleHqTax()));
        d.setFixedAssetsOthersSubCategoryHqTax(AppUtils.add(d.getFixedAssetsOthersSubCategoryHqTax(), b.getFixedAssetsOthersSubCategoryHqTax()));


        d.setFixedAssetsEquipment(AppUtils.add(d.getFixedAssetsEquipment(), b.getFixedAssetsEquipment()));
        d.setFixedAssetFurniture(AppUtils.add(d.getFixedAssetFurniture(), b.getFixedAssetFurniture()));
        d.setFixedAssetsIT(AppUtils.add(d.getFixedAssetsIT(), b.getFixedAssetsIT()));
        d.setFixedAssetsKitchenEquipment(
                AppUtils.add(d.getFixedAssetsKitchenEquipment(), b.getFixedAssetsKitchenEquipment()));
        d.setFixedAssetsOfficeEquipment(
                AppUtils.add(d.getFixedAssetsOfficeEquipment(), b.getFixedAssetsOfficeEquipment()));
        d.setFixedAssetsVehicle(AppUtils.add(d.getFixedAssetsVehicle(), b.getFixedAssetsVehicle()));
        d.setFixedAssetsOthersSubCategory(
                AppUtils.add(d.getFixedAssetsOthersSubCategory(), b.getFixedAssetsOthersSubCategory()));

        d.setFixedAssetsDepreciation(AppUtils.add(d.getFixedAssetsDepreciation(), b.getFixedAssetsDepreciation()));
        d.setFixedAssetsLost(AppUtils.add(d.getFixedAssetsLost(), b.getFixedAssetsLost()));
        d.setFixedAssetsDamage(AppUtils.add(d.getFixedAssetsDamage(), b.getFixedAssetsDamage()));
    }

    private void setConsumablesSubCategoryAggregate(UnitExpenditureDetail d, UnitExpenditureDetail e) {
        d.setConsumableUtilityTax(AppUtils.add(d.getConsumableUtilityTax(), e.getConsumableUtilityTax()));
        d.setConsumableStationaryTax(AppUtils.add(d.getConsumableStationaryTax(), e.getConsumableStationaryTax()));
        d.setConsumableUniformTax(AppUtils.add(d.getConsumableUniformTax(), e.getConsumableUniformTax()));
        d.setConsumableEquipmentTax(AppUtils.add(d.getConsumableEquipmentTax(), e.getConsumableEquipmentTax()));
        d.setConsumableCutleryTax(AppUtils.add(d.getConsumableCutleryTax(), e.getConsumableCutleryTax()));
        d.setConsumableDisposableTax(AppUtils.add(d.getConsumableDisposableTax(), e.getConsumableDisposableTax()));

        d.setConsumableLhiTax(AppUtils.add(d.getConsumableLhiTax(), e.getConsumableLhiTax()));
        d.setConsumableItTax(AppUtils.add(d.getConsumableItTax(), e.getConsumableItTax()));
        d.setConsumableMaintenanceTax(AppUtils.add(d.getConsumableMaintenanceTax(), e.getConsumableMaintenanceTax()));
        d.setConsumableOfficeEquipmentTax(AppUtils.add(d.getConsumableOfficeEquipmentTax(), e.getConsumableOfficeEquipmentTax()));
        d.setConsumableChaiMonkTax(AppUtils.add(d.getConsumableChaiMonkTax(), e.getConsumableChaiMonkTax()));
        d.setConsumableKitchenEquipmentTax(AppUtils.add(d.getConsumableKitchenEquipmentTax(), e.getConsumableKitchenEquipmentTax()));
    }

    private void setCommissions(UnitExpenditureDetail d, UnitExpenditureDetail b) {
        d.setCreditCardTransactionCharges(
                AppUtils.add(d.getCreditCardTransactionCharges(), b.getCreditCardTransactionCharges()));
        d.setVoucherTransactionCharges(
                AppUtils.add(d.getVoucherTransactionCharges(), b.getVoucherTransactionCharges()));
        d.setWalletsTransactionCharges(
                AppUtils.add(d.getWalletsTransactionCharges(), b.getWalletsTransactionCharges()));
        d.setCommissionChannelPartners(
                AppUtils.add(d.getCommissionChannelPartners(), b.getCommissionChannelPartners()));
        d.setCancellationChargesChannelPartners(
                AppUtils.add(d.getCancellationChargesChannelPartners(), b.getCancellationChargesChannelPartners()));
        //d.setCommissionOthers(AppUtils.add(d.getCommissionOthers(), b.getCommissionOthers()));
    }

    private void setRevenue(UnitExpenditureDetail d, UnitExpenditureDetail b) {

        d.setTicket(d.getTicket() + b.getTicket());
        d.setRevenue(AppUtils.add(d.getRevenue(), b.getRevenue()));
        d.setNetSales(AppUtils.add(d.getNetSales(), b.getNetSales()));
        d.setNetRevenue(AppUtils.add(d.getNetRevenue(), b.getNetRevenue()));
        d.setApc(AppUtils.divide(d.getRevenue(), new BigDecimal(d.getTicket())));
        d.setGmv(AppUtils.add(d.getGmv(), b.getGmv()));
        d.setDiscount(AppUtils.add(d.getDiscount(), b.getDiscount()));
        d.setDiscountLoyalty(AppUtils.add(d.getDiscountLoyalty(), b.getDiscountLoyalty()));
        d.setDiscountMarketing(AppUtils.add(d.getDiscountMarketing(), b.getDiscountMarketing()));
        d.setDiscountOps(AppUtils.add(d.getDiscountOps(), b.getDiscountOps()));
        d.setDiscountBd(AppUtils.add(d.getDiscountBd(), b.getDiscountBd()));
        d.setDiscountEmployeeFico(AppUtils.add(d.getDiscountEmployeeFico(), b.getDiscountEmployeeFico()));

        d.setEmpDiscountLoyalty(AppUtils.add(d.getEmpDiscountLoyalty(), b.getEmpDiscountLoyalty()));
        d.setEmpDiscountMarketing(AppUtils.add(d.getEmpDiscountMarketing(), b.getEmpDiscountMarketing()));
        d.setEmpDiscountOps(AppUtils.add(d.getEmpDiscountOps(), b.getEmpDiscountOps()));
        d.setEmpDiscountBd(AppUtils.add(d.getEmpDiscountBd(), b.getEmpDiscountBd()));
        d.setEmpDiscountEmployeeFico(AppUtils.add(d.getEmpDiscountEmployeeFico(), b.getEmpDiscountEmployeeFico()));

        d.setDineInTicket(d.getDineInTicket() + b.getDineInTicket());
        d.setDineInSales(AppUtils.add(d.getDineInSales(), b.getDineInSales()));
        d.setDineInApc(AppUtils.divide(d.getDineInSales(), new BigDecimal(d.getDineInTicket())));
        d.setDineInGmv(AppUtils.add(d.getDineInGmv(), b.getDineInGmv()));
        d.setDineInDiscount(AppUtils.add(d.getDineInDiscount(), b.getDineInDiscount()));
        d.setDineInDiscountLoyalty(AppUtils.add(d.getDineInDiscountLoyalty(), b.getDineInDiscountLoyalty()));
        d.setDineInDiscountMarketing(AppUtils.add(d.getDineInDiscountMarketing(), b.getDineInDiscountMarketing()));
        d.setDineInDiscountOps(AppUtils.add(d.getDineInDiscountOps(), b.getDineInDiscountOps()));
        d.setDineInDiscountBd(AppUtils.add(d.getDineInDiscountBd(), b.getDineInDiscountBd()));
        d.setDineInDiscountEmployeeFico(AppUtils.add(d.getDineInDiscountEmployeeFico(), b.getDineInDiscountEmployeeFico()));
        d.setDeliveryTicket(d.getDeliveryTicket() + b.getDeliveryTicket());
        d.setDeliverySales(AppUtils.add(d.getDeliverySales(), b.getDeliverySales()));
        d.setDeliveryGmv(AppUtils.add(d.getDeliveryGmv(), b.getDeliveryGmv()));
        d.setDeliveryApc(AppUtils.divide(d.getDeliverySales(), new BigDecimal(d.getDeliveryTicket())));
        d.setDeliveryDiscount(AppUtils.add(d.getDeliveryDiscount(), b.getDeliveryDiscount()));
        d.setDeliveryDiscountLoyalty(AppUtils.add(d.getDeliveryDiscountLoyalty(), b.getDeliveryDiscountLoyalty()));
        d.setDeliveryDiscountMarketing(AppUtils.add(d.getDeliveryDiscountMarketing(), b.getDeliveryDiscountMarketing()));
        d.setDeliveryDiscountOps(AppUtils.add(d.getDeliveryDiscountOps(), b.getDeliveryDiscountOps()));
        d.setDeliveryDiscountBd(AppUtils.add(d.getDeliveryDiscountBd(), b.getDeliveryDiscountBd()));
        d.setDeliveryDiscountEmployeeFico(AppUtils.add(d.getDeliveryDiscountEmployeeFico(), b.getDeliveryDiscountEmployeeFico()));
        d.setEmployeeMealSales(AppUtils.add(d.getEmployeeMealSales(), b.getEmployeeMealSales()));
        d.setEmployeeMealGmv(AppUtils.add(d.getEmployeeMealGmv(), b.getEmployeeMealGmv()));
        d.setEmployeeMealTicket(d.getEmployeeMealTicket() + b.getEmployeeMealTicket());
        d.setGiftCardSale(AppUtils.add(d.getGiftCardSale(), b.getGiftCardSale()));
        d.setGiftCardRedemption(AppUtils.add(d.getGiftCardRedemption(), b.getGiftCardRedemption()));
        d.setGiftCardNetSale(AppUtils.subtract(d.getGiftCardSale(), d.getGiftCardRedemption()));

        d.setDeliveryNetSales(AppUtils.add(d.getDeliveryNetSales(), b.getDeliveryNetSales()));
        d.setDeliveryGiftCardSale(AppUtils.add(d.getDeliveryGiftCardSale(), b.getDeliveryGiftCardSale()));
        d.setDeliveryGiftCardRedemption(
                AppUtils.add(d.getDeliveryGiftCardRedemption(), b.getDeliveryGiftCardRedemption()));
        d.setDeliveryGiftCardNetSale(AppUtils.subtract(d.getDeliveryGiftCardSale(), d.getDeliveryGiftCardRedemption()));

        d.setDineInNetSales(AppUtils.add(d.getDineInNetSales(), b.getDineInNetSales()));
        d.setDineInGiftCardSale(AppUtils.add(d.getDineInGiftCardSale(), b.getDineInGiftCardSale()));
        d.setDineInGiftCardRedemption(AppUtils.add(d.getDineInGiftCardRedemption(), b.getDineInGiftCardRedemption()));
        d.setDineInGiftCardNetSale(AppUtils.subtract(d.getDineInGiftCardSale(), b.getDineInGiftCardRedemption()));
        d.setGiftCardOffer(AppUtils.add(d.getGiftCardOffer(), b.getGiftCardOffer()));

    }


    /**
     * @param e
     * @param budget
     */
    private ElectricityAggregate getElectricityData(UnitExpenditureDetail e, UnitBudgetoryDetail budget) {
        ElectricityAggregate aggregate = new ElectricityAggregate();
        if (budget == null) {
            return aggregate;
        }
        Map<ElectricityBillType, Map<Integer, Integer>> map = getExpenseService().getMeterReading(e.getUnitId(),
                e.getMonth(), e.getYear());
        if (map != null && map.size() > 0) {
            for (ElectricityBillType key : map.keySet()) {
                switch (key) {
                    case DG:
                        Integer dgReading = map.get(key).get(1);
                        if (dgReading == null) {
                            aggregate.setEnergyDG(BigDecimal.ZERO);
                        } else {
                            aggregate.setEnergyDG(calculateDGExpense(dgReading, budget));
                        }
                        break;
                    case ELECTRICITY:
                        for (Integer meterNo : map.get(key).keySet()) {
                            Integer meterReading = map.get(key).get(meterNo);
                            if (meterReading != null) {
                                aggregate.setEnergyElectricity(aggregate.getEnergyElectricity()
                                        .add(calculateElectricityExpense(meterNo, meterReading, budget)));
                            }
                        }
                        break;

                }
            }
        }
        return aggregate;

    }

    private BigDecimal calculateDGExpense(Integer dgReading, UnitBudgetoryDetail budget) {
        BigDecimal cost = new BigDecimal(0d);
        cost = cost.add(budget.getDgMeterFixedCharge());
        cost = cost.add(AppUtils.multiply(new BigDecimal(dgReading), budget.getDgMeterPerUnitCharge()));
        cost = cost.add(AppUtils.percentOf(cost, budget.getDgMeterTaxPercentage()));
        cost = cost.add(budget.getDgMeterOtherCharge());
        return cost;
    }

    private BigDecimal calculateElectricityExpense(Integer meterNo, Integer meterReading, UnitBudgetoryDetail budget) {
        BigDecimal cost = new BigDecimal(0d);
        switch (meterNo) {
            case 1:
                cost = cost.add(budget.getElectricityMeter1FixedCharge());
                cost = cost
                        .add(AppUtils.multiply(new BigDecimal(meterReading), budget.getElectricityMeter1PerUnitCharge()));
                cost = cost.add(AppUtils.percentOf(cost, budget.getElectricityMeter1TaxPercentage()));
                cost = cost.add(budget.getElectricityMeter1OtherCharge());
                break;
            case 2:
                cost = cost.add(budget.getElectricityMeter2FixedCharge());
                cost = cost
                        .add(AppUtils.multiply(new BigDecimal(meterReading), budget.getElectricityMeter2PerUnitCharge()));
                cost = cost.add(AppUtils.percentOf(cost, budget.getElectricityMeter2TaxPercentage()));
                cost = cost.add(budget.getElectricityMeter2OtherCharge());
                break;
            case 3:
                cost = cost.add(budget.getElectricityMeter3FixedCharge());
                cost = cost
                        .add(AppUtils.multiply(new BigDecimal(meterReading), budget.getElectricityMeter3PerUnitCharge()));
                cost = cost.add(AppUtils.percentOf(cost, budget.getElectricityMeter3TaxPercentage()));
                cost = cost.add(budget.getElectricityMeter3OtherCharge());
                break;
        }
        return cost;
    }

    /**
     * @param detail
     * @param e
     */
    private List<VoucherData> setExpenseData(BudgetDetail detail, UnitExpenditureDetail e) {
        List<ExpenseDetailData> expenses = getExpenseService().getUnAccountedPnlExpenses(e.getUnitId(), e.getMonth(),
                e.getYear());


        Map<ExpenseField.ExpenseRecordCategory, BigDecimal> map = new HashMap<>();
        if (expenses != null && expenses.size() > 0) {
            map = getExpenseAggregate(expenses);
            //aggregate = createExpenseData(map);
        }

        List<VoucherData> voucherDataList = getExpenseService().getUnAccountedPnlVoucherExpenses(e.getUnitId());
        if (voucherDataList != null && voucherDataList.size() > 0) {
            getVoucherExpenseAggregate(map, voucherDataList);
        }
        ExpenseAggregate aggregate = createExpenseData(map);
        detail.setExpenses(aggregate);
        return voucherDataList;
    }

    /**
     * @param mtd
     */
    private void setExpenseData(UnitExpenditureDetail mtd) {
        List<ExpenseDetailData> expenses = getExpenseService().getPnlAccountableExpenses(mtd.getUnitId(),
                mtd.getMonth(), mtd.getYear());


        Map<ExpenseField.ExpenseRecordCategory, BigDecimal> map = new HashMap<>();
        if (expenses != null && expenses.size() > 0) {
            map = getExpenseAggregate(expenses);
        }

        List<VoucherData> voucherDataList = getExpenseService().getAccountedPnlVoucherExpenses(mtd.getUnitId(), mtd.getMonth(), mtd.getYear());
        if (voucherDataList != null && voucherDataList.size() > 0) {
            getVoucherExpenseAggregate(map, voucherDataList);
        }
        ExpenseAggregate aggregate = createExpenseData(map);
        BudgetUtils.setExpenseAggregate(mtd, aggregate);
    }

    /**
     * @param map
     * @return
     */
    private ExpenseAggregate createExpenseData(Map<ExpenseRecordCategory, BigDecimal> map) {
        // TODO Abhishek Sirohi
        ExpenseAggregate a = new ExpenseAggregate();
        for (ExpenseRecordCategory cat : map.keySet()) {
            BigDecimal value = map.get(cat);
            switch (cat) {
                case ENERGY_DG_RUNNING_CAFE:
                    a.setEnergyDGRunningCafe(value);
                    break;
//                case TRAVELLING_EXPENSE_ODC:
//                	a.setTravellingExpenseODC(value);
//                	break;
                case TRAVELLING_EXPENSES:
                    a.setTravellingExpense(value);
                    break;
                case WATER_CHARGES_CAFE:
                    a.setWaterChargesCafe(value);
                    break;
                case MARKETING_NPI_CAFE:
                    a.setMarketingNPI(value);
                    break;
                case BUILDING_MAINTENANCE_CAFE:
                    a.setBuildingMaintenance(value);
                    break;
                case COMPUTER_IT_MAINTENANCE_CAFE:
                    a.setComputerMaintenance(value);
                    break;
                case EQUIPMENT_MAINTENANCE_CAFE:
                    a.setEquipmentMaintenance(value);
                    break;
                case VEHICLE_REGULAR_MAINTENANCE_CAFE:
                    a.setVehicleRegularMaintenance(value);
                    break;
                case NONE:
                    break;
                case PARKING_CHARGES_CAFE:
                    a.setParkingCharges(value);
                    break;
                case MAINTENANCE_PEST_CONTROL_CAFE:
                    a.setMaintenancePestControlCafe(value);
                    break;
                case CONVEYANCE_ODC:
                    a.setConveyanceOdc(value);
                    break;
                case CONVEYANCE_OPERATIONS:
                    a.setConveyanceOperation(value);
                    break;
                case CONVEYANCE_MARKETING:
                    a.setConveyanceMarketing(value);
                    break;
                case CONVEYANCE_OTHERS:
                    a.setConveyanceOthers(value);
                    break;
                case COGS_OTHERS:
                    a.setCogsOthers(value);
                    break;
                case MARKETING_DATA_ANALYSIS:
                    a.setMarketingDataAnalysis(value);
                    break;
                case FUEL_CHARGES_CAFE:
                    a.setFuelChargesCafe(value);
                    break;
                case COURIER_CHARGES:
                    a.setCourierChargesCafe(value);
                    break;
                case PRINTING_AND_STATIONARY:
                    a.setPrintingAndStationaryCafe(value);
                    break;
                case BUSINESS_PROMOTION:
                    a.setBusinessPromotionCafe(value);
                    break;
                case CLEANING_CHARGES:
                    a.setCleaningChargesCafe(value);
                    break;
                case PHOTO_COPY_EXPENSES:
                    a.setPhotoCopyExpensesCafe(value);
                    break;
                case COMMISSION_CHANGE_CAFE:
                    a.setCommissionChangeCafe(value);
                    break;
                case STAFF_WELFARE_EXPENSES:
                    a.setStaffWelfareExpensesCafe(value);
                    break;
                case NEWSPAPER_CHARGES_CAFE:
                    a.setNewsPaperCafe(value);
                    break;
                default:
                    a.setExpenseOthers(a.getExpenseOthers().add(value));
                    break;
            }
        }
        return a;
    }

    private Map<ExpenseField.ExpenseRecordCategory, BigDecimal> getExpenseAggregate(List<ExpenseDetailData> expenses) {
        Map<ExpenseField.ExpenseRecordCategory, BigDecimal> map = new HashMap<>();
        for (ExpenseDetailData d : expenses) {
            ExpenseRecordCategory cat = ExpenseRecordCategory.valueOf(d.getBudgetCategory());
            if (!map.containsKey(cat)) {
                map.put(cat, new BigDecimal(0d));
            }
            map.put(cat, AppUtils.add(map.get(cat), d.getAmount()));
        }
        return map;
    }

    private void getVoucherExpenseAggregate(Map<ExpenseField.ExpenseRecordCategory, BigDecimal> map,
                                            List<VoucherData> expenses) {
        for (VoucherData d : expenses) {
            ExpenseRecordCategory cat = ExpenseRecordCategory.valueOf(d.getBudgetCategory());
            if (!map.containsKey(cat)) {
                map.put(cat, new BigDecimal(0d));
            }
            map.put(cat, AppUtils.add(map.get(cat), d.getExpenseAmount()));
        }
    }

    /**
     * @param e
     * @param budget
     */
    private void setRentData(UnitExpenditureDetail e, UnitBudgetoryDetail budget) {
        if (budget == null) {
            e.setPropertyFixRent(BigDecimal.ZERO);
            e.setRevenueShare(BigDecimal.ZERO);
            e.setOnRevenueShare(AppConstants.NO);
            e.setRevenueShareDineIn(BigDecimal.ZERO);
            e.setRevenueShareDelivery(BigDecimal.ZERO);
            return;
        }
        if (AppConstants.getValue(budget.getOnRevenueShare())) {
            BigDecimal revenueShareDineIn = AppUtils.percentOf(e.getDineInNetSales(),
                    budget.getRevenueShareDineInPercent());
            BigDecimal revenueShareDelivery = AppUtils.percentOf(e.getDeliveryNetSales(),
                    budget.getRevenueShareDeliveryPercent());
            BigDecimal revenueShare = AppUtils.add(revenueShareDineIn, revenueShareDelivery);
            if (revenueShare.compareTo(budget.getPropertyFixRent()) > 0) {
                e.setPropertyFixRent(budget.getPropertyFixRent());
                BigDecimal share = AppUtils.subtract(revenueShare, budget.getPropertyFixRent());
                e.setRevenueShare(share);
                e.setRevenueShareDineIn(AppUtils.multiply(AppUtils.divideWithScale10(e.getDineInNetSales(),
                        AppUtils.add(e.getDineInNetSales(), e.getDeliveryNetSales())), share));
                e.setRevenueShareDelivery(AppUtils.multiply(AppUtils.divideWithScale10(e.getDeliveryNetSales(),
                        AppUtils.add(e.getDineInNetSales(), e.getDeliveryNetSales())), share));
            } else {
                e.setPropertyFixRent(budget.getPropertyFixRent());
                e.setRevenueShare(BigDecimal.ZERO);
                e.setRevenueShareDineIn(BigDecimal.ZERO);
                e.setRevenueShareDelivery(BigDecimal.ZERO);
            }
            e.setOnRevenueShare(AppConstants.YES);
        } else {
            e.setPropertyFixRent(budget.getPropertyFixRent());
            e.setRevenueShare(BigDecimal.ZERO);
            e.setRevenueShareDineIn(BigDecimal.ZERO);
            e.setRevenueShareDelivery(BigDecimal.ZERO);
        }
    }

    public void generateReportsPostDayClose() throws DataNotFoundException, IOException, EmailGenerationException {
        Date businessDate = AppUtils.getBusinessDate();
        // Commented to avoid locking while order creation
        /*try {
            runPostClosureProcess(businessDate);
        } catch (Exception e) {
            LOG.error("Error in running post closure processes for cafes", e);
        }*/
        try {
            generateReportsPostDayClose(businessDate);
        } catch (Exception e) {
            LOG.error("Error in generating reports post day close", e);
        }
        try {
            generatePnLReports(businessDate);
        } catch (Exception e) {
            LOG.error("Error in generating PNL Reports", e);
        }
    }

    private void runPostClosureProcess(Date businessDate) {
        LOG.info("Running Post Day Close processes for date {}", businessDate);
        List<UnitClosureDetails> closures = getMetadataService().getClosures(businessDate, ReportStatus.PENDING);
        if (closures != null && !closures.isEmpty()) {
            LOG.info("Found {} day close to run post day close process",closures.size());
            for (UnitClosureDetails closure : closures) {
                int unitId = closure.getUnitId();
                LOG.info("Running post day close process for {}", unitId );
                updateNewCustomerFlagForOrders(unitId, businessDate);
            }
        }
    }

    private void updateNewCustomerFlagForOrders(int unitId, Date businessDate) {
        try {
            getOrderManagementService().updateNewCustomerFlagForOrders(unitId, businessDate);
        } catch (Exception e) {
            new ErrorNotification("Update New Customer Post Day Close Process",
                    "Error in Updating New Customer Post Day Close Process for unit " + unitId + " and businessDate " + businessDate, e,
                    getEnvironmentProperties().getEnvironmentType()).sendEmail();
        }
    }

    public void generateMTDPnL() throws DataNotFoundException, IOException, EmailGenerationException {
        Date businessDate = AppUtils.getPreviousDate();
        generateMTDPnL(businessDate);
    }

    public void generateMTDPnL(Date businessDate) throws DataNotFoundException, IOException, EmailGenerationException {
        try {
            generateMTDPnLReports(businessDate);
        } catch (Exception e) {
            LOG.error("Error in generating PNL Reports", e);
        }
        try {
            List<UnitExpenditureDetail> pnlDetails = getUnitBudgetService().getAllUnitExpenditureDetail(businessDate,
                    CalculationType.MTD);
            new PnLNotification(new PnLMailReceipt(pnlDetails, businessDate, getEnvironmentProperties().getBasePath()),
                    getEnvironmentProperties()).sendEmail();
        } catch (Exception e) {
            LOG.error("Error in generating PNL Summary Email", e);
        }
    }

    public void generateReportsPostDayClose(Date businessDate)
            throws DataNotFoundException, IOException, EmailGenerationException {
        LOG.info("Generating Reports Post Day Close for date {}" , businessDate);
        List<UnitClosureDetails> closures = getMetadataService().getClosures(businessDate, ReportStatus.PENDING);
        if (closures != null && !closures.isEmpty()) {
            LOG.info("Found {} day close to generate report",closures.size());
            for (UnitClosureDetails closure : closures) {
                boolean success = true;
                try {
                    int unitId = closure.getUnitId();
                    LOG.info("Generating report for {} ", unitId);
                    Map<String, List<ReportDetailData>> reports = generateQueryBasedManagersReportData(
                            closure.getUnitId(), businessDate);
                    PostClosureReportInputData input = new PostClosureReportInputData(businessDate,
                            getMasterDataCache().getUnit(closure.getUnitId()), reports);
                    PostDayClosureReportController controller = new PostDayClosureReportController(
                            getEnvironmentProperties(), getMasterDataCache());
                    SettlementReportOutputData output = controller.execute(input);
                    if (output.isReportGenerated()) {
                        try {
                            emailReport(output, new ReportNotification(output));
                        } catch (Exception e) {
                            LOG.error("Error sending email for the score card for unit " + unitId, e);
                            output.setReportEmailed(false);
                            success = false;
                        }
                    }

                } catch (Exception e) {
                    LOG.error(
                            "Error while generating Query Reports for Managers Reports for unit " + closure.getUnitId(),
                            e);
                    success = false;
                }
                getMetadataService().updateReportStatus(closure.getClosureId(),
                        success ? ReportStatus.SUCCESS : ReportStatus.FAILED);
            }
        }
    }

    protected void writeDataToDSR(Date businessDate) throws IOException {
        List<UnitExpenditureDetail> pnlDetails = getUnitBudgetService().getAllUnitExpenditureDetail(businessDate,
                CalculationType.CURRENT);
        if (pnlDetails != null && !pnlDetails.isEmpty()) {
            List<List<Object>> list = new ArrayList<>();
            for (UnitExpenditureDetail e : pnlDetails) {
                if (e.getStatus().equals(CalculationStatus.CANCELLED.name())) {
                    continue;
                }
                List<Object> pnlData = Arrays.asList(e.getUnitId(), AppUtils.getDateString(e.getBusinessDate()),
                        e.getUnitName(), e.getTicket(), e.getApc(), e.getNetSales(), e.getDeliveryTicket(),
                        e.getDeliveryApc(), e.getDeliveryNetSales(), e.getDineInTicket(), e.getDineInApc(),
                        e.getDineInNetSales());
                list.add(pnlData);
            }
            GoogleSheetLoader loader = new GoogleSheetLoader();
            loader.writeRowsSheet(AppConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL,
                    AppUtils.isDev(getEnvironmentProperties().getEnvironmentType())
                            ? "1p619ubFO_0GZG0imIl6W37Rhm_Mf4AnCPu-E6xlQ5ZI"
                            : "1zPkeyrc0jI8hXDM3dqHRFeTwumnE9XvX4otZQ-YnKBA",
                    "Daily Sales Report", list);
        }
    }

    public abstract PullTransferSettlementReasonService getPullTransferSettlementReasonService();

    public abstract PosMetadataService getMetadataService();

    public abstract CashManagementService getCashManagementService();

    public abstract MetadataCache getMetadataCache();

    public abstract OfferManagementExternalService getOfferManagementExternalService();

    public abstract TaxDataCache getTaxDataCache();

    public abstract MasterDataCache getMasterDataCache();

    public abstract OrderManagementService getOrderManagementService();

    public abstract OrderSearchService getOrderSearchService();

    public abstract UnitBudgetService getUnitBudgetService();

    public abstract EnvironmentProperties getEnvironmentProperties();

    public abstract ItemConsumptionHelper getItemConsumptionHelper();

    public abstract ExpenseManagementService getExpenseService();

    public abstract MenuItemConsumptionHelper getMenuItemConsumptionHelper();

    public abstract TableService getTableService();


    public abstract CardService getCardService();


    /**
     * Method to be overridden by extending class only when sending external reports
     *
     * @return
     */
    public abstract List<ReportOutput> processExternalReports(int unitId, List<Order> ordersForTheDay, Date businessDate);


    /**
     * param businessDate
     *
     * @return Map<Integer, ChannelPartnerDetail>
     * @throws DataNotFoundException
     */
    protected Map<Integer, ChannelPartnerDetail> getChannelPartners(Date businessDate) throws DataNotFoundException {
        Map<Integer, ChannelPartnerDetail> map = new HashMap<>();
        for (ChannelPartnerDetail p : getMetadataService().getAllChannelPartner(businessDate)) {
            map.put(p.getId(), p);
        }
        return map;
    }
}
