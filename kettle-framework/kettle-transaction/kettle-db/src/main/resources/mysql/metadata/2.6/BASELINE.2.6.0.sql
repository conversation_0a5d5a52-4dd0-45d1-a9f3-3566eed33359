ALTER TABLE KETTLE_DEV.ORDER_PAYMENT_DETAIL MODIFY COLUMN REFUND_REASON VARCHAR(150) NULL;
ALTER TABLE KETTLE_DEV.ORDER_PAYMENT_DETAIL MODIFY COLUMN REFUND_STATUS VARCHAR(25) NULL;

ALTER TABLE KETTLE_DEV.ORDER_PAYMENT_DETAIL add column REFUND_ID VARCHAR(100) NULL;

ALTER TABLE KETTLE_DEV.CUSTOMER_ADDRESS_INFO ADD COLUMN SUB_LOCALITY VARCHAR(100) ;

INSERT INTO KETTLE_DEV.PARTNER_ATTRIBUTES (ID, PARTNER_ID, MAPPING_TYPE, MAPPING_VALUE, PARTNER_TYPE) VALUES (NULL, '26021', 'TENANT_ID', 'mck60311', 'SALES_REPORT');
INSERT INTO KETTLE_DEV.PARTNER_ATTRIBUTES (ID, PARTNER_ID, MAPPING_TYPE, MAPPING_VALUE, PARTNER_TYPE) VALUES (NULL, '26021', 'FTP_SERVER', '*************/Chaayos', 'SALES_REPORT');
INSERT INTO KETTLE_DEV.PARTNER_ATTRIBUTES (ID, PARTNER_ID, MAPPING_TYPE, MAPPING_VALUE, PARTNER_TYPE) VALUES (NULL, '26021', 'FTP_PORT', '21', 'SALES_REPORT');
INSERT INTO KETTLE_DEV.PARTNER_ATTRIBUTES (ID, PARTNER_ID, MAPPING_TYPE, MAPPING_VALUE, PARTNER_TYPE) VALUES (NULL, '26021', 'USERNAME', 'mck60311', 'SALES_REPORT');
INSERT INTO KETTLE_DEV.PARTNER_ATTRIBUTES (ID, PARTNER_ID, MAPPING_TYPE, MAPPING_VALUE, PARTNER_TYPE) VALUES (NULL, '26021', 'PASSWORD', 'C6Cvwhur', 'SALES_REPORT');
INSERT INTO KETTLE_DEV.PARTNER_ATTRIBUTES (ID, PARTNER_ID, MAPPING_TYPE, MAPPING_VALUE, PARTNER_TYPE) VALUES (NULL, '26021', 'STATUS', 'ACTIVE', 'SALES_REPORT');
INSERT INTO KETTLE_DEV.PARTNER_ATTRIBUTES (ID, PARTNER_ID, MAPPING_TYPE, MAPPING_VALUE, PARTNER_TYPE) VALUES (NULL, '26021', 'REPORT_TYPE', 'PATHFINDER', 'SALES_REPORT');