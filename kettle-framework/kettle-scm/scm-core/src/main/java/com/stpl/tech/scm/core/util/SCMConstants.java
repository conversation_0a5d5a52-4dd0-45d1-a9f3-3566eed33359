package com.stpl.tech.scm.core.util;

import com.stpl.tech.scm.domain.model.IdCodeName;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class SCMConstants {

    private Map<Integer, IdCodeName> template = new HashMap<>();
    public Map<Integer, IdCodeName> getVendorContractTemplate() {
        if (template.isEmpty()) {
            template.put(1,new IdCodeName(1,"VendorContract_1","365"));
        }
        return template;
    }
}
