<div id="fullOfferDiv" ng-init="init()" xmlns:70vh xmlns:70vh xmlns:70vh
     xmlns:max-height="http://www.w3.org/1999/xhtml">
    <div id="offerListDivDetail">
        <div class="row row-spacing">
            <div class="col s8">
                <br>
                <h1 class="page-header">
                    Upload Bulk Recipe
                </h1>
            </div>
            <span style="color: #1ca62c">**Please upload data for all region then only <strong style="color: #c9302c"> click submit to save data</strong> <br></span>
            <div style="display: flex; flex-direction: row; width: 70vw; align-items: center; gap: 10px;">
                <div class="form-group" style="width: 100%;">
                    <label>Select Region</label>
                    <select class="form-control"
                            data-ng-options="profile as profile.code for profile in profiles"
                            data-ng-model="selectedProfile">
                        <!--data-ng-change="setSelectedRegion(selectedRegion)"-->
                    </select>
                    
                </div>
                <button type="button" style="height: 45px;" class="btn btn-primary" data-ng-click="fetchDetail()"
                            data-ng-disabled="selectedProfile==null"> Fetch Detail
                    </button>
            </div>
            <table data-ng-if="selectedProfileBasicDetail!=null" style="width:100%; margin-top:50px; margin-bottom:50px; border:1px solid ">
                <tr style="height:30px; background-color:black; color:white">
                    <th>Version</th>
                    <th>Profile</th>
                    <th>Status</th>
                    <th>Create On</th>
                    <th>Activated On</th>
                    <th>Updated By</th>
                    <th>Activated By</th>
                    <th>Closed By</th>
                    <th>Action</th>
                </tr>
                <tr>
                    <td>{{selectedProfileBasicDetail.version}}</td>
                    <td>{{selectedProfileBasicDetail.region}}</td>
                    <td>{{selectedProfileBasicDetail.status}}</td>
                    <td>{{selectedProfileBasicDetail.genetrationTime}}</td>
                    <td>{{selectedProfileBasicDetail.activationTime}}</td>
                    <td>{{selectedProfileBasicDetail.updatedById}}</td>
                    <td>{{selectedProfileBasicDetail.activatedById}}</td>
                    <td>{{selectedProfileBasicDetail.closedById}}</td>
                    <td><button type="button" style="height: 40px;" class="btn btn-primary" data-ng-click="downloadSheet()"> Download
                    </button></td>
                </tr>
            </table>
            <div class="form-group ">
                <div class="row row-spacing">
                    <div class="col-xs-12 form-group">
                        <label class="control-label">Select File : </label>
                        <input type="file" file-model="bulkCSVFile"/>
                    </div>
                    <div class="col-xs-12 form-group">
                        <button type="button" class="btn btn-primary" data-ng-click="addVersion()"
                                data-ng-disabled="bulkCSVFile==null">Upload File
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div ng-show="showUploaded">
            <div id="regionFiles" class="row row-spacing">

                <div class="form-group">
                    <label>File Uploaded for Regions are </label>
                    <div ng-repeat="region in regions">
                        <div  class="success" ng-show="region.flag">{{region.name}}</div>
                    </div>
                </div>

                <div class="form-group ">
                    <button type="button" class="btn btn-primary" data-ng-click="saveVersions()">Submit Versions
                    </button>
                </div>
            </div>
        </div>
        <div ng-show="afterUploading">
            <div id="uploadedData" class="row row-spacing">
                <div class="form-group" ng-if="afterUploadingResponse.length>0">
                    <label >Latest version of regions are </label>
                    <div ng-repeat="versions in afterUploadingResponse">
                        <div><span style="color: #1ca62c">{{versions.region}}</span> : <span >{{versions.version}}</span>
                            <span style="color: #1ca62c">Count is </span> : <span >{{versions.content.length}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

