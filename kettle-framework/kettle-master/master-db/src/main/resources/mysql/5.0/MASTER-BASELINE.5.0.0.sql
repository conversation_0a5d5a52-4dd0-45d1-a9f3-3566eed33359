DROP TABLE IF EXISTS KETTLE_DEV.MONTHLY_TARGETS_DATA;

CREATE TABLE KETTLE_DEV.MONTHLY_TARGETS_DATA (
    TARGETS_DATA_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    <PERSON>IT_ID INTEGER NOT NULL,
    TARGET_MONTH INTEGER NOT NULL,
    TARGET_YEAR INTEGER NOT NULL,
    RECORD_STATUS VARCHAR(10),
    NET_SALE DECIMAL(20 , 2 ),
    NET_SALE_DELIVERY DECIMAL(20 , 2 ),
    NET_SALE_DINE_IN DECIMAL(20 , 2 ),
    TICKETS INTEGER,
    TICKETS_DELIVERY INTEGER,
    TICKETS_DINE_IN INTEGER,
    GMV DECIMAL(20 , 2 ),
    GMV_DELIVERY DECIMAL(20 , 2 ),
    GMV_DINE_IN DECIMAL(20 , 2 ),
    APC DECIMAL(20 , 2 ),
    APC_DELIVERY DECIMAL(20 , 2 ),
    APC_<PERSON>INE_IN DECIMAL(20 , 2 ),
    <PERSON>O<PERSON> INTEGER,
    NON_VEG INTEGER,
    CO<PERSON> INTEGER,
    <PERSON><PERSON> INTEGER,
    MEAL INTEGER,
    CAKE INTEGER,
    GIFT_CARD INTEGER,
    MERCHANDISE INTEGER,
    BEVERAGE INTEGER,
    NEW_CUST_WITH_NO_PRIMARY_PRODUCT INTEGER,
    SEASONAL INTEGER,
    CUSTOMER INTEGER,
    NEW_CUSTOMER INTEGER,
    DIMENSION_REGULAR INTEGER,
    DIMENSION_FULL INTEGER
);


CREATE INDEX UNIT_ID_MONTHLY_TARGETS_DATA ON KETTLE_DEV.MONTHLY_TARGETS_DATA(UNIT_ID) USING BTREE;
CREATE INDEX TARGET_MONTH_MONTHLY_TARGETS_DATA ON KETTLE_DEV.MONTHLY_TARGETS_DATA(TARGET_MONTH) USING BTREE;
CREATE INDEX TARGET_YEAR_MONTHLY_TARGETS_DATA ON KETTLE_DEV.MONTHLY_TARGETS_DATA(TARGET_YEAR) USING BTREE;
CREATE INDEX RECORD_STATUS_MONTHLY_TARGETS_DATA ON KETTLE_DEV.MONTHLY_TARGETS_DATA(RECORD_STATUS) USING BTREE;