package com.stpl.tech.master.core.external.cache;

import java.util.Date;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.RandomStringGenerator;

@Component
public class ApiTokenCache {

    private static final Logger LOG = LoggerFactory.getLogger(ApiTokenCache.class);

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance hinstance;

    @Autowired
    MasterDataCache masterDataCache;

    private IMap<String, Date> apiTokenMap;

    public boolean isValidRequest(String module, String token, String method) {
        if (masterDataCache.getTokenizedApis().containsKey(module) &&  masterDataCache.getTokenizedApis().get(module).equalsIgnoreCase(method)) {
            if (apiTokenMap.containsKey(token)) {
                apiTokenMap.remove(token);
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    public String generateApiToken() {
        RandomStringGenerator randomStringGenerator = new RandomStringGenerator();
        String code = randomStringGenerator.getRandomCode(16);
        apiTokenMap.put(code, AppUtils.getCurrentTimestamp());
        return code;
    }

    public boolean flushAll(){
        apiTokenMap.clear();
        return true;
    }

    @PostConstruct
    public void createApiSet() {
        apiTokenMap = hinstance.getMap("apiTokenMap");
    }

    @Scheduled(cron = "0 0 6 * * *", zone = "GMT+05:30")
    public void invalidateOldKeys() {
		LOG.info("POST-CONSTRUCT ApiTokenCache - STARTED");
        LOG.info("Starting to flush api access tokens older than 24 hours");
        Date currentTimestamp = AppUtils.getCurrentTimestamp();
        apiTokenMap.keySet().forEach(key -> {
            if(apiTokenMap.get(key) == null) {
                apiTokenMap.remove(key);
            } else if (AppUtils.getSecondsDiff(currentTimestamp, apiTokenMap.get(key)) > 85000) {
                apiTokenMap.remove(key);
            }
        });
    }

}
