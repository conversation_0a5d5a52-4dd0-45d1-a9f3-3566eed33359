/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.notification;

import java.util.ArrayList;
import java.util.List;

import com.stpl.tech.kettle.report.metadata.model.ReportOutput;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.AttachmentData;
import com.stpl.tech.util.notification.EmailNotification;

public class ReportEmailNotification extends EmailNotification {

	private String[] toEmails;
	private String fromEmail;
	private String subject;
	private ReportOutput outputType;
	private boolean attachFile;
	private List<AttachmentData> attachmentData;
	private EnvType env;

	public ReportEmailNotification(String subject, EnvType env) {
		this.subject = subject;
		this.env = env;
	}

	public String[] getToEmails() {
		return toEmails;
	}

	public void setToEmails(String[] toEmails) {
		this.toEmails = toEmails;
	}

	public String getFromEmail() {
		return fromEmail;
	}

	public void setFromEmail(String fromEmail) {
		this.fromEmail = fromEmail;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public List<AttachmentData> getAttachmentData() {
		if (attachmentData == null) {
			attachmentData = new ArrayList<>();
		}
		return attachmentData;
	}

	public void setAttachmentData(List<AttachmentData> attachmentData) {
		this.attachmentData = attachmentData;
	}

	public ReportOutput getOutputType() {
		return outputType;
	}

	public void setOutputType(ReportOutput outputType) {
		this.outputType = outputType;
	}

	public boolean isAttachFile() {
		return attachFile;
	}

	public void setAttachFile(boolean attachFile) {
		this.attachFile = attachFile;
	}

	@Override
	public String subject() {
		return subject;
	}

	@Override
	public String body() throws EmailGenerationException {
		// TODO Auto-generated method stub
		return "";
	}

	@Override
	public EnvType getEnvironmentType() {
		return env;
	}

}
