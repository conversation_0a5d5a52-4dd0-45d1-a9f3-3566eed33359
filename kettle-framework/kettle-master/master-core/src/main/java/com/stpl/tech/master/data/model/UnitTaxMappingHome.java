/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 3 Aug, 2015 5:36:59 PM by Hibernate Tools 4.0.0

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * Home object for domain model class UnitTaxMapping.
 * 
 * @see com.stpl.tech.master.data.model.UnitTaxMapping
 * <AUTHOR> Tools
 */
@Stateless
public class UnitTaxMappingHome {

	private static final Log log = LogFactory.getLog(UnitTaxMappingHome.class);

	@PersistenceContext
	private EntityManager entityManager;

	public void persist(UnitTaxMapping transientInstance) {
		log.debug("persisting UnitTaxMapping instance");
		try {
			entityManager.persist(transientInstance);
			log.debug("persist successful");
		} catch (RuntimeException re) {
			log.error("persist failed", re);
			throw re;
		}
	}

	public void remove(UnitTaxMapping persistentInstance) {
		log.debug("removing UnitTaxMapping instance");
		try {
			entityManager.remove(persistentInstance);
			log.debug("remove successful");
		} catch (RuntimeException re) {
			log.error("remove failed", re);
			throw re;
		}
	}

	public UnitTaxMapping merge(UnitTaxMapping detachedInstance) {
		log.debug("merging UnitTaxMapping instance");
		try {
			UnitTaxMapping result = entityManager.merge(detachedInstance);
			log.debug("merge successful");
			return result;
		} catch (RuntimeException re) {
			log.error("merge failed", re);
			throw re;
		}
	}

	public UnitTaxMapping findById(Integer id) {
		log.debug("getting UnitTaxMapping instance with productId: " + id);
		try {
			UnitTaxMapping instance = entityManager.find(UnitTaxMapping.class, id);
			log.debug("get successful");
			return instance;
		} catch (RuntimeException re) {
			log.error("get failed", re);
			throw re;
		}
	}
}
