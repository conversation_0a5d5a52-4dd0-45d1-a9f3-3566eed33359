import os
import smtplib
import pymongo
from pymongo import MongoClient
import pandas as pd
import datetime
import pytz
import numpy as np
import calendar
import traceback
import xlwt
from xlwt import Workbook
import mysql.connector
from mysql.connector import MySQLConnection, Error
from configparser import ConfigParser
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import COMMASPACE, formatdate
from os.path import basename
from pymongo import ReadPreference
from dateutil.relativedelta import relativedelta

# BASEPATH = '/home/<USER>/stockout_scripts'
# BASEPATH = '/data/app/kettle/prod/python_reports/stockouts'
BASEPATH = '/data/app/kettle/prod/python_reports/swiggy_report'

def read_db_config(filename=BASEPATH + '/config.ini', section='mongo'):
    """ Read database configuration file and return a dictionary object
    :param filename: name of the configuration file
    :param section: section of database configuration
    :return: a dictionary of database parameters
    """
    return read_config(filename, section)


def read_email_config(filename=BASEPATH + '/config.ini', section='email'):
    """ Read database configuration file and return a dictionary object
    :param filename: name of the configuration file
    :param section: section of email configuration
    :return: a dictionary of email parameters
    """
    # get section, default to mysql
    return read_config(filename=filename, section=section)


def read_config(filename, section):
    # create parser and read ini configuration file
    parser = ConfigParser()
    parser.read(filename)
    config = {}
    if parser.has_section(section):
        items = parser.items(section)
        for item in items:
            config[item[0]] = item[1]
    else:
        raise Exception('{0} not found in the {1} file'.format(section, filename))

    return config

def connect(type="mongo", schema="inventory"):
    if type == "mongo":
        mongo_config = read_db_config(section="mongo")
        client = pymongo.MongoClient(mongo_config["uri"], read_preference=ReadPreference.PRIMARY)
        db = client[schema]
        return db
    elif type == "mysql":
        """ Connect to MySQL database """
        db_config = read_db_config(section="mysql")
        conn = None
        try:
            print('Connecting to MySQL database...')
            conn = MySQLConnection(**db_config)

            if conn.is_connected():
                print('connection established.')
                return conn
            else:
                print('connection failed.')

        except Error as error:
            print(error)



def startProcess():
    mysql_conn = connect("mysql", "KETTLE_MASTER")
    db = connect(type="mongo", schema="channel_partner")
    collection = db.partnerCafeStatusHistory

    mycursor = mysql_conn.cursor()

    wb = Workbook()
    sheet = wb.add_sheet('CHAAYOS')
    sheet2 = wb.add_sheet('GNT')
    partnersList = ["swiggy"]

    for partner in partnersList:
        print("-------------------------------------------------------------------------------------------")
        if partner == "swiggy":
            print("------------    For SWIGGY     --------------------")
        else:
            print("------------    For ZOMATO     --------------------")
        print("-------------------------------------------------------------------------------------------")
        start = [0,5,12,15,20,22]
        end = [5,12,15,20,22,0]

        startLength = len(start);
        k=0;
        mydate = datetime.datetime.now(pytz.timezone('Asia/Kolkata'))
        #         mydate = datetime.datetime(2021,11,29,21,35,25)
        lastdate = mydate - datetime.timedelta(days=1)
        lastLastDate = mydate - datetime.timedelta(days=2)
        #         day = calendar.day_name[my_date.weekday()]

        dict = {}
        dict["sheet2Data"]={}

        listOfIds = list(collection.find().distinct("unitId"))
        listOfIds.sort()
        print(listOfIds)
        li =[10000]

        bold = 'font: bold 1'
        bold_style = xlwt.easyxf(bold)
        col_names = ["UNIT ID","UNIT NAME","SLOT","ACTUAL TIME","ACTUAL TIME(Hours)","ACTUAL WORKING TIME",
                     "ACTUAL WORKING TIME(Hours)","TIME DIFFERENCE","TIME DIFFERENCE(Hours)","PERCENTAGE OF TOTAL"]
        r=0
        c=0

        for i in range(len(col_names)):
            sheet.write(r, c, col_names[i],bold_style)
            sheet2.write(r, c, col_names[i],bold_style)
            c +=1
        row = 1
        for id in listOfIds:
            print("Id in loop is "+str(id))
            dict=slotsWise(id,collection,mydate,lastdate,partner,dict,lastLastDate,k,startLength,start,end,mycursor)
            findingUnitName = list(collection.find({"unitId":id}).limit(1));
            print("finding name is :")
            print(findingUnitName[0]["name"])
            unitNameFound = findingUnitName[0]["name"]
            listOfUnitSlots = dict.get("unitData" ,"nothing")
            actualTimeInHours = 0
            actualWorkingTimeInHours = 0
            TimeDifferenceInHours = 0

            actualTimeInHoursSheet2 = 0
            actualWorkingTimeInHoursSheet2 = 0
            TimeDifferenceInHoursSheet2 = 0
            unit_id =[]
            slots=[]
            AT = []
            ATHours = []
            AWH = []
            AWHHours = []
            DIT = []
            DITHours =[]
            pcent = []

            listOfUnitSlotsSheet2 = dict.get("unitDataSheet2","nothing")
            unit_idSheet2 =[]
            slotsSheet2=[]
            ATSheet2 = []
            ATHoursSheet2 = []
            AWHSheet2 = []
            AWHHoursSheet2 = []
            DITSheet2 = []
            DITHoursSheet2 = []
            pcentSheet2 = []

            checkForArrays= ["IN_ACTIVE","No_DATA","Unit In_Active"]
            if(listOfUnitSlots !="nothing"):
                unit_id = listOfUnitSlots[0];
                slots = listOfUnitSlots[1];
                AT = listOfUnitSlots[2];
                AWH = listOfUnitSlots[3];
                DIT=listOfUnitSlots[4];
                pcent=listOfUnitSlots[5];

                splitArray =[AT,AWH,DIT]
                splitIntArray=[ATHours,AWHHours,DITHours]
                print("split Array is :---")
                print(splitArray)
                for i in range(len(splitArray)):
                    for item in range(len(splitArray[i])):
                        if splitArray[i][item] in checkForArrays:
                            splitIntArray[i].append(splitArray[i][item])
                        else:
                            TimeSplit = splitArray[i][item].split(":")
                            TimeSPlitInt = []
                            for itemIn in range(len(TimeSplit)-1):
                                TimeSPlitInt.append(int(TimeSplit[itemIn]))
                            #str to decimal conversion
                            #6 + (15 ÷ 60) + (30 ÷ 3,600)
                            timeInside = TimeSPlitInt[0] + TimeSPlitInt[1]/60
                            splitIntArray[i].append(round(timeInside,2))
                print("after conversion : ")
                print(splitIntArray)

            if(listOfUnitSlotsSheet2 !="nothing"):
                unit_idSheet2 = listOfUnitSlotsSheet2[0];
                slotsSheet2 = listOfUnitSlotsSheet2[1];
                ATSheet2 = listOfUnitSlotsSheet2[2];
                AWHSheet2 = listOfUnitSlotsSheet2[3];
                DITSheet2=listOfUnitSlotsSheet2[4];
                pcentSheet2=listOfUnitSlotsSheet2[5];

                splitArraySheet2 =[ATSheet2,AWHSheet2,DITSheet2]
                splitIntArraySheet2=[ATHoursSheet2,AWHHoursSheet2,DITHoursSheet2]
                print("split Array is Sheet 2:---")
                print(splitArraySheet2)
                for i in range(len(splitArraySheet2)):
                    for item in range(len(splitArraySheet2[i])):
                        if splitArraySheet2[i][item] in checkForArrays:
                            splitIntArraySheet2[i].append(splitArraySheet2[i][item])
                        else:
                            TimeSplitSheet2 = splitArraySheet2[i][item].split(":")
                            TimeSPlitIntSheet2 = []
                            for itemIn in range(len(TimeSplitSheet2)-1):
                                TimeSPlitIntSheet2.append(int(TimeSplitSheet2[itemIn]))

                            timeInsideSheet2 = TimeSPlitIntSheet2[0] + TimeSPlitIntSheet2[1]/60
                            splitIntArraySheet2[i].append(round(timeInsideSheet2,2))
                print("after conversion : ")
                print(splitIntArraySheet2)

            if(listOfUnitSlots !="nothing"):
                for i in range(6):
                    column = 0
                    sheet.write(row, column, unit_id[i])
                    sheet2.write(row, column, unit_idSheet2[i])
                    column +=1
                    sheet.write(row, column, unitNameFound)
                    sheet2.write(row, column, unitNameFound)
                    column +=1
                    sheet.write(row, column, slots[i])
                    sheet2.write(row, column, slotsSheet2[i])
                    column +=1
                    sheet.write(row, column, AT[i])
                    sheet2.write(row, column, ATSheet2[i])
                    column +=1
                    sheet.write(row, column, ATHours[i])
                    sheet2.write(row, column, ATHoursSheet2[i])
                    column +=1
                    sheet.write(row, column, AWH[i])
                    sheet2.write(row, column, AWHSheet2[i])
                    column +=1
                    sheet.write(row, column, AWHHours[i])
                    sheet2.write(row, column, AWHHoursSheet2[i])
                    column +=1
                    sheet.write(row, column, DIT[i])
                    sheet2.write(row, column, DITSheet2[i])
                    column +=1
                    sheet.write(row, column, DITHours[i])
                    sheet2.write(row, column, DITHoursSheet2[i])
                    column +=1
                    sheet.write(row, column, pcent[i])
                    sheet2.write(row, column, pcentSheet2[i])
                    row += 1

        fileNameDateFormat = "Swiggy Cafe Status Report for "+str(lastdate.day) + "/" + str(lastdate.month) + "/" + str(lastdate.year)
        filename = BASEPATH + "/SwiggyCafeStatusReport.xls"
        wb.save(filename)
        send_mail(send_to=["<EMAIL>","<EMAIL>"], text=None,
                  subject=fileNameDateFormat, filename=filename)
        os.remove(filename)
        print("report sent success")
        if mysql_conn is not None:
            mysql_conn.close()
            print('Connection closed.')


def send_mail(send_to, subject, text=None, filename=None, server="smtp.gmail.com"):
    email_config = read_email_config()
    msg = MIMEMultipart()
    msg['From'] = email_config['user']
    msg['To'] = COMMASPACE.join(send_to) if type(send_to) is list else send_to
    msg['Date'] = formatdate(localtime=True)
    msg['Subject'] = subject

    if text is not None:
        msg.attach(MIMEText(text, "html"))

    if filename is not None:
        with open(filename, "rb") as fil:
            part = MIMEApplication(fil.read(), Name=basename(filename))
            # After the file is closed
            part['Content-Disposition'] = 'attachment; filename="%s"' % basename(filename)
            msg.attach(part)

    smtp = smtplib.SMTP(server, 587)
    smtp.ehlo()
    smtp.starttls()
    smtp.login(email_config['user'], email_config['password'])
    smtp.sendmail(email_config['user'], send_to, msg.as_string())
    smtp.quit()
    smtp.close()

def slotsWise(id,collection,mydate,lastdate,partner,dict,lastLastDate,k,startLength,start,end,mycursor):
    unitArray = [];
    unitArraySheet2 = [];
    for i in range(6):
        unitArray.append(id);
        unitArraySheet2.append(id);
    actualTime = []
    differenceTime = []
    actualWorkingHours = []
    percentage = []
    #for sheet 2
    actualTimeSheet2 = []
    differenceTimeSheet2 = []
    actualWorkingHoursSheet2 = []
    percentageSheet2 = []
    #to get day of week
    day = calendar.day_name[lastdate.weekday()]
    query = "select * from BUSINESS_HOURS WHERE UNIT_ID = {} AND DAY_OF_WEEK_TEXT = '{}';".format(id,day)
    mycursor.execute(query)
    myresult = mycursor.fetchone()

    isHavingNullData = False
    inActiveForThatSlot = False
    isDifferentday = False

    startOfDineIn = str(myresult[9])
    closeOfDineIn = str(myresult[10])
    is_Operational = myresult[2]

    duplicatecloseTimeOfCafe=''
    diffInTime = None
    TimeArray = []

    if(is_Operational !='N'):
        print("dine in start is "+ str(startOfDineIn) )
        print("end of Dine in is "+ str(closeOfDineIn))
        if(startOfDineIn == None and closeOfDineIn == None):
            isHavingNullData = True
        if(isHavingNullData !=True):
            startOfDineInArray =startOfDineIn.split(":")
            startOfDIneInHMS = []
            for x in startOfDineInArray:
                startOfDIneInHMS.append(int(x))
            print(startOfDIneInHMS)
            closeOfDineInArray =closeOfDineIn.split(":")
            closeOfDIneInHMS = []
            for x in closeOfDineInArray:
                closeOfDIneInHMS.append(int(x))
            print(closeOfDIneInHMS)

            TimeCheckArray = [0,1,2,3,4,5,6,7,8,9]
            startTimeOfCafe =  datetime.datetime.combine(lastdate, datetime.time(hour=startOfDIneInHMS[0],
                                                                                 minute=startOfDIneInHMS[1], second = startOfDIneInHMS[2]))

            if(closeOfDIneInHMS[0] in TimeCheckArray):
                isDifferentday = True
                closeTimeOfCafe =  datetime.datetime.combine(mydate, datetime.time(hour=closeOfDIneInHMS[0],
                                                                                   minute=closeOfDIneInHMS[1], second = closeOfDIneInHMS[2]))
                duplicatecloseTimeOfCafe = closeTimeOfCafe =  datetime.datetime.combine(lastdate, datetime.time(hour=closeOfDIneInHMS[0],
                                                                                                                minute=closeOfDIneInHMS[1], second = closeOfDIneInHMS[2]))

            else:
                closeTimeOfCafe =  datetime.datetime.combine(lastdate, datetime.time(hour=closeOfDIneInHMS[0],
                                                                                     minute=closeOfDIneInHMS[1], second = closeOfDIneInHMS[2]))


            while k<startLength:
                inActiveForThatSlot = False
                isInSlot = False
                if(k == 0):
                    print("DAY_SLOT_OVERNIGHT")
                elif(k == 1):
                    print("DAY_SLOT_BREAKFAST")
                elif(k == 2):
                    print("DAY_SLOT_LUNCH")
                elif(k == 3):
                    print("DAY_SLOT_EVENING")
                elif(k == 4):
                    print("DAY_SLOT_DINNER")
                elif(k == 5):
                    print("DAY_SLOT_POST_DINNER")


                startTime = datetime.datetime.combine(lastdate, datetime.time(hour=start[k], minute=00))
                if(k == startLength -1):
                    endTime = datetime.datetime.combine(mydate, datetime.time(hour=end[k], minute=00))
                else:
                    endTime = datetime.datetime.combine(lastdate, datetime.time(hour=end[k], minute=00))

                if(isDifferentday == True):
                    TimeArray = []
                    d = startTime <= startTimeOfCafe <=endTime
                    print("sattus is :"+str(d))
                    if(startTime <= startTimeOfCafe <=endTime):
                        if(startTime <= duplicatecloseTimeOfCafe <= endTime):
                            TimeArray= [startTime,startTimeOfCafe,endTime,duplicatecloseTimeOfCafe]
                            TimeArray.sort()
                            isInSlot = True
                            print("Time array is :"+str(TimeArray))

                    if(isInSlot == False):
                        if(startTimeOfCafe > startTime):
                            if(not startTimeOfCafe > endTime ):
                                print("start Time changed here  ..")
                                startTime = startTimeOfCafe

                    if(duplicatecloseTimeOfCafe > startTime and duplicatecloseTimeOfCafe <endTime):
                        endTime = duplicatecloseTimeOfCafe



                if(isDifferentday == False):
                    if(((startTimeOfCafe > startTime) and (startTimeOfCafe > endTime)) or startTime >= closeTimeOfCafe):
                        inActiveForThatSlot = True

                    if(startTimeOfCafe > startTime):
                        print("before start time is   :"+str(startTime))
                        startTime= startTimeOfCafe
                        print("after start time is   :"+str(startTime))
                    if(closeTimeOfCafe < endTime):
                        endTime = closeTimeOfCafe

                    if((endTime-datetime.datetime(endTime.year,endTime.month,endTime.day)).total_seconds() - (startTime-datetime.datetime(startTime.year,startTime.month,startTime.day)).total_seconds() <= 60 ):
                        inActiveForThatSlot = True


                print("start is "+str(startTime))
                print("end is "+str(endTime))

                print("startTime of cafe is " + str(startTimeOfCafe))
                dict = Traverse(id,startTime,endTime,collection,mydate,lastdate,partner,dict,lastLastDate,inActiveForThatSlot,isInSlot,TimeArray,duplicatecloseTimeOfCafe)
                k +=1
                actualTime.append(str(dict.get("actualTime")))
                differenceTime.append(str(dict.get("differenceTime")))
                actualWorkingHours.append(str(dict.get("actualWorkingHours")))

                actualTimeSheet2.append(str(dict.get("sheet2Data").get("actualTime")))
                differenceTimeSheet2.append(str(dict.get("sheet2Data").get("differenceTime")))
                actualWorkingHoursSheet2.append(str(dict.get("sheet2Data").get("actualWorkingHours")))

                if(dict.get("actualWorkingHours") == "IN_ACTIVE"):
                    percentage.append(dict.get("actualWorkingHours"))
                else:
                    if(dict.get("actualTime").total_seconds() !=0):
                        percentage.append((dict.get("actualWorkingHours").total_seconds()/dict.get("actualTime").total_seconds())*100)
                    else:
                        percentage.append((dict.get("actualWorkingHours").total_seconds()/1)*100)

                #test
                if(dict.get("sheet2Data").get("actualWorkingHours") == "IN_ACTIVE"):
                    percentageSheet2.append(dict.get("sheet2Data").get("actualWorkingHours"))
                else:
                    if(dict.get("sheet2Data").get("actualTime").total_seconds() !=0):
                        percentageSheet2.append((dict.get("sheet2Data").get("actualWorkingHours").total_seconds()/dict.get("sheet2Data").get("actualTime").total_seconds())*100)
                    else:
                        percentageSheet2.append((dict.get("sheet2Data").get("actualWorkingHours").total_seconds()/1)*100)


                print(dict)

        if(isHavingNullData):
            for i in range(6):
                actualTime.append("No_DATA")
                differenceTime.append("No_DATA")
                actualWorkingHours.append("No_DATA")
                percentage.append("No_DATA")

                actualTimeSheet2.append("No_DATA")
                differenceTimeSheet2.append("No_DATA")
                actualWorkingHoursSheet2.append("No_DATA")
                percentageSheet2.append("No_DATA")

    if(is_Operational =='N'):
        print("-----------------------------------")
        print("| Unit is In_Active for that Unit |")
        print("-----------------------------------")
        for i in range(6):
            actualTime.append("Unit In_Active")
            differenceTime.append("Unit In_Active")
            actualWorkingHours.append("Unit In_Active")
            percentage.append("Unit In_Active")

            actualTimeSheet2.append("Unit In_Active")
            differenceTimeSheet2.append("Unit In_Active")
            actualWorkingHoursSheet2.append("Unit In_Active")
            percentageSheet2.append("Unit In_Active")

    listOfUnitSlots = []
    listOfUnitSlotsSheet2 = []

    slot = ["DAY_SLOT_OVERNIGHT","DAY_SLOT_BREAKFAST","DAY_SLOT_LUNCH","DAY_SLOT_EVENING","DAY_SLOT_DINNER","DAY_SLOT_POST_DINNER"]
    Frame = {'Unit_Id':unitArray,
             'Slot':["DAY_SLOT_OVERNIGHT","DAY_SLOT_BREAKFAST","DAY_SLOT_LUNCH","DAY_SLOT_EVENING","DAY_SLOT_DINNER","DAY_SLOT_POST_DINNER"],
             'Actual Time':actualTime,
             'Difference Time': differenceTime,
             'Actual Working hours':actualWorkingHours,
             'Percentage' : percentage  }

    finalDataFrame = pd.DataFrame(Frame)
    #     pd.set_option('display.colheader_justify', 'center')
    left_aligned_df = finalDataFrame.style.set_properties(**{'text-align': 'left'})
    print("----------------------------------------------------------------------------------------------------------------")
    #     display(left_aligned_df)

    print("----------------------------------------------------------------------------------------------------------------")
    content = [unitArray,slot,actualTime,actualWorkingHours,differenceTime,percentage]
    contentSheet2 = [unitArraySheet2,slot,actualTimeSheet2,actualWorkingHoursSheet2,differenceTimeSheet2,percentageSheet2]
    for i in range(len(content)):
        listOfUnitSlots.append(content[i])
        listOfUnitSlotsSheet2.append(contentSheet2[i])
    dict["unitData"]=listOfUnitSlots
    dict["unitDataSheet2"]=listOfUnitSlotsSheet2
    return dict


def Traverse(id,startTime,endTime,collection,mydate,lastdate,partnerName,dict,lastLastDate,inActiveForThatSlot,isInSlot = False,TimeArray = [],duplicatecloseTimeOfCafe=''):
    #changes
    brandIds=[1,3]
    for brandId in brandIds:
        print("traversing for brand :"+str(brandId))
        previousDayStatus =[]
        print("len of Time array is "+str(len(TimeArray)))
        print("--------------------------------------------------------\n")
        print("id is"+ str(id))
        print("inactive status "+str(inActiveForThatSlot))
        newStartTime= startTime
        arr = []
        addedAt = []
        TrueOrfalse = []
        TimeOriginal = startTime
        updating = startTime
        isElse = False
        if(inActiveForThatSlot !=True):
            swiggyData =[]
            if(isInSlot == True):
                if(len(TimeArray)>0):
                    firstList =list (collection.find({"unitId":id ,"brandId":brandId, "partnerName":partnerName , "lastUpdatedTime" : {"$gte": TimeArray[0], "$lt": TimeArray[1]}}).sort('lastUpdatedTime', pymongo.ASCENDING))
                    secondList = list (collection.find({"unitId":id ,"brandId":brandId, "partnerName":partnerName , "lastUpdatedTime" : {"$gte": TimeArray[2], "$lt": TimeArray[3]}}).sort('lastUpdatedTime', pymongo.ASCENDING))
                    for i in firstList:
                        swiggyData.append(i)
                    for i in secondList:
                        swiggyData.append(i)
            else:
                swiggyData = list (collection.find({"unitId":id ,"brandId":brandId, "partnerName":partnerName , "lastUpdatedTime" : {"$gte": startTime, "$lt": endTime}}).sort('lastUpdatedTime', pymongo.ASCENDING))

            print(len(swiggyData))
            #             To check for cafe is active or not for previous slot
            if(startTime.hour == 00):
                if(duplicatecloseTimeOfCafe !=''):
                    if(duplicatecloseTimeOfCafe.hour >= 0 and duplicatecloseTimeOfCafe.hour < 9):
                        prevStartTime = datetime.datetime.combine(lastLastDate, datetime.time(hour=23, minute=40))
                        prevEndTime =  datetime.datetime.combine(startTime, datetime.time(hour=00, minute=00))
                    else:
                        prevStartTime = datetime.datetime.combine(lastLastDate, datetime.time(hour=duplicatecloseTimeOfCafe.hour -1, minute=duplicatecloseTimeOfCafe.minute +40))
                        prevEndTime =  datetime.datetime.combine(startTime, datetime.time(hour=duplicatecloseTimeOfCafe.hour, minute=duplicatecloseTimeOfCafe.minute))

                else:
                    prevStartTime = datetime.datetime.combine(lastLastDate, datetime.time(hour=23, minute=40))
                    prevEndTime =  datetime.datetime.combine(startTime, datetime.time(hour=00, minute=00))
                prevDayData =  list (collection.find({"unitId":id ,"brandId":brandId, "partnerName":partnerName , "lastUpdatedTime" : {"$gte": prevStartTime, "$lt": prevEndTime}}).sort('lastUpdatedTime', pymongo.DESCENDING))

                if(len(prevDayData) > 0):
                    print("length od prev data is "+ str(len(prevDayData)))
                    checkOfTrueOrFalse = prevDayData[0]["partnerStatus"]
                    previousDayStatus.append(checkOfTrueOrFalse)
                    print("prevdayData is \n" + str(prevDayData))
                    print()
            if len(swiggyData)>0:
                for data in swiggyData:
                    s=("   ------ ").join((str(data["_id"]),str(data["partnerStatus"]),str(data["lastUpdatedTime"])))
                    print(s)

            dataCount =1
            dataCountTotal = len(swiggyData)
            falseCount = 1
            #if list is not null
            if(dataCountTotal > 0):
                isAlreadyInserted = False
                firstCheck = False
                for data in swiggyData:
                    zeroPrevCheck = False
                    if(dataCount == dataCountTotal):
                        if(brandId == 1):
                            dict[data["unitId"]]=data["partnerStatus"]
                        else:
                            dict.get("sheet2Data")[data["unitId"]]=data["partnerStatus"]
                    #if true    if(dataCount == 1):
                    if(data["partnerStatus"]):
                        #for first iteration of overnight slot checking previous day is true or false
                        if(dataCount == 1):
                            print("hour data is :")
                            print(data["lastUpdatedTime"])

                            if(len(previousDayStatus) > 0):
                                if(previousDayStatus[0] == False ):
                                    print("It was not active from the previous day")
                                    diff = data["lastUpdatedTime"] - newStartTime
                                    arr.append(diff)
                                    addedAt.append(dataCount)
                                    zeroPrevCheck = True
                                else:
                                    firstCheck = True

                            if(zeroPrevCheck == False):
                                checkToCheck = False
                                if(brandId == 1):
                                    checkToCheck = dict.get(id,"nothing") == False or (dict.get(id,"nothing") == "nothing" and not firstCheck)
                                else:
                                    checkToCheck = dict.get("sheet2Data").get(id,"nothing") == False or (dict.get("sheet2Data").get(id,"nothing") == "nothing" and not firstCheck)
                                print("check to check is :"+str(checkToCheck))
                                if(checkToCheck):
                                    print("It was not active from the previous day slot or it is 1st element True")
                                    diff = data["lastUpdatedTime"] - newStartTime
                                    arr.append(diff)
                                    addedAt.append(dataCount)

                        if(len(TrueOrfalse) == 0):
                            TrueOrfalse.append(data["partnerStatus"])
                        else:
                            if(TrueOrfalse[-1] != data["partnerStatus"]):
                                TrueOrfalse.append(data["partnerStatus"])
                                diff = data["lastUpdatedTime"] - newStartTime
                                arr.append(diff)
                                addedAt.append(dataCount)
                    else:
                        #false part
                        print("cou nt is " + str(dataCount) + str(data["_id"]))
                        print("True or false array is "+str(TrueOrfalse))
                        if(dataCount == 1):
                            if(len(previousDayStatus) > 0):
                                if(previousDayStatus[0] == False):
                                    print("It was not active from the previous day")
                                    diff = data["lastUpdatedTime"] - newStartTime
                                    arr.append(diff)
                                    addedAt.append(dataCount)
                                    zeroPrevCheck = True

                            if(zeroPrevCheck == False):
                                checkToCheck = False
                                if(brandId == 1):
                                    checkToCheck = dict.get(id,"nothing") == False or dict.get(id,"nothing") == "nothing"
                                else:
                                    checkToCheck = dict.get("sheet2Data").get(id,"nothing") == False or dict.get("sheet2Data").get(id,"nothing") == "nothing"

                                if(checkToCheck):
                                    print("It was not active from the previous day slot or it is 1st element False")
                                    diff = data["lastUpdatedTime"] - newStartTime
                                    print("time is "+str(data["lastUpdatedTime"])+"   new start is "+str(newStartTime) +"   diff is   "+str(diff))
                                    arr.append(diff)
                                    addedAt.append(dataCount)
                        #for last iteration if it is false then subracting the remaining time too
                        if(dataCount == dataCountTotal):
                            if(len(TrueOrfalse) > 0):
                                if(TrueOrfalse[-1] == data["partnerStatus"]):
                                    isAlreadyInserted = True
                                    diff = endTime - newStartTime
                                    arr.append(diff)
                                    addedAt.append(dataCount)
                                else:
                                    newStartTime = data["lastUpdatedTime"]
                                    isAlreadyInserted = True
                                    diff = endTime - newStartTime
                                    arr.append(diff)
                                    addedAt.append(dataCount)
                        if(len(TrueOrfalse) == 0):
                            TrueOrfalse.append(data["partnerStatus"])
                            newStartTime= data["lastUpdatedTime"]
                        else:
                            if(TrueOrfalse[-1] != data["partnerStatus"]):
                                TrueOrfalse.append(data["partnerStatus"])
                                newStartTime= data["lastUpdatedTime"]
                        print("updated false time is "+str(newStartTime))

                    dataCount +=1
            else:
                isElse = True

        print("arr item is added at: "+str(addedAt))
        print("checking is else"+str(isElse))
        for i in arr:
            print("array item is "+ str(i))
            updating += i
        finTime = updating - TimeOriginal
        print()

        print("length of TrueOrFalse is after"+str(len(TrueOrfalse)))
        print("length of arr is after"+str(len(arr)))

        print()
        if(not inActiveForThatSlot):
            if(isInSlot ==True ):
                print("ACtual working hours is : " +str((TimeArray[3]-TimeArray[0]) - (TimeArray[2]-TimeArray[1])))
                if(brandId == 1):
                    dict["actualTime"]=((TimeArray[3]-TimeArray[0]) - (TimeArray[2]-TimeArray[1]))
                else:
                    dict.get("sheet2Data")["actualTime"]=((TimeArray[3]-TimeArray[0]) - (TimeArray[2]-TimeArray[1]))
            else:
                print("ACtual working hours is : " +str(endTime - startTime))
                if(brandId == 1):
                    dict["actualTime"]=(endTime - startTime)
                else:
                    dict.get("sheet2Data")["actualTime"]=(endTime - startTime)
            print(" difference in time is " + str(finTime))
            if(brandId == 1):
                dict["differenceTime"]=(finTime)
            else:
                dict.get("sheet2Data")["differenceTime"]=(finTime)

            print("-----------------*************************************---------------------------------")
            if(isElse):
                print("No of hours cafe was actually opeartional is T "+ str(endTime - endTime))
                if(brandId == 1):
                    dict["actualWorkingHours"]=(endTime - endTime)
                else:
                    dict.get("sheet2Data")["actualWorkingHours"] = (endTime - endTime)
            else:
                if(isInSlot ==True):
                    print("No of hours cafe was actually opeartional is F "+ str(((TimeArray[3]-TimeArray[0]) - (TimeArray[2]-TimeArray[1]))-finTime) )
                    if(brandId == 1):
                        dict["actualWorkingHours"]=(((TimeArray[3]-TimeArray[0]) - (TimeArray[2]-TimeArray[1]))-finTime)
                    else:
                        dict.get("sheet2Data")["actualWorkingHours"]=(((TimeArray[3]-TimeArray[0]) - (TimeArray[2]-TimeArray[1]))-finTime)
                else:
                    print("No of hours cafe was actually opeartional is F "+ str(endTime - startTime - finTime))
                    if(brandId == 1):
                        dict["actualWorkingHours"]=(endTime - startTime - finTime)
                    else:
                        dict.get("sheet2Data")["actualWorkingHours"]=(endTime - startTime - finTime)

        else:
            print("-----------------------------------")
            print("| Unit is In_Active for that slot |")
            print("-----------------------------------")
            if(brandId == 1):
                dict["actualTime"]="IN_ACTIVE"
                dict["differenceTime"]="IN_ACTIVE"
                dict["actualWorkingHours"]="IN_ACTIVE"
            else:
                dict.get("sheet2Data")["actualTime"]="IN_ACTIVE"
                dict.get("sheet2Data")["differenceTime"]="IN_ACTIVE"
                dict.get("sheet2Data")["actualWorkingHours"]="IN_ACTIVE"
            print("-----------------*************************************---------------------------------")

        print("dict before ret is "+str(dict))
    return dict


if __name__ == '__main__':
    try:
        startProcess()
    except Exception as error:
        print("error occurred")
        print(error)
        traceback.print_exc()
        err=traceback.format_exc()
        send_mail(send_to=["<EMAIL>"], text=err,
                  subject="Error in generating swiggy report")
