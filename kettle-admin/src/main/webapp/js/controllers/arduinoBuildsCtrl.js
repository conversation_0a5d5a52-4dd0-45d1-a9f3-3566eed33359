/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("arduinoBuildsCtrl",
    function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location,fileService, AppUtil) {

        $scope.init = function () {
            $scope.activeVersion = null;
            $scope.initiatedVersion = null;
            $scope.buildUnits = null;
            $scope.currentUser = AppUtil.getCurrentUser();
            $scope.getActiveVersion();
        };

        $scope.getUnitList = function () {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.allUnits,
                params: {
                    category: "CAFE"
                }
            }).then(function success(response) {
                $scope.unitList = response.data.filter(function (unit) {
                    return unit.status === "ACTIVE";
                });
            }, function error(response) {
                console.log("error:" + response);
            });
        };


        $scope.addUnit = function(unit, monks){
            if($scope.buildUnits == null){
                $scope.buildUnits = [];
            }

        };

        function showInitBuild(response){
            $rootScope.showFullScreenLoader = false;
            if(!AppUtil.isEmptyObject(response.data)){
                $scope.initiatedVersion = response.data;
                $scope.getUnitList();
            }
        }

        $scope.initiatedBuild = function () {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.appsManagement.initiatedArduinoBuild
            }).then(showInitBuild, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.initiateBuild = function () {
            $http({
                method: 'POST',
                url: AppUtil.restUrls.appsManagement.initiateArduinoBuild + "/" + $scope.currentUser.id
            }).then(showInitBuild, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.getActiveVersion = function () {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.appsManagement.latestArduinoBuild
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                if(!AppUtil.isEmptyObject(response.data)){
                    $scope.activeVersion = response.data;
                }else{
                    $scope.initiatedBuild();
                }
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.activateBuild = function (build) {
            $http({
                method: 'POST',
                url: AppUtil.restUrls.appsManagement.activateArduinoBuild,
                data: build
            }).then(function success(response) {
                if(response.data){
                    alert("Activation Successful for Arduino Build");
                }else{
                    alert("Activation Failed for Arduino Build");
                }
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                console.log("error:" + response);
                alert("Activation Failed for Arduino Build");
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.uploadBuild = function () {
            if (fileService.getFile() == null || fileService.getFile() == undefined) {
                bootbox.alert("Please select a .xlsx file");
                return;
            }

            var fd = new FormData();
            fd.append("file", fileService.getFile());
            fd.append("userId", AppUtil.getCurrentUser()).userId;
            $rootScope.showFullScreenLoader = true;

            $http({
                url : AppUtil.restUrls.appsManagement.uploadArduinoBuild,
                method : 'POST',
                data : fd,
                headers : {
                    'Content-Type' : undefined
                },
                transformRequest : angular.identity
            }).success(function(response) {
                $rootScope.showFullScreenLoader = false;
                if (!AppUtil.isEmptyObject(response)) {
                    alert("Upload Successful for Arduino Build");
                } else {
                    alert("Unable to upload Arduino Build");
                }
                $scope.bankName = null;
            }).error(function(response) {
                $rootScope.showFullScreenLoader = false;
                alert("Error while uploading Arduino Build");
            });
        };

    }
);
