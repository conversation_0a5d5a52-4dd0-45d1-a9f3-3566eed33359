package com.stpl.tech.kettle.test.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.channelpartner.controller.ZomatoOrderAbstractResource;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.domain.model.DateDeserializer;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderCustomerDetailsV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderResponse;
import com.stpl.tech.kettle.test.mongo.dao.TestPartnerOrderDao;
import com.stpl.tech.kettle.test.mongo.model.PartnerOrderDetail;
import com.stpl.tech.kettle.test.service.TestZomatoService;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.endpoint.Endpoints;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.ZOMATO_TEST_ROOT_CONTEXT;

@Slf4j
@Service
public class TestZomatoServiceImpl extends ZomatoOrderAbstractResource implements TestZomatoService {

    @Autowired
    private TestPartnerOrderDao testPartnerOrderDao;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Override
    public List<PartnerOrderDetail> getZomatoLiveOrders(String partnerName, Integer startOrderId, Integer endOrderId) {
        try{
            List<String> orderIds = getOrderIdsInRange(startOrderId,endOrderId);
            return testPartnerOrderDao.searchAllByPartnerNameAndKettleOrderIdIn(partnerName,orderIds);
        }catch(Exception e ){
            log.error("Error while getting order data in range for partnerId :{}::::::::",partnerName, e);
            return  null;
        }
    }

    @Override
    public PartnerOrderDetail getPartnerOrder(String kettleOrderId) {
        try{
            return testPartnerOrderDao.searchByKettleOrderId(kettleOrderId);
        }catch(Exception e ){
            log.error("Error while partner order detail for orderId  :{}::::::::",kettleOrderId, e);
            return null;
        }
    }

    @Override
    public void updateTestCustomerDetails(ZomatoOrderRequestV3 zomatoOrderRequest, Integer unitId) {
        if(Objects.nonNull(zomatoOrderRequest)){
            if(Objects.nonNull(zomatoOrderRequest.getCustomerDetails())){
                zomatoOrderRequest.getCustomerDetails().setPhoneNumber(AppConstants.TEST_CONTACT_NUMBER);
            }else{
                ZomatoOrderCustomerDetailsV3 zomatoOrderCustomerDetailsV3 = new ZomatoOrderCustomerDetailsV3();
                zomatoOrderCustomerDetailsV3.setPhoneNumber(AppConstants.TEST_CONTACT_NUMBER);
            }
            if(Objects.nonNull(unitId)) {
                zomatoOrderRequest.setOutletId(Integer.toString(unitId));
            }
        }
    }

    @Override
    public List<PartnerOrderDetail> sendRequestToGetLivePartnerOrderData(String partnerName, Integer startOrderId, Integer endOrderId) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        log.info("&&&&&&&&&&&&&&&&& STEP 1 : PROCESSING BULK REQUEST TO GET LIVE ORDERS FOR PARTNER ::{} with startOrderID ::{} --------------------------", partnerName, startOrderId);
        String endPoint = environmentProperties.getLiveOrderBaseUrl()+ Endpoints.CHANNELPARTNER_SERVICE_ENTRY_POINT+ZOMATO_TEST_ROOT_CONTEXT+SEPARATOR+"get-live-orders";
        String authorization = environmentProperties.getLiveOrdersToken();
        Map<String,String> urivariables = new HashMap<>();
        urivariables.put("partnerName", partnerName);
        urivariables.put("startOrderId", startOrderId.toString());
        urivariables.put("endOrderId",endOrderId.toString());
        List<PartnerOrderDetail> partnerOrderDetailList = new ArrayList<>();
        try {
            List<?> list = WebServiceHelper.getRequestWithParam(endPoint, authorization, urivariables, List.class);
            GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer());
            if (list != null) {
                list.forEach(p -> {
                    Gson gson = gSonBuilder.create();
                    String str = gson.toJson(p);
                    PartnerOrderDetail partnerOrderDetail = gson.fromJson(str, PartnerOrderDetail.class);
                    if (partnerOrderDetail != null) {
                        partnerOrderDetailList.add(partnerOrderDetail);
                    }
                });
            }else{
                //do nothing
            }

        } catch (IOException | URISyntaxException e) {
            log.error("Exception thrown while getting live order partner data  for partner ::{}", partnerName);
        }
        log.info("&&&&&&&&&&&&&&&&& COMPLETED BULK REQUEST TO GET LIVE ORDERS FOR PARTNER ::{} with startOrderID ::{} and endOrderId ::{} in :::{} --------------------------", partnerName, startOrderId, endOrderId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return partnerOrderDetailList;
    }

    @Override
    public boolean addBatchedOrders(List<PartnerOrderDetail> partnerOrderDetailList, Integer startOrderId, Integer endOrderId, String partnerName, Map<String, Object> failedOrderMap, Integer unitId) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        log.info("&&&&&&&&&&&&&&&&& STEP 2 : PROCESSING BULK REQUEST TO ADD LIVE ORDERS FOR PARTNER  ON ENV  :::{} --------------------------", environmentProperties.getEnvType());
        ExecutorService taskExecutor = Executors.newFixedThreadPool(100);// or default 100
        AtomicBoolean isSuccess = new AtomicBoolean(true);
            if (Objects.nonNull(partnerOrderDetailList) && !partnerOrderDetailList.isEmpty()) {
                for (PartnerOrderDetail partnerOrderDetail : partnerOrderDetailList) {
                    taskExecutor.execute(() -> {
                        ObjectMapper mapper = new ObjectMapper();
                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                        String jsonString = new Gson().toJson(partnerOrderDetail.getPartnerOrder());
                        ZomatoOrderRequestV3 zomatoOrderRequest = null;
                        try {
                            zomatoOrderRequest = mapper.readValue(jsonString, ZomatoOrderRequestV3.class);
                        } catch (JsonProcessingException e) {
                            log.error("Exception while parsing zomato order request for partnerOrderId :{}::::::::::::::", partnerOrderDetail.getPartnerOrderId(), e);
                        }
                        updateTestCustomerDetails(zomatoOrderRequest, unitId);
                        isSuccess.set(addOrderRequest(zomatoOrderRequest,failedOrderMap));
                    });
                }
                taskExecutor.shutdown();
                try {
                    taskExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
                } catch (InterruptedException e) {
                    log.error("--------------------INTERUPTION !!!!-------------Error in completion of order threads", e);
                    return false;
                }
            }
        log.info("&&&&&&&&&&&&&&&&& COMPLETED BULK REQUEST TO ADD LIVE PARTNER ORDERS ::{} with startOrderID ::{} and endOrderId ::{} in :::{} --------------------------", partnerName, startOrderId, endOrderId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return isSuccess.get();
    }

    @Override
    public boolean addOrderRequest(ZomatoOrderRequestV3 zomatoOrderRequestV3,Map<String,Object> map) {
            ZomatoOrderResponse orderResponse = null ;
            try{
                orderResponse = addZomatoOrder(zomatoOrderRequestV3);
                if(Objects.nonNull(orderResponse)){
                    log.info("Response on processing  zomato add order request for partnerOrderId ::{} {}::::::::::::::::", zomatoOrderRequestV3.getOrderId(), new Gson().toJson(orderResponse));
                    if(orderResponse.getCode()!=200){
                        if(Objects.nonNull(map)){
                            map.put(String.valueOf(zomatoOrderRequestV3.getOrderId()),orderResponse);
                        }
                        return false;
                    }
                }
                return true ;
            }catch (Exception e ){
                log.error("Exception while adding order with orderId :::{}", zomatoOrderRequestV3.getOrderId(),e);
                return false;
            }
    }

    private List<String> getOrderIdsInRange(Integer startOrderId, Integer endOrderId) {
        List<String> orderIds = new ArrayList<>();
        for(int i = startOrderId ; i<= endOrderId;i++){
            orderIds.add(Integer.toString(i));
        }
        return orderIds;
    }

}
