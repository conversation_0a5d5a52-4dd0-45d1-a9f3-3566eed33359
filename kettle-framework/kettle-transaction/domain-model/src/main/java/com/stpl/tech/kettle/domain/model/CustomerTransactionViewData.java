package com.stpl.tech.kettle.domain.model;

import java.util.Date;

public class CustomerTransactionViewData {

	private Integer customerId;
	private Integer firstOrderId;
	private String firstOrderSource;
	private Integer firstOrderChannelPartnerId;
	private Date firstOrderBusinessDate;
	private Integer firstOrderUnitId;
	private Integer lastOrderId;
	private String lastOrderSource;
	private Integer lastOrderChannelPartnerId;
	private Date lastOrderBusinessDate;
	private Integer lastOrderUnitId;
	private Integer totalDineInOrders;
	private Integer totalDeliveryOrders;
	private Integer totalOrders;


	public CustomerTransactionViewData() {

	}

	public CustomerTransactionViewData(Integer customerId, Integer firstOrderId, String firstOrderSource,
									   Integer firstOrderChannelPartnerId, Date firstOrderBusinessDate, Integer firstOrderUnitId,
									   Integer lastOrderId, String lastOrderSource, Integer lastOrderChannelPartnerId,
									   Date lastOrderBusinessDate, Integer lastOrderUnitId, Integer totalDineInOrders,
									   Integer totalDeliveryOrders, Integer totalOrders) {
		this.customerId = customerId;
		this.firstOrderId = firstOrderId;
		this.firstOrderSource = firstOrderSource;
		this.firstOrderChannelPartnerId = firstOrderChannelPartnerId;
		this.firstOrderBusinessDate = firstOrderBusinessDate;
		this.firstOrderUnitId = firstOrderUnitId;
		this.lastOrderId = lastOrderId;
		this.lastOrderSource = lastOrderSource;
		this.lastOrderChannelPartnerId = lastOrderChannelPartnerId;
		this.lastOrderBusinessDate = lastOrderBusinessDate;
		this.lastOrderUnitId = lastOrderUnitId;
		this.totalDineInOrders = totalDineInOrders;
		this.totalDeliveryOrders = totalDeliveryOrders;
		this.totalOrders = totalOrders;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public Integer getFirstOrderId() {
		return firstOrderId;
	}

	public void setFirstOrderId(Integer firstOrderId) {
		this.firstOrderId = firstOrderId;
	}

	public String getFirstOrderSource() {
		return firstOrderSource;
	}

	public void setFirstOrderSource(String firstOrderSource) {
		this.firstOrderSource = firstOrderSource;
	}

	public Integer getFirstOrderChannelPartnerId() {
		return firstOrderChannelPartnerId;
	}

	public void setFirstOrderChannelPartnerId(Integer firstOrderChannelPartnerId) {
		this.firstOrderChannelPartnerId = firstOrderChannelPartnerId;
	}

	public Date getFirstOrderBusinessDate() {
		return firstOrderBusinessDate;
	}

	public void setFirstOrderBusinessDate(Date firstOrderBusinessDate) {
		this.firstOrderBusinessDate = firstOrderBusinessDate;
	}

	public Integer getFirstOrderUnitId() {
		return firstOrderUnitId;
	}

	public void setFirstOrderUnitId(Integer firstOrderUnitId) {
		this.firstOrderUnitId = firstOrderUnitId;
	}

	public Integer getLastOrderId() {
		return lastOrderId;
	}

	public void setLastOrderId(Integer lastOrderId) {
		this.lastOrderId = lastOrderId;
	}

	public String getLastOrderSource() {
		return lastOrderSource;
	}

	public void setLastOrderSource(String lastOrderSource) {
		this.lastOrderSource = lastOrderSource;
	}

	public Integer getLastOrderChannelPartnerId() {
		return lastOrderChannelPartnerId;
	}

	public void setLastOrderChannelPartnerId(Integer lastOrderChannelPartnerId) {
		this.lastOrderChannelPartnerId = lastOrderChannelPartnerId;
	}

	public Date getLastOrderBusinessDate() {
		return lastOrderBusinessDate;
	}

	public void setLastOrderBusinessDate(Date lastOrderBusinessDate) {
		this.lastOrderBusinessDate = lastOrderBusinessDate;
	}

	public Integer getLastOrderUnitId() {
		return lastOrderUnitId;
	}

	public void setLastOrderUnitId(Integer lastOrderUnitId) {
		this.lastOrderUnitId = lastOrderUnitId;
	}

	public Integer getTotalDineInOrders() {
		return totalDineInOrders;
	}

	public void setTotalDineInOrders(Integer totalDineInOrders) {
		this.totalDineInOrders = totalDineInOrders;
	}

	public Integer getTotalDeliveryOrders() {
		return totalDeliveryOrders;
	}

	public void setTotalDeliveryOrders(Integer totalDeliveryOrders) {
		this.totalDeliveryOrders = totalDeliveryOrders;
	}

	public Integer getTotalOrders() {
		return totalOrders;
	}

	public void setTotalOrders(Integer totalOrders) {
		this.totalOrders = totalOrders;
	}

	@Override
	public String toString() {
		return "CustomerTransactionViewData{" +
				"customerId=" + customerId +
				", firstOrderId=" + firstOrderId +
				", firstOrderSource='" + firstOrderSource + '\'' +
				", firstOrderChannelPartnerId=" + firstOrderChannelPartnerId +
				", firstOrderBusinessDate=" + firstOrderBusinessDate +
				", firstOrderUnitId=" + firstOrderUnitId +
				", lastOrderId=" + lastOrderId +
				", lastOrderSource='" + lastOrderSource + '\'' +
				", lastOrderChannelPartnerId=" + lastOrderChannelPartnerId +
				", lastOrderBusinessDate=" + lastOrderBusinessDate +
				", lastOrderUnitId=" + lastOrderUnitId +
				", totalDineInOrders=" + totalDineInOrders +
				", totalDeliveryOrders=" + totalDeliveryOrders +
				", totalOrders=" + totalOrders +
				'}';
	}
}
