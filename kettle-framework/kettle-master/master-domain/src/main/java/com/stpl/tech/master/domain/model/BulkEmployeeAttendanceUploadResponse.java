package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * Response DTO for bulk upload operations
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BulkEmployeeAttendanceUploadResponse {
    
    private int totalRecords;
    private int validRecords;
    private int invalidRecords;
    private int successfullyProcessed;
    private List<BulkUploadValidationError> validationErrors;
    private String message;
    private boolean success;
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BulkUploadValidationError {
        private int rowNumber;
        private String empId;
        private String field;
        private String errorMessage;
        private String originalValue;
    }
}
