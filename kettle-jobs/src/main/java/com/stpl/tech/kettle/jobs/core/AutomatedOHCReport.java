package com.stpl.tech.kettle.jobs.core;

import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpGet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.carfey.ops.job.Context;
import com.carfey.ops.job.param.Description;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.OrderSummaryOHC;
import com.stpl.tech.kettle.jobs.domain.model.OHCResponse;
import com.stpl.tech.kettle.partner.reporting.service.PartnerReportService;
import com.stpl.tech.kettle.reports.external.impl.FTPTransmission;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.util.AppUtils;

public class AutomatedOHCReport {

	@Component
	@Description("OHC EOD Daily Report")
	public static class OHCDailyEODReportJSON extends AbstractAutomatedJobReport {

		private static final Logger LOG = LoggerFactory.getLogger(OHCDailyEODReportJSON.class);

		@Autowired
		private EnvironmentProperties props;

		@Autowired
		private PartnerReportService reportService;

		@Override
		public void execute(Context context) throws Exception {
			String requestUrl = "http://www.mallsalesdata.com/api/requiredsales.php?apitoken=ChaayosOHC";
			int unitId = 26036;
			String storeId = "chaayos_OHC";
			String server = "ohc.mallsalesdata.com";
			int port = 21;
			String username = "<EMAIL>";
			String password = "SCos[{GSB}]RnCXh6a6yPv";
			List<Object> dateList = getAdditionalDate(requestUrl);

			Date currentDate = AppUtils.getPreviousBusinessDate();
			dateList.add(currentDate);

			for (Object obj : dateList) {
				Date businessDate = null;
				if (obj instanceof String) {
					businessDate = AppUtils.parseDateIST((String) obj);
				} else {
					businessDate = (Date) obj;
				}

				if (businessDate != null) {
					OrderSummaryOHC orderOHC = reportService.getOrderReportOHC(businessDate, unitId, storeId);

					if (orderOHC == null || orderOHC.getBills().size() == 0) {
						LOG.info("Report  can not be generated. As there are no orders");
					}
					if (orderOHC != null && orderOHC.getBills().size() > 0) {
						orderOHC.setFileExportDateTime(AppUtils.generateSimpleDateFormat(AppUtils.getCurrentDate()));
						ObjectMapper mapper = new ObjectMapper();
						String reportName = getReportName(orderOHC.getPOSDate());
						File file = new File(props.getBasePath() + "/reports/partnerOrder/" + reportName + ".txt");
						if (!file.exists()) {
							file.getParentFile().mkdirs();
						}
						mapper.writeValue(file, orderOHC);
						LOG.info("Generated report name : " + file.getName());

						FTPTransmission ftp = new FTPTransmission(server, port, username, password, null);
						ftp.transferJSONFile(props.getEnvironmentType(), file);
					}
				}
			}
		}

		@Override
		public EnvironmentProperties getProps() {
			return props;
		}

		private String getReportName(String posDate) {
			StringBuilder sb = new StringBuilder();
			sb.append("Chaayos");
			sb.append("_");
			sb.append(posDate);
			return sb.toString();
		}

		private List<Object> getAdditionalDate(String requestUrl) {
			HttpGet reqObj = new HttpGet(requestUrl);
			try {
				HttpResponse response = WebServiceHelper.getRequest(reqObj);
				System.out.println("response" + response);
				OHCResponse ohcResponse = null;
				if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
					ohcResponse = WebServiceHelper.convertResponse(response, OHCResponse.class);
					System.out.println("dlf report response " + ohcResponse);
				}
				return ohcResponse.getRequired_saledts();
			} catch (Exception e) {
				LOG.error("Error while getting dates from OHC api", e);
			} finally {
				reqObj.releaseConnection();
				reqObj.abort();
			}
			return new ArrayList<>();
		}
	}

	@Component
	@Description("OHC EOD Daily Report")
	public static class OHCDailyEODReportEmail extends AbstractAutomatedJobReport {

		private static final Logger LOG = LoggerFactory.getLogger(OHCDailyEODReportJSON.class);

		@Autowired
		private EnvironmentProperties props;

		@Autowired
		private PartnerReportService reportService;

		@Override
		public void execute(Context context) throws Exception {
			/**
			 * date format : "01-03-2018"
			 */
			String date = context.getConfig().getString("startDate");
			List<Date> dateList = getAdditionalDate(date);

			for (Date businessDate : dateList) {

				if (businessDate != null) {
					int unitId = 26036;
					String storeId = "chaayos_OHC";
					OrderSummaryOHC orderOHC = reportService.getOrderReportOHC(businessDate, unitId, storeId);

					if (orderOHC == null || orderOHC.getBills().size() == 0) {
						LOG.info("E-mail Report  can not be generated for eamil. As there are no orders");
					}
					if (orderOHC != null && orderOHC.getBills().size() > 0) {
						orderOHC.setFileExportDateTime(AppUtils.generateSimpleDateFormat(AppUtils.getCurrentDate()));
						ObjectMapper mapper = new ObjectMapper();
						String reportName = getReportName(orderOHC.getPOSDate());
						File file = new File(props.getBasePath() + "/reports/partnerOrder/" + reportName + ".txt");
						if (!file.exists()) {
							file.getParentFile().mkdirs();
						}
						mapper.writeValue(file, orderOHC);
						LOG.info("Generated report name : " + file.getName());
						/*List<String> list = new ArrayList<>();
						list.add("<EMAIL>");
						OHCReportNotification emailNotification = new OHCReportNotification(reportName + ".txt",
								getEnvironmentType(), list, AppConstants.JSON_MIME_TYPE);
						try {
							List<AttachmentData> attachments = new ArrayList<>();
							AttachmentData ohcReport = new AttachmentData();
							ohcReport.setAttachment(IOUtils.toByteArray(new FileInputStream(file)));
							ohcReport.setContentType(AppConstants.JSON_MIME_TYPE);
							ohcReport.setFileName(reportName);
							attachments.add(ohcReport);
							emailNotification.sendRawMail(attachments);
							// file.delete();// delete the temp file
							// created on Server
						} catch (Exception e) {
							LOG.error("Error while sending email to for OHC sales report :", e);
						}*/
					}
				}
			}
		}

		@Override
		public EnvironmentProperties getProps() {
			return props;
		}

		private String getReportName(String posDate) {
			StringBuilder sb = new StringBuilder();
			sb.append("Chaayos");
			sb.append("_");
			sb.append(posDate);
			return sb.toString();
		}

		private List<Date> getAdditionalDate(String startDate) {
			List<Date> list = new ArrayList<>();
			SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
			try {
				Date date = sdf.parse(startDate);
				list = getDaysBetweenDates(date, AppUtils.getPreviousBusinessDate());
				return list;
			} catch (ParseException e) {
				LOG.error("Error while parsing date :", e);
			}
			return new ArrayList<>();
		}
		
		private List<Date> getDaysBetweenDates(Date startdate, Date enddate)
		{
		    List<Date> dates = new ArrayList<Date>();
		    Calendar calendar = new GregorianCalendar();
		    calendar.setTime(startdate);

		    while (calendar.getTime().before(enddate))
		    {
		        Date result = calendar.getTime();
		        dates.add(result);
		        calendar.add(Calendar.DATE, 1);
		    }
		    return dates;
		}
	}
	
	/*public static void main(String[] args) {
		System.out.println("hello");
		System.out.println(new OHCDailyEODReportEmail().getAdditionalDate());
	}*/

}
