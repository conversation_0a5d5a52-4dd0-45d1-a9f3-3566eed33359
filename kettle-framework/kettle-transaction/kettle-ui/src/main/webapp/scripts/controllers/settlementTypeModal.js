/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp')
  .controller('settlementTypeModal', ['$scope','settlementTypeObj','$sce','$modalInstance','PrintService', 'AppUtil',
    function ($scope,settlementTypeObj,$sce,$modalInstance,PrintService, AppUtil) {
      $scope.settlementType = $sce.trustAsHtml('<div style="text-align: center;"><pre style="display: inline-block; text-align: left;">'
		+ AppUtil.clearSpecialChars(settlementTypeObj.replace(/6px/g,"12px")) + '</pre></div>');
      $scope.cancel= function(){
        $modalInstance.dismiss('dismiss');
      };

      $scope.print = function() {
        $modalInstance.dismiss('dismiss');
		if (AppUtil.isAndroid) {
			Android.printText(settlementTypeObj);
		} else {
			PrintService.printOnBilling(settlementTypeObj, "RAW");
		}
      };
    }]);
