package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.PriceProfileData;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface PriceProfileDao extends CrudRepository<PriceProfileData,Integer> {
    List<PriceProfileData> findAllByStatus(String status);

    PriceProfileData findByPriceProfileName(String priceProfileName);

    List<PriceProfileData> findAll();

    @Query("SELECT p.priceProfileName FROM PriceProfileData p WHERE p.status = :status")
    List<String> findPriceProfileNameByStatus(@Param(value = "status") String status);
}
