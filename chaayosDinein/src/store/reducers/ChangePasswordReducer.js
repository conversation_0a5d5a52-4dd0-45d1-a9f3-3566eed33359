export default function reducer(state = {
    showLoader: false,
    oldPassword:null,
    newPassword:null,
    confirmNewPassword:null,
    isOldPasswordValid: false,
    isOldPasswordIncorrect: false,
    isNewPasswordIncorrect: false,
    errorChangePassword: false,
    successChangePassword: false,
    isOPFormViewEnabled: false,
    isCPFormViewEnabled: false,
    hideOPassword: true,
    hideNPassword: true,
    hideCNPassword: true,
    isConfirmNewPasswordValid: false,
    isPasswordMismatch: false
}, action) {

    switch (action.type) {
        case "CHANGE_PASSWORD_LOADING":
        {
            return {...state, showLoader:action.payload};
            break;
        }
        case "SET_OLD_PASSWORD":
        {
            return {...state, oldPassword:action.payload};
            break;
        }
        case "SET_NEW_PASSWORD":
        {
            return {...state, newPassword:action.payload};
            break;
        }
        case "SET_CONFIRM_NEW_PASSWORD":
        {
            return {...state, confirmNewPassword:action.payload};
            break;
        }
        case "SET_IS_OLD_PASSWORD_VALID":
        {
            return {...state, isOldPasswordValid:action.payload};
            break;
        }
        case "SET_IS_OLD_PASSWORD_INCORRECT":
        {
            return {...state, isOldPasswordIncorrect:action.payload};
            break;
        }
        case "SET_IS_NEW_PASSWORD_INCORRECT":
        {
            return {...state, isNewPasswordIncorrect:action.payload};
            break;
        }
        case "SET_IS_PASSWORD_MISMATCH":
        {
            return {...state, isPasswordMismatch:action.payload};
            break;
        }
        case "SET_ERROR_CHANGE_PASSWORD":
        {
            return {...state, errorChangePassword:action.payload};
            break;
        }
        case "SET_SUCCESS_CHANGE_PASSWORD":
        {
            return {...state, successChangePassword:action.payload};
            break;
        }
        case "SET_IS_OP_FORM_VIEW_ENABLED":
        {
            return {...state, isOPFormViewEnabled:action.payload};
            break;
        }
        case "SET_IS_CP_FORM_VIEW_ENABLED":
        {
            return {...state, isCPFormViewEnabled:action.payload};
            break;
        }
        case "SET_HIDE_OLD_PASSWORD":
        {
            return {...state, hideOPassword:action.payload};
            break;
        }
        case "SET_HIDE_NEW_PASSWORD":
        {
            return {...state, hideNPassword:action.payload};
            break;
        }
        case "SET_HIDE_CONFIRM_NEW_PASSWORD":
        {
            return {...state, hideCNPassword:action.payload};
            break;
        }
        case "SET_IS_NEW_PASSWORD_VALID":
        {
            return {...state, isNewPasswordValid:action.payload};
            break;
        }
        case "SET_IS_CONFIRM_NEW_PASSWORD_VALID":
        {
            return {...state, isConfirmNewPasswordValid:action.payload};
            break;
        }
        default:
            return state;
    }

    return state;

}
