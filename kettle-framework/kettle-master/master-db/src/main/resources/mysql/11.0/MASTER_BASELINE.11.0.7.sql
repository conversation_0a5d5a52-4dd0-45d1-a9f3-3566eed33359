INSERT INTO KETTLE_MASTER_DEV.BRAND_ATTRIBUTE_DETAIL(BRAND_ID,ATTRIBUTE_KEY,ATTRIBUTE_VALUE,ATTRIBUTE_TYPE)
    VALUE (1,"INTERNAL_ORDER_FEEDBACK_URL","https://cafes.chaayos.com/feedback","java.lang.string");
INSERT INTO KETTLE_MASTER_DEV.BRAND_ATTRIBUTE_DETAIL(BRAND_ID,ATTRIBUTE_KEY,ATTRIBUTE_VALUE,ATTRIBUTE_TYPE)
    VALUE (2,"INTERNAL_ORDER_FEEDBACK_URL","https://cafes.chaayos.com/feedback","java.lang.string");

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING (PARTNER_ID, PERMISSION, ACL_ID, PPM_STATUS) VALUES ( 20, 1111, 153, 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME) VALUE("kettle-service.order-management.order-detail-feedback","Fetch order detail privilage","ACTIVE","KETTLE_SERVICE");


CREATE TABLE `KETTLE_MASTER_DEV`.`PRODUCT_NUTRITION_DETAIL` (
    `PRODUCT_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `CALORIE_COUNT` DECIMAL(10,2) NOT NULL,
    `PROTEIN_COUNT` DECIMAL(10,2) DEFAULT NULL,
    `FAT_COUNT` DECIMAL(10,2) DEFAULT NULL,
    `CARBOHYDRATE_COUNT` DECIMAL(10,2) DEFAULT NULL,
    `FIBER_COUNT` DECIMAL(10,2) DEFAULT NULL,
    PRIMARY KEY (`PRODUCT_ID`)
)

ALTER TABLE `KETTLE_MASTER_DEV`.`PRODUCT_NUTRITION_DETAIL`
    ADD COLUMN `PRODUCT_NUTRITION_DETAIL_ID` INT(11) NOT NULL AUTO_INCREMENT FIRST,
CHANGE COLUMN `PRODUCT_ID` `PRODUCT_ID` INT(11) NOT NULL ,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`PRODUCT_NUTRITION_DETAIL_ID`);

ALTER TABLE `KETTLE_MASTER_DEV`.`PRODUCT_NUTRITION_DETAIL`
    ADD UNIQUE INDEX `PRODUCT_ID_UNIQUE` (`PRODUCT_ID` ASC);

ALTER TABLE KETTLE_MASTER_DEV.EMPLOYEE_SESSION_DETAILS
    ADD COLUMN APP_VERSION VARCHAR(100);
