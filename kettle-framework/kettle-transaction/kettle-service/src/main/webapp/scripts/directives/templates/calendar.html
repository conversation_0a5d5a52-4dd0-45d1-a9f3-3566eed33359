<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="header">
    <i class="fa fa-angle-left" ng-click="previous()"></i>
    <span>{{month.format("MMMM, YYYY")}}</span>
    <i class="fa fa-angle-right" ng-click="next()"></i>
</div>
<div class="week names">
    <span class="day">Sun</span>
    <span class="day">Mon</span>
    <span class="day">Tue</span>
    <span class="day">Wed</span>
    <span class="day">Thu</span>
    <span class="day">Fri</span>
    <span class="day">Sat</span>
</div>
<div class="week" ng-repeat="week in weeks">
    <span class="day" ng-class="{
    today: day.isToday,
    'past-Date': isPastDate(day),
    selected: day.date.isSame(selected) }"
          ng-click="select(day)" ng-repeat="day in week.days">{{day.number}}</span>
</div>

