package com.stpl.tech.kettle.data.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "CASH_PACKET_LOG_DATA")
public class CashPacketLogData {

	private Integer cashPacketLogId;
	private Integer cashPacketId;
	private String transactionType;
	private String transactionCode; /* SignUpRefrral, Cash Bonus 5th SignUp, Cash Bonus 10th Sign Up */
	private String transactionCodeType; /* Referral,Cash Bonus, Initial Load */
	private BigDecimal transactionAmount;
	private String transactionReason; /* Sign Up, Initial Load */
	private Date transactionTime;
	private Integer cashLogDataId;/* Reference for chaayos transaction Log (Many to one) */
	private Integer orderId;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CASH_PACKET_LOG_DATA_ID", unique = true, nullable = false)
	public Integer getCashPacketLogId() {
		return cashPacketLogId;
	}

	public void setCashPacketLogId(Integer cashPacketLogId) {
		this.cashPacketLogId = cashPacketLogId;
	}

	@Column(name = "CASH_PACKET_ID", nullable = false)
	public Integer getCashPacketId() {
		return cashPacketId;
	}

	public void setCashPacketId(Integer casPacketId) {
		this.cashPacketId = casPacketId;
	}

	@Column(name = "TRANSACTION_TYPE", nullable = false)
	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	@Column(name = "TRANSACTION_CODE", nullable = false)
	public String getTransactionCode() {
		return transactionCode;
	}

	public void setTransactionCode(String transactionCode) {
		this.transactionCode = transactionCode;
	}

	@Column(name = "TRANSACTION_CODE_TYPE", nullable = false)
	public String getTransactionCodeType() {
		return transactionCodeType;
	}

	public void setTransactionCodeType(String transactionCodeType) {
		this.transactionCodeType = transactionCodeType;
	}

	@Column(name = "TRANSACTION_AMOUNT", nullable = false)
	public BigDecimal getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(BigDecimal transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	@Column(name = "TRANSACTION_REASON", nullable = false)
	public String getTransactionReason() {
		return transactionReason;
	}

	public void setTransactionReason(String transactionReason) {
		this.transactionReason = transactionReason;
	}

	@Column(name = "CASH_LOG_DATA_ID", nullable = true)
	public Integer getCashLogDataId() {
		return cashLogDataId;
	}

	public void setCashLogDataId(Integer cashDataLogId) {
		this.cashLogDataId = cashDataLogId;
	}

	@Column(name = "TRANSACTION_TIME", nullable = false)
	public Date getTransactionTime() {
		return transactionTime;
	}

	public void setTransactionTime(Date transactionTime) {
		this.transactionTime = transactionTime;
	}

	@Column(name = "ORDER_ID", nullable = true)
	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

}
