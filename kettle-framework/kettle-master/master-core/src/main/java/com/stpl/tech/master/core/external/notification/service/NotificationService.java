package com.stpl.tech.master.core.external.notification.service;

import com.stpl.tech.kettle.core.service.authorization.OTPMapper;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;

import javax.jms.JMSException;
import java.io.IOException;

/**
 * Created by Chaayos on 21-09-2016.
 */
public interface NotificationService {

	public boolean sendNotification(String type, String message, String contact, MessagingClient client,
									boolean sendNotification, NotificationPayload payload) throws IOException, JMSException;

	public OTPMapper getOTPMapperInstance();

	boolean sendOTPRequestViaIVR(String type, String contact, SMSWebServiceClient client, boolean sendNotification, NotificationPayload payload, String token, Integer ivrId);
}
