package com.stpl.tech.master.data.dao.impl;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.master.core.MappingStatus;
import com.stpl.tech.master.data.dao.RiderMappingDao;
import com.stpl.tech.master.data.model.EmployeeDetail;
import com.stpl.tech.master.data.model.RiderUnitMapping;
import com.stpl.tech.master.domain.model.EmploymentStatus;
import com.stpl.tech.util.AppUtils;

@Repository
public class RiderMappingDaoImpl extends AbstractMasterDaoImpl implements RiderMappingDao {

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.master.data.dao.impl.RiderMappingDao#addMapping(int,
	 * int)
	 */
	@Override
	public boolean addMapping(int employeeId, int unitId) {
		EmployeeDetail employee = manager.find(EmployeeDetail.class, employeeId);
		if (employee == null || employee.getEmploymentStatus().equals(EmploymentStatus.IN_ACTIVE.name())) {
			return false;
		}
		RiderUnitMapping mapping = find(employeeId, unitId);
		if (mapping == null) {

			mapping = new RiderUnitMapping(employeeId, unitId, MappingStatus.ENABLED.name(),
					AppUtils.getCurrentTimestamp());
			manager.persist(mapping);
		} else {
			mapping.setMappingStatus(MappingStatus.ENABLED.name());
			mapping.setLastUpdateTime(AppUtils.getCurrentTimestamp());
		}
		manager.flush();
		return true;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.master.data.dao.impl.RiderMappingDao#deleteMapping(int,
	 * int)
	 */
	@Override
	public boolean deleteMapping(int employeeId, int unitId) {
		return changeStatus(MappingStatus.DELETED, employeeId, unitId);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.master.data.dao.impl.RiderMappingDao#disableMapping(int,
	 * int)
	 */
	@Override
	public boolean disableMapping(int employeeId, int unitId) {
		return changeStatus(MappingStatus.DISABLED, employeeId, unitId);

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.master.data.dao.impl.RiderMappingDao#enableMapping(int,
	 * int)
	 */
	@Override
	public boolean enableMapping(int employeeId, int unitId) {
		return addMapping(employeeId, unitId);

	}

	private boolean changeStatus(MappingStatus status, int employeeId, int unitId) {
		RiderUnitMapping mapping = find(employeeId, unitId);
		if (mapping == null) {
			return false;
		} else {
			mapping.setMappingStatus(status.name());
			mapping.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			manager.flush();
			return true;
		}

	}

	private RiderUnitMapping find(int employeeId, int unitId) {
		try {
			Query query = manager
					.createQuery("FROM RiderUnitMapping E where E.unitId = :unitId and E.employeeId = :employeeId");
			query.setParameter("employeeId", employeeId);
			query.setParameter("unitId", unitId);
			return (RiderUnitMapping) query.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}
	}

	@Override
	public List<RiderUnitMapping> getMapping(int unitId, List<String> mappingStatus) {
		try {
			Query query = manager.createQuery(
					"FROM RiderUnitMapping E where E.unitId = :unitId and E.mappingStatus IN (:mappingStatus)");
			query.setParameter("mappingStatus", mappingStatus);
			query.setParameter("unitId", unitId);
			return query.getResultList();
		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.master.data.dao.RiderMappingDao#getMapping(int)
	 */
	@Override
	public List<RiderUnitMapping> getMapping(int employeeId) {
		try {
			Query query = manager.createQuery(
					"FROM RiderUnitMapping E where E.employeeId = :employeeId");
			query.setParameter("employeeId", employeeId);
			return query.getResultList();
		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}
}
