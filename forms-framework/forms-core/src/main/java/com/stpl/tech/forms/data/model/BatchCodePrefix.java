package com.stpl.tech.forms.data.model;

import javax.persistence.*;

@Entity
@Table(name = "BATCH_CODE_PREFIX" , uniqueConstraints = @UniqueConstraint(columnNames = {"ID", "KEY_ID", "CODE_PREFIX"}))
public class BatchCodePrefix {

    private Integer id;

    private String keyId;

    private String keyType;

    private String codePrefix;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "KEY_ID", unique = true, nullable = false)
    public String getKeyId() {
        return keyId;
    }

    public void setKeyId(String keyId) {
        this.keyId = keyId;
    }

    @Column(name = "KEY_TYPE", nullable = false)
    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    @Column(name = "CODE_PREFIX", unique = true, nullable = false)
    public String getCodePrefix() {
        return codePrefix;
    }

    public void setCodePrefix(String codePrefix) {
        this.codePrefix = codePrefix;
    }

}