package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.ApplicationData;
import com.stpl.tech.master.data.model.ApplicationInstallationData;
import com.stpl.tech.master.data.repository.ApplicationDataRepository;
import com.stpl.tech.master.data.repository.ApplicationInstallationDataRepository;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.recipe.model.BasicInfo;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
public class LocationValidationService {
    private static final Logger LOG = LoggerFactory.getLogger(LocationValidationService.class);

    @Autowired
    private ApplicationDataRepository applicationDataRepository;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private ApplicationInstallationDataRepository installationDataRepository;

    public void validateLocation(Integer applicationId, String macAddress, String geoLocation, Integer unitId)
            throws AuthenticationFailureException {
        
        ApplicationData appData = applicationDataRepository.findById(applicationId).get();

        if (appData == null) {
            throw new AuthenticationFailureException("Invalid application");
        }

        // Validate MAC address if enabled
        if (AppUtils.getStatus(appData.getMacValidationEnabled())) {
            validateMacAddress(macAddress, unitId);
        }

        // Validate geolocation if enabled
        if (AppUtils.getStatus(appData.getGeoValidationEnabled())) {
            validateGeoLocation(geoLocation, unitId, appData.getGeoValidationRadius());
        }
    }

    private void validateMacAddress(String macAddress, Integer unitId)
            throws AuthenticationFailureException {
        
        if (macAddress == null || macAddress.trim().isEmpty()) {
            throw new AuthenticationFailureException("MAC address is required");
        }

        // Get registered MAC addresses for the unit
        List<ApplicationInstallationData> installations = 
            installationDataRepository.findByUnitIdAndStatus(unitId, "ACTIVE");

        boolean isValid = installations.stream()
            .anyMatch(installation -> {
                //if (installation.getMachineId() == null) return false;
                
                // Check if MAC address is within allowed radius
                return isMacAddressValid(installation.getMachineId(), macAddress);
            });

        if (!isValid) {
            LOG.warn("MAC address validation failed for unit {} with MAC {}", unitId, macAddress);
            throw new AuthenticationFailureException(
                "MAC address validation failed. Device not registered or out of range.");
        }
    }

    private void validateGeoLocation(String geoLocation, Integer unitId, BigDecimal radius)
            throws AuthenticationFailureException {
        
        if (geoLocation == null || geoLocation.trim().isEmpty()) {
            throw new AuthenticationFailureException("Geolocation is required");
        }

        // Get unit's location
        UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(unitId);
        String lat = unitBasicDetail.getLatitude();
        String longitude = unitBasicDetail.getLongitude();

        if (lat == null || longitude == null) {
            throw new AuthenticationFailureException("Unit location not configured");
        }

        // Check if user's location is within allowed radius
        if (!isLocationInRange(Double.parseDouble(lat),Double.parseDouble(longitude), geoLocation, radius.doubleValue())) {
            LOG.warn("Geolocation validation failed for unit {} with location {}", unitId, geoLocation);
            throw new AuthenticationFailureException(
                "Geolocation validation failed. Device out of allowed range.");
        }
    }

    private boolean isMacAddressValid(String registeredMac, String currentMac) {
        // For MAC address, we'll do a simple equality check
        // In a real implementation, you might want to do more complex validation
        return registeredMac.equalsIgnoreCase(currentMac);
    }

    private boolean isLocationInRange(Double unitLat,Double unitLon , String userLocation, Double radius) {
        try {
            // Parse locations (assuming format: "latitude,longitude")
            String[] userCoords = userLocation.split(",");

            double userLat = Double.parseDouble(userCoords[0]);
            double userLon = Double.parseDouble(userCoords[1]);

            // Calculate distance using Haversine formula
            double distance = calculateDistance(unitLat, unitLon, userLat, userLon);

            return distance <= radius;
        } catch (Exception e) {
            LOG.error("Error calculating location distance", e);
            return false;
        }
    }

    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371000; // Earth's radius in meters

        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }
} 