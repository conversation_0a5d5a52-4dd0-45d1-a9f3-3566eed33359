<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		
		<category name="Unit Wise Intra Day Sales Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails=""
                        schedule="">
			<reports>
				<report id="1" name="Summary of Intra Day Sales Report " executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
	ud.UNIT_ID, 
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    A.NET_TICKETS,
    A.NET_SALES,
    A.NET_APC,
    B.LWSD_NET_TICKETS,
    B.LWSD_NET_SALES,
    B.LWSD_NET_APC,
    A.NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_SALES,
    A.NET_DELIVERY_APC,
    COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0) LWSD_NET_DELIVERY_TICKETS,
    COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) LWSD_NET_DELIVERY_SALES,
    COALESCE(B.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 2) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
	FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE IN ('order','paid-employee-meal')
         AND od.UNIT_ID IN (:unitIds)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE IN ('order','paid-employee-meal')
         AND od.UNIT_ID IN (:unitIds)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048,1157,1158)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE IN ('order','paid-employee-meal')
         AND od.UNIT_ID IN (:unitIds)
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE IN ('order','paid-employee-meal')
                 AND od.UNIT_ID IN (:unitIds)
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE IN ('order','paid-employee-meal')
         AND od.UNIT_ID IN (:unitIds)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE IN ('order','paid-employee-meal')
         AND od.UNIT_ID IN (:unitIds)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT
        a.UNIT_ID,
            SUM(case when a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT then 1 else 0 end ) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE IN ('order','paid-employee-meal')
         AND od.UNIT_ID IN (:unitIds)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048,1157,1158)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE IN ('order','paid-employee-meal')
         AND od.UNIT_ID IN (:unitIds)
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE IN ('order','paid-employee-meal')
                 AND od.UNIT_ID IN (:unitIds)
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE IN ('order','paid-employee-meal')
         AND od.UNIT_ID IN (:unitIds)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) B ON B.UNIT_ID = ud.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
        OR B.LWSD_NET_TICKETS IS NOT NULL
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME

 ]]>
					</content>
					<params>
						<param name="unitIds" displayName="unitIds"
							dataType="INTEGER" />
					</params>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>
