/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stpl.tech.kettle.core.DeliveryConstants;
import com.stpl.tech.kettle.delivery.model.AuthorizationObject;
import com.stpl.tech.kettle.delivery.model.DeliveryRequest;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.master.core.WebServiceHelper;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.codehaus.jettison.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public abstract class AbstractDeliveryStrategy {
	private static final Logger LOG = LoggerFactory.getLogger(AbstractDeliveryStrategy.class);

	protected AuthorizationObject authorization;

	public abstract DeliveryResponse readResponse(HttpResponse response, String orderId);

	public HttpUriRequest addHeaders(HttpUriRequest requestObject, AuthorizationObject token) {
		LOG.info("Content type ::: {}", ContentType.APPLICATION_JSON.toString());
		requestObject.addHeader(WebServiceHelper.AUTHORIZATION, token.getToken());
		requestObject.addHeader(WebServiceHelper.CONTENT_TYPE, ContentType.APPLICATION_JSON.toString());
		return requestObject;
	}

	public HttpResponse createRequest(String creationEndpoint, DeliveryRequest request) {
		HttpPost requestObject = new HttpPost(creationEndpoint);
		requestObject = (HttpPost) addHeaders(requestObject, getAuthorization());
		String requestJson = null;
		try {
			requestJson = WebServiceHelper.convertToString(request);
			HttpEntity orderJson = new StringEntity(requestJson, DeliveryConstants.CHARSET);
			LOG.info("requestJson is ::::::::::::::::::::::::::: {}", requestJson.toString());
			requestObject.setEntity(orderJson);
			LOG.info("inside createRequest of Abstract Strategy before sending request");
			HttpResponse responseFromRequest = WebServiceHelper.postRequest(requestObject);
			LOG.info("HttpResponse after request sent ::::: {} :::: {}",
					responseFromRequest.getStatusLine().getStatusCode(),
					responseFromRequest.getStatusLine().getReasonPhrase());

			return responseFromRequest;
		} catch (JsonProcessingException e) {
			LOG.error("Unable to parse json :::", e);

		} catch (ClientProtocolException e) {
			LOG.error("Invalid client protocol :::", e);
		} catch (IOException e) {
			LOG.error("IO Exception encountered :::", e);
		}
		return null;
	}

	public <T> HttpResponse putRequest(String putEndpoint, T request) {
		HttpPut requestObject = new HttpPut(putEndpoint);
		requestObject = (HttpPut) addHeaders(requestObject, getAuthorization());
		String requestJson = null;
		try {
			if (request instanceof JSONObject) {
				requestJson = request.toString();
			} else {
				requestJson = WebServiceHelper.convertToString(request);
			}
			HttpEntity orderJson = new StringEntity(requestJson, DeliveryConstants.CHARSET);
			LOG.info("requestJson is ::::::::::::::::::::::::::: {}", requestJson.toString());
			requestObject.setEntity(orderJson);
			LOG.info("inside put request of Abstract Strategy before sending request");
			HttpResponse responseFromRequest = WebServiceHelper.putRequest(requestObject);
			LOG.info("HttpResponse after request sent ::::: {} :::: {}",
					responseFromRequest.getStatusLine().getStatusCode(),
					responseFromRequest.getStatusLine().getReasonPhrase());

			return responseFromRequest;
		} catch (JsonProcessingException e) {
			LOG.error("Unable to parse json :::", e);
		} catch (ClientProtocolException e) {
			LOG.error("Invalid client protocol :::", e);
		} catch (IOException e) {
			LOG.error("IO Exception encountered :::", e);
		}
		return null;
	}

	public String replaceInEndPoint(String endpoint, String toReplace, String taskId) {
		endpoint = endpoint.replaceAll(toReplace, taskId);
		return endpoint;
	}

	public AuthorizationObject getAuthorization() {
		return authorization;
	}

}
