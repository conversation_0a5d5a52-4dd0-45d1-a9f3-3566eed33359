window.changeMainRating=(rating)=>{
    if(rating>0 && selectedMainRating==0 && (!orderDetail.sonr || (orderDetail.sonr && selectedNpsRating>0))){
        for(var i=1;i<=orderDetail.fiol.length;i++){
            document.getElementsByClassName("productExpand")[i-1].style.display='block';
        }
    }
    selectedMainRating=rating;
    orderDetail.or=rating;
    if(rating > 0 && (!orderDetail.sonr || (orderDetail.sonr && selectedNpsRating>0))){
        document.getElementById("submitButton").style.backgroundColor='#108A45';
        document.getElementById("submitFeedbackButton").style.display='block';
        document.getElementById("orderCommentArea").style.display='block';
        document.getElementById("orderCommentText").style.display='block';
    }
    if(orderDetail.sor && selectedMainRating > 0 && selectedMainRating <= 3){
        document.getElementById("callbackCheckboxContainer").style.display='block';
    }else if(orderDetail.sonr && selectedNpsRating > 0 && selectedNpsRating <= 6){
        document.getElementById("callbackCheckboxContainer").style.display='block';
    }else{
        document.getElementById("callbackCheckboxContainer").style.display='none';
    }
    document.getElementById("main_star_1").innerHTML=
        (selectedMainRating == 0)? inActiveStar():
            (selectedMainRating == 1) ? horribleStar(11,"Horrible") :
                (selectedMainRating == 2) ? redStar(11) :
                    (selectedMainRating == 3) ? yellowStar(): greenStar();
    document.getElementById("main_star_2").innerHTML=
        (selectedMainRating <=1)? inActiveStar():
            (selectedMainRating == 2) ? badStar(11,"Bad") :
                (selectedMainRating == 3) ? yellowStar(11) : greenStar();
    document.getElementById("main_star_3").innerHTML=
        (selectedMainRating <= 2)? inActiveStar():
            (selectedMainRating == 3) ? averageStar(11,"Average") : greenStar();
    document.getElementById("main_star_4").innerHTML=
        (selectedMainRating <= 3)? inActiveStar():
            (selectedMainRating == 4) ? goodStar(11,"Good") : greenStar();
    document.getElementById("main_star_5").innerHTML=
        (selectedMainRating <= 4)? inActiveStar() : excellentStar(11,"Excellent");
}

window.getBasePath=(uiBaseUrl)=>{
    if(uiBaseUrl.indexOf("cafes.chaayos.com") >= 0){
        return "https://relax.chaayos.com";
    }else if(uiBaseUrl.indexOf("stage.kettle.chaayos.com") >= 0){
        return "http://stage.kettle.chaayos.com:8080"
    }else if(uiBaseUrl.indexOf("dev.kettle.chaayos.com")){
        return "http://dev.kettle.chaayos.com:8080"
    }else if(uiBaseUrl.indexOf("localhost")) {
        return "http://localhost:8080"
    }
}
window.changeNpsRating=(rating)=>{
    if(rating>0 && selectedNpsRating==0 && (!orderDetail.sor || (orderDetail.sor && selectedMainRating>0))){
        for(var i=1;i<=orderDetail.fiol.length;i++){
            document.getElementsByClassName("productExpand")[i-1].style.display='block';
        }
    }
    selectedNpsRating=rating;
    orderDetail.onr=rating;
    if(rating > 0 && (!orderDetail.sor || (orderDetail.sor && selectedMainRating>0))){
        document.getElementById("submitButton").style.backgroundColor='#108A45';
        document.getElementById("submitFeedbackButton").style.display='block';
        document.getElementById("orderCommentArea").style.display='block';
        document.getElementById("orderCommentText").style.display='block';
    }
    if(orderDetail.sor && selectedMainRating > 0 && selectedMainRating <= 3){
        document.getElementById("callbackCheckboxContainer").style.display='block';
    }else if(orderDetail.sonr && selectedNpsRating > 0 && selectedNpsRating <= 6){
        document.getElementById("callbackCheckboxContainer").style.display='block';
    }else{
        document.getElementById("callbackCheckboxContainer").style.display='none';
    }
    document.getElementById("main_nps_star_1").innerHTML=
        (selectedNpsRating == 0)? inActiveStar():
            (selectedNpsRating == 1) ? horribleStar(11,"") :
                (selectedNpsRating >= 1 && selectedNpsRating <=6 && selectedNpsRating!=1) ? redStar(11) :
                    (selectedNpsRating == 7 || selectedNpsRating==8) ? yellowStar(): greenStar();
    document.getElementById("main_nps_star_2").innerHTML=
        (selectedNpsRating < 2)? inActiveStar():
            (selectedNpsRating == 2) ? horribleStar(11,"") :
                (selectedNpsRating >= 1 && selectedNpsRating <=6 && selectedNpsRating!=2) ? redStar(11) :
                    (selectedNpsRating == 7 || selectedNpsRating==8) ? yellowStar(): greenStar();
    document.getElementById("main_nps_star_3").innerHTML=
        (selectedNpsRating <3)? inActiveStar():
            (selectedNpsRating == 3) ? horribleStar(11,"") :
                (selectedNpsRating >= 1 && selectedNpsRating <=6 && selectedNpsRating!=3) ? redStar(11) :
                    (selectedNpsRating == 7 || selectedNpsRating==8) ? yellowStar(): greenStar();
    document.getElementById("main_nps_star_4").innerHTML=
        (selectedNpsRating <4)? inActiveStar():
            (selectedNpsRating == 4) ? badStar(11,"") :
                (selectedNpsRating >= 1 && selectedNpsRating <=6 && selectedNpsRating!=4) ? redStar(11) :
                    (selectedNpsRating == 7 || selectedNpsRating==8) ? yellowStar(11) : greenStar();
    document.getElementById("main_nps_star_5").innerHTML=
        (selectedNpsRating <5)? inActiveStar():
            (selectedNpsRating == 5) ? badStar(11,"") :
                (selectedNpsRating >= 1 && selectedNpsRating <=6 && selectedNpsRating!=5) ? redStar(11) :
                    (selectedNpsRating == 7 || selectedNpsRating==8) ? yellowStar(11) : greenStar();
    document.getElementById("main_nps_star_6").innerHTML=
        (selectedNpsRating <6)? inActiveStar():
            (selectedNpsRating == 6) ? badStar(11,"") :
                (selectedNpsRating >= 1 && selectedNpsRating <=6 && selectedNpsRating!=6) ? redStar(11) :
                    (selectedNpsRating == 7 || selectedNpsRating==8) ? yellowStar(11) : greenStar();
    document.getElementById("main_nps_star_7").innerHTML=
        (selectedNpsRating <7)? inActiveStar():
            (selectedNpsRating == 7) ? averageStar(11,"") :
                (selectedNpsRating == 8) ? yellowStar():greenStar();
    document.getElementById("main_nps_star_8").innerHTML=
        (selectedNpsRating <8)? inActiveStar():
            (selectedNpsRating == 8) ? averageStar(11,"") : greenStar();
    document.getElementById("main_nps_star_9").innerHTML=
        (selectedNpsRating <9)? inActiveStar():
            (selectedNpsRating == 9) ? goodStar(11,"") : greenStar();
    document.getElementById("main_nps_star_10").innerHTML=
        (selectedNpsRating <10)? inActiveStar() : excellentStar(11,"");
}
window.changeAlreadyRating=(rating)=>{
    document.getElementById("already_star_1").innerHTML=
        (rating == 0)? inActiveStar():
            (rating == 1) ? horribleStar(11,"Horrible") :
                (rating == 2) ? redStar(11) :
                    (rating == 3) ? yellowStar(): greenStar();
    document.getElementById("already_star_2").innerHTML=
        (rating <=1)? inActiveStar():
            (rating == 2) ? badStar(11,"Bad") :
                (rating == 3) ? yellowStar(11) : greenStar();
    document.getElementById("already_star_3").innerHTML=
        (rating <= 2)? inActiveStar():
            (rating == 3) ? averageStar(11,"Average") : greenStar();
    document.getElementById("already_star_4").innerHTML=
        (rating <= 3)? inActiveStar():
            (rating == 4) ? goodStar(11,"Good") : greenStar();
    document.getElementById("already_star_5").innerHTML=
        (rating <= 4)? inActiveStar() : excellentStar(11,"Excellent");
}
window.changeAlreadyNpsRating=(rating)=>{
    document.getElementById("already_star_nps_1").innerHTML=
        (rating == 0)? inActiveStar():
            (rating == 1) ? horribleStar(11,"") :
                (rating >= 1 && rating <=6 && rating!=1) ? redStar(11) :
                    (rating == 7 || rating==8) ? yellowStar(): greenStar();
    document.getElementById("already_star_nps_2").innerHTML=
        (rating < 2)? inActiveStar():
            (rating == 2) ? horribleStar(11,"") :
                (rating >= 1 && rating <=6 && rating!=2) ? redStar(11) :
                    (rating == 7 || rating==8) ? yellowStar(): greenStar();
    document.getElementById("already_star_nps_3").innerHTML=
        (rating <3)? inActiveStar():
            (rating == 3) ? horribleStar(11,"") :
                (rating >= 1 && rating <=6 && rating!=3) ? redStar(11) :
                    (rating == 7 || rating==8) ? yellowStar(): greenStar();
    document.getElementById("already_star_nps_4").innerHTML=
        (rating <4)? inActiveStar():
            (rating == 4) ? badStar(11,"") :
                (rating >= 1 && rating <=6 && rating!=4) ? redStar(11) :
                    (rating == 7 || rating==8) ? yellowStar(11) : greenStar();
    document.getElementById("already_star_nps_5").innerHTML=
        (rating <5)? inActiveStar():
            (rating == 5) ? badStar(11,"") :
                (rating >= 1 && rating <=6 && rating!=5) ? redStar(11) :
                    (rating == 7 || rating==8) ? yellowStar(11) : greenStar();
    document.getElementById("already_star_nps_6").innerHTML=
        (rating <6)? inActiveStar():
            (rating == 6) ? badStar(11,"") :
                (rating >= 1 && rating <=6 && rating!=6) ? redStar(11) :
                    (rating == 7 || rating==8) ? yellowStar(11) : greenStar();
    document.getElementById("already_star_nps_7").innerHTML=
        (rating <7)? inActiveStar():
            (rating == 7) ? averageStar(11,"") :
                (rating == 8) ? yellowStar():greenStar();
    document.getElementById("already_star_nps_8").innerHTML=
        (rating <8)? inActiveStar():
            (rating == 8) ? averageStar(11,"") : greenStar();
    document.getElementById("already_star_nps_9").innerHTML=
        (rating <9)? inActiveStar():
            (rating == 9) ? goodStar(11,"") : greenStar();
    document.getElementById("already_star_nps_10").innerHTML=
        (rating <10)? inActiveStar() : excellentStar(11,"");
}
window.changeProductRating=(rating,index)=>{
    console.log(orderDetail);
    productSelectedRating[index-1]=rating;
    let productRating=productSelectedRating[index-1];
    orderDetail.fiol[index-1].ir = rating;
    console.log(rating,index);
    document.getElementById("product_star_"+index+"_1").innerHTML=
        (productRating == 0)? inActiveStar():
            (productRating == 1) ? horribleStar(9,"Horrible") :
                (productRating == 2) ? redStar(9) :
                    (productRating == 3) ? yellowStar(): greenStar();
    document.getElementById("product_star_"+index+"_2").innerHTML=
        (productRating <=1)? inActiveStar():
            (productRating == 2) ? badStar(9,"Bad") :
                (productRating == 3) ? yellowStar() : greenStar();
    document.getElementById("product_star_"+index+"_3").innerHTML=
        (productRating <= 2)? inActiveStar():
            (productRating == 3) ? averageStar(9, "Average") : greenStar();
    document.getElementById("product_star_"+index+"_4").innerHTML=
        (productRating <= 3)? inActiveStar():
            (productRating == 4) ? goodStar(9, "Good") : greenStar();
    document.getElementById("product_star_"+index+"_5").innerHTML=
        (productRating <= 4)? inActiveStar() : excellentStar(9, "Excellent");
}
window.getSubmitButtonChild=(submitting)=>{
    isSubmitting = submitting;
    if(!submitting){
        return `<p style="margin: 0; font-family: 'Nunito'; font-size: 12px;" id="submitButtonText">SUBMIT FEEDBACK</p>`;
    }
    return `<div class="spinner-border text-light" role="status" id="spinner" style="width: 25px; height: 25px"></div>`;
}
window.setOrderComment=()=>{
    orderDetail.oc = document.getElementById("order_textarea").value;
}

window.addIssueTag=(i,tagIndex)=>{
    orderDetail.fiol[i-1].tags[tagIndex-1] = !orderDetail.fiol[i-1].tags[tagIndex-1];
    document.getElementById("issueTagContainer_"+i).innerHTML = "";
    document.getElementById("issueTagContainer_"+i).innerHTML = getIssueTagButtons(i,orderDetail.fiol[i-1].tags);
    orderDetail.fiol[i-1].it = getTagString(orderDetail.fiol[i-1].tags);
}
window.getTagString=(tags)=>{
    var tagString = "";
    for(var i=0; i<tags.length; i++){
        if(tags[i]){
            tagString += tagsDesc[i]+",";
        }
    }
    return tagString.substr(0,tagString.length-1);
}

window.submitFeedback=()=>{
    if((!orderDetail.sonr || (orderDetail.sonr && selectedNpsRating>0))
        && (!orderDetail.sor || (orderDetail.sor && selectedMainRating>0) )
        && !isSubmitting
    ){
        setOrderComment();
        setItemComment();
        document.getElementById("submitButton").innerHTML=getSubmitButtonChild(true);
        orderDetail.t = token;
        if(window.isMobile) orderDetail.ua = "Mobile";
        else orderDetail.ua = "Desktop";
        if(document.getElementById("callbackCheckboxContainer").style.display === 'block'
            && document.getElementById("callbackCheckbox").checked){
            orderDetail.cc='Y';
        }else{
            orderDetail.cc='N';
        }
        console.log(JSON.stringify(orderDetail));
        $.ajax({
            method: "POST",
            url: getBasePath(window.location.origin) + "/neo-service/rest/v1/ref/submit-feedback",
            data: JSON.stringify(orderDetail),
            headers: {"Content-type":"application/json"},
            success: function (result) {
                if(result){
                    if(orderDetail.ru.length > 0){
                        window.location.replace(orderDetail.ru);
                    }else{
                        window.location.replace(orderDetail.rud);
                    }
                    document.getElementById("submitButton").innerHTML=getSubmitButtonChild(false);
                }
            },
            error: function (error) {
                console.log(error);
                document.getElementById("submitButton").innerHTML=getSubmitButtonChild(false);
                alert("Some thing went wrong");
                window.location.reload();
            }
        });
    }
}

window.checkboxClick=()=>{
    if(document.getElementById("callbackCheckbox").checked){
        document.getElementById("callbackCheckbox").style.backgroundColor = "#108A45";
    }else{
        document.getElementById("callbackCheckbox").style.backgroundColor = "#fff";
    }
}

