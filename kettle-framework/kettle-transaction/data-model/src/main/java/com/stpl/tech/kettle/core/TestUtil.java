/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.stpl.tech.kettle.domain.model.ComplimentaryDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.ObjectFactory;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.SettlementType;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.domain.model.AddonData;
import com.stpl.tech.master.domain.model.MasterObjectFactory;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppUtils;

public class TestUtil {

	private static final ObjectFactory objectFactory = new ObjectFactory();
	private static final MasterObjectFactory masterObjectFactory = new MasterObjectFactory();

	private static final double AMOUNT = 555.00d;

	public static void createReportDef(ReportDefinitionEnum def) {

	}

	public static Customer getCustomer(String contactNumber, String email, String firstName) {
		Customer data = objectFactory.createCustomer();
		data.setContactNumber(contactNumber);
		data.setEmailId(email);
		data.setFirstName(firstName);
		return data;
	}

	public static Order getOrder(int customerId, int multiplier) {
		Order order = objectFactory.createOrder();
		order.setBillStartTime(AppUtils.getCurrentTimestamp());
		order.setBillingServerTime(AppUtils.getCurrentTimestamp());
		order.setBillCreationSeconds(0);
		order.setChannelPartner(1);
		order.setDeliveryPartner(5);
		order.setCustomerId(customerId);
		order.setEmployeeId(100000);
		order.setHasParcel(false);
		order.setUnitId(12002);
		order.setSettlementType(SettlementType.DEBIT);
		order.setStatus(OrderStatus.SETTLED);
		order.setTransactionDetail(getTransactionDetail(multiplier));
		order.setBillCreationTime(AppUtils.getCurrentTimestamp());
		order.getSettlements().addAll(getSettlements(AMOUNT, multiplier));
		order.getOrders().addAll(getOrderItems(multiplier));
		order.setOrderRemark("Add Extra Napkins");
		order.setSource(UnitCategory.COD.toString());
		return order;
	}

	private static List<OrderItem> getOrderItems(int multiplier) {
		List<OrderItem> list = new ArrayList<OrderItem>();
		List<AddonData> adds = new ArrayList<AddonData>();

		adds.add(getAdOn(117, "Kesar", "Kesar"));
		adds.add(getAdOn(116, "Mulethi", "Mulethi"));
		list.add(getOrderItem(10, "Desi Chai", "REGULAR", 59.00d, 1, 70.00d, multiplier, true, "Employee Meal", adds));
		/*
		 * list.add(getOrderItem(46, "Modinagar Shikanji", "REGULAR", 100.00d,
		 * 1, 0.00d, multiplier ));
		 */
		list.add(getOrderItem(80, "Kulhad Chai", "None", 89.00d, 1, 89.00d, multiplier));
		return list;
	}

	private static AddonData getAdOn(int id, String code, String name) {
		AddonData data = masterObjectFactory.createAddonData();
		data.setCode(code);
		data.setId(id);
		data.setName(name);
		return data;
	}

	private static OrderItem getOrderItem(int productId, String name, String dimension, double price, int quantity,
			double amount, int multiplier, boolean isComplimentary, String reasonCode, Collection<AddonData> addOns) {
		OrderItem item = objectFactory.createOrderItem();
		item.setAmount(big(amount * multiplier));
		item.setDimension(dimension);
		item.setPrice(big(price));
		item.setProductId(productId);
		item.setQuantity(quantity * multiplier);
		item.setProductName(name);
		item.setComplimentaryDetail(getDetail(isComplimentary, reasonCode));
		return item;
	}

	private static ComplimentaryDetail getDetail(boolean isComplimentary, String reasonCode) {
		if (!isComplimentary) {
			return null;
		}
		ComplimentaryDetail d = objectFactory.createComplimentaryDetail();
		d.setIsComplimentary(isComplimentary);
		d.setReason(reasonCode);
		d.setReasonCode(2100);
		return d;
	}

	private static OrderItem getOrderItem(int productId, String name, String dimension, double price, int quantity,
			double amount, int multiplier) {
		return getOrderItem(productId, name, dimension, price, quantity, amount, multiplier, false, null, null);
	}

	private static List<Settlement> getSettlements(double amount, int multiplier) {
		List<Settlement> list = new ArrayList<Settlement>();
		list.add(getSettlement(multiplier, amount * multiplier));
		return list;
	}

	private static Settlement getSettlement(int counter, double amount) {
		Settlement s = objectFactory.createSettlement();
		s.setAmount(big(amount));
		s.setMode((counter % 6) + 1);
		return s;
	}

	private static TransactionDetail getTransactionDetail(int multiplier) {
		TransactionDetail t = objectFactory.createTransactionDetail();
		t.setPaidAmount(big(AMOUNT * multiplier));
		t.setRoundOffValue(big(0.09d * multiplier));
		t.setTaxableAmount((big(779.00d).subtract(big(311.60d))).multiply(big(multiplier)));
		t.setTotalAmount(big(779.00d * multiplier));
		t.setTotalAmount(big(779.00d * multiplier));
		DiscountDetail d = objectFactory.createDiscountDetail();
		d.setDiscount(getCostDetail(big(311.60d * multiplier), big(40.00d)));
		d.setDiscountReason("UnsatisfiedCustomer");
		d.setDiscountCode(2001);
		t.setDiscountDetail(d);
		return t;
	}

	private static BigDecimal big(double d) {
		return new BigDecimal(d);
	}

	private static PercentageDetail getCostDetail(BigDecimal d, BigDecimal e) {
		PercentageDetail p = objectFactory.createPercentageDetail();
		p.setPercentage(e);
		p.setValue(d);
		return p;
	}
}
