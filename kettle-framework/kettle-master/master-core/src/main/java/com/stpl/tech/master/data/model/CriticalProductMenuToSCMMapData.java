package com.stpl.tech.master.data.model;

import java.math.BigDecimal;

public class CriticalProductMenuToSCMMapData {
    private Integer productId;
    private String productName;
    private String uom;
    private BigDecimal quantity;
    private String ingredientType;

    public CriticalProductMenuToSCMMapData() {
    }

    public CriticalProductMenuToSCMMapData(Integer productId, String productName, String uom, BigDecimal quantity, String ingredientType) {
        this.productId = productId;
        this.productName = productName;
        this.uom = uom;
        this.quantity = quantity;
        this.ingredientType = ingredientType;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getIngredientType() {
        return ingredientType;
    }

    public void setIngredientType(String ingredientType) {
        this.ingredientType = ingredientType;
    }
}
