package com.stpl.tech.master.core.external.excel.impl;

import com.amazonaws.util.CollectionUtils;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.itextpdf.text.pdf.PdfWriter;
import com.stpl.tech.master.core.exception.FileDownloadException;
import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.external.excel.GenericExcelManagementService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.core.external.excel.dao.GenericExcelManagementDao;
import com.stpl.tech.master.data.model.DocumentDetailData;
import com.stpl.tech.master.data.model.ExcelRequestData;
import com.stpl.tech.master.domain.model.DocUploadTypeDTO;
import com.stpl.tech.master.domain.model.DocumentDetailDTO;
import com.stpl.tech.master.domain.model.FileTypeDTO;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import static org.apache.poi.ss.usermodel.CellType.STRING;

@Slf4j
@Service
public class GenericExcelManagementServiceImpl implements GenericExcelManagementService {

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private Environment env;

    @Autowired
    private GenericExcelManagementDao dao;

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CompletableFuture<DocumentDetailDTO> uploadDocumentAsync(FileTypeDTO type, MimeType mimeType,
                                                                    DocUploadTypeDTO docType, Integer userId, MultipartFile file,
                                                                    String fileName, String baseDir) throws IOException, DocumentException {
        File destFile = null;
        MultipartFile compressedFile = null;
        if(mimeType.equals(MimeType.PDF)) {
            log.info("#########Uploaded FIle is PDF , Trying To Compress ###### ");
            //Compression of PDF
            try{
                compressedFile = compressPdf(destFile, file,compressedFile);
                log.info("###### size before compression : {}", file.getSize());
                log.info("###### Size After Compression : {}", compressedFile.getSize());
            }catch (MasterException e){
                log.info("######Error While Compressing File , Uploading Without Compression");
                compressedFile = null;
            }
        }else if(mimeType.equals(MimeType.PNG)){
            log.info("#######Uploaded File Is Of PNG Format. Uploading Without Compression .");
        }else{
            log.info("#######Uploaded File is Of {} Format, Trying To Compress", mimeType.extension());
            try{
                byte[] imageByte = compressImage(file, mimeType.extension());
                compressedFile = new MockMultipartFile(file.getName(),
                        file.getOriginalFilename(), file.getContentType(), imageByte);
                log.info("###### size before compression : {}", file.getSize());
                log.info("###### Size After Compression : {}", compressedFile.getSize());
            }catch (Exception e){
                log.info("#######Error While Compressing Image , Uploading Without Compression");
                compressedFile = null;
            }
        }


        try {
            FileDetail s3File = fileArchiveService.saveFileToS3(env.getProperty("amazon.s3.bucket", "chaayosdevtest"), baseDir, fileName, Objects.isNull(compressedFile) ? file : compressedFile);
            DocumentDetailDTO documentDetailDTO = new DocumentDetailDTO();
            documentDetailDTO.setMimeType(mimeType);
            documentDetailDTO.setUploadType(docType);
            documentDetailDTO.setFileType(type);
            documentDetailDTO.setDocumentLink(fileName);
            documentDetailDTO.setS3Key(s3File.getKey());
            documentDetailDTO.setFileUrl(s3File.getUrl());
            documentDetailDTO.setS3Bucket(s3File.getBucket());
            documentDetailDTO.setUpdatedBy(MasterDataConverter.generateIdCodeName(userId, "", "System"));
            documentDetailDTO.setUpdateTime(AppUtils.getCurrentTimestamp());
            DocumentDetailData data = dao.add(MasterDataConverter.convert(documentDetailDTO));
            if(Objects.nonNull(destFile)){
                destFile.delete();
            }
            if (data.getDocumentId() != null) {
                return CompletableFuture.completedFuture(MasterDataConverter.convert(data));
            }

        } catch (Exception e) {
            log.error("Encountered error while uploading document", e);
        }
        return null;


    }

    public MultipartFile compressPdf(File destFile , MultipartFile file , MultipartFile compressedFile) throws IOException, DocumentException, MasterException {
        try {
            String dest = env.getProperty("server.base.dir") + file.getOriginalFilename();
            destFile = new File(dest);
            PdfReader reader = new PdfReader(file.getInputStream());
            PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(dest), PdfWriter.VERSION_1_5);
            stamper.getWriter().setCompressionLevel(9);
            int total = reader.getNumberOfPages() + 1;
            for (int i = 1; i < total; i++) {
                reader.setPageContent(i, reader.getPageContent(i));
            }
            stamper.setFullCompression();
            stamper.close();
            reader.close();
            FileInputStream input = new FileInputStream(destFile);
            compressedFile = new MockMultipartFile(file.getName(),
                    file.getOriginalFilename(), file.getContentType(), IOUtils.toByteArray(input));
            input.close();
        }catch (Exception e) {
            log.info("Error While Compressing PDF : {} " , file.getOriginalFilename());
            log.info(e.getMessage());
            throw new MasterException("Error While Compressing PDF",e.getMessage());
        }
        return  compressedFile;
    }

    public byte[] compressImage(MultipartFile image , String mimeType) throws IOException {

        InputStream inputStream = image.getInputStream();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        float imageQuality = 0.6f;

        // Create the buffered image
        BufferedImage bufferedImage = ImageIO.read(inputStream);

        // Get image writers
        Iterator<ImageWriter> imageWriters = ImageIO.getImageWritersByFormatName("jpeg"); // Input your Format Name here

        if (!imageWriters.hasNext())
            throw new IllegalStateException("Writers Not Found!!");

        ImageWriter imageWriter = imageWriters.next();
        ImageOutputStream imageOutputStream = ImageIO.createImageOutputStream(outputStream);
        imageWriter.setOutput(imageOutputStream);

        ImageWriteParam imageWriteParam = imageWriter.getDefaultWriteParam();

        // Set the compress quality metrics
        imageWriteParam.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        imageWriteParam.setCompressionQuality(imageQuality);

        // Compress and insert the image into the byte array.
        imageWriter.write(null, new IIOImage(bufferedImage, null, null), imageWriteParam);

        byte[] imageBytes = outputStream.toByteArray();

        // close all streams
        inputStream.close();
        outputStream.close();
        imageOutputStream.close();
        imageWriter.dispose();


        return imageBytes;
    }

    @Override
    public <T> List<T> convertExcelDataIntoList(MultipartFile excelFile, String className) {
        try {
            Workbook workbook;
            if (excelFile.getOriginalFilename().endsWith("xls")) {
                workbook = new HSSFWorkbook(excelFile.getInputStream());
            } else {
                workbook = new XSSFWorkbook(excelFile.getInputStream());
            }
            List<ExcelParsingException> errors = new ArrayList<>();
            Class<T> clazz = (Class<T>) Class.forName(className);
            List<T> entityList = new SheetParser().createEntity(workbook.getSheetAt(0), clazz, errors::add);
            workbook.close();
            if (errors.isEmpty()) {
                return entityList;
            } else {
                log.info("Error parsing Excel, total errors: {}", errors.size());
                StringBuilder sb = new StringBuilder();
                errors.forEach(e -> sb.append(e.getMessage()).append('\n'));
                log.info("{}", sb.toString());
                throw new ExcelParsingException(sb.toString());
            }
        } catch (Exception exp) {
            log.error("Error while converting excel into list of objects", exp);
            throw new ExcelParsingException("Error while parsing excel to list");
        }
    }

    private CellStyle generateHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        Font font = workbook.createFont();
        font.setFontName("Arial");
        font.setBold(true);
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        style.setFont(font);

        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.PALE_BLUE.getIndex());

        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }

    private void isExcelRequestDataValid(ExcelRequestData excelRequestData) throws FileDownloadException {
        if(!StringUtils.hasLength(excelRequestData.getFileName())) {
            throw new FileDownloadException("File Name should not be null or empty");
        }
        if(CollectionUtils.isNullOrEmpty(excelRequestData.getHeaderNames())) {
            throw new FileDownloadException("Excel Header Names list should not be Empty or null");
        }
        if(CollectionUtils.isNullOrEmpty(excelRequestData.getBody())) {
            throw new FileDownloadException("Excel Body list should not be Empty or null");
        }
    }

    private String getValue(Object value) {
        return Objects.nonNull(value) ? value.toString() : "";
    }

    private Sheet getSheetForWorkBook(Workbook workbook, String sheetName) {
        int index = workbook.getSheetIndex(sheetName);
        if (index != -1) {
            workbook.removeSheetAt(index);
        }
        return workbook.createSheet(sheetName);
    }

    @Override
    public View  downloadExcelFromRequestData(ExcelRequestData excelRequestData) throws Exception {
        try {
            isExcelRequestDataValid(excelRequestData);
            return new AbstractXlsxView() {
                @Override
                protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request, HttpServletResponse response) throws Exception {
                    String fileName = excelRequestData.getFileName() + "-"
                            + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
                    response.addHeader("Content-Disposition", "attachment; filename=" + fileName);

                    Sheet sheet =  getSheetForWorkBook(workbook, excelRequestData.getFileName());
                    sheet.setDefaultColumnWidth(excelRequestData.getHeaderNames().size());
                    CellStyle headerStyle = generateHeaderStyle(workbook);

                    // Create styles for editable and non-editable cells
                    CellStyle editableCellStyle = workbook.createCellStyle();
                    editableCellStyle.setLocked(false);

                    CellStyle nonEditableCellStyle = workbook.createCellStyle();
                    nonEditableCellStyle.setLocked(true);

                    // Create a map to store indices of non-editable columns
                    Map<Integer, Boolean> nonEditableColumnIndices = new HashMap<>();

                    // adding headerName's
                    Row headerRow = sheet.createRow(0);
                    for(int i = 0; i < excelRequestData.getHeaderNames().size(); i++) {
                        sheet.setColumnWidth(i, 6000);
                        Cell cell = headerRow.createCell(i);
                        String headerName = getValue(excelRequestData.getHeaderNames().get(i));
                        cell.setCellValue(getValue(headerName));
                        cell.setCellStyle(headerStyle);
                        if (!CollectionUtils.isNullOrEmpty(excelRequestData.getNonEditableColumnNames()) &&
                                excelRequestData.getNonEditableColumnNames().contains(headerName)) {
                            nonEditableColumnIndices.put(i, true);
                        }
                    }

                    // adding Values to excel body
                    for(int i = 0; i < excelRequestData.getBody().size(); i++) {
                        Row bodyRow = sheet.createRow(i + 1);
                        for(int j = 0; j < excelRequestData.getBody().get(i).length; j++) {
                            Cell newCell = bodyRow.createCell(j, STRING);
                            newCell.setCellValue(getValue(excelRequestData.getBody().get(i)[j]));

                            if (nonEditableColumnIndices.containsKey(j)) {
                                newCell.setCellStyle(nonEditableCellStyle);
                            } else {
                                newCell.setCellStyle(editableCellStyle);
                            }
                        }
                    }

//                    sheet.protectSheet("");
                }
            };

        } catch (FileDownloadException exp) {
            log.error("Error validating data : " + excelRequestData.getFileName(), exp);
            throw new FileDownloadException(exp.getMessage());
        } catch (Exception exp) {
            log.error("Error Downloading data of : " + excelRequestData.getFileName(), exp);
            throw new Exception(exp.getMessage());
        }
    }


}
