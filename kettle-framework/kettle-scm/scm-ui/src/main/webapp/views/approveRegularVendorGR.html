<style>
    #viewGRList .collapsible-header.inverse{
        background-color: #CDDC39;
        color: white;
    }
    #viewGRList .collapsible-header{
        color: #654848;
        background-color: white;
        border: 1px solid #26a69a;
    }
    .scrollabe-table {
        display: block !important;
        overflow-x: auto !important;
        white-space: nowrap !important;
    }
    .scrollabe-table thead tr th{
        min-width: 150px;
    }
    .greyBackground {
        background-color: lightgrey;
    }
    .modal-large{
        width:80% !important;
    }

    .modal-medium{
        width:50% !important;
    }
</style>
<div class="row" data-ng-init="init()">
    <div style="width=100%" id="printHide">
        <div class="col s12">
            <div class="row white z-depth-3 custom-listing-li">
                <div class="row">
                    <div class="col s12">
                        <h4>Approve Goods Receivings</h4>
                    </div>
                </div>
                <div class="row">
                    <div class="col s4">
                        <label>Select Unit</label>
                        <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Unit'}"
                                ng-options="userMappingUnit as userMappingUnit.name for userMappingUnit in userMappingUnits"
                                data-ng-change="selectUnit(unitSelected)" data-ng-model="unitSelected"></select>
                    </div>
                    <div class="col s4">
                        <label>Select Vendor</label>
                        <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}"
                                ng-options="vendor as vendor.entityName for vendor in vendors"
                                data-ng-change="selectVendor(vendorSelected)" data-ng-model="vendorSelected"></select>
                    </div>
                    <div class="col s2">
                        <button class="btn margin-top-20" data-ng-click="getGRs()">Find</button>
                    </div>
                </div>
                <hr>
                <div class="row" style="padding:30px;color:gray;text-align: center;"
                     data-ng-show="grs==null || grs.length==0">
                    No Vendor Receivings found for the selected criteria
                </div>
                <div class="row" data-ng-show="grs!=null && grs.length>0">
                    <ul id="viewGRList" class="col s12" data-collapsible="accordion" watch>
                        <li class="row" data-ng-repeat="gr in grs track by gr.id">
                            <div class="col s10 collapsible-header waves-effect waves-light lighten-5"
                                 data-ng-class="{'inverse':gr.id==grId}" data-ng-click="selectOpenGr(gr)">
                                <div class="left">
                                    GR No.: <b>{{gr.id}}</b> for {{gr.generatedForVendor.name}}
                                    [{{gr.dispatchLocation.city}}]
                                    <span class="chip" data-ng-if="gr.invalid">INVALID</span>
                                </div>
                                <div class="right">
                                    <span class="chip grey white-text">{{gr.receiptType}}: {{gr.receiptNumber}}</span>
                                    <span data-ng-class="{'chip green white-text':gr.toBePaid == true, 'chip red white-text':gr.toBePaid != true}">To be paid: {{gr.toBePaid?'Yes':'No'}}</span>
                                    <span class="chip grey white-text">
                                    {{gr.generationTime | date:'dd/MM/yyyy @ h:mma'}}
                                </span>
                                    <span class="chip grey white-text">GR Status: {{gr.status}}</span>
                                    <i class="fa fa-caret-down right"></i>
                                </div>
                            </div>
                            <div class="collapsible-body">
                                <table class="bordered striped">
                                    <thead>
                                    <tr>
                                        <th class="center-align">SKU</th>
                                        <th class="center-align">SKU ID</th>
                                        <th class="center-align">POI ID</th>
                                        <th class="center-align">PO REQ. Qty(PKG)</th>
                                        <th class="center-align">PO PENDING Qty(PKG)</th>
                                        <th class="center-align">Updation Reason</th>
                                        <th class="center-align">Comments</th>
                                        <th class="center-align">GR RQ. Qty(PKG)</th>
                                        <th class="center-align">Updated Qty(PKG)</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr data-ng-repeat="item in gr.vendorPoGRItems track by $index">
                                        <td class="center-align">{{item.skuName}}</td>
                                        <td class="center-align">{{item.skuId}}</td>
                                        <td class="center-align">{{item.purchaseOrderId}}</td>
                                        <td class="center-align">{{((item.poItemRequestedQuantity)/item.conversionRatio).toFixed(2)}}</td>
                                        <td class="center-align">{{((item.poItemRequestedQuantity - item.poItemReceivedQuantity)/item.conversionRatio).toFixed(2)}}</td>
                                        <td><select id="reason" placeholder="Select Reasons"
                                                    ng-options="reasons.deviationReason for reasons in updationReason"
                                                    data-ng-change="selectedReasons(item,selectedReason)"
                                                    data-ng-model="selectedReason"></select></td>
                                        <td><input class="form-control" type="text" maxlength="200"
                                                   data-ng-model="item.description" placeholder="Add Description"
                                                   required/></td>
                                        <td class="center-align">{{item.poGrItemPackagingQuantity}}</td>
                                        <td class="center-align">
                                            <input type="number"
                                                   min="0" step="0.001" ng-model="item.updatedQuantity"
                                                   data-ng-disabled="gr.canBeCancelled =='N'"
                                                   ng-change="changeItemQuantity(item)">
                                        </td>
                                    </tr>
                                    </tbody>
                                    <tr>
                                        <td>
                                            <button data-ng-if="gr.status=='INITIATED' && gr.canBeCancelled !='N'" class="btn red"
                                                    acl-action="VGRCL" data-ng-click="cancelGR(gr.id)">CancelGR
                                            </button>
                                        </td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>
                                            <button data-ng-if="gr.status=='INITIATED'" class="btn green"
                                                    acl-action="VEREQC" data-ng-click="previewQualityCheck(gr.id)">ApproveQC
                                            </button>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Structure -->
<script type="text/ng-template" id="previewQualityCheck.html">
    <div id="previewQualityCheck" data-ng-init="initPreview()">
        <div class="modal-content" style="overflow-x: auto; max-height: 350px;">
            <div class="row">
                <h5>Preview for Goods Received Id: <b>{{updatedGr.id}}</b></h5>
            </div>
            <div class="row" style="width: 98%;">
                <table class="bordered striped">
                    <thead>
                    <tr>
                        <th class="center-align">SKU</th>
                        <th class="center-align">SKU ID</th>
                        <th class="center-align">Updation Reason</th>
                        <th class="center-align">Comments</th>
                        <th class="center-align">GR RQ. Qty(PKG)</th>
                        <th class="center-align">Updated Qty(PKG)</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="item in updatedGr.vendorPoGRItems track by $index">
                        <td class="center-align">{{item.skuName}}</td>
                        <td class="center-align">{{item.skuId}}</td>
                        <td class="center-align"><span data-ng-if="item.updationreason==null">-</span>{{item.updationreason}}</td>
                        <td class="center-align"><span data-ng-if="item.description==null">-</span>{{item.description}}</td>
                        <td class="center-align">{{item.poGrItemPackagingQuantity}}</td>
                        <td class="center-align">{{item.grItemAcceptedQuantity}}</td>
                    </tr>
                    </tbody>
                </table>
                <hr>
            </div>
        </div>
        <div class="modal-footer right">
            <button class="waves-effect waves-green btn-flat" data-ng-click="close()">Cancel</button>
            <button class="waves-effect waves-green btn" data-ng-click="submit()">Approve</button>
        </div>
    </div>
</script>