/**
 *
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

import java.math.BigDecimal;


@ExcelSheet(value = "Unit Service Budget Detail")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class ServiceAggregate {

    @ExcelField
    protected int unitId;
    @ExcelField
    protected String unitName;
    @ExcelField
    protected int year;
    @ExcelField
    protected int month;
    @ExcelField
    protected String status;

    @ExcelField(headerName = "OTHER_SERVICE_CHARGES")
    private BigDecimal otherServiceCharges;

    @ExcelField(headerName = "SECURITY_GUARD_CHARGES")
    private BigDecimal securityGuardCharges;

    @ExcelField(headerName = "FUEL_CHARGES")
    private BigDecimal fuelCharges = new BigDecimal(0d);

    @ExcelField(headerName = "LOGISTIC_CHARGES")
    private BigDecimal logisticCharges;

    @ExcelField(headerName = "COMMUNICATION_INTERNET")
    private BigDecimal communicationInternet;

    @ExcelField(headerName = "COMMUNICATION_TELEPHONE")
    private BigDecimal communicationTelephone;

    @ExcelField(headerName = "COMMUNICATION_ILL")
    private BigDecimal communicationILL;

    @ExcelField(headerName = "PAYROLL_PROCESSING_FEES")
    private BigDecimal payrollProcessingFee;

    @ExcelField(headerName = "NEWSPAPER_CHARGES")
    private BigDecimal newsPaper = new BigDecimal(0d);

    @ExcelField(headerName = "STAFF_WELFARE_EXPENSES")
    private BigDecimal staffWelfareExpenses = new BigDecimal(0d);

    @ExcelField(headerName = "COURIER_CHARGES")
    private BigDecimal courierCharges = new BigDecimal(0d);

    @ExcelField(headerName = "PRINTING_AND_STATIONARY")
    private BigDecimal printingAndStationary = new BigDecimal(0d);

    @ExcelField(headerName = "BUSINESS_PROMOTION")
    private BigDecimal businessPromotion = new BigDecimal(0d);


    @ExcelField(headerName = "LEGAL_CHARGES")
    private BigDecimal legalCharges;

    @ExcelField(headerName = "PROFESSIONAL_CHARGES")
    private BigDecimal professionalCharges;

    @ExcelField(headerName = "CLEANING_CHARGES")
    private BigDecimal cleaningCharges = new BigDecimal(0d);


    @ExcelField(headerName = "PEST_CONTROL_CHARGES")
    private BigDecimal pestControlCharges = new BigDecimal(0d);

    @ExcelField(headerName = "TECHNOLOGY_TRAINING")
    private BigDecimal techologyTraining;

    @ExcelField(headerName = "CORPORATE_MARKETING_DIGITAL")
    private BigDecimal corporateMarketingDigital = new BigDecimal(0d);

    @ExcelField(headerName = "CORPORATE_MARKETING_AD_OFFLINE")
    private BigDecimal corporateMarketingAdvOffline = new BigDecimal(0d);


    @ExcelField(headerName = "CORPORATE_MARKETING_AD_ONLINE")
    private BigDecimal corporateMarketingAdvOnline = new BigDecimal(0d);

    @ExcelField(headerName = "CORPORATE_MARKETING_CHANNEL_PARTNER")
    private BigDecimal corporateMarketingChannelPartner = new BigDecimal(0d);



    @ExcelField(headerName = "CORPORATE_MARKETING_OUTDOOR")
    private BigDecimal corporateMarketingOutdoor = new BigDecimal(0d);

    @ExcelField(headerName = "CORPORATE_MARKETING_AGENCY_FEES")
    private BigDecimal corporateMarketingAgencyFees = new BigDecimal(0d);

    @ExcelField(headerName = "DELIVERY_CHARGES_VARIABLE")
    private BigDecimal deliveryChargesVariable;

//	@ExcelField(headerName = "CONVEYANCE_MARKETING")
//	private BigDecimal conveyanceMarketing = new BigDecimal(0d);

//	@ExcelField(headerName = "CONVEYANCE_OPERATION")
//	private BigDecimal conveyanceOperations = new BigDecimal(0d);

//	@ExcelField(headerName = "CONVEYANCE_OTHERS")
//	private BigDecimal conveyanceOthers = new BigDecimal(0d);

    @ExcelField(headerName = "AUDIT_FEE")
    private BigDecimal auditFee;

    @ExcelField(headerName = "AUDIT_FEE_OUT_OF_POCKET")
    private BigDecimal auditFeeOutOfPocket;

    @ExcelField(headerName = "BROKERAGE")
    private BigDecimal brokerage;

    @ExcelField(headerName = "CHARITY_AND_DONATIONS")
    private BigDecimal charityAndDonations;

    @ExcelField(headerName = "DOMESTIC_TICKETS_AND_HOTELS")
    private BigDecimal domesticTicketsAndHotels;

    @ExcelField(headerName = "INTERNATIONAL_TICKETS_AND_HOTELS")
    private BigDecimal internationalTicketsAndHotels;


    @ExcelField(headerName = "HOUSEKEEPING_CHARGES")
    private BigDecimal houseKeepingCharges;

    @ExcelField(headerName = "LATE_FEE_CHARGES")
    private BigDecimal lateFeeCharges;

//	@ExcelField(headerName = "MARKETING_DATA_ANALYSIS")
//	private BigDecimal marketingDataAnalysis = new BigDecimal(0d);

    @ExcelField(headerName = "MISCELLANEOUS_EXPENSES")
    private BigDecimal miscellaneousExpenses;

    @ExcelField(headerName = "PENALTY")
    private BigDecimal penalty;

    @ExcelField(headerName = "PHOTO_COPY_EXPENSES")
    private BigDecimal photoCopyExpenses = new BigDecimal(0d);

    @ExcelField(headerName = "QCR_EXPENSE")
    private BigDecimal qcrExpense;

    @ExcelField(headerName = "RECRUITMENT_CONSULTANTS")
    private BigDecimal recuritmentConsultants;

    @ExcelField(headerName = "ROC_FEES")
    private BigDecimal rocFees;

    @ExcelField(headerName = "DEBIT_CREDIT_WRITTEN_OFF")
    private BigDecimal debitCreditWrittenOff;

    @ExcelField(headerName = "DIFFENECE_IN_EXCHANGE")
    private BigDecimal differenceInExchange;

    @ExcelField(headerName = "RND_ENGINEERING_EXPENSE")
    private BigDecimal rnDEngineeringExpenses = new BigDecimal(0d);

    @ExcelField(headerName = "CAPITAL_IMPROVEMENT_EXPENSES")
    private BigDecimal capitalImprovementExpenses;


    @ExcelField(headerName = "LEASE_HOLD_IMPROVEMENTS")
    private BigDecimal leaseHoldImprovements;

    @ExcelField(headerName = "MARKETING_LAUNCH")
    private BigDecimal marketingLaunch;

    @ExcelField(headerName = "CORPORATE_MARKETING_PHOTO")
    private BigDecimal corporateMarketingPhotography = new BigDecimal(0d);

    @ExcelField(headerName = "ODC_RENTAL")
    private BigDecimal odcRental;

//	@ExcelField(headerName = "COGS_OTHERS")
//	private BigDecimal cogsOthers = new BigDecimal(0d);

    @ExcelField(headerName = "COMMISSION_CHANGE")
    private BigDecimal commissionChange = new BigDecimal(0d);

    @ExcelField(headerName = "VEHICLE_REGULAR_MAINTENANCE_HQ")
    private BigDecimal vehicleRegularMaintenanceHq;

    @ExcelField(headerName = "BUILDING_MAINTENANCE_HQ")
    private BigDecimal buildingMaintenanceHq;

    @ExcelField(headerName = "COMPUTER_IT_MAINTENANCE_HQ")
    private BigDecimal computerItMaintenanceHq;

    @ExcelField(headerName = "EQUIPMENT_MAINTENANCE_HQ")
    private BigDecimal equipmentMaintenanceHq;

    @ExcelField(headerName = "MARKETING_NPI_HQ")
    private BigDecimal marketingNpiHq;

    @ExcelField(headerName = "LICENSE_EXPENSES")
    private BigDecimal licenseExpenses;

    @ExcelField(headerName = "CORPORATE_MARKETING_ATL_RADIO")
    private BigDecimal corporateMarketingAtlRadio;

    @ExcelField(headerName = "CORPORATE_MARKETING_ATL_TV")
    private BigDecimal corporateMarketingAtlTv;

    @ExcelField(headerName = "CORPORATE_MARKETING_ATL_PRINT_AD")
    private BigDecimal corporateMarketingAtlPrintAd;

    @ExcelField(headerName = "CORPORATE_MARKETING_ATL_CINEMA")
    private BigDecimal corporateMarketingAtlCinema;

    @ExcelField(headerName = "CORPORATE_MARKETING_ATL_DIGITAL")
    private BigDecimal corporateMarketingAtlDigital;

    @ExcelField(headerName = "LOGISTIC_INTRASTATE_COLD_VEHICLE")
    private BigDecimal logisticInterstateColdVehicle;

    @ExcelField(headerName = "LOGISTIC_INTRASTATE_NON_COLD_VEHICLE")
    private BigDecimal logisticInterstateNonColdVehicle;

    @ExcelField(headerName = "LOGISTIC_INTERSTATE_AIR")
    private BigDecimal logisticInterstateAir;

    @ExcelField(headerName = "LOGISTIC_INTERSTATE_ROAD")
    private BigDecimal logisticInterstateRoad;

    @ExcelField(headerName = "LOGISTIC_INTERSTATE_TRAIN")
    private BigDecimal logisticInterstateTrain;

    @ExcelField(headerName = "AIR_CONDITIONER_AMC")
    private BigDecimal airConditionerAmc;

    @ExcelField(headerName = "CORPORATE_MARKETING_SMS_EMAIL")
    private BigDecimal corporateMarketingSms;

    @ExcelField(headerName = "SECURITY_DEPOSIT_PROPERTY")
    private BigDecimal securityDepositProperty;

    @ExcelField(headerName = "SECURITY_DEPOSIT_MVAT")
    private BigDecimal securityDepositMVAT;

    @ExcelField(headerName = "SECURITY_DEPOSIT_ELECTRICITY")
    private BigDecimal securityDepositElectricity;

    @ExcelField(headerName = "MARKETING_DISCOUNT_ECOM")
    private BigDecimal marketingDiscountEcom;

    @ExcelField(headerName = "SHIPPING_CHARGES")
    private BigDecimal shippingCharges;

    @ExcelField(headerName = "OTHER_TRANSACTION_CHARGES")
    private BigDecimal otherTransactionCharges;

    @ExcelField(headerName = "DISCOUNT_DEALER_MARGIN")
    private BigDecimal discountDealerMargin;

    @ExcelField(headerName = "PERFORMANCE_MARKETING_SERVICE")
    private BigDecimal performanceMarketingService;

    @ExcelField(headerName = "INSURANCE_MARINE")
    private BigDecimal insuranceMarine;

    @ExcelField(headerName = "SHARE_STAMPING_CHARGES")
    private BigDecimal shareStampingCharges;

    @ExcelField(headerName = "OTHER_CHARGES_ECOM")
    private BigDecimal otherChargesEcom;

    @ExcelField(headerName = "COMISSION_CHANNEL_PARTNER_FIXED")
    private BigDecimal comissionChannelPartnerFixed;

    @ExcelField(headerName = "COGS_TRADING_GOODS")
    private BigDecimal cogsTradingGoods;

    @ExcelField(headerName = "ROYALTY_FEES")
    private BigDecimal royaltyFees;

    @ExcelField(headerName = "FREIGHT_CHARGES")
    private BigDecimal freightCharges;


    @ExcelField(headerName = "EMPLOYEE_FACILITATION_CHARGES")
    private BigDecimal employeeFacilitationCharges;

//    @ExcelField(headerName = "CORPORATE_MARKETING_SMS_EMAIL")
//    private BigDecimal corporateMarketingSmsEmail;

    @ExcelField(headerName = "PRONTO_AMC")
    private BigDecimal prontoAMC;

    @ExcelField(headerName = "OTHERS_MAINTENANCE")
    private BigDecimal othersMaintenance;

    @ExcelField(headerName = "TECHNOLOGY_PLATFORM_CHARGES")
    private BigDecimal technologyPlatformCharges;

    @ExcelField(headerName = "INTEREST_ON_TERM_LOAN")
    private BigDecimal interestOnTermLoan;

    @ExcelField(headerName = "TECHNOLOGY_OTHERS")
    private BigDecimal technologyOthers;

    @ExcelField(headerName = "SYSTEM_RENTAL")
    private BigDecimal systemRental;

    @ExcelField(headerName = "RO_RENTAL")
    private BigDecimal roRental;

    @ExcelField(headerName = "INSURANCE_ACCIDENTAL")
    private BigDecimal insuranceAccidental;

    @ExcelField(headerName = "DG_RENTAL")
    private BigDecimal dgRental;

    @ExcelField(headerName = "OTHERS_AMC")
    private BigDecimal othersAMC;

    @ExcelField(headerName = "MUSIC_RENTALS")
    private BigDecimal musicRentals;

//    @ExcelField(headerName = "VOUCHER_TRANSACTION_CHARGES")
//    private BigDecimal employeeFacilitationExpenses;

    @ExcelField(headerName = "INSURANCE_ASSETS")
    private BigDecimal insuranceAssets;

    @ExcelField(headerName = "INSURANCE_CGL")
    private BigDecimal insuranceCGL;

    @ExcelField(headerName = "INSURANCE_MEDICAL")
    private BigDecimal insuranceMedical;

//    @ExcelField(headerName = "PROPERTY_FIX_RENT")
//    private BigDecimal employeeFacilitationExpenses;

    @ExcelField(headerName = "PETTY_CASH_RENTALS")
    private BigDecimal pettyCashRentals;

//    @ExcelField(headerName = "ENERGY_DG_RUNNING_CAFE")
//    private BigDecimal energyDGRunningCafe;

    @ExcelField(headerName = "EDC_RENTAL")
    private BigDecimal edcRental;





    public ServiceAggregate(int unitId, String unitName, int year, int month, String status) {

        this.unitId = unitId;
        this.unitName = unitName;
        this.year = year;
        this.month = month;
        this.status = status;
    }

    public ServiceAggregate() {
    }

    public BigDecimal getSecurityGuardCharges() {
        return securityGuardCharges;
    }

    public void setSecurityGuardCharges(BigDecimal securityGuardCharges) {
        this.securityGuardCharges = securityGuardCharges;
    }

    public BigDecimal getOtherServiceCharges() {
        return otherServiceCharges;
    }

    public void setOtherServiceCharges(BigDecimal otherServiceCharges) {
        this.otherServiceCharges = otherServiceCharges;
    }

    public BigDecimal getFuelCharges() {
        return fuelCharges;
    }

    public void setFuelCharges(BigDecimal fuelCharges) {
        this.fuelCharges = fuelCharges;
    }

    public BigDecimal getLogisticCharges() {
        return logisticCharges;
    }

    public void setLogisticCharges(BigDecimal logisticCharges) {
        this.logisticCharges = logisticCharges;
    }

    public BigDecimal getCommunicationInternet() {
        return communicationInternet;
    }

    public void setCommunicationInternet(BigDecimal communicationInternet) {
        this.communicationInternet = communicationInternet;
    }

    public BigDecimal getCommunicationTelephone() {
        return communicationTelephone;
    }

    public void setCommunicationTelephone(BigDecimal communicationTelephone) {
        this.communicationTelephone = communicationTelephone;
    }

    public BigDecimal getCommunicationILL() {
        return communicationILL;
    }

    public void setCommunicationILL(BigDecimal communicationILL) {
        this.communicationILL = communicationILL;
    }

    public BigDecimal getPayrollProcessingFee() {
        return payrollProcessingFee;
    }

    public void setPayrollProcessingFee(BigDecimal payrollProcessingFee) {
        this.payrollProcessingFee = payrollProcessingFee;
    }

    public BigDecimal getNewsPaper() {
        return newsPaper;
    }

    public void setNewsPaper(BigDecimal newsPaper) {
        this.newsPaper = newsPaper;
    }

    public BigDecimal getStaffWelfareExpenses() {
        return staffWelfareExpenses;
    }

    public void setStaffWelfareExpenses(BigDecimal staffWelfareExpenses) {
        this.staffWelfareExpenses = staffWelfareExpenses;
    }

    public BigDecimal getCourierCharges() {
        return courierCharges;
    }

    public void setCourierCharges(BigDecimal courierCharges) {
        this.courierCharges = courierCharges;
    }

    public BigDecimal getPrintingAndStationary() {
        return printingAndStationary;
    }

    public void setPrintingAndStationary(BigDecimal printingAndStationary) {
        this.printingAndStationary = printingAndStationary;
    }

    public BigDecimal getBusinessPromotion() {
        return businessPromotion;
    }

    public void setBusinessPromotion(BigDecimal businessPromotion) {
        this.businessPromotion = businessPromotion;
    }

    public BigDecimal getLegalCharges() {
        return legalCharges;
    }

    public void setLegalCharges(BigDecimal legalCharges) {
        this.legalCharges = legalCharges;
    }

    public BigDecimal getProfessionalCharges() {
        return professionalCharges;
    }

    public void setProfessionalCharges(BigDecimal professionalCharges) {
        this.professionalCharges = professionalCharges;
    }

    public BigDecimal getCleaningCharges() {
        return cleaningCharges;
    }

    public void setCleaningCharges(BigDecimal cleaningCharges) {
        this.cleaningCharges = cleaningCharges;
    }

    public BigDecimal getPestControlCharges() {
        return pestControlCharges;
    }

    public void setPestControlCharges(BigDecimal pestControlCharges) {
        this.pestControlCharges = pestControlCharges;
    }

    public BigDecimal getTechologyTraining() {
        return techologyTraining;
    }

    public void setTechologyTraining(BigDecimal techologyTraining) {
        this.techologyTraining = techologyTraining;
    }

    public BigDecimal getCorporateMarketingDigital() {
        return corporateMarketingDigital;
    }

    public void setCorporateMarketingDigital(BigDecimal corporateMarketingDigital) {
        this.corporateMarketingDigital = corporateMarketingDigital;
    }

    public BigDecimal getCorporateMarketingAdvOffline() {
        return corporateMarketingAdvOffline;
    }

    public void setCorporateMarketingAdvOffline(BigDecimal corporateMarketingAdvOffline) {
        this.corporateMarketingAdvOffline = corporateMarketingAdvOffline;
    }

    public BigDecimal getCorporateMarketingAdvOnline() {
        return corporateMarketingAdvOnline;
    }

    public void setCorporateMarketingAdvOnline(BigDecimal corporateMarketingAdvOnline) {
        this.corporateMarketingAdvOnline = corporateMarketingAdvOnline;
    }

    public BigDecimal getCorporateMarketingOutdoor() {
        return corporateMarketingOutdoor;
    }

    public void setCorporateMarketingOutdoor(BigDecimal corporateMarketingOutdoor) {
        this.corporateMarketingOutdoor = corporateMarketingOutdoor;
    }

    public BigDecimal getCorporateMarketingAgencyFees() {
        return corporateMarketingAgencyFees;
    }

    public void setCorporateMarketingAgencyFees(BigDecimal corporateMarketingAgencyFees) {
        this.corporateMarketingAgencyFees = corporateMarketingAgencyFees;
    }

    public BigDecimal getDeliveryChargesVariable() {
        return deliveryChargesVariable;
    }

    public void setDeliveryChargesVariable(BigDecimal deliveryChargesVariable) {
        this.deliveryChargesVariable = deliveryChargesVariable;
    }

//	public BigDecimal getConveyanceMarketing() {
//		return conveyanceMarketing;
//	}
//
//	public void setConveyanceMarketing(BigDecimal conveyanceMarketing) {
//		this.conveyanceMarketing = conveyanceMarketing;
//	}
//
//	public BigDecimal getConveyanceOperations() {
//		return conveyanceOperations;
//	}
//
//	public void setConveyanceOperations(BigDecimal conveyanceOperations) {
//		this.conveyanceOperations = conveyanceOperations;
//	}
//
//	public BigDecimal getConveyanceOthers() {
//		return conveyanceOthers;
//	}
//
//	public void setConveyanceOthers(BigDecimal conveyanceOthers) {
//		this.conveyanceOthers = conveyanceOthers;
//	}

    public BigDecimal getAuditFee() {
        return auditFee;
    }

    public void setAuditFee(BigDecimal auditFee) {
        this.auditFee = auditFee;
    }

    public BigDecimal getAuditFeeOutOfPocket() {
        return auditFeeOutOfPocket;
    }

    public void setAuditFeeOutOfPocket(BigDecimal auditFeeOutOfPocket) {
        this.auditFeeOutOfPocket = auditFeeOutOfPocket;
    }

    public BigDecimal getBrokerage() {
        return brokerage;
    }

    public void setBrokerage(BigDecimal brokerage) {
        this.brokerage = brokerage;
    }

    public BigDecimal getCharityAndDonations() {
        return charityAndDonations;
    }

    public void setCharityAndDonations(BigDecimal charityAndDonations) {
        this.charityAndDonations = charityAndDonations;
    }

    public BigDecimal getDomesticTicketsAndHotels() {
        return domesticTicketsAndHotels;
    }

    public void setDomesticTicketsAndHotels(BigDecimal domesticTicketsAndHotels) {
        this.domesticTicketsAndHotels = domesticTicketsAndHotels;
    }

    public BigDecimal getInternationalTicketsAndHotels() {
        return internationalTicketsAndHotels;
    }

    public void setInternationalTicketsAndHotels(BigDecimal internationalTicketsAndHotels) {
        this.internationalTicketsAndHotels = internationalTicketsAndHotels;
    }

    public BigDecimal getHouseKeepingCharges() {
        return houseKeepingCharges;
    }

    public void setHouseKeepingCharges(BigDecimal houseKeepingCharges) {
        this.houseKeepingCharges = houseKeepingCharges;
    }

    public BigDecimal getLateFeeCharges() {
        return lateFeeCharges;
    }

    public void setLateFeeCharges(BigDecimal lateFeeCharges) {
        this.lateFeeCharges = lateFeeCharges;
    }

//	public BigDecimal getMarketingDataAnalysis() {
//		return marketingDataAnalysis;
//	}
//
//	public void setMarketingDataAnalysis(BigDecimal marketingDataAnalysis) {
//		this.marketingDataAnalysis = marketingDataAnalysis;
//	}

    public BigDecimal getMiscellaneousExpenses() {
        return miscellaneousExpenses;
    }

    public void setMiscellaneousExpenses(BigDecimal miscellaneousExpenses) {
        this.miscellaneousExpenses = miscellaneousExpenses;
    }

    public BigDecimal getPenalty() {
        return penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public BigDecimal getPhotoCopyExpenses() {
        return photoCopyExpenses;
    }

    public void setPhotoCopyExpenses(BigDecimal photoCopyExpenses) {
        this.photoCopyExpenses = photoCopyExpenses;
    }

    public BigDecimal getQcrExpense() {
        return qcrExpense;
    }

    public void setQcrExpense(BigDecimal qcrExpense) {
        this.qcrExpense = qcrExpense;
    }

    public BigDecimal getRecuritmentConsultants() {
        return recuritmentConsultants;
    }

    public void setRecuritmentConsultants(BigDecimal recuritmentConsultants) {
        this.recuritmentConsultants = recuritmentConsultants;
    }

    public BigDecimal getRocFees() {
        return rocFees;
    }

    public void setRocFees(BigDecimal rocFees) {
        this.rocFees = rocFees;
    }

    public BigDecimal getDebitCreditWrittenOff() {
        return debitCreditWrittenOff;
    }

    public void setDebitCreditWrittenOff(BigDecimal debitCreditWrittenOff) {
        this.debitCreditWrittenOff = debitCreditWrittenOff;
    }

    public BigDecimal getDifferenceInExchange() {
        return differenceInExchange;
    }

    public void setDifferenceInExchange(BigDecimal differenceInExchange) {
        this.differenceInExchange = differenceInExchange;
    }

    public BigDecimal getRnDEngineeringExpenses() {
        return rnDEngineeringExpenses;
    }

    public void setRnDEngineeringExpenses(BigDecimal rnDEngineeringExpenses) {
        this.rnDEngineeringExpenses = rnDEngineeringExpenses;
    }

    public BigDecimal getCapitalImprovementExpenses() {
        return capitalImprovementExpenses;
    }

    public void setCapitalImprovementExpenses(BigDecimal capitalImprovementExpenses) {
        this.capitalImprovementExpenses = capitalImprovementExpenses;
    }

    public BigDecimal getLeaseHoldImprovements() {
        return leaseHoldImprovements;
    }

    public void setLeaseHoldImprovements(BigDecimal leaseHoldImprovements) {
        this.leaseHoldImprovements = leaseHoldImprovements;
    }

    public BigDecimal getMarketingLaunch() {
        return marketingLaunch;
    }

    public void setMarketingLaunch(BigDecimal marketingLaunch) {
        this.marketingLaunch = marketingLaunch;
    }

    public BigDecimal getCorporateMarketingPhotography() {
        return corporateMarketingPhotography;
    }

    public void setCorporateMarketingPhotography(BigDecimal corporateMarketingPnS) {
        this.corporateMarketingPhotography = corporateMarketingPnS;
    }

    public BigDecimal getOdcRental() {
        return odcRental;
    }

    public void setOdcRental(BigDecimal odcRental) {
        this.odcRental = odcRental;
    }


//	public BigDecimal getCogsOthers() {
//		return cogsOthers;
//	}
//
//	public void setCogsOthers(BigDecimal cogsOthers) {
//		this.cogsOthers = cogsOthers;
//	}

    public BigDecimal getCommissionChange() {
        return commissionChange;
    }

    public void setCommissionChange(BigDecimal commissionChange) {
        this.commissionChange = commissionChange;
    }

    public BigDecimal getVehicleRegularMaintenanceHq() {
        return vehicleRegularMaintenanceHq;
    }

    public void setVehicleRegularMaintenanceHq(BigDecimal vehicleRegularMaintenanceHq) {
        this.vehicleRegularMaintenanceHq = vehicleRegularMaintenanceHq;
    }

    public BigDecimal getBuildingMaintenanceHq() {
        return buildingMaintenanceHq;
    }

    public void setBuildingMaintenanceHq(BigDecimal buildingMaintenanceHq) {
        this.buildingMaintenanceHq = buildingMaintenanceHq;
    }

    public BigDecimal getComputerItMaintenanceHq() {
        return computerItMaintenanceHq;
    }

    public void setComputerItMaintenanceHq(BigDecimal computerItMaintenanceHq) {
        this.computerItMaintenanceHq = computerItMaintenanceHq;
    }

    public BigDecimal getEquipmentMaintenanceHq() {
        return equipmentMaintenanceHq;
    }

    public void setEquipmentMaintenanceHq(BigDecimal equipmentMaintenanceHq) {
        this.equipmentMaintenanceHq = equipmentMaintenanceHq;
    }

    public BigDecimal getMarketingNpiHq() {
        return marketingNpiHq;
    }

    public void setMarketingNpiHq(BigDecimal marketingNpiHq) {
        this.marketingNpiHq = marketingNpiHq;
    }

    public BigDecimal getLicenseExpenses() {
        return licenseExpenses;
    }

    public void setLicenseExpenses(BigDecimal licenseExpenses) {
        this.licenseExpenses = licenseExpenses;
    }

    public BigDecimal getCorporateMarketingAtlRadio() {
        return corporateMarketingAtlRadio;
    }

    public void setCorporateMarketingAtlRadio(BigDecimal corporateMarketingAtlRadio) {
        this.corporateMarketingAtlRadio = corporateMarketingAtlRadio;
    }

    public BigDecimal getCorporateMarketingAtlTv() {
        return corporateMarketingAtlTv;
    }

    public void setCorporateMarketingAtlTv(BigDecimal corporateMarketingAtlTv) {
        this.corporateMarketingAtlTv = corporateMarketingAtlTv;
    }

    public BigDecimal getCorporateMarketingAtlPrintAd() {
        return corporateMarketingAtlPrintAd;
    }

    public void setCorporateMarketingAtlPrintAd(BigDecimal corporateMarketingAtlPrintAd) {
        this.corporateMarketingAtlPrintAd = corporateMarketingAtlPrintAd;
    }

    public BigDecimal getCorporateMarketingAtlCinema() {
        return corporateMarketingAtlCinema;
    }

    public void setCorporateMarketingAtlCinema(BigDecimal corporateMarketingAtlCinema) {
        this.corporateMarketingAtlCinema = corporateMarketingAtlCinema;
    }

    public BigDecimal getCorporateMarketingAtlDigital() {
        return corporateMarketingAtlDigital;
    }

    public void setCorporateMarketingAtlDigital(BigDecimal corporateMarketingAtlDigital) {
        this.corporateMarketingAtlDigital = corporateMarketingAtlDigital;
    }

    public BigDecimal getLogisticInterstateColdVehicle() {
        return logisticInterstateColdVehicle;
    }

    public void setLogisticInterstateColdVehicle(BigDecimal logisticInterstateColdVehicle) {
        this.logisticInterstateColdVehicle = logisticInterstateColdVehicle;
    }

    public BigDecimal getLogisticInterstateNonColdVehicle() {
        return logisticInterstateNonColdVehicle;
    }

    public void setLogisticInterstateNonColdVehicle(BigDecimal logisticInterstateNonColdVehicle) {
        this.logisticInterstateNonColdVehicle = logisticInterstateNonColdVehicle;
    }

    public BigDecimal getLogisticInterstateAir() {
        return logisticInterstateAir;
    }

    public void setLogisticInterstateAir(BigDecimal logisticInterstateAir) {
        this.logisticInterstateAir = logisticInterstateAir;
    }

    public BigDecimal getLogisticInterstateRoad() {
        return logisticInterstateRoad;
    }

    public void setLogisticInterstateRoad(BigDecimal logisticInterstateRoad) {
        this.logisticInterstateRoad = logisticInterstateRoad;
    }

    public BigDecimal getLogisticInterstateTrain() {
        return logisticInterstateTrain;
    }

    public void setLogisticInterstateTrain(BigDecimal logisticInterstateTrain) {
        this.logisticInterstateTrain = logisticInterstateTrain;
    }

    public BigDecimal getAirConditionerAmc() {
        return airConditionerAmc;
    }

    public void setAirConditionerAmc(BigDecimal airConditionerAmc) {
        this.airConditionerAmc = airConditionerAmc;
    }

    public BigDecimal getCorporateMarketingSms() {
        return corporateMarketingSms;
    }

    public void setCorporateMarketingSms(BigDecimal corporateMarketingSms) {
        this.corporateMarketingSms = corporateMarketingSms;
    }

    public BigDecimal getSecurityDepositProperty() {
        return securityDepositProperty;
    }

    public void setSecurityDepositProperty(BigDecimal securityDepositProperty) {
        this.securityDepositProperty = securityDepositProperty;
    }

    public BigDecimal getSecurityDepositMVAT() {
        return securityDepositMVAT;
    }

    public void setSecurityDepositMVAT(BigDecimal securityDepositMVAT) {
        this.securityDepositMVAT = securityDepositMVAT;
    }

    public BigDecimal getSecurityDepositElectricity() {
        return securityDepositElectricity;
    }

    public void setSecurityDepositElectricity(BigDecimal securityDepositElectricity) {
        this.securityDepositElectricity = securityDepositElectricity;
    }

    public BigDecimal getMarketingDiscountEcom() {
        return marketingDiscountEcom;
    }

    public void setMarketingDiscountEcom(BigDecimal marketingDiscountEcom) {
        this.marketingDiscountEcom = marketingDiscountEcom;
    }

    public BigDecimal getShippingCharges() {
        return shippingCharges;
    }

    public void setShippingCharges(BigDecimal shippingCharges) {
        this.shippingCharges = shippingCharges;
    }

    public BigDecimal getOtherTransactionCharges() {
        return otherTransactionCharges;
    }

    public void setOtherTransactionCharges(BigDecimal otherTransactionCharges) {
        this.otherTransactionCharges = otherTransactionCharges;
    }

    public BigDecimal getDiscountDealerMargin() {
        return discountDealerMargin;
    }

    public void setDiscountDealerMargin(BigDecimal discountDealerMargin) {
        this.discountDealerMargin = discountDealerMargin;
    }

    public BigDecimal getPerformanceMarketingService() {
        return performanceMarketingService;
    }

    public void setPerformanceMarketingService(BigDecimal performanceMarketingService) {
        this.performanceMarketingService = performanceMarketingService;
    }

    public BigDecimal getInsuranceMarine() {
        return insuranceMarine;
    }

    public void setInsuranceMarine(BigDecimal insuranceMarine) {
        this.insuranceMarine = insuranceMarine;
    }

    public BigDecimal getShareStampingCharges() {
        return shareStampingCharges;
    }

    public void setShareStampingCharges(BigDecimal shareStampingCharges) {
        this.shareStampingCharges = shareStampingCharges;
    }

    public BigDecimal getOtherChargesEcom() {
        return otherChargesEcom;
    }

    public void setOtherChargesEcom(BigDecimal otherChargesEcom) {
        this.otherChargesEcom = otherChargesEcom;
    }

    public BigDecimal getComissionChannelPartnerFixed() {
        return comissionChannelPartnerFixed;
    }

    public void setComissionChannelPartnerFixed(BigDecimal comissionChannelPartnerFixed) {
        this.comissionChannelPartnerFixed = comissionChannelPartnerFixed;
    }

    public BigDecimal getCogsTradingGoods() {
        return cogsTradingGoods;
    }

    public void setCogsTradingGoods(BigDecimal cogsTradingGoods) {
        this.cogsTradingGoods = cogsTradingGoods;
    }

    public BigDecimal getRoyaltyFees() {
        return royaltyFees;
    }

    public void setRoyaltyFees(BigDecimal royaltyFees) {
        this.royaltyFees = royaltyFees;
    }

    public BigDecimal getFreightCharges() {
        return freightCharges;
    }

    public void setFreightCharges(BigDecimal freightCharges) {
        this.freightCharges = freightCharges;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public int getYear() {
        return year;
    }


    public void setYear(int year) {
        this.year = year;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getCorporateMarketingChannelPartner() {
        return corporateMarketingChannelPartner;
    }

    public void setCorporateMarketingChannelPartner(BigDecimal corporateMarketingChannelPartner) {
        this.corporateMarketingChannelPartner = corporateMarketingChannelPartner;
    }

    public BigDecimal getEmployeeFacilitationCharges() {
        return employeeFacilitationCharges;
    }

    public void setEmployeeFacilitationCharges(BigDecimal employeeFacilitationCharges) {
        this.employeeFacilitationCharges = employeeFacilitationCharges;
    }

//    public BigDecimal getCorporateMarketingSmsEmail() {
//        return corporateMarketingSmsEmail;
//    }
//
//    public void setCorporateMarketingSmsEmail(BigDecimal corporateMarketingSmsEmail) {
//        this.corporateMarketingSmsEmail = corporateMarketingSmsEmail;
//    }

    public BigDecimal getProntoAMC() {
        return prontoAMC;
    }

    public void setProntoAMC(BigDecimal prontoAMC) {
        this.prontoAMC = prontoAMC;
    }

    public BigDecimal getOthersMaintenance() {
        return othersMaintenance;
    }

    public void setOthersMaintenance(BigDecimal othersMaintenance) {
        this.othersMaintenance = othersMaintenance;
    }

    public BigDecimal getTechnologyPlatformCharges() {
        return technologyPlatformCharges;
    }

    public void setTechnologyPlatformCharges(BigDecimal technologyPlatformCharges) {
        this.technologyPlatformCharges = technologyPlatformCharges;
    }

    public BigDecimal getInterestOnTermLoan() {
        return interestOnTermLoan;
    }

    public void setInterestOnTermLoan(BigDecimal interestOnTermLoan) {
        this.interestOnTermLoan = interestOnTermLoan;
    }

    public BigDecimal getTechnologyOthers() {
        return technologyOthers;
    }

    public void setTechnologyOthers(BigDecimal technologyOthers) {
        this.technologyOthers = technologyOthers;
    }

    public BigDecimal getSystemRental() {
        return systemRental;
    }

    public void setSystemRental(BigDecimal systemRental) {
        this.systemRental = systemRental;
    }

    public BigDecimal getRoRental() {
        return roRental;
    }

    public void setRoRental(BigDecimal roRental) {
        this.roRental = roRental;
    }

    public BigDecimal getInsuranceAccidental() {
        return insuranceAccidental;
    }

    public void setInsuranceAccidental(BigDecimal insuranceAccidental) {
        this.insuranceAccidental = insuranceAccidental;
    }

    public BigDecimal getDgRental() {
        return dgRental;
    }

    public void setDgRental(BigDecimal dgRental) {
        this.dgRental = dgRental;
    }

    public BigDecimal getOthersAMC() {
        return othersAMC;
    }

    public void setOthersAMC(BigDecimal othersAMC) {
        this.othersAMC = othersAMC;
    }

    public BigDecimal getMusicRentals() {
        return musicRentals;
    }

    public void setMusicRentals(BigDecimal musicRentals) {
        this.musicRentals = musicRentals;
    }

    public BigDecimal getInsuranceAssets() {
        return insuranceAssets;
    }

    public void setInsuranceAssets(BigDecimal insuranceAssets) {
        this.insuranceAssets = insuranceAssets;
    }

    public BigDecimal getInsuranceCGL() {
        return insuranceCGL;
    }

    public void setInsuranceCGL(BigDecimal insuranceCGL) {
        this.insuranceCGL = insuranceCGL;
    }

    public BigDecimal getInsuranceMedical() {
        return insuranceMedical;
    }

    public void setInsuranceMedical(BigDecimal insuranceMedical) {
        this.insuranceMedical = insuranceMedical;
    }

    public BigDecimal getPettyCashRentals() {
        return pettyCashRentals;
    }

    public void setPettyCashRentals(BigDecimal pettyCashRentals) {
        this.pettyCashRentals = pettyCashRentals;
    }

//    public BigDecimal getEnergyDGRunningCafe() {
//        return energyDGRunningCafe;
//    }
//
//    public void setEnergyDGRunningCafe(BigDecimal energyDGRunningCafe) {
//        this.energyDGRunningCafe = energyDGRunningCafe;
//    }

    public BigDecimal getEdcRental() {
        return edcRental;
    }

    public void setEdcRental(BigDecimal edcRental) {
        this.edcRental = edcRental;
    }
}
