package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class CustomerVO implements Serializable {

    private static final long serialVersionUID = 5129852663798251007L;

    private String _id;
    
    protected int id;
    
    protected String firstName;
    
    protected String middleName;
    
    protected String lastName;
    
    protected String countryCode;
    
    protected String contactNumber;
    
    protected String emailId;
    
    protected boolean emailVerified;
    
    protected int loyaltyPoints;
    
    protected BigDecimal chaayosCash;
    
    protected boolean contactNumberVerified;
    
    protected boolean availedSignupOffer;
    
    protected boolean smsSubscriber;
    
    protected boolean emailSubscriber;
    
    protected boolean loyaltySubscriber;
    
    protected boolean isBlacklisted;
    
    protected boolean internal;

    protected boolean isDND;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    public boolean isEmailVerified() {
        return emailVerified;
    }

    public void setEmailVerified(boolean emailVerified) {
        this.emailVerified = emailVerified;
    }

    public int getLoyaltyPoints() {
        return loyaltyPoints;
    }

    public void setLoyaltyPoints(int loyaltyPoints) {
        this.loyaltyPoints = loyaltyPoints;
    }

    public BigDecimal getChaayosCash() {
        return chaayosCash;
    }

    public void setChaayosCash(BigDecimal chaayosCash) {
        this.chaayosCash = chaayosCash;
    }

    public boolean isContactNumberVerified() {
        return contactNumberVerified;
    }

    public void setContactNumberVerified(boolean contactNumberVerified) {
        this.contactNumberVerified = contactNumberVerified;
    }

    public boolean isAvailedSignupOffer() {
        return availedSignupOffer;
    }

    public void setAvailedSignupOffer(boolean availedSignupOffer) {
        this.availedSignupOffer = availedSignupOffer;
    }

    public boolean isSmsSubscriber() {
        return smsSubscriber;
    }

    public void setSmsSubscriber(boolean smsSubscriber) {
        this.smsSubscriber = smsSubscriber;
    }

    public boolean isEmailSubscriber() {
        return emailSubscriber;
    }

    public void setEmailSubscriber(boolean emailSubscriber) {
        this.emailSubscriber = emailSubscriber;
    }

    public boolean isLoyaltySubscriber() {
        return loyaltySubscriber;
    }

    public void setLoyaltySubscriber(boolean loyaltySubscriber) {
        this.loyaltySubscriber = loyaltySubscriber;
    }

    public boolean isBlacklisted() {
        return isBlacklisted;
    }

    public void setBlacklisted(boolean blacklisted) {
        isBlacklisted = blacklisted;
    }

    public boolean isInternal() {
        return internal;
    }

    public void setInternal(boolean internal) {
        this.internal = internal;
    }

    public boolean isDND() {
        return isDND;
    }

    public void setDND(boolean DND) {
        isDND = DND;
    }
}
