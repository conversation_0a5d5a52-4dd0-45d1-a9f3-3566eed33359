/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

import javax.persistence.NoResultException;

import com.stpl.tech.kettle.core.SignUpTimeSlotCount;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.data.model.CustomerCampaignJourney;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.WebOfferDetail;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.CustomerOneViewData;
import com.stpl.tech.kettle.domain.model.CustomerTransactionViewData;
import com.stpl.tech.master.core.CouponMappingType;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.domain.model.CouponMapping;

public interface CustomerOfferManagementDao extends AbstractDao {

	public OfferOrder applyCoupon(OfferOrder offerOrder, BigDecimal offerValue) throws OfferValidationException, DataNotFoundException;

	public void checkCouponMapping(Set<CouponMapping> mappingList, List<CouponMapping> values, CouponMappingType type)
			throws OfferValidationException, DataNotFoundException;

	public boolean availedCashOffer(Integer customerId, Date startOfBusinessDay, Collection<String> offerCodes, int maxUsageAllowed);

	List<WebOfferDetail> getWebOfferDetails(Integer customerId, String offerType) throws NoResultException;

	WebOfferDetail saveWebOfferDetail(WebOfferDetail webOfferDetail);

	SignUpTimeSlotCount fetchSignUpOfferTimeSlots(String dateOfDelivery);

	public boolean hasCustomerReceivedPostOrderOffer(int customerId, Integer lastNDays, Integer campaignId);

	boolean updateOfferApplicationDetails(String couponCode, Integer customerId, Integer orderId, BigDecimal savings);

	CustomerOneViewData getCustomerOneViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds);

	public CustomerCampaignOfferDetail getActiveCustomerOffer(int customerId, String strategy);

    CustomerCampaignJourney getJourneyForSession(CustomerCampaignJourney journeyDetail);

	CustomerTransactionViewData getCustomerTransactionViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds);

	public CustomerEmailData getCustomerEmailData(int customerId, Integer brandId);

	public CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds);

	CustomerCampaignOfferDetail getActiveOfferCampaign(int customerId, Integer campaignId);

	boolean hasCustomerReceivedDNBO(int customerId, Integer dinePostOrderOfferCheckLastNDaysValue, Integer campaignId);
}
