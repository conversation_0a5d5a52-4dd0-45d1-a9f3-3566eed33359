import axios from "axios";
import apis from "../APIs";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import _ from "lodash";
import {browserHistory} from "react-router";
import * as CartManagementActions from "./CartManagementActions";
import * as CustomizationModalActions from "./CustomizationModalActions";
import * as LocalityActions from "./LocalityActions";
import * as UtilityActions from "./UtilityActions";
import trackUtils from "../utils/TrackUtils";
import * as CustomerActions from "./CustomerActions";
import * as CampaignManagementActions from "./CampaignManagementActions";

export function getUnitProducts(criteria, city, locality, outlet) {
    console.log("getUnitProducts:: criteria:: " + criteria + " city:: " + JSON.stringify(city) + " locality:: " + JSON.stringify(locality) + " outlet:: " + JSON.stringify(outlet));
    return dispatch => {
        if ((criteria == "DELIVERY" && !appUtil.checkEmpty(locality)) ||
            ((criteria == "DINE_IN" || criteria == "TAKE_AWAY") && !appUtil.checkEmpty(outlet))) {
            let url;
            let method;
            let reqData;
            if (criteria == "DELIVERY") {
                url = apis.getUrls().neoCache.getDeliveryUnit;
                method = "POST";
                reqData = JSON.stringify({city: city.city, locality: locality.label});
            } else {
                url = apis.getUrls().unitCache.getUnit + "?unitId=" + outlet.value;
                method = "GET";
                reqData = {};
            }
            dispatch({type: "LOAD_UNIT_PRODUCTS_PENDING"});
            axios({
                method: method,
                url: url,
                data: reqData,
                headers: {"Content-Type": "application/json"}
            }).then(function (response) {
                if (!appUtil.checkEmpty(response.data) && response.status == 200) {
                    let unitData = response.data.unit;
                    if (criteria == "DELIVERY") {
                        dispatch(getZomatoRedirectLink(response.data.unit));
                    }
                    appUtil.setTaxes(unitData.taxes);
                    var skipDeliveryPackaging = {
                        delivery: response.data.skipDeliveryCharge,
                        packaging: response.data.skipPackagingCharge
                    };
                    dispatch({type: "SET_SKIP_DELIVERY_PACKAGING", payload: skipDeliveryPackaging});
                    if (criteria == "DELIVERY") {
                        trackDeliveryAnalytics(city.city, locality, unitData.name, response.data.type);
                    } else {
                        trackTakeawayAnalytics(city != null ? city.city : "", unitData.name, response.data.type);
                    }
                    StorageUtils.setUnitDetails({id: unitData.id, name: unitData.name});
                    //dispatch(LocalityActions.selectOutlet({label:unitData.name, value:unitData.id}));
                    appUtil.setInclusiveTaxPrices(unitData, appUtil.isInterstate(unitData, city));
                    dispatch(loadProductImages(unitData));
                    dispatch({type: "LOAD_UNIT_PRODUCTS_FULFILLED", payload: unitData});
                    CartManagementActions.setDeliveryAndPackagingItem(unitData);
                    dispatch(CartManagementActions.initCart(unitData, skipDeliveryPackaging, appUtil.isInterstate(unitData, city)));
                    dispatch(initAutoRecipeLoad(unitData));
                    dispatch(loadSpecialMenu());
                    dispatch(loadUnitInventory(unitData)); //TODO add this for inventory call
                    dispatch(getProductOffers(unitData.id));
                    if (response.data.error && response.data.error > 0) {
                        dispatch(UtilityActions.showPopup(response.data.message, "info", 4000));
                        try {
                            trackUtils.trackUnitClosed({
                                city: city.city != null ? city.city : "",
                                locality: !appUtil.checkEmpty(locality) ? locality.label : "",
                                outlet: unitData.name,
                                type: type,
                                msg: response.data.message
                            });
                        } catch (e) {
                        }
                    }
                    dispatch(loadBoughtByYou());
                    dispatch(loadProductTags());
                    if (!appUtil.checkEmpty(StorageUtils.getCartDetail())) {
                        let crt = StorageUtils.getCartDetail();
                        crt.orderDetail.unitId = unitData.id;
                        crt.orderDetail.unitName = unitData.name;
                        dispatch({type: "UPDATE_CART", payload: crt});
                    }
                } else {
                    dispatch(reselectLocalityMeta(city));
                }
            }).catch(function (error) {
                console.log(error);
                dispatch(reselectLocalityMeta(city));
            });
        } else {
            browserHistory.push("/");
            dispatch(UtilityActions.showPopup("Please select locality or outlet."));
        }
    }
}

export function getZomatoRedirectLink(unit, isStoreRedirect) {
    return dispatch => {
        dispatch({type: "LOADING_REDIRECT", payload: true});
        axios({
            method: "POST",
            url: apis.getUrls().neoMetadata.getRedirectLink,
            data: unit.id,
            headers: {"Content-Type": "application/json"}
        }).then(function (response) {
            //console.log('getZomatoRedirectLink ' , JSON.stringify(response));
            dispatch({type: "LOADING_REDIRECT", payload: false});
            if (!appUtil.checkEmpty(response.data) && response.status == 200) {
                dispatch({type: "SET_REDIRECT_DETAILS", payload: response.data});
                if (appUtil.isMobile()) {
                    dispatch(redirectToZomatoApp(response.data, isStoreRedirect));
                } else {
                    dispatch(redirectToZomatoWeb(response.data, isStoreRedirect));
                }
            } else {
                dispatch({type: "SET_REDIRECT_DETAILS", payload: null});
            }
            dispatch({type: "LOADING_REDIRECT", payload: false});
        }).catch(function (error) {
            console.log(error);
            dispatch({type: "LOADING_REDIRECT", payload: false});
        });
    }
}

export function redirectToZomatoWeb(redirectDetails, isStoreRedirect) {
    return dispatch => {
        UtilityActions.showFullPageLoader("Redirecting you to Zomato menu for delivery ordering.");
        dispatch(trackUserDataForRedirect("web", isStoreRedirect));
        trackUtils.trackDeliveryRedirected({source: (isStoreRedirect == true ? "STORE" : "NO_STORE"), type: "WEB"});
        try {
            gtag('event', 'conversion', {
                'send_to': 'AW-857135357/jlHbCO-d06QBEP2x25gD',
                'transaction_id': ''
            });
        } catch (e) {
            console.log(e);
        }
        window.location.href = redirectDetails.webLink;
    }
}

export function redirectToZomatoApp(redirectDetails, isStoreRedirect) {
    return dispatch => {
        UtilityActions.showFullPageLoader("Redirecting you to Zomato menu for delivery ordering.");
        dispatch(trackUserDataForRedirect("app", isStoreRedirect));
        trackUtils.trackDeliveryRedirected({source: (isStoreRedirect == true ? "STORE" : "NO_STORE"), type: "APP"});
        try {
            gtag('event', 'conversion', {
                'send_to': 'AW-857135357/jlHbCO-d06QBEP2x25gD',
                'transaction_id': ''
            });
        } catch (e) {
            console.log(e);
        }
        window.location.href = redirectDetails.appLink;
    }
}

export function trackUserDataForRedirect(type, isStoreRedirect) {
    return dispatch => {
        var locality = null;
        if (StorageUtils.getLocalityMetadata() != null && StorageUtils.getLocalityMetadata().locality != null) {
            locality = StorageUtils.getLocalityMetadata().locality.label;
        }
        var source = "STORE";
        if (isStoreRedirect != true) {
            source = "NO_STORE";
        }
        axios({
            method: "POST",
            url: apis.getUrls().neoMetadata.trackRedirect,
            data: {
                deviceKey: (StorageUtils.getAuthDetail() != null ? StorageUtils.getAuthDetail().deviceKey : null),
                sessionKey: (StorageUtils.getAuthDetail() != null ? StorageUtils.getAuthDetail().sessionKey : null),
                name: (StorageUtils.getCustomerDetail() != null ? StorageUtils.getCustomerDetail().name : null),
                email: (StorageUtils.getCustomerDetail() != null ? StorageUtils.getCustomerDetail().email : null),
                contact: (StorageUtils.getCustomerDetail() != null ? StorageUtils.getCustomerDetail().contact : null),
                city: (StorageUtils.getLocalityMetadata() != null ? StorageUtils.getLocalityMetadata().city : null),
                locality: locality,
                userAgent: navigator.userAgent,
                type: type,
                source: source
            },
            headers: {"Content-Type": "application/json"}
        }).then(function (response) {
        }).catch(function (error) {
            console.log(error);
        });
    }
}

const trackDeliveryAnalytics = (city, locality, outlet, type) => {
    try {
        trackUtils.trackLocalityMetadata({
            city: city,
            locality: !appUtil.checkEmpty(locality) ? locality.label : "",
            outlet: outlet,
            type: type
        });
    } catch (e) {
    }
}

const trackTakeawayAnalytics = (city, outlet, type) => {
    try {
        trackUtils.trackLocalityMetadata({city: city, locality: "", outlet: outlet, type: type});
    } catch (e) {
    }

}

const reselectLocalityMeta = (cityObj) => {
    return dispatch => {
        dispatch(LocalityActions.selectCity(cityObj.city, cityObj.state));
        dispatch(UtilityActions.showPopup("Please select locality/outlet again!"));
        if (appUtil.isMobile()) {
            browserHistory.push("/");
        } else {
            browserHistory.push("/menu");
            dispatch(LocalityActions.toggleLocationWrapper(true));
        }
    }
}

const initAutoRecipeLoad = (unit) => {
    return dispatch => {
        var products = [];
        var filtered = _.filter(unit.products, (o) => {
            return o.strategy != 5 && [10, 12, 50].indexOf(o.id) < 0
        });
        _.map(filtered, (prod) => {
            var product = [];
            _.map(prod.prices, (price) => {
                product.push({productId: prod.id, dimension: price.dimension, profile: price.profile});
            });
            products.push(product);
        });
        dispatch({type: "SET_RECIPE_PRODUCTS", payload: products});
        dispatch(autoRecipeLoad(products, 0));
    }
};

const autoRecipeLoad = (products, index) => {
    return dispatch => {
        if (products.length > index) {
            axios({
                method: "POST",
                url: apis.getUrls().neoCache.productRecipes,
                data: JSON.stringify(products[index]),
                headers: {'Content-Type': 'application/json'}
            }).then((response) => {
                let recipesList = response.data;
                recipesList.map(function (recipeData) {
                    var recipes = {};
                    recipeData.recipes.map(function (recipe) {
                        recipes[recipe.dimension.code] = recipe;
                    });
                    recipeData.recipes = recipes;
                });
                dispatch({type: "SILENT_LOAD_PRODUCT_RECIPE", payload: recipesList});
                dispatch(autoRecipeLoad(products, index + 1));
            }).catch((error) => {
                dispatch(autoRecipeLoad(products, index + 1));
            });
        }
    }
};

const loadSpecialMenu = () => {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().neoCache.specialMenu,
            data: JSON.stringify({}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "SPECIAL_MENU_LOADED", payload: response.data});
        }).catch(function (error) {
            dispatch({type: "SPECIAL_MENU_REJECTED", payload: error});
        });
    }
};

const loadProductTags = () => {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().neoCache.productTags,
            data: {},
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "PRODUCT_TAGS_LOADED", payload: response.data});
        }).catch(function (error) {
        });
    }
};

const loadProductImages = (unit) => {
    return dispatch => {
        const productIds = [];
        unit.products.map((product) => {
            productIds.push(product.id);
        });
        axios({
            method: "POST",
            url: apis.getUrls().neoCache.productImages,
            data: productIds,
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "PRODUCT_IMAGES_LOADED", payload: response.data});
        }).catch(function (error) {
            console.log(error);
        });
    }
};

const loadBoughtByYou = () => {
    return dispatch => {
        if (StorageUtils.getSessionId() != null) {
            axios({
                method: "POST",
                url: apis.getUrls().customer.boughtByYou,
                data: JSON.stringify(StorageUtils.getCustomerId()),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                if (response && response.data) {
                    dispatch({type: "BOUGHT_BY_YOU_LOADED", payload: response.data});
                }
            }).catch(function (error) {
                console.log(error);
            });
        }
    }
};

export function loadUnitInventory(unit) {
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().webInventory.unit,
            data: JSON.stringify(StorageUtils.getUnitDetails().id),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            if (response.data) {
                dispatch({type: "INVENTORY_FULFILLED", payload: response.data});
                dispatch(CartManagementActions.updateStockInCart(StorageUtils.getCartDetail(), response.data));
                trackInitialStockout(unit, response.data);
            }
        }).catch(function (error) {
            dispatch({type: "INVENTORY_REJECTED", payload: error});
        });
    }
}

export function trackInitialStockout(unit, stock) {
    if (unit && unit.products) {
        let productNames = [];
        unit.products.map((product) => {
            if (stock[product.id] == 0) {
                productNames.push(product.name);
            }
        });
        productNames.length > 0 ? trackUtils.trackInitialStockout(productNames.join(",")) : null;
    }
}

export function getTags() {
    return dispatch => {
        dispatch({type: "LOAD_TAGS_PENDING"});
        axios({
            method: "GET",
            url: apis.getUrls().neoCache.tags,
            data: {},
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            dispatch({type: "LOAD_TAGS_FULFILLED", payload: response.data});
        }).catch(function (error) {
            dispatch({type: "LOAD_TAGS_REJECTED", payload: error});
        });
    }
}

export function trackSelectionStockout(productName) {
    return dispatch => {
        !appUtil.checkEmpty(productName) ? trackUtils.trackSelectionStockout(productName) : null;
    }
}

export function loadProductRecipe(unit, product, cartItem) {
    return dispatch => {
        if (!product.recipesLoaded) {
            //let requestData = [{productId: [10, 11, 12, 50].indexOf(product.id) >= 0 ? 11 : product.id, recipes: {}}];
            let requestData = [];
            _.forEach(product.prices, (price) => {
                requestData.push({
                    productId: [10, 11, 12, 50].indexOf(product.id) >= 0 ? 11 : product.id,
                    dimension: price.dimension,
                    profile: price.profile
                });
                //requestData[0].recipes[price.dimension] = null;
            });
            dispatch({type: "LOAD_RECIPE_TO_PRODUCT_PENDING", payload: product.id});
            axios({
                method: "POST",
                url: apis.getUrls().neoCache.productRecipes,
                data: JSON.stringify(requestData),
                headers: {'Content-Type': 'application/json'}
            }).then(function (response) {
                let recipesList = response.data;
                recipesList.map(function (recipeData) {
                    var recipes = {};
                    recipeData.recipes.map(function (recipe) {
                        recipes[recipe.dimension.code] = recipe;
                    });
                    recipeData.recipes = recipes;
                });
                /*var constituentProductIds = []; // composite product case
                if (product.type == 8) {
                    recipesList.map((productRecipe) => {
                        productRecipe.recipes["None"].ingredient.compositeProduct.details.map((detail) => {
                            detail.menuProducts.map((product) => {
                                unit.products.map((unitProduct) => {
                                    if (unitProduct.id == product.product.productId && !unitProduct.recipesLoaded) {
                                        constituentProductIds.push(product.product.productId);
                                    }
                                });
                            });
                        });
                    });
                }
                if(constituentProductIds.length>0) {
                    //load constituent product recipes which have not been loaded
                    axios({
                        method: "POST",
                        url: apis.getUrls().neoCache.productRecipes,
                        data: JSON.stringify(constituentProductIds),
                        headers: {'Content-Type': 'application/json'}
                    }).then(function (response) {
                        recipesList = recipesList.concat(response.data);
                        console.log(recipesList);
                        product.recipesLoaded = true;
                        dispatch(CustomizationModalActions.updateCustomizationProduct(product));
                        dispatch({type: "LOAD_RECIPE_TO_PRODUCT_FULFILLED", payload: recipesList});
                        dispatch(setCustomizationActions(product, recipesList, cartItem));
                    }).catch(function (error) {
                        dispatch({type: "LOAD_RECIPE_TO_PRODUCT_REJECTED"});
                    });
                }else {*/
                // product recipe List object returned
                product.recipesLoaded = true;
                dispatch(CustomizationModalActions.updateCustomizationProduct(product));
                dispatch({type: "LOAD_RECIPE_TO_PRODUCT_FULFILLED", payload: recipesList});
                dispatch(setCustomizationActions(product, recipesList, cartItem));
                /*}*/
            }).catch(function (error) {
                dispatch({type: "LOAD_RECIPE_TO_PRODUCT_REJECTED"});
            });
        }
    }
}

const setCustomizationActions = (product, recipesList, ci) => {
    return dispatch => {
        //set customisation code
        var customizationType = ci == null ? "MENU_ITEM" : "CART_ITEM";
        if (product.customize) {
            if (product.prices.length > 1) {
                //open customization modal
                dispatch(CustomizationModalActions.showCustomizationModal(product, customizationType, ci));
                if (product.id == 11 && ci == null) {
                    product.id = 10;
                    product.name = "Desi Chai";
                }
                dispatch(CustomizationModalActions.updateCustomizationProduct(product));
            } else {
                if (product.type != 8) {
                    var count = 0;
                    for (var key in recipesList[0].recipes) {
                        if (product.prices[0].dimension == key) {
                            count += recipesList[0].recipes[key].customizationCount;
                        }
                    }
                    if (count > 1) {
                        dispatch(CustomizationModalActions.showCustomizationModal(product, customizationType, ci));
                    } else {
                        //show customization below product
                        dispatch({type: "SET_SHOW_CUSTOMIZATION", payload: product});
                    }
                } else {
                    dispatch(CustomizationModalActions.showCustomizationModal(product, customizationType, ci));
                }
            }
        } else {
            if (product.prices.length > 1) {
                //show dimensions below product
                dispatch({type: "SET_SHOW_CUSTOMIZATION", payload: product});
            } else {
                //add item to cart
                var retObj = appUtil.setCustomizationModal(product, "MENU_ITEM", null);
                var cartItem = retObj.product;
                cartItem.selectedDimension = retObj.product.prices[0];
                cartItem.prices = [];
                dispatch(CartManagementActions.addItemToCart({cartItem: cartItem, addons: [], quantity: 1}));
            }
        }
    }
}

export function discardUnit() {
    return dispatch => {
        dispatch({type: "DISCARD_UNIT"});
    }
}

export function toggleCustomization(product) {
    return dispatch => {
        product.showCustomization = !product.showCustomization;
        dispatch({type: "SET_SHOW_CUSTOMIZATION", payload: product});
    }
}

export function setInitMenu(val) {
    return dispatch => {
        dispatch({type: "SET_INIT_MENU", payload: val});
    }
}


export function loadDineinMenu(unitId) {
    return dispatch => {
        dispatch(LocalityActions.setCriteria("DINE_IN"));
        var authDetail = StorageUtils.getAuthDetail();
        if (!appUtil.checkEmpty(authDetail)) {
            dispatch(CustomerActions.setDeviceKey(authDetail.deviceKey));
            dispatch(CustomerActions.setSessionKey(authDetail.sessionKey));
        }
        let url;
        let method;
        let reqData;
        url = apis.getUrls().unitCache.getUnit + "?unitId=" + unitId;
        method = "GET";
        reqData = {};
        dispatch({type: "LOAD_UNIT_PRODUCTS_PENDING"});
        axios({
            method: method,
            url: url,
            data: reqData,
            headers: {"Content-Type": "application/json"}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data) && response.status == 200) {
                let unitData = response.data.unit;
                StorageUtils.setUnitDetails({id: unitData.id, name: unitData.name});
                //console.log(unitData.location);
                let city = unitData.location.code;
                let state = unitData.location.state.code;
                dispatch(LocalityActions.loadOutletsList(city));
                dispatch(LocalityActions.selectCity(city, state));
                let outlet = {label: unitData.name, value: unitData.id};
                dispatch(LocalityActions.selectOutlet(outlet));
                dispatch(LocalityActions.setShowOutlet(true));
                window.location.href = window.location.origin + "/menu";
                //dispatch(getUnitProducts("DINE_IN",city,null, outlet));
            }
        }).catch(function (error) {
            console.log(error);
        });
    }
}

export function loadTakeawayMenu(unitId) {
    return dispatch => {
        dispatch(LocalityActions.setCriteria("TAKE_AWAY"));
        var authDetail = StorageUtils.getAuthDetail();
        if (!appUtil.checkEmpty(authDetail)) {
            dispatch(CustomerActions.setDeviceKey(authDetail.deviceKey));
            dispatch(CustomerActions.setSessionKey(authDetail.sessionKey));
        }
        let url;
        let method;
        let reqData;
        url = apis.getUrls().unitCache.getUnit + "?unitId=" + unitId;
        method = "GET";
        reqData = {};
        dispatch({type: "LOAD_UNIT_PRODUCTS_PENDING"});
        axios({
            method: method,
            url: url,
            data: reqData,
            headers: {"Content-Type": "application/json"}
        }).then(function (response) {
            if (!appUtil.checkEmpty(response.data) && response.status == 200) {
                let unitData = response.data.unit;
                StorageUtils.setUnitDetails({id: unitData.id, name: unitData.name});
                //console.log(unitData.location);
                let city = unitData.location.code;
                let state = unitData.location.state.code;
                dispatch(LocalityActions.loadOutletsList(city));
                dispatch(LocalityActions.selectCity(city, state));
                let outlet = {label: unitData.name, value: unitData.id};
                dispatch(LocalityActions.selectOutlet(outlet));
                dispatch(LocalityActions.setShowOutlet(true));
                window.location.href = window.location.origin + "/menu";
                //dispatch(getUnitProducts("DINE_IN",city,null, outlet));
            }
        }).catch(function (error) {
            console.log(error);
        });
    }
}

export function getProductOffers(unitId) {
    //console.log("UNIT ID getProductOffers() " , unitId);
    return dispatch => {
        axios({
            method: "POST",
            url: apis.getUrls().offer.getOfferDetails + "?unitId=" + unitId,
            data: JSON.stringify({}),
            headers: {'Content-Type': 'application/json'}
        }).then(function (response) {
            //console.log(response.data);
            //console.log(JSON.stringify('Offer details received::: '+ response.data));
            let productIdOfferMap = null;
            if (!appUtil.checkEmpty(response.data)) {
                productIdOfferMap = new Map();
                response.data.map((product) => {
                    productIdOfferMap.set(product.productId, product);
                });
            }
            dispatch({type: "SET_OFFER_DETAILS", payload: productIdOfferMap});
        }).catch(function (error) {
            console.log('Could not fetch offer details: ', error);
        });
    }
}

export function getUnitProductsForSignUpOffer(unitId) {
    return dispatch => {
        if (unitId != null) {
            let url;
            let method;
            let reqData;
            url = apis.getUrls().unitCache.getUnit + "?unitId=" + unitId;
            method = "GET";
            reqData = {};
            axios({
                method: method,
                url: url,
                data: reqData,
                headers: {"Content-Type": "application/json"}
            }).then(function (response) {
                if (!appUtil.checkEmpty(response.data) && response.status === 200) {
                    let unitData = response.data.unit;
                    StorageUtils.setUnitDetails({id: unitData.id, name: unitData.name});
                    dispatch({type: "LOAD_UNIT_PRODUCTS_FULFILLED", payload: unitData});
                    /*dispatch(initAutoRecipeLoad(unitData));*/
                    if (unitData != null && unitData.products != null && unitData.products.length > 0) {
                        unitData.products.map((product) => {
                            if (product != null && product.id === 10) {
                                console.log("product:: " + JSON.stringify(product));
                                dispatch({type: 'SET_SIGN_UP_OFFER_PRODUCT', payload: product});
                            }
                        });
                    }
                    if (response.data.error && response.data.error > 0) {
                        dispatch(UtilityActions.showPopup("No product found for sign up offer!"));
                    }
                }
            }).catch(function (error) {
                console.log(error);
                dispatch(UtilityActions.showPopup("No product found for sign up offer!"));
            });
        } else {
            dispatch(UtilityActions.showPopup("No product found for sign up offer!"));
        }
    }
}
