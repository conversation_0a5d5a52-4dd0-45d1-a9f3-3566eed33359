/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.notification.dao.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.master.core.external.notification.dao.ExternalUserManagementDao;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.EmployeeDetail;

@Repository
public class ExternalUserManagementDaoImpl extends AbstractMasterDaoImpl implements ExternalUserManagementDao {

	@Override
	public List<String> getAllInternalOpsEmployeeContactNumbers() {

		Set<String> contacts = new HashSet<>();
		Query query = manager.createQuery(
				"FROM EmployeeDetail l where l.designation.designationId NOT IN (1007, 1530, 1534) and l.department.deptId = 101 and l.employmentStatus = 'ACTIVE'");
		List<EmployeeDetail> list = query.getResultList();
		if (list != null && list.size() > 0) {
			for (EmployeeDetail data : list) {
				String contactNumber1 = data.getEmpContactNum1();
				String contactNumber2 = data.getEmpContactNum2();
				String contactNumber3 = data.getSdpContact();
				if (contactNumber1 != null && contactNumber1.trim().length() > 0) {
					contacts.add(contactNumber1);
				}
				if (contactNumber2 != null && contactNumber2.trim().length() > 0) {
					contacts.add(contactNumber2);
				}
				if (contactNumber3 != null && contactNumber3.trim().length() > 0) {
					contacts.add(contactNumber3);
				}
			}
		}
		return new ArrayList<>(contacts);
	}
}
