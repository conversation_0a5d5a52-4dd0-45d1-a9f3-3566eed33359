package com.stpl.tech.analytics.data.dao;

import com.stpl.tech.kettle.data.model.OrderDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderDetailDao extends JpaRepository<OrderDetail,Integer> {

    @Query("SELECT o FROM OrderDetail  o JOIN FETCH o.orderItems oi WHERE (o.linkedOrderId = ?1 AND o.orderType LIKE ?2 ) OR o.orderId = ?1  ")
    public List<OrderDetail> getRemakeChaiOrders(Integer orderId,String orderType);

    @Query("SELECT o FROM OrderDetail  o JOIN FETCH o.orderItems oi WHERE o.orderId = ?1  ")
    public List<OrderDetail> getOrder(Integer orderId);

}
