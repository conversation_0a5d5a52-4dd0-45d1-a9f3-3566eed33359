package com.stpl.tech.kettle.data.crypto;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.stpl.tech.spring.crypto.DataEncrypterFailureHandler;

import lombok.extern.slf4j.Slf4j;

@Service("dataEncrypterFailureHandler")
@Slf4j
public class DataEncrypterFailureHandlerImpl implements DataEncrypterFailureHandler {

	@PersistenceContext(unitName = "TransactionDataSourcePUName")
	@Qualifier(value = "TransactionDataSourceEMFactory")
	private EntityManager entityManager;

	private static final String QUERY_STRING = "SELECT var.%s FROM CustomerDataLookup var WHERE var.%s = :value AND var.type = :type ";

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public String fetchData(String filter, String value, String attribute, String type) {
		Query query = entityManager.createQuery(String.format(QUERY_STRING, attribute, filter), String.class);
		query.setParameter("value", value);
		query.setParameter("type", type);
		List<String> results = query.getResultList();
		if (CollectionUtils.isEmpty(results)) {
			log.error("unable to lookup encrypted data for value {}", value);
			return value;// throw error
		}
		return results.get(0);
	}

}
