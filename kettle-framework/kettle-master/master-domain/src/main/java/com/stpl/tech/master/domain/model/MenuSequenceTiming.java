package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MenuSequenceTiming implements Serializable {
    private static final long serialVersionUID = -6203652993620214459L;
    private String service;
    private String time1From;
    private String time1To;
    private String time2From;
    private String time2To;
    private String time3From;
    private String time3To;
    private boolean dayMonday;
    private boolean dayTuesday;
    private boolean dayWednesday;
    private boolean dayThursday;
    private boolean dayFriday;
    private boolean daySaturday;
    private boolean daySunday;
    private String startDate;

    private String endDate;

    private String daySlots;

}
