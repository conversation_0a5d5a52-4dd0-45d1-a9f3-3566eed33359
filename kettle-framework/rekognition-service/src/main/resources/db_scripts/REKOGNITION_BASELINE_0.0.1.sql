CREATE SCHEMA KETTLE_REKOGNITION_DEV;

DROP TABLE IF EXISTS KETTLE_REKOGNITION_DEV.LOOKUP_IMAGE_MAPPING_DATA;
CREATE TABLE KETTLE_REKOGNITION_DEV.LOOKUP_IMAGE_MAPPING_DATA(
  `IMAGE_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `SUBJECT_NAME` varchar(100) DEFAULT NULL,
  `CONTACT_NUMBER` varchar(15) DEFAULT NULL,
  `FACE_ID` varchar(100) DEFAULT NULL,
  `FILE_NAME` varchar(150) DEFAULT NULL,
  `FILE_PATH` varchar(500) DEFAULT NULL,
  `FACE_METADATA_ID` varchar(100) DEFAULT NULL,
  `QUALITY` decimal(10,2) DEFAULT NULL,
  `BATCH_ID` varchar(100) DEFAULT NULL,
  `CREATION_TIME` timestamp NULL DEFAULT NULL,
  `LAST_UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `MAPPED_FACE_ID` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`IMAGE_MAPPING_ID`)
);


DROP TABLE IF EXISTS KETTLE_REKOGNITION_DEV.SIGNUP_IMAGE_MAPPING_DATA;
CREATE TABLE KETTLE_REKOGNITION_DEV.SIGNUP_IMAGE_MAPPING_DATA (
  `IMAGE_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `SUBJECT_NAME` varchar(100) NOT NULL,
  `CONTACT_NUMBER` varchar(15) NOT NULL,
  `FACE_ID` varchar(100) DEFAULT NULL,
  `FILE_NAME` varchar(150) DEFAULT NULL,
  `FILE_PATH` varchar(500) DEFAULT NULL,
  `FACE_METADATA_ID` varchar(100) DEFAULT NULL,
  `QUALITY` decimal(10,2) DEFAULT NULL,
  `BATCH_ID` varchar(100) DEFAULT NULL,
  `CREATION_TIME` timestamp NULL DEFAULT NULL,
  `LAST_UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `MAPPED_FACE_ID` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`IMAGE_MAPPING_ID`)
);


ALTER TABLE KETTLE_REKOGNITION_DEV.SIGNUP_IMAGE_MAPPING_DATA ADD COLUMN SESSION_ID VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.LOOKUP_IMAGE_MAPPING_DATA ADD COLUMN SESSION_ID VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.IMAGE_MAPPING_DATA ADD COLUMN SESSION_ID VARCHAR(50) NULL;


DROP TABLE IF EXISTS KETTLE_REKOGNITION_DEV.ORIGINAL_IMAGE_MAPPING_DATA;
CREATE TABLE KETTLE_REKOGNITION_DEV.ORIGINAL_IMAGE_MAPPING_DATA (
  IMAGE_MAPPING_ID int(11) NOT NULL AUTO_INCREMENT,
  FILE_NAME varchar(150) DEFAULT NULL,
  FILE_PATH varchar(500) DEFAULT NULL,
  FACE_METADATA_ID varchar(100) DEFAULT NULL,
  CREATION_TIME timestamp NULL DEFAULT NULL,
  LAST_UPDATE_TIME timestamp NULL DEFAULT NULL,
  SESSION_ID varchar(50) DEFAULT NULL,
  PRIMARY KEY (`IMAGE_MAPPING_ID`)
);


DROP TABLE IF EXISTS KETTLE_REKOGNITION_DEV.APP_VERSION_DATA;
CREATE TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_DATA (
  VERSION_DATA_ID int(11) NOT NULL AUTO_INCREMENT,
  FILE_NAME varchar(150) DEFAULT NULL,
  FILE_PATH varchar(500) DEFAULT NULL,
  APP_VERSION varchar(100) DEFAULT NULL,
  APP_TYPE varchar(100) DEFAULT NULL,
  CREATION_TIME timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`VERSION_DATA_ID`)
);

DROP TABLE IF EXISTS KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA;
CREATE TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA (
  LOG_ID INTEGER NOT NULL AUTO_INCREMENT,
  APP_VERSION_MAPPING_ID INTEGER NOT NULL,
  UNIT_ID INTEGER NOT NULL,
  TERMINAL_ID INTEGER NOT NULL,
  APP_VERSION varchar(100) DEFAULT NULL,
  APP_TYPE varchar(100) DEFAULT NULL,
  LAST_UPDATE_TIME timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`LOG_ID`)
);


DROP TABLE IF EXISTS KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA;
CREATE TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA (
  APP_VERSION_MAPPING_ID INTEGER NOT NULL AUTO_INCREMENT,
  UNIT_ID INTEGER NOT NULL,
  TERMINAL_ID INTEGER NOT NULL,
  APP_VERSION varchar(100) DEFAULT NULL,
  APP_TYPE varchar(100) DEFAULT NULL,
  LAST_UPDATE_TIME timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`APP_VERSION_MAPPING_ID`)
);




