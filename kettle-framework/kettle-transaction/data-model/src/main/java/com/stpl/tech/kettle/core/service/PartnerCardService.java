package com.stpl.tech.kettle.core.service;

import java.util.List;

import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.data.model.ExternalPartnerCardDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.data.model.ExternalPartnerDetail;

public interface PartnerCardService {
	
	public List<ExternalPartnerCardDetail> getPartnerCardDetail(String cardNumber, String partnerCode, String status);
	
	public List<String> verifyVoucher(Order order, boolean consume) throws CardValidationException;
	
	public void updateVoucher(String voucherCode, String cardNumber, String partnerCode);
	
	public ExternalPartnerDetail getExternalPartner(String partnerCode);

	public String getUniquePartnerBillNumber();
	
}
