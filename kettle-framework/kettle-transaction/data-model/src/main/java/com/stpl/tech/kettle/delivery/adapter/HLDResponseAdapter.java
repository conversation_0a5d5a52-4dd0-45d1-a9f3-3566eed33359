/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.adapter;

import com.stpl.tech.kettle.delivery.model.*;
import com.stpl.tech.util.AppUtils;

public class HLDResponseAdapter
		implements ResponseAdapter<HLDResponse, HLDErrorResponse, HLDCallbackObject, DeliveryResponse> {

	private static final Integer PARTNER_ID = 6;

	@Override
	public DeliveryResponse adapt(HLDResponse data, String orderId) {

		if (data.getError() != null || data.getErrorMessage() != null) {
			if (data.getErrorMessage() != null) {
				HLDErrorResponse error = new HLDErrorResponse();
				error.setMsg(data.getErrorMessage());
				data.setError(error);
				return adaptError(data.getError(), orderId);
			}
			return adaptError(data.getError(), orderId);

		} else {
			DeliveryResponse deliveryResponse = new DeliveryResponse();
			deliveryResponse.setDeliveryPartnerId(PARTNER_ID);
			deliveryResponse.setDeliveryTaskId(data.getTask_Id());
			deliveryResponse.setDeliveryBoyName(data.getDelivery_Boy_Name());
			deliveryResponse.setDeliveryBoyPhoneNum(data.getDelivery_Boy_Phone());
			deliveryResponse.setDeliveryStatus(Integer.parseInt(data.getStatus()));
			deliveryResponse.setStatusUpdateTime(AppUtils.getCurrentTimestamp());
			deliveryResponse.setGeneratedOrderId(orderId);
			return deliveryResponse;
		}
	}

	@Override
	public DeliveryResponse adaptError(HLDErrorResponse data, String orderId) {
		DeliveryResponse deliveryResponse = new DeliveryResponse();
		if (data != null) {
			deliveryResponse.setFailureCode(data.getCode());
			deliveryResponse.setFailureMessage(data.getMsg());
		}
		deliveryResponse.setDeliveryPartnerId(PARTNER_ID);
		deliveryResponse.setGeneratedOrderId(orderId);
		deliveryResponse.setDeliveryStatus(DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus());
		return deliveryResponse;
	}

	@Override
	public DeliveryResponse adaptCallback(HLDCallbackObject data) {
		DeliveryResponse deliveryResponse = new DeliveryResponse();
		deliveryResponse.setDeliveryBoyName(data.getDelivery_Boy_Name());
		deliveryResponse.setDeliveryBoyPhoneNum(data.getDelivery_Boy_Phone());
		deliveryResponse.setDeliveryPartnerId(PARTNER_ID);
		deliveryResponse.setDeliveryStatus(Integer.parseInt(data.getStatus()));
		deliveryResponse.setStatusUpdateTime(AppUtils.parseDate(data.getTime()));
		deliveryResponse.setDeliveryTaskId(data.getTask_Id());
		return deliveryResponse;
	}

	@Override
	public DeliveryResponse adaptCancel(String orderId, String taskId) {
		return null;
	}

}
