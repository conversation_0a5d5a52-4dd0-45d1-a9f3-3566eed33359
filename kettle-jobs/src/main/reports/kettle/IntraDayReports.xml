<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Intra Day Sales Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails=""
                        schedule="">
			<reports>
				<report id="1" name="All Regions" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    A.BIZ_DATE,
    'Overall' AS TOTAL,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) LWSD_DINE_IN_TICKETS,
    TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES - A.GIFT_CARD_AMOUNT) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
        0) NET_DINE_IN_APC,
    TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_GIFT_CARD_AMOUNT) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS),
        0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - B.LWSD_NET_DELIVERY_SALES) LWSD_NET_DINE_IN_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES,
    A.DONATION,
    B.LWSD_DONATION,A.GIFT_BOX_QUANTITY,B.LWSD_GIFT_BOX_QUANTITY
FROM
    (SELECT 
        m.BIZ_DATE,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION,
            COALESCE(n.DONATION, 0) DONATION,  COALESCE(n.GIFT_BOX_QUANTITY, 0) GIFT_BOX_QUANTITY
    FROM
        (SELECT 
        od.BIZ_DATE,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_APC,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) od) m
    LEFT OUTER JOIN (SELECT 
        qa.BIZ_DATE,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) qa
    GROUP BY qa.BIZ_DATE) j ON j.BIZ_DATE = m.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY BIZ_DATE) n ON m.BIZ_DATE = n.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        a.BIZ_DATE,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) a
    INNER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY BIZ_DATE) b ON a.BIZ_DATE = b.BIZ_DATE) p ON m.BIZ_DATE = p.BIZ_DATE) A
        LEFT OUTER JOIN
    (SELECT 
        m.BIZ_DATE,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.DONATION, 0) LWSD_DONATION, COALESCE(n.GIFT_BOX_QUANTITY, 0) LWSD_GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) m
    LEFT OUTER JOIN (SELECT 
        qa.BIZ_DATE,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.BIZ_DATE) j ON j.BIZ_DATE = m.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026, 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY BIZ_DATE) n ON m.BIZ_DATE = n.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        a.BIZ_DATE,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) a
    INNER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY BIZ_DATE) b ON a.BIZ_DATE = b.BIZ_DATE) p ON m.BIZ_DATE = p.BIZ_DATE) B ON A.BIZ_DATE = B.BIZ_DATE
UNION ALL
SELECT 
    A.BIZ_DATE,
    'Current Cycle' AS TOTAL,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) LWSD_DINE_IN_TICKETS,
    TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES - A.GIFT_CARD_AMOUNT) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
        0) NET_DINE_IN_APC,
    TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_GIFT_CARD_AMOUNT) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS),
        0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - B.LWSD_NET_DELIVERY_SALES) LWSD_NET_DINE_IN_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES,
    A.DONATION,
    B.LWSD_DONATION,A.GIFT_BOX_QUANTITY,B.LWSD_GIFT_BOX_QUANTITY
FROM
    (SELECT 
        m.BIZ_DATE,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION,
            COALESCE(n.DONATION, 0) DONATION,  COALESCE(n.GIFT_BOX_QUANTITY, 0) GIFT_BOX_QUANTITY
    FROM
        (SELECT 
        od.BIZ_DATE,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_APC,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) od) m
    LEFT OUTER JOIN (SELECT 
        qa.BIZ_DATE,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) qa
    GROUP BY qa.BIZ_DATE) j ON j.BIZ_DATE = m.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY BIZ_DATE) n ON m.BIZ_DATE = n.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        a.BIZ_DATE,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
					AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) a
    INNER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY BIZ_DATE) b ON a.BIZ_DATE = b.BIZ_DATE) p ON m.BIZ_DATE = p.BIZ_DATE) A
        LEFT OUTER JOIN
    (SELECT 
        m.BIZ_DATE,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.DONATION, 0) LWSD_DONATION, COALESCE(n.GIFT_BOX_QUANTITY, 0) LWSD_GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) m
    LEFT OUTER JOIN (SELECT 
        qa.BIZ_DATE,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.BIZ_DATE) j ON j.BIZ_DATE = m.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026, 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY BIZ_DATE) n ON m.BIZ_DATE = n.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        a.BIZ_DATE,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
					AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) a
    INNER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY BIZ_DATE) b ON a.BIZ_DATE = b.BIZ_DATE) p ON m.BIZ_DATE = p.BIZ_DATE) B ON A.BIZ_DATE = B.BIZ_DATE

UNION ALL
SELECT 
    A.BIZ_DATE,
    'New Cafes' AS TOTAL,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) LWSD_DINE_IN_TICKETS,
    TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES - A.GIFT_CARD_AMOUNT) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
        0) NET_DINE_IN_APC,
    TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_GIFT_CARD_AMOUNT) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS),
        0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - B.LWSD_NET_DELIVERY_SALES) LWSD_NET_DINE_IN_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES,
    A.DONATION,
    B.LWSD_DONATION,A.GIFT_BOX_QUANTITY,B.LWSD_GIFT_BOX_QUANTITY
FROM
    (SELECT 
        m.BIZ_DATE,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION,
            COALESCE(n.DONATION, 0) DONATION,  COALESCE(n.GIFT_BOX_QUANTITY, 0) GIFT_BOX_QUANTITY
    FROM
        (SELECT 
        od.BIZ_DATE,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_APC,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) od) m
    LEFT OUTER JOIN (SELECT 
        qa.BIZ_DATE,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) qa
    GROUP BY qa.BIZ_DATE) j ON j.BIZ_DATE = m.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY BIZ_DATE) n ON m.BIZ_DATE = n.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        a.BIZ_DATE,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
					AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) a
    INNER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY BIZ_DATE) b ON a.BIZ_DATE = b.BIZ_DATE) p ON m.BIZ_DATE = p.BIZ_DATE) A
        LEFT OUTER JOIN
    (SELECT 
        m.BIZ_DATE,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.DONATION, 0) LWSD_DONATION, COALESCE(n.GIFT_BOX_QUANTITY, 0) LWSD_GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) m
    LEFT OUTER JOIN (SELECT 
        qa.BIZ_DATE,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.BIZ_DATE) j ON j.BIZ_DATE = m.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026, 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY BIZ_DATE) n ON m.BIZ_DATE = n.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        a.BIZ_DATE,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
					AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) a
    INNER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY BIZ_DATE) b ON a.BIZ_DATE = b.BIZ_DATE) p ON m.BIZ_DATE = p.BIZ_DATE) B ON A.BIZ_DATE = B.BIZ_DATE



        ]]>
					</content>

				</report>
				<report id="1" name="Regional" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
	'Regional Overall',
    A.BIZ_DATE,
    A.UNIT_REGION_INFO,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) LWSD_DINE_IN_TICKETS,
    TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES - A.GIFT_CARD_AMOUNT) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
        0) NET_DINE_IN_APC,
    TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_GIFT_CARD_AMOUNT) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS),
        0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - B.LWSD_NET_DELIVERY_SALES) LWSD_NET_DINE_IN_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES,
    A.DONATION,
    B.LWSD_DONATION,A.GIFT_BOX_QUANTITY,B.LWSD_GIFT_BOX_QUANTITY
FROM
    (SELECT 
        m.BIZ_DATE,
            m.UNIT_REGION_INFO,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.DONATION, 0) DONATION,  COALESCE(n.GIFT_BOX_QUANTITY, 0) GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        od.BIZ_DATE,
            od.UNIT_REGION_INFO,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
                    CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,

            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY UNIT_REGION_INFO) od) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_REGION_INFO,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
                CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,

            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) qa
    GROUP BY qa.UNIT_REGION_INFO) j ON j.UNIT_REGION_INFO = m.UNIT_REGION_INFO
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION_INFO,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
                    CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,

            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , UNIT_REGION_INFO) a
    GROUP BY a.UNIT_REGION_INFO) n ON m.UNIT_REGION_INFO = n.UNIT_REGION_INFO
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION_INFO,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
                CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,

            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY UNIT_REGION_INFO) a
    INNER JOIN (SELECT 
                CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY UNIT_REGION_INFO) b ON a.UNIT_REGION_INFO = b.UNIT_REGION_INFO) p ON m.UNIT_REGION_INFO = p.UNIT_REGION_INFO) A
        LEFT OUTER JOIN
    (SELECT 
        m.BIZ_DATE,
            m.UNIT_REGION_INFO,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION,
            COALESCE(n.DONATION, 0) LWSD_DONATION, COALESCE(n.GIFT_BOX_QUANTITY, 0) LWSD_GIFT_BOX_QUANTITY
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        od.UNIT_ID = ud.UNIT_ID
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY UNIT_REGION_INFO) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_REGION_INFO,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.UNIT_REGION_INFO) j ON j.UNIT_REGION_INFO = m.UNIT_REGION_INFO
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION_INFO,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , UNIT_REGION_INFO) a
    GROUP BY a.UNIT_REGION_INFO) n ON m.UNIT_REGION_INFO = n.UNIT_REGION_INFO
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION_INFO,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY UNIT_REGION_INFO) a
    INNER JOIN (SELECT 
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY UNIT_REGION_INFO) b ON a.UNIT_REGION_INFO = b.UNIT_REGION_INFO) p ON m.UNIT_REGION_INFO = p.UNIT_REGION_INFO) B ON A.BIZ_DATE = B.BIZ_DATE
        AND A.UNIT_REGION_INFO = B.UNIT_REGION_INFO
GROUP BY A.UNIT_REGION_INFO

UNION ALL


SELECT 
	'Regional Current Cycle',
    A.BIZ_DATE,
    A.UNIT_REGION_INFO,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) LWSD_DINE_IN_TICKETS,
    TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES - A.GIFT_CARD_AMOUNT) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
        0) NET_DINE_IN_APC,
    TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_GIFT_CARD_AMOUNT) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS),
        0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - B.LWSD_NET_DELIVERY_SALES) LWSD_NET_DINE_IN_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES,
    A.DONATION,
    B.LWSD_DONATION,A.GIFT_BOX_QUANTITY,B.LWSD_GIFT_BOX_QUANTITY
FROM
    (SELECT 
        m.BIZ_DATE,
            m.UNIT_REGION_INFO,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.DONATION, 0) DONATION,  COALESCE(n.GIFT_BOX_QUANTITY, 0) GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        od.BIZ_DATE,
            od.UNIT_REGION_INFO,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
                    CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,

            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY UNIT_REGION_INFO) od) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_REGION_INFO,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
                CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,

            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) qa
    GROUP BY qa.UNIT_REGION_INFO) j ON j.UNIT_REGION_INFO = m.UNIT_REGION_INFO
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION_INFO,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
                    CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,

            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , UNIT_REGION_INFO) a
    GROUP BY a.UNIT_REGION_INFO) n ON m.UNIT_REGION_INFO = n.UNIT_REGION_INFO
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION_INFO,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
                CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,

            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
					AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY UNIT_REGION_INFO) a
    INNER JOIN (SELECT 
                CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY UNIT_REGION_INFO) b ON a.UNIT_REGION_INFO = b.UNIT_REGION_INFO) p ON m.UNIT_REGION_INFO = p.UNIT_REGION_INFO) A
        LEFT OUTER JOIN
    (SELECT 
        m.BIZ_DATE,
            m.UNIT_REGION_INFO,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION,
            COALESCE(n.DONATION, 0) LWSD_DONATION, COALESCE(n.GIFT_BOX_QUANTITY, 0) LWSD_GIFT_BOX_QUANTITY
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        od.UNIT_ID = ud.UNIT_ID
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY UNIT_REGION_INFO) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_REGION_INFO,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.UNIT_REGION_INFO) j ON j.UNIT_REGION_INFO = m.UNIT_REGION_INFO
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION_INFO,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , UNIT_REGION_INFO) a
    GROUP BY a.UNIT_REGION_INFO) n ON m.UNIT_REGION_INFO = n.UNIT_REGION_INFO
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION_INFO,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
					AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY UNIT_REGION_INFO) a
    INNER JOIN (SELECT 
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY UNIT_REGION_INFO) b ON a.UNIT_REGION_INFO = b.UNIT_REGION_INFO) p ON m.UNIT_REGION_INFO = p.UNIT_REGION_INFO) B ON A.BIZ_DATE = B.BIZ_DATE
        AND A.UNIT_REGION_INFO = B.UNIT_REGION_INFO
GROUP BY A.UNIT_REGION_INFO

UNION ALL


SELECT 
	'Regional New Cafe',
    A.BIZ_DATE,
    A.UNIT_REGION_INFO,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) LWSD_DINE_IN_TICKETS,
    TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES - A.GIFT_CARD_AMOUNT) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
        0) NET_DINE_IN_APC,
    TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_GIFT_CARD_AMOUNT) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS),
        0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - B.LWSD_NET_DELIVERY_SALES) LWSD_NET_DINE_IN_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES,
    A.DONATION,
    B.LWSD_DONATION,A.GIFT_BOX_QUANTITY,B.LWSD_GIFT_BOX_QUANTITY
FROM
    (SELECT 
        m.BIZ_DATE,
            m.UNIT_REGION_INFO,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.DONATION, 0) DONATION,  COALESCE(n.GIFT_BOX_QUANTITY, 0) GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        od.BIZ_DATE,
            od.UNIT_REGION_INFO,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
                    CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,

            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY UNIT_REGION_INFO) od) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_REGION_INFO,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
                CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,

            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) qa
    GROUP BY qa.UNIT_REGION_INFO) j ON j.UNIT_REGION_INFO = m.UNIT_REGION_INFO
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION_INFO,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
                    CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,

            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , UNIT_REGION_INFO) a
    GROUP BY a.UNIT_REGION_INFO) n ON m.UNIT_REGION_INFO = n.UNIT_REGION_INFO
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION_INFO,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
                CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,

            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
					AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY UNIT_REGION_INFO) a
    INNER JOIN (SELECT 
                CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY UNIT_REGION_INFO) b ON a.UNIT_REGION_INFO = b.UNIT_REGION_INFO) p ON m.UNIT_REGION_INFO = p.UNIT_REGION_INFO) A
        LEFT OUTER JOIN
    (SELECT 
        m.BIZ_DATE,
            m.UNIT_REGION_INFO,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION,
            COALESCE(n.DONATION, 0) LWSD_DONATION, COALESCE(n.GIFT_BOX_QUANTITY, 0) LWSD_GIFT_BOX_QUANTITY
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        od.UNIT_ID = ud.UNIT_ID
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY UNIT_REGION_INFO) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_REGION_INFO,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.UNIT_REGION_INFO) j ON j.UNIT_REGION_INFO = m.UNIT_REGION_INFO
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION_INFO,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , UNIT_REGION_INFO) a
    GROUP BY a.UNIT_REGION_INFO) n ON m.UNIT_REGION_INFO = n.UNIT_REGION_INFO
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION_INFO,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
					AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY UNIT_REGION_INFO) a
    INNER JOIN (SELECT 
        CASE WHEN ud.UNIT_REGION IN ('CHANDIGARH','NCR','NCR_EDU') THEN 'NORTH' WHEN ud.UNIT_REGION IN ('MUMBAI') THEN 'WEST' WHEN ud.UNIT_REGION IN ('BANGALORE') THEN 'SOUTH' END UNIT_REGION_INFO, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID NOT IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY UNIT_REGION_INFO) b ON a.UNIT_REGION_INFO = b.UNIT_REGION_INFO) p ON m.UNIT_REGION_INFO = p.UNIT_REGION_INFO) B ON A.BIZ_DATE = B.BIZ_DATE
        AND A.UNIT_REGION_INFO = B.UNIT_REGION_INFO
GROUP BY A.UNIT_REGION_INFO


        ]]>
					</content>
				</report>
				<report id="1" name="Area Managers" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    A.BIZ_DATE,
    UCASE(ed.EMP_NAME) AREA_MANAGER,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) LWSD_DINE_IN_TICKETS,
    TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES - A.GIFT_CARD_AMOUNT) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
        0) NET_DINE_IN_APC,
    TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_GIFT_CARD_AMOUNT) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS),
        0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - B.LWSD_NET_DELIVERY_SALES) LWSD_NET_DINE_IN_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES,
    A.DONATION,
    B.LWSD_DONATION,A.GIFT_BOX_QUANTITY,B.LWSD_GIFT_BOX_QUANTITY
FROM
    (SELECT 
        m.BIZ_DATE,
            m.UNIT_MANAGER,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.DONATION, 0) DONATION,  COALESCE(n.GIFT_BOX_QUANTITY, 0) GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        od.BIZ_DATE,
            od.UNIT_MANAGER,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            ud.UNIT_MANAGER,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY ud.UNIT_MANAGER) od) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_MANAGER,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_MANAGER,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) qa
    GROUP BY qa.UNIT_MANAGER) j ON j.UNIT_MANAGER = m.UNIT_MANAGER
    LEFT OUTER JOIN (SELECT 
        a.UNIT_MANAGER,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_MANAGER,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026, 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , ud.UNIT_MANAGER) a
    GROUP BY a.UNIT_MANAGER) n ON m.UNIT_MANAGER = n.UNIT_MANAGER
    LEFT OUTER JOIN (SELECT 
        a.UNIT_MANAGER,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        ud.UNIT_MANAGER,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
					AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY ud.UNIT_MANAGER) a
    INNER JOIN (SELECT 
        ud.UNIT_MANAGER, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY ud.UNIT_MANAGER) b ON a.UNIT_MANAGER = b.UNIT_MANAGER) p ON m.UNIT_MANAGER = p.UNIT_MANAGER) A
        LEFT OUTER JOIN
    (SELECT 
        m.BIZ_DATE,
            m.UNIT_MANAGER,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.DONATION, 0) LWSD_DONATION, COALESCE(n.GIFT_BOX_QUANTITY, 0) LWSD_GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            ud.UNIT_MANAGER,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        od.UNIT_ID = ud.UNIT_ID
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY ud.UNIT_MANAGER) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_MANAGER,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_MANAGER,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.UNIT_MANAGER) j ON j.UNIT_MANAGER = m.UNIT_MANAGER
    LEFT OUTER JOIN (SELECT 
        a.UNIT_MANAGER,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_MANAGER,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158, 1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291, 1026, 1027, 1048, 1157, 1158, 1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158, 1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , ud.UNIT_MANAGER) a
    GROUP BY a.UNIT_MANAGER) n ON m.UNIT_MANAGER = n.UNIT_MANAGER
    LEFT OUTER JOIN (SELECT 
        a.UNIT_MANAGER,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        ud.UNIT_MANAGER,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
					AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY ud.UNIT_MANAGER) a
    INNER JOIN (SELECT 
        ud.UNIT_MANAGER, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY ud.UNIT_MANAGER) b ON a.UNIT_MANAGER = b.UNIT_MANAGER) p ON m.UNIT_MANAGER = p.UNIT_MANAGER) B ON A.BIZ_DATE = B.BIZ_DATE
        AND A.UNIT_MANAGER = B.UNIT_MANAGER
        INNER JOIN
    KETTLE_MASTER.EMPLOYEE_DETAIL ed ON A.UNIT_MANAGER = ed.EMP_ID
GROUP BY A.UNIT_MANAGER
ORDER BY AREA_MANAGER


        ]]>
					</content>
				</report>
				<report id="1" name="Deputy Area Managers" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    A.BIZ_DATE,
    UCASE(ed.EMP_NAME) DEPUTY_AREA_MANAGER,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) LWSD_DINE_IN_TICKETS,
    TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES - A.GIFT_CARD_AMOUNT) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
        0) NET_DINE_IN_APC,
    TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_GIFT_CARD_AMOUNT) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS),
        0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - B.LWSD_NET_DELIVERY_SALES) LWSD_NET_DINE_IN_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES,
    A.DONATION,
    B.LWSD_DONATION,A.GIFT_BOX_QUANTITY,B.LWSD_GIFT_BOX_QUANTITY
FROM
    (SELECT 
        m.BIZ_DATE,
            m.EMPLOYEE_ID,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.DONATION, 0) DONATION,  COALESCE(n.GIFT_BOX_QUANTITY, 0) GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        od.BIZ_DATE,
            od.EMPLOYEE_ID,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            ud.CAFE_MANAGER EMPLOYEE_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY ud.CAFE_MANAGER) od) m
    LEFT OUTER JOIN (SELECT 
        qa.EMPLOYEE_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        ud.CAFE_MANAGER EMPLOYEE_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) qa
    GROUP BY qa.EMPLOYEE_ID) j ON j.EMPLOYEE_ID = m.EMPLOYEE_ID
    LEFT OUTER JOIN (SELECT 
        a.EMPLOYEE_ID,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            ud.CAFE_MANAGER EMPLOYEE_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158,1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291 , 1026, 1027, 1048, 1157, 1158,1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158,1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , ud.CAFE_MANAGER) a
    GROUP BY a.EMPLOYEE_ID) n ON m.EMPLOYEE_ID = n.EMPLOYEE_ID
    LEFT OUTER JOIN (SELECT 
        a.EMPLOYEE_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        ud.CAFE_MANAGER EMPLOYEE_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
             AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
           AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
					AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
					AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY ud.CAFE_MANAGER) a
    INNER JOIN (SELECT 
        ud.CAFE_MANAGER EMPLOYEE_ID,
            SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
             AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
           AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY ud.CAFE_MANAGER) b ON a.EMPLOYEE_ID = b.EMPLOYEE_ID) p ON m.EMPLOYEE_ID = p.EMPLOYEE_ID) A
        LEFT OUTER JOIN
    (SELECT 
        m.BIZ_DATE,
            m.EMPLOYEE_ID,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.DONATION, 0) LWSD_DONATION, COALESCE(n.GIFT_BOX_QUANTITY, 0) LWSD_GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            ud.CAFE_MANAGER EMPLOYEE_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od, KETTLE_MASTER.UNIT_DETAIL ud
    WHERE
        od.UNIT_ID = ud.UNIT_ID
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
             AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
           AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY ud.CAFE_MANAGER) m
    LEFT OUTER JOIN (SELECT 
        qa.EMPLOYEE_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        ud.CAFE_MANAGER EMPLOYEE_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
             AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
           AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.EMPLOYEE_ID) j ON j.EMPLOYEE_ID = m.EMPLOYEE_ID
    LEFT OUTER JOIN (SELECT 
        a.EMPLOYEE_ID,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            ud.CAFE_MANAGER EMPLOYEE_ID,
            CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158,1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END GIFT_CARD_AMOUNT,
            CASE
                WHEN oi.PRODUCT_ID NOT IN (1291 , 1026, 1027, 1048, 1157, 1158,1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END MERCHANDISE_SALES,
            CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END DONATION,
            CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
             AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
           AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158,1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , ud.CAFE_MANAGER) a
    GROUP BY a.EMPLOYEE_ID) n ON m.EMPLOYEE_ID = n.EMPLOYEE_ID
    LEFT OUTER JOIN (SELECT 
        a.EMPLOYEE_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        ud.CAFE_MANAGER EMPLOYEE_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
             AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
           AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
					AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
                   AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY ud.CAFE_MANAGER) a
    INNER JOIN (SELECT 
        ud.CAFE_MANAGER EMPLOYEE_ID,
            SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        AND ud.CAFE_MANAGER <> ud.UNIT_MANAGER
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID NOT IN (26007 , 26008, 26009, 26010, 26011)
            AND od.UNIT_ID IN (10000,10001,10002,10003,10005,10006,10009,10013,12011,12014,12015,12018,12019,12020,12028,12030,12031,26005,26012,26015,26016,26018,26019,26021,26022,26023,26025,26027,26028,26029,26034,26036,26037,26038,26039,26043,26044,26048,26052,26055,26056,26058,26059,26060,26066,26071,26076,26081,26084,26087,26090,26091,26093,26094,26095,26104,26105,26106)
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY ud.CAFE_MANAGER) b ON a.EMPLOYEE_ID = b.EMPLOYEE_ID) p ON m.EMPLOYEE_ID = p.EMPLOYEE_ID) B ON A.BIZ_DATE = B.BIZ_DATE
        AND A.EMPLOYEE_ID = B.EMPLOYEE_ID
        INNER JOIN
    KETTLE_MASTER.EMPLOYEE_DETAIL ed ON A.EMPLOYEE_ID = ed.EMP_ID
GROUP BY A.EMPLOYEE_ID
ORDER BY DEPUTY_AREA_MANAGER

        ]]>
					</content>
				</report>
				<report id="2" name="Cafes" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    ud.UNIT_NAME,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    ud.SHORT_NAME,
    A.NET_DELIVERY_TICKETS,
    COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0) LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    COALESCE(B.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) LWSD_NET_DELIVERY_SALES,
    ud.SHORT_NAME,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0)) LWSD_DINE_IN_TICKETS,
    TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES - A.GIFT_CARD_AMOUNT) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
        0) NET_DINE_IN_APC,
    COALESCE(TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) - COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0)) / (B.LWSD_NET_TICKETS - COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0)),
                0),
            0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - COALESCE(B.LWSD_NET_DELIVERY_SALES, 0)) LWSD_NET_DINE_IN_SALES,
    A.COLD_PENETRATION,
    B.LWSD_COLD_PENETRATION,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    ud.SHORT_NAME,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES,
    UCASE(ed.EMP_NAME) AREA_MANAGER,
    A.DONATION,
    B.LWSD_DONATION,A.GIFT_BOX_QUANTITY,B.LWSD_GIFT_BOX_QUANTITY
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
        INNER JOIN
    KETTLE_MASTER.EMPLOYEE_DETAIL ed ON ud.UNIT_MANAGER = ed.EMP_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.DONATION, 0) DONATION,  COALESCE(n.GIFT_BOX_QUANTITY, 0) GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        od.UNIT_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) qa
    GROUP BY qa.UNIT_ID) j ON m.UNIT_ID = j.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158,1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291 , 1026, 1027, 1048, 1157, 1158,1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158,1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0) - COALESCE(n.DONATION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0) - COALESCE(n.DONATION, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.DONATION, 0) LWSD_DONATION, COALESCE(n.GIFT_BOX_QUANTITY, 0) LWSD_GIFT_BOX_QUANTITY,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        od.UNIT_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.UNIT_ID) j ON m.UNIT_ID = j.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.DONATION) DONATION, SUM(a.GIFT_BOX_QUANTITY) GIFT_BOX_QUANTITY,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048, 1157, 1158,1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1291 , 1026, 1027, 1048, 1157, 1158,1218) THEN oi.AMOUNT_PAID
                ELSE 0
            END) MERCHANDISE_SALES,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1291) THEN oi.AMOUNT_PAID
                ELSE 0
            END) DONATION,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (692,1143,1144,1219,1303,1304) THEN oi.QUANTITY
                ELSE 0
            END) GIFT_BOX_QUANTITY,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1291 , 1026, 1027, 1048, 1056, 1157, 1158,1218, 691, 692, 700, 710, 720, 730, 840, 850, 1057, 1059, 1129, 1143, 1144, 1153, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1225, 1219, 1289, 1283, 1303,1304)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) B ON B.UNIT_ID = ud.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
        OR B.LWSD_NET_TICKETS IS NOT NULL
ORDER BY AREA_MANAGER , ud.UNIT_NAME

                                                ]]>
					</content>

				</report>

				<report id="4" name="New Product Total Sales" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BIZ_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
		OR (IS_COMPLIMENTARY = 'Y' AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 1)
        THEN
            oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY
        ELSE 0
    END) UNSATISFIED_QUANTITY__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY
        ELSE 0
    END) TRAINING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY
        ELSE 0
    END) SAMPLING_MARKETING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2103
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) OTHER_GMV_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) UNSATISFIED_GMV__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) TRAINING_GMV_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) SAMPLING_MARKETING_GMV_NON_ACCOUNTABLE,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        LEFT JOIN
    KETTLE_MASTER.REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
        AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(CURRENT_DATE(), 1))
            AND oi.PRODUCT_ID IN (1281,1282,1057,1237,1283,1289,1149,1296,1297,1024, 1301,1302,1303,1304)
GROUP BY BIZ_DATE , pd.PRODUCT_NAME , rl.RTL_CODE , oi.DIMENSION
ORDER BY BIZ_DATE , pd.PRODUCT_NAME , oi.DIMENSION
                                                ]]>
					</content>
				</report>
				<report id="10" name="New Product Total Sales Area Manager Wise" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
	ed.EMP_NAME AREA_MANAGER,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 4
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BIZ_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
		OR (IS_COMPLIMENTARY = 'Y' AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 1)
        THEN
            oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY
        ELSE 0
    END) UNSATISFIED_QUANTITY__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY
        ELSE 0
    END) TRAINING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY
        ELSE 0
    END) SAMPLING_MARKETING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2103
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) OTHER_GMV_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) UNSATISFIED_GMV__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) TRAINING_GMV_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) SAMPLING_MARKETING_GMV_NON_ACCOUNTABLE,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        LEFT JOIN
    KETTLE_MASTER.REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID
		LEFT JOIN
    KETTLE_MASTER.EMPLOYEE_DETAIL ed ON ud.UNIT_MANAGER = ed.EMP_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
        AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(CURRENT_DATE(), 1))
        AND oi.PRODUCT_ID IN (1281,1282,1057,1237,1283,1289,1149,1296,1297,1024, 1301,1302,1303,1304)
GROUP BY BIZ_DATE , pd.PRODUCT_NAME , rl.RTL_CODE , oi.DIMENSION, ud.UNIT_MANAGER
ORDER BY ud.UNIT_MANAGER, BIZ_DATE , pd.PRODUCT_NAME , oi.DIMENSION
]]>
					</content>
				</report>

				<report id="10" name="New Product Total Sales Unit Wise" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
	ud.UNIT_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BIZ_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
		OR (IS_COMPLIMENTARY = 'Y' AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 1)
        THEN
            oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY
        ELSE 0
    END) UNSATISFIED_QUANTITY__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY
        ELSE 0
    END) TRAINING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY
        ELSE 0
    END) SAMPLING_MARKETING_QUANTITY_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2103
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) OTHER_GMV_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) UNSATISFIED_GMV__NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2105
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) TRAINING_GMV_NON_ACCOUNTABLE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2106
        THEN
            oi.QUANTITY * oi.PRICE
        ELSE 0
    END) SAMPLING_MARKETING_GMV_NON_ACCOUNTABLE,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        LEFT JOIN
    KETTLE_MASTER.REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
        AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(CURRENT_DATE(), 1))
       AND oi.PRODUCT_ID IN (1281,1282,1057,1237,1283,1289,1149,1296,1297,1024, 1301,1302,1303,1304)
GROUP BY BIZ_DATE , pd.PRODUCT_NAME , rl.RTL_CODE , oi.DIMENSION, ud.UNIT_NAME
ORDER BY BIZ_DATE , pd.PRODUCT_NAME , oi.DIMENSION, ud.UNIT_NAME
]]>
					</content>
				</report>
				<!--<report id="5" name="Gift Card Sales" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
                        ud.UNIT_NAME,
            oi.PRODUCT_NAME,
            SUM(oi.QUANTITY) QUANTITY,
            SUM(oi.PRICE * oi.QUANTITY) TOTAL_SALES,
                        CONCAT(COALESCE(ud.UNIT_NAME, 'NA'),' - ',case when oi.PRODUCT_ID = 1026 then 'GC:500' else 'GC:1000' end,' - ',COALESCE( SUM(oi.QUANTITY), 'NA'),' - ',COALESCE(SUM(oi.PRICE * oi.QUANTITY), 'NA')) GIFT_CARD_SUMMARY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID > (SELECT
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE(), 1))
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048,1157,1158,1218)
    GROUP BY ud.UNIT_NAME , oi.PRODUCT_ID

]]>
					</content>
				</report>-->
				<!--<report id="5" name="Region Wise Gift Card Sales" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
                        ud.UNIT_REGION,
            oi.PRODUCT_NAME,
            SUM(oi.QUANTITY) QUANTITY,
            SUM(oi.PRICE * oi.QUANTITY) TOTAL_SALES,
                        CONCAT(COALESCE(ud.UNIT_REGION, 'NA'),' - ',case when oi.PRODUCT_ID = 1026 then 'GC:500' else 'GC:1000' end,' - ',COALESCE( SUM(oi.QUANTITY), 'NA'),' - ',COALESCE(SUM(oi.PRICE * oi.QUANTITY), 'NA')) GIFT_CARD_SUMMARY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID > (SELECT
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE(), 1))
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048,1157,1158,1218)
    GROUP BY ud.UNIT_REGION , oi.PRODUCT_ID

]]>
					</content>
				</report>-->
				<!--<report id="5" name="Product Wise Gift Card Sales" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
                        oi.PRODUCT_NAME,
            SUM(oi.QUANTITY) QUANTITY,
            SUM(oi.PRICE * oi.QUANTITY) TOTAL_SALES,
                        CONCAT(oi.PRODUCT_NAME,' - ',COALESCE( SUM(oi.QUANTITY), 'NA'),' - ',COALESCE(SUM(oi.PRICE * oi.QUANTITY), 'NA')) GIFT_CARD_SUMMARY
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID > (SELECT
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(CURRENT_DATE(), 1))
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048,1157,1158,1218)
    GROUP BY oi.PRODUCT_NAME
]]>
					</content>
				</report>-->
				<report id="9" name="Daily Web App Orders" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    ORDER_SOURCE,
    SUM(CASE
        WHEN PAYMENT_MODE_ID = 1 THEN 1
        ELSE 0
    END) CASH_ORDERS,
    SUM(CASE
        WHEN PAYMENT_MODE_ID = 12 || PAYMENT_MODE_ID = 13 THEN 1
        ELSE 0
    END) ONLINE_PAYMENT_ORDERS
FROM
    KETTLE.ORDER_DETAIL od,
    KETTLE.ORDER_SETTLEMENT os
WHERE
    od.ORDER_ID = os.ORDER_ID
        AND od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
        AND ORDER_SOURCE_ID LIKE 'NEO-%'
        AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN
                HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5
            THEN
                SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                    1)
            ELSE CURRENT_DATE
        END),
        INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
GROUP BY ORDER_SOURCE

]]>
					</content>
				</report>
				
				<report id="4" name="Quick value meal sales report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BIZ_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    cp.PARTNER_DISPLAY_NAME CHANNEL_PARTNER,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        LEFT JOIN
    KETTLE_MASTER.REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID
        LEFT JOIN
    KETTLE.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE = 'order'
        AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN
                HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5
            THEN
                SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                    1)
            ELSE CURRENT_DATE
        END),
        INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
        AND pd.PRODUCT_TYPE = 43
GROUP BY BIZ_DATE , pd.PRODUCT_NAME , rl.RTL_CODE , oi.DIMENSION , cp.PARTNER_ID
ORDER BY BIZ_DATE , pd.PRODUCT_NAME , oi.DIMENSION;

                                                ]]>
					</content>
				</report>
				<report id="4" name="Quick value meal sales report aggregate" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BIZ_DATE,
    cp.PARTNER_DISPLAY_NAME CHANNEL_PARTNER,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE.ORDER_DETAIL od
        LEFT JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        LEFT JOIN
    KETTLE_MASTER.REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID
        LEFT JOIN
    KETTLE.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE = 'order'
        AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN
                HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5
            THEN
                SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                    1)
            ELSE CURRENT_DATE
        END),
        INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
        AND pd.PRODUCT_TYPE = 43
GROUP BY BIZ_DATE, cp.PARTNER_ID
ORDER BY BIZ_DATE, cp.PARTNER_DISPLAY_NAME;

                                                ]]>
					</content>
				</report>
				<report id="99" name="KIOSK Sales" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content>
                                                <![CDATA[

SELECT 
    ud.UNIT_NAME,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
	A.NET_DELIVERY_TICKETS,
    COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0) LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    COALESCE(B.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) LWSD_NET_DELIVERY_SALES,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0)) LWSD_DINE_IN_TICKETS,
	TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES  - A.GIFT_CARD_AMOUNT)/(A.NET_TICKETS - A.NET_DELIVERY_TICKETS),0) NET_DINE_IN_APC,
	COALESCE(TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) - COALESCE(B.LWSD_GIFT_CARD_AMOUNT,0))/(B.LWSD_NET_TICKETS - COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0)),0),0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - COALESCE(B.LWSD_NET_DELIVERY_SALES, 0)) LWSD_NET_DINE_IN_SALES,
    A.COLD_PENETRATION,
    B.LWSD_COLD_PENETRATION,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES,
	UCASE(ed.EMP_NAME) AREA_MANAGER
FROM
    KETTLE_MASTER.UNIT_DETAIL ud
    INNER JOIN
    KETTLE_MASTER.EMPLOYEE_DETAIL ed ON ud.UNIT_MANAGER = ed.EMP_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_SOURCE_ID LIKE 'KSK-%'  AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        od.UNIT_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_SOURCE_ID LIKE 'KSK-%'  AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')) qa
    GROUP BY qa.UNIT_ID) j ON m.UNIT_ID = j.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048,1157,1158,1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1026 , 1027, 1048,1157,1158,1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_SOURCE_ID LIKE 'KSK-%'  AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND oi.PRODUCT_ID IN (1026,1027,1048,1056,1157,1158,1218,691,692,700,710,720,730,840,850,1057,1059,1129,1143,1144,1153,1164,1165,1166,1167,1168,1169,1170,1171,1225,1219)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_SOURCE_ID LIKE 'KSK-%'  AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_SOURCE_ID LIKE 'KSK-%'  AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_SOURCE_ID LIKE 'KSK-%'  AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_SOURCE_ID LIKE 'KSK-%'  AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        od.UNIT_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_SOURCE_ID LIKE 'KSK-%'  AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.UNIT_ID) j ON m.UNIT_ID = j.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048,1157,1158,1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GIFT_CARD_AMOUNT,
            SUM(CASE
                WHEN oi.PRODUCT_ID NOT IN (1026 , 1027, 1048,1157,1158,1218) THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_ITEM oi
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_SOURCE_ID LIKE 'KSK-%'  AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026,1027,1048,1056,1157,1158,1218,691,692,700,710,720,730,840,850,1057,1059,1129,1143,1144,1153,1164,1165,1166,1167,1168,1169,1170,1171,1225,1219)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_SOURCE_ID LIKE 'KSK-%'  AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE.ORDER_SETTLEMENT os
            INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_SOURCE_ID LIKE 'KSK-%'  AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE.ORDER_SETTLEMENT os
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_SOURCE_ID LIKE 'KSK-%'  AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) B ON B.UNIT_ID = ud.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
        OR B.LWSD_NET_TICKETS IS NOT NULL
ORDER BY AREA_MANAGER , ud.UNIT_NAME

											
						]]>
                                        </content>
                                </report>
	<report id="5" name="Donations" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    ud.UNIT_NAME,
    oi.PRODUCT_NAME,
    SUM(oi.QUANTITY) QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_DONATION
FROM
    KETTLE.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        INNER JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE = 'order'
        AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN
                HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5
            THEN
                SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                    1)
            ELSE CURRENT_DATE
        END),
        INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
        AND oi.PRODUCT_ID IN (1291)
GROUP BY ud.UNIT_NAME , oi.PRODUCT_ID

]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>
