package com.stpl.tech.scm.notification.email.template;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.VendorRegistrationRequest;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

public class VendorRegistrationRequestNotificationTemplate extends AbstractVelocityTemplate {

	private String basePath;
	private VendorRegistrationRequest request;
	private String link;

	public VendorRegistrationRequestNotificationTemplate() {

	}

	public VendorRegistrationRequestNotificationTemplate(VendorRegistrationRequest request, String link,
			String basePath) {
		this.basePath = basePath;
		this.request = request;
		this.link = link;
	}

	@Override
	public String getTemplatePath() {
		return "templates/VendorRegistrationRequest.html";
	}

	@Override
	public String getFilepath() {
		return basePath + "/vendor/registrations/" + request.getId() + '_' + request.getVendorName() + '_'
				+ SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";
	}

	@Override
	public Map<String, Object> getData() {
		Map<String, Object> stringObjectMap = new HashMap<>();
		stringObjectMap.put("request", request);
		stringObjectMap.put("link", link);
		return stringObjectMap;
	}

}
