/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.inventory.domain.redis;

import java.io.Serializable;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;

import com.stpl.tech.master.domain.model.EmploymentStatus;

@RedisHash("EmployeeBasicDetail")
public class EmployeeBasicDetail implements Serializable {

	private static final long serialVersionUID = -1854003503992144900L;
	@Id
	private int id;
	private String name;
	private String emailId;
	private EmploymentStatus status;
	private String departmentName;
	private String designation;
	private String employeeCode;
	private String contactNumber;
	private String mappingStatus;
	private String sdpContact;
	private Integer mealAllowanceLimit;
	private Integer reportingManagerId;
	private String slackChannel;

	public EmployeeBasicDetail() {

	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public EmploymentStatus getStatus() {
		return status;
	}

	public void setStatus(EmploymentStatus status) {
		this.status = status;
	}

	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	public String getDesignation() {
		return designation;
	}

	public void setDesignation(String designation) {
		this.designation = designation;
	}

	public String getEmployeeCode() {
		return employeeCode;
	}

	public void setEmployeeCode(String employeeCode) {
		this.employeeCode = employeeCode;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getMappingStatus() {
		return mappingStatus;
	}

	public void setMappingStatus(String mappingStatus) {
		this.mappingStatus = mappingStatus;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public String getSdpContact() {
		return sdpContact;
	}

	public void setSdpContact(String sdpContact) {
		this.sdpContact = sdpContact;
	}

	public Integer getMealAllowanceLimit() {
		return mealAllowanceLimit;
	}

	public void setMealAllowanceLimit(Integer mealAllowanceLimit) {
		this.mealAllowanceLimit = mealAllowanceLimit;
	}

	public Integer getReportingManagerId() {
		return reportingManagerId;
	}

	public void setReportingManagerId(Integer reportingManagerId) {
		this.reportingManagerId = reportingManagerId;
	}

	public String getSlackChannel() {
		return slackChannel;
	}

	public void setSlackChannel(String slackChannel) {
		this.slackChannel = slackChannel;
	}

	@Override
	public String toString() {
		return "EmployeeBasicDetail [id=" + id + ", name=" + name + ", emailId=" + emailId + ", status=" + status
				+ ", departmentName=" + departmentName + ", designation=" + designation + ", employeeCode="
				+ employeeCode + ", contactNumber=" + contactNumber + ", mappingStatus=" + mappingStatus
				+ ", sdpContact=" + sdpContact + ", mealAllowanceLimit=" + mealAllowanceLimit + ", reportingManagerId="
				+ reportingManagerId + ", slackChannel=" + slackChannel + "]";
	}
	
	
}
