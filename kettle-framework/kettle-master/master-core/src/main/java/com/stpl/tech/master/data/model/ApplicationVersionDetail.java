package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "APPLICATION_VERSION_DETAIL")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class ApplicationVersionDetail {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    private Integer id;
    @Column(name = "APPLICATION_NAME")
    private String applicationName;
    @Column(name = "APPLICATION_VERSION")
    private String applicationVersion;
    @Column(name = "RELEASED_TYPE")
    private String releaseType;
    @Column(name = "UPDATED_BY")
    private Integer updatedBy;
    @Column(name = "UPDATED_TIME")
    private Date updatedTime;
    @Column(name = "VERSION_STATUS")
    private String status;
    @Column(name = "DEPLOYMENT_DESCRIPTION",columnDefinition = "TEXT")
    private String deploymentDescription;
    @Column(name = "BUILD_NAME")
    private String buildName;

}
