/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.04.28 at 02:13:55 PM IST
//


package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.domain.model.PartnerDetail;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for UnitMetadata complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="UnitMetadata"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="deliveryPartners" type="{http://www.w3schools.com}PartnerDetail" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="lastBusinessDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitMetadata", propOrder = {
    "unitId",
    "deliveryPartners",
    "lastBusinessDate",
    "directGiftCardPurchase",
    "edcPartnerDetails",
    "walletRecommendationDrools",
})
public class UnitMetadata {

    protected int unitId;
    protected List<PartnerDetail> deliveryPartners;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date lastBusinessDate;
    private String directGiftCardPurchase;
    private String  walletRecommendationDrools;
    private List<UnitToEdcMappingData> edcPartnerDetails;
    /**
     * Gets the value of the unitId property.
     *
     */
    public int getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     *
     */
    public void setUnitId(int value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the deliveryPartners property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the deliveryPartners property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDeliveryPartners().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PartnerDetail }
     *
     *
     */
    public List<PartnerDetail> getDeliveryPartners() {
        if (deliveryPartners == null) {
            deliveryPartners = new ArrayList<PartnerDetail>();
        }
        return this.deliveryPartners;
    }

    public List<UnitToEdcMappingData> getEdcPartnerDetails() {
        if (edcPartnerDetails == null) {
            edcPartnerDetails = new ArrayList<UnitToEdcMappingData>();
        }
        return this.edcPartnerDetails;
    }

    /**
     * Gets the value of the lastBusinessDate property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getLastBusinessDate() {
        return lastBusinessDate;
    }

    /**
     * Sets the value of the lastBusinessDate property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLastBusinessDate(Date value) {
        this.lastBusinessDate = value;
    }

    public String getDirectGiftCardPurchase() {
        return directGiftCardPurchase;
    }

    public void setDirectGiftCardPurchase(String directGiftCardPurchase) {
        this.directGiftCardPurchase = directGiftCardPurchase;
    }

    public String getWalletRecommendationDrools() {
        return walletRecommendationDrools;
    }

    public void setWalletRecommendationDrools(String walletRecommendationDrools) {
        this.walletRecommendationDrools = walletRecommendationDrools;
    }
}
