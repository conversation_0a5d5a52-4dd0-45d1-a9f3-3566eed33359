package com.stpl.tech.kettle.referral.dao.impl;

import com.stpl.tech.kettle.core.LoyaltyEventStatus;
import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.customer.dao.impl.AuthorizationDaoImpl;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyEvents;
import com.stpl.tech.kettle.data.model.LoyaltyLogHistory;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.LoyaltyTransfer;
import com.stpl.tech.kettle.domain.model.webengage.survey.LoyaltyGiftingSMSToken;
import com.stpl.tech.kettle.loyaltyTransfer.model.LoyaltyTransferStatus;
import com.stpl.tech.kettle.referral.dao.LoyaltyTransferDao;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.jms.JMSException;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Repository
public class LoyaltyTransferDaoImpl extends AbstractDaoImpl implements LoyaltyTransferDao {

    private EntityManager entityManager;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private SMSClientProviderService providerService;

    private static final Logger LOG = LoggerFactory.getLogger(AuthorizationDaoImpl.class);

    @Override
    public LoyaltyScore getLoyalityScore(int customerId) {
        try {
            Query query = manager.createQuery("FROM LoyaltyScore lt WHERE lt.customerId= :customerId");
            query.setParameter("customerId", customerId);
            return (LoyaltyScore) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.error("Invalid Customer Id : " + customerId, e);
        } catch (NonUniqueResultException e) {
            LOG.error("Invalid Customer Id, more than one record found for customer id : " + customerId, e);
        }
        return null;
    }


    @Override
    public LoyaltyTransfer getLoyalityByEventId(int eventId) {
        try {
            return manager.find(LoyaltyTransfer.class, eventId);
        } catch (NoResultException e) {
            LOG.error("Invalid eventId no record found in LOYALTY_TRANSFER for event id : " + eventId, e);
        }
        return null;
    }

    @Override
    public CustomerInfo getCustomerInfoByCustomerId(int customerId) {
        try {
            Query query = manager.createQuery("FROM CustomerInfo ci WHERE ci.customerId= :customerId");
            query.setParameter("customerId", customerId);
            return (CustomerInfo) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.error("Invalid customer id no record found in CUSTOMER_INFO for customer id : " + customerId, e);
        } catch (NonUniqueResultException e) {
            LOG.error("Invalid customer id more than one record found in CUSTOMER_INFO for customer id : " + customerId, e);
        }
        return null;
    }

    @Override
    public CustomerInfo getCustomerInfoByContactNumber(String contactNumber) {
        try {
            Query query = manager.createQuery("FROM CustomerInfo ci WHERE ci.contactNumber= :contactNumber");
            query.setParameter("contactNumber", contactNumber);
            return (CustomerInfo) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.error("Invalid contactNumber no record found in CUSTOMER_INFO for contact number : " + contactNumber, e);
        } catch (NonUniqueResultException e) {
            LOG.error("Invalid contactNumber more than one record found in CUSTOMER_INFO for contact number : " + contactNumber, e);
        }
        return null;
    }


    @Override
    public List<LoyaltyTransfer> getAllSentGift(int customerId) {
        Query query = manager.createQuery("FROM LoyaltyTransfer lt WHERE lt.senderId=:customerId");
        query.setParameter("customerId", customerId);
        List<LoyaltyTransfer> list = query.getResultList();
        return list;
    }

    @Override
    public List<LoyaltyTransfer> getAllReceivedGift(String contactNumber) {
        Query query = manager.createQuery("FROM LoyaltyTransfer lt WHERE lt.receiverContactNumber=:contactNumber");
        query.setParameter("contactNumber", contactNumber);
        List<LoyaltyTransfer> list = query.getResultList();
        List<LoyaltyTransfer> result = new ArrayList<>();
        for (LoyaltyTransfer loyaltyTransfer : list) {
            LOG.info(loyaltyTransfer.getTransferStatus());
            if (loyaltyTransfer.getTransferStatus().equals(LoyaltyTransferStatus.CREATED.name()) ||
                    loyaltyTransfer.getTransferStatus().equals(LoyaltyTransferStatus.CLAIMED.name()) ||
                    loyaltyTransfer.getTransferStatus().equals(LoyaltyTransferStatus.EXPIRED.name())) {
                result.add(loyaltyTransfer);
            }
        }
        return result;
    }

    @Override
    public List<LoyaltyTransfer> getNotClaimedGift(int customerId) {
        Query query = manager.createQuery("FROM LoyaltyTransfer lt WHERE lt.senderId=:customerId and lt.transferStatus = :transferStatus");
        query.setParameter("customerId", customerId);
        query.setParameter("transferStatus", LoyaltyTransferStatus.CREATED.name());
        List<LoyaltyTransfer> list = query.getResultList();
        return list;
    }

    @Override
    public List<LoyaltyTransfer> getNotClaimedReceivedGift(String contactNumber) {
        Query query = manager.createQuery("FROM LoyaltyTransfer lt WHERE lt.receiverContactNumber=:contactNumber and lt.transferStatus = :transferStatus");
        query.setParameter("contactNumber", contactNumber);
        query.setParameter("transferStatus", LoyaltyTransferStatus.CREATED.name());
        List<LoyaltyTransfer> list = query.getResultList();
        return list;
    }

    @Override
    public List<LoyaltyTransfer> getClaimedGift(int customerId) {
        Query query = manager.createQuery("FROM LoyaltyTransfer lt WHERE lt.senderId=:customerId and lt.transferStatus = :transferStatus");
        query.setParameter("customerId", customerId);
        query.setParameter("transferStatus", LoyaltyTransferStatus.CLAIMED.name());
        List<LoyaltyTransfer> list = query.getResultList();
        return list;
    }

    @Override
    public List<LoyaltyTransfer> getClaimedReceivedGift(String contactNumber) {
        Query query = manager.createQuery("FROM LoyaltyTransfer lt WHERE lt.receiverContactNumber=:contactNumber and lt.transferStatus = :transferStatus");
        query.setParameter("contactNumber", contactNumber);
        query.setParameter("transferStatus", LoyaltyTransferStatus.CLAIMED.name());
        List<LoyaltyTransfer> list = query.getResultList();
        return list;
    }

    @Override
    public List<LoyaltyTransfer> allExpiredLoyaltyTransfers() {
        Query query = manager.createQuery("FROM LoyaltyTransfer lt WHERE lt.expiryTime<= :expiryTime AND lt.transferStatus = :status");
        query.setParameter("expiryTime", AppUtils.getCurrentTimestamp());
        query.setParameter("status", LoyaltyTransferStatus.CREATED.name());
        List<LoyaltyTransfer> loyaltyTransfers = query.getResultList();
        return loyaltyTransfers;
    }

    @Override
    public boolean sendLoyaltyGiftingReminderSMS(LoyaltyGiftingSMSToken loyaltyGiftingSMSToken, String contactNumber) {
        try {
            String message = CustomerSMSNotificationType.LOYALTY_GIFTING_REMINDER_NOTIFICATION.getMessage(loyaltyGiftingSMSToken);
                return notificationService.sendNotification(CustomerSMSNotificationType.LOYALTY_GIFTING_REMINDER_NOTIFICATION.name(),
                        message, contactNumber,
                        providerService.getSMSClient(
                                CustomerSMSNotificationType.LOYALTY_GIFTING_REMINDER_NOTIFICATION.getTemplate().getSMSType(),
                                ApplicationName.KETTLE_CRM
                        ), props.getLoyaltyGiftingReminderSMS(), null);

        } catch (IOException | JMSException e) {
            LOG.error("Error while sending the referral message to " + contactNumber, e);
        }
        return false;
    }

    @Override
    public LoyaltyEvents getLoyaltyEventByEventId(Integer eventId) {
        try {
            LOG.info("Query to fetch loyalty events for event Id : {}",eventId);
            Query query = manager.createQuery("FROM LoyaltyEvents le WHERE le.eventId= :eventId and le.transactionCode = :transactionCode "
            + "and le.transactionStatus = :transactionStatus");
            query.setParameter("eventId", eventId);
            query.setParameter("transactionCode", LoyaltyEventType.LOYALTY_GIFTING.name());
            query.setParameter("transactionStatus","SUCCESS");
            return (LoyaltyEvents) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.error("Invalid event Id : " + eventId, e);
        } catch (NonUniqueResultException e) {
            LOG.error("Invalid event Id, more than one record found for customer id : " + eventId, e);
        }
        return null;
    }

    @Override
    public List<LoyaltyLogHistory> getLoyaltyLogHistoryByTransactionEventId(Integer eventId) {
        try {
            LOG.info("Query to fetch loyalty Log History for event Id : {}",eventId);
            Query query = manager.createQuery("FROM LoyaltyLogHistory llh WHERE llh.transactionEventId= :eventId and llh.transactionCodeType = :transactionCodeType "
                    + "and llh.transactionStatus = :transactionStatus");
            query.setParameter("eventId", eventId);
            query.setParameter("transactionCodeType", LoyaltyEventStatus.GIFTED.name());
            query.setParameter("transactionStatus","SUCCESS");
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Exception occur for event Id : " + eventId, e);
        }
        return null;
    }

    @Override
    public List<LoyaltyEvents> getLoyaltyEventByCustomerIdExcludeGiftingEvent(Integer customerId) {
        try {
            LOG.info("Query to fetch loyalty events excluding Gifting Events for customer Id  : {}",customerId);
            Query query = manager.createQuery("FROM LoyaltyEvents le WHERE le.customerId= :customerId and le.transactionCode <> :transactionCode "
                    + "and le.transactionStatus = :transactionStatus and le.loyaltyEventStatus = :loyaltyEventStatus");
            query.setParameter("customerId", customerId);
            query.setParameter("transactionCode", LoyaltyEventType.LOYALTY_GIFTING.name());
            query.setParameter("transactionStatus","SUCCESS");
            query.setParameter("loyaltyEventStatus",LoyaltyEventStatus.ACTIVE.name());
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Exception occur for customer Id : " + customerId, e);
        }
        return null;
    }





}
