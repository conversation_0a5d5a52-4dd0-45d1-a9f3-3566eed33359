package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.Objects;

public class UnitChannelPartnerMapping implements Serializable {

    private static final long serialVersionUID = -1540075256787094892L;
    private Integer id;
    private IdCodeName unit;
    private IdCodeName channelPartner;
    private IdCodeName deliveryPartner;
    private String status;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public IdCodeName getUnit() {
        return unit;
    }

    public void setUnit(IdCodeName unit) {
        this.unit = unit;
    }

    public IdCodeName getChannelPartner() {
        return channelPartner;
    }

    public void setChannelPartner(IdCodeName channelPartner) {
        this.channelPartner = channelPartner;
    }

    public IdCodeName getDeliveryPartner() {
        return deliveryPartner;
    }

    public void setDeliveryPartner(IdCodeName deliveryPartner) {
        this.deliveryPartner = deliveryPartner;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UnitChannelPartnerMapping that = (UnitChannelPartnerMapping) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(unit, that.unit) &&
                Objects.equals(channelPartner, that.channelPartner) &&
                Objects.equals(deliveryPartner, that.deliveryPartner) &&
                Objects.equals(status, that.status);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
