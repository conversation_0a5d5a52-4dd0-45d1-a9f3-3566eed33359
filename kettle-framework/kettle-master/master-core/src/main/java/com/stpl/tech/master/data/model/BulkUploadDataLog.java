package com.stpl.tech.master.data.model;

import com.stpl.tech.master.core.service.batchProcess.batchAnnotation.BatchProcess;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Lob;
import java.util.Date;

@Entity
@Getter
@Setter
@BatchProcess(batchSize = 300)
@Table(name = "BULK_UPLOAD_DATA_LOGS")
public class BulkUploadDataLog {
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer bulkUploadLogId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BULK_UPLOAD_ID")
    private BulkUploadData bulkUploadData;

    @Column(name = "UPDATED_FIELD_ID")
    private Integer updatedFieldId;

    @Column(name = "UPDATED_FIELD_TYPE")
    @Enumerated(EnumType.STRING)
    private BulkUploadFieldType updatedFieldType;

    @Column(name = "OLD_VALUE")
    private String oldValue;

    @Column(name = "NEW_VALUE")
    private String newValue;

    @Column(name = "STATUS")
    @Enumerated(EnumType.STRING)
    private BulkUploadStatus status;

    @Lob
    @Column(name = "COMMENT",columnDefinition = "LONGTEXT")
    private String comment;

    @Column(name = "CREATED_AT")
    private Date createdAt = new Date();

}


