<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    .partner-page ul {
        margin-left: -40px;
    }

    .partner-page ul li {
        background: #fff;
        padding: 5px;
        border: #efefef 1px solid;
        cursor: pointer;
    }

    .partner-page ul li.selected {
        background: green;
        color: #fff;
    }

    .checkbox-inline {
        padding: 10px;
        border: #ccc 1px solid;
        border-radius: 5px;
        padding-left: 35px;
        margin: 5px;
    }
</style>

<div class="container-fluid partner-page" data-ng-init="init()">
    <div class="row">
        <h2 class="text-center" style="color: #737370;text-align: center;">Partner Management Dashboard</h2>
    </div>

    <div class="row" style="margin-bottom: 20px; border-bottom: #ddd 1px solid; padding: 0 0 10px 0;">
        <div class="col-xs-12">
            <div class="btn-group" role="group">
                <button type="button" data-ng-repeat="action in actionList track by $index"
                        data-ng-class="{'btn btn-default':selectedAction!=action,'btn btn-primary':selectedAction==action}"
                        data-ng-click="selectAction(action)">{{action}}
                </button>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'DESI CHAI PROFILES'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to create desi chai profiles</h3>
                    <p>Profile can be of product type or customization type. Product type profile is used to launch
                        specific type of products created from desi chai.
                        For example adrak chai can be created by only setting addrak addon in product type profile.
                        Customization type profile is used to set
                        different default customizations for desi chai than the global defaults. For example if you want
                        to make no sugar option as default for some area then
                        you can do it by creating a customization type profile and selecting no sugar option in sugar
                        type.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12" style="margin-bottom: 10px">
                    <button type="button" class="btn btn-success btn-lg" data-ng-click="addDesiChaiProfile()">Add
                        Profile
                    </button>
                </div>
            </div>
            <table class="table table-bordered">
                <tr>
                    <th>Profile Name</th>
                    <th>Profile Type</th>
                    <th>Product Name</th>
                    <th>Product Description</th>
                    <th>Dimension Type</th>
                    <th>Milk Type</th>
                    <th>Sugar Type</th>
                    <th>Patti Type</th>
                    <th>Addons</th>
                </tr>
                <tr data-ng-repeat="profile in desiChaiProfiles track by profile.id">
                    <td>{{profile.profileName}}</td>
                    <td>{{profile.profileType}}</td>
                    <td>{{profile.productName}}</td>
                    <td>{{profile.productDescription}}</td>
                    <td>{{profile.dimensionType}}</td>
                    <td>{{profile.milkType}}</td>
                    <td>{{profile.sugarType}}</td>
                    <td>{{profile.pattiType}}</td>
                    <td><span data-ng-repeat="addon in profile.addons">{{addon.name}}</span></td>
                </tr>
            </table>
            <div style="display: none;">
                <input type="button" class="btn btn-primary" value="Reload unit channel partner mapping"
                       data-ng-click="reloadUnitChannelPartnerMapping()"/>
                <input type="button" class="btn btn-primary" value="Reload channel partner cache"
                       data-ng-click="reloadChannelPartnerCache()"/>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="selectedAction == 'UNIT PROFILE MAPPING'">
        <div class="col-xs-12">
            <div class="row alert alert-info">
                <div class="col-xs-12">
                    <h3>Use this panel to update unit status or refresh stock.</h3>
                    <p>Select all the outlets where you want to take the action and then select the partners where you
                        want to take action
                        and press the corresponding button.</p>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <h3>Profile Mapping</h3>
                    <div class="row">
                        <div class="col-xs-6">
                            <div class="pull-right">
                                <label>Select All</label>
                                <input type="checkbox" data-ng-model='selectAllUnits'
                                       style="width: 20px; height: 20px" data-ng-click="setSelectAllUnits()">
                            </div>
                            <label>Select Units</label>
                            <div class="form-group">
                                <input type="text" class="form-control" data-ng-model="search" ng-change="filter()"
                                       placeholder="Filter">
                            </div>
                            <ul style="max-height: 400px;overflow: auto; list-style: none; width: 100%">
                                <li data-ng-repeat="unit in unitList | filter: search | orderBy:'name' track by unit.id"
                                    data-ng-class="{'selected':unit.selected}"
                                    data-ng-click="unit.selected == true? unit.selected = false:unit.selected=true">
                                    {{unit.name}}
                                </li>
                            </ul>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label>Select Brand</label>
                                <select class="form-control"
                                        data-ng-options="brand as brand.brandName for brand in brands track by brand.brandId"
                                        data-ng-model="selectedBrand"
                                        data-ng-change="setSelectedBrand(selectedBrand)">
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Select Partner</label>
                                <select class="form-control"
                                        data-ng-options="partner as partner.name for partner in channelPartners track by partner.id"
                                        data-ng-model="selectedPartner"
                                        data-ng-change="setSelectedPartner(selectedPartner)">
                                </select>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Get Profile mappings"
                                       data-ng-click="getDesiChaiProfileMappings()"/>
                                <input type="button" class="btn btn-success" value="Add Profile mappings"
                                       data-ng-click="addDesiChaiProfileMappings()"/>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <input type="text" class="form-control" data-ng-model="mappingSearch"
                                       ng-change="filter()" placeholder="Filter">
                            </div>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Mapping Id</th>
                                    <th>Unit Name</th>
                                    <th>Profile Name</th>
                                    <th>Partner Name</th>
                                    <th>Brand Name</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                <tr data-ng-repeat="mapping in desiChaiProfileMappings | filter: mappingSearch track by mapping.id">
                                    <td>{{mapping.id}}</td>
                                    <td>{{mapping.unitName}}</td>
                                    <td>{{mapping.profileName}}</td>
                                    <td>{{mapping.partnerName}}</td>
                                    <td>{{mapping.brandName}}</td>
                                    <td>{{mapping.status}}</td>
                                    <td>
                                        <input type="button" class="btn btn-primary" value="View"
                                               data-ng-click="viewProfileData(mapping.profileId)"/>
                                        <input type="button" class="btn btn-success" value="Activate"
                                               data-ng-show="mapping.status === 'IN_ACTIVE'"
                                               data-ng-click="updateProfileMappingStatus(mapping, 'ACTIVE')"/>
                                        <input type="button" class="btn btn-danger" value="Deactivate"
                                               data-ng-show="mapping.status === 'ACTIVE'"
                                               data-ng-click="updateProfileMappingStatus(mapping, 'IN_ACTIVE')"/>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="desiChaiProfileModal" tabindex="-1" role="dialog"
     aria-labelledby="desiChaiProfileModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="desiChaiProfileModalLabel">Add New Desi Chai Profile</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <form name="newDesiChaiProfile" novalidate>
                            <div class="form-group">
                                <label>Select profile type*</label>
                                <select data-ng-model="newProfile.profileType" class="form-control">
                                    <option value="CUSTOMIZATION">Customization</option>
                                    <option value="PRODUCT">Product</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Profile Name*</label>
                                <input type="text" data-ng-model="newProfile.profileName" class="form-control">
                            </div>
                            <div class="form-group" data-ng-show="newProfile.profileType=='PRODUCT'">
                                <label>Product Name*</label>
                                <input type="text" data-ng-model="newProfile.productName" class="form-control">
                            </div>
                            <div class="form-group" data-ng-show="newProfile.profileType=='PRODUCT'">
                                <label>Product Description*</label>
                                <textarea data-ng-model="newProfile.productDescription" class="form-control" />
                            </div>
                            <div class="form-group" data-ng-show="newProfile.profileType == 'CUSTOMIZATION'">
                                <label>Dimension*</label>
                                <select data-ng-model="newProfile.dimensionType" class="form-control">
                                    <option value="Miniketli">Miniketli</option>
                                    <option value="ChotiKetli">ChotiKetli</option>
                                    <option value="BadiKetli">BadiKetli</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Milk type*</label>
                                <select data-ng-model="newProfile.milkType" class="form-control">
                                    <option value="Regular Milk">Regular Milk</option>
                                    <option value="Full Doodh">Full Doodh</option>
                                    <option value="Doodh Kum">Doodh Kum</option>
                                    <option value="Paani Kum">Paani Kum</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Sugar type*</label>
                                <select data-ng-model="newProfile.sugarType" class="form-control">
                                    <option value="Regular Sugar">Regular Sugar</option>
                                    <option value="No Sugar">No Sugar</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Patti type*</label>
                                <select data-ng-model="newProfile.pattiType" class="form-control">
                                    <option value="Regular Patti">Regular Patti</option>
                                    <option value="Kadak">Kadak</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Addons</label>
                            </div>
                            <div class="form-group form-check">
                                <label class="checkbox-inline" data-ng-show="desiChaiRecipe != null"
                                       data-ng-repeat="addon in desiChaiRecipe.addons">
                                    <input type="checkbox" ng-model="newProfile.addons[addon.product.productId]"/><b>{{addon.product.name}}</b>
                                </label>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12" style="text-align: right;">
                        <input type="button" class="btn btn-primary" value="Add Profile"
                               data-ng-click="saveDesiChaiProfile()"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="desiChaiProfileMappingModal" tabindex="-1" role="dialog"
     aria-labelledby="desiChaiProfileMappingModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="desiChaiProfileMappingModalLabel">Add New Desi Chai Profile Mapping</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <form name="newDesiChaiProfile" novalidate>
                            <div class="form-group">
                                <label>Select Profile</label>
                                <select class="form-control"
                                        data-ng-options="profile as profile.profileName for profile in desiChaiProfiles track by profile.id"
                                        data-ng-model="selectedProfile"
                                        data-ng-change="setSelectedProfile(selectedProfile)">
                                </select>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12" style="text-align: right;">
                        <input type="button" class="btn btn-primary" value="Add Profile Mappings"
                               data-ng-click="saveDesiChaiProfileMappings()"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="desiChaiProfileViewModal" tabindex="-1" role="dialog"
     aria-labelledby="desiChaiProfileViewModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="desiChaiProfileViewModalLabel">View Desi Chai Profile</h4>
            </div>
            <div class="modal-body">
                <div class="row" style="margin: 5px 0;padding: 5px 0;">
                    <div class="col-xs-4">
                        <label>Profile Id:</label><br/>
                        {{viewProfile.id}}
                    </div>
                    <div class="col-xs-4">
                        <label>Profile Name:</label><br/>
                        {{viewProfile.profileName}}
                    </div>
                    <div class="col-xs-4">
                        <label>Profile Type:</label><br/>
                        {{viewProfile.profileType}}
                    </div>
                </div>
                <div class="row" style="margin: 5px 0;padding: 5px 0;">
                    <div class="col-xs-4">
                        <label>Dimension type:</label><br/>
                        {{viewProfile.dimensionType}}
                    </div>
                    <div class="col-xs-4">
                        <label>Milk type:</label><br/>
                        {{viewProfile.milkType}}
                    </div>
                    <div class="col-xs-4">
                        <label>Sugar type:</label><br/>
                        {{viewProfile.sugarType}}
                    </div>
                </div>
                <div class="row" style="margin: 5px 0;padding: 5px 0;">
                    <div class="col-xs-4">
                        <label>Patti type:</label><br/>
                        {{viewProfile.pattiType}}
                    </div>
                    <div class="col-xs-4">
                        <label>Addons:</label><br/>
                        <span data-ng-repeat="addon in viewProfile.addons">{{addon.name}}</span>
                    </div>
                    <div class="col-xs-4">
                        <label>Product Name:</label><br/>
                        {{viewProfile.productName}}
                    </div>
                </div>
                <div class="row" style="margin: 5px 0;padding: 5px 0;">
                    <div class="col-xs-12">
                        <label>Product Description:</label><br/>
                        {{viewProfile.productDescription}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>