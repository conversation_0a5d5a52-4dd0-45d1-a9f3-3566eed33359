CREATE TABLE ORDER_ENQUIRY_ITEM (
ORDER_<PERSON>NQUIRY_ITEM_ID INT PRIMARY KEY AUTO_INCREMENT,
PRODUCT_ID INT NOT NULL,
ORDERED_QUANTITY INT NOT NULL,
<PERSON><PERSON><PERSON><PERSON><PERSON>_QUANTITY INT NOT NULL,
IS_REPLACEMENT_SERVED BIT NOT NULL,
ORDER_ID INT DEFAULT NULL,
CUSTOMER_ID INT DEFAULT NULL,
UNIT_ID INT DEFAULT NULL,
FOREIG<PERSON> KEY (PRODUCT_ID) REFERENCES PRODUCT_DETAIL(PRODUCT_ID),
FOREIG<PERSON> KEY (ORDER_ID) REFERENCES ORDER_DETAIL(ORDER_ID),
FOR<PERSON>GN KEY (CUSTOMER_ID) REFERENCES CUSTOMER_INFO(CUSTOMER_ID),
FOREIGN KEY (UNIT_ID) REFERENCES UNIT_DETAIL(UNIT_ID)
)ENGINE=INNODB;

#Added a new column for dimension
ALTER TABLE COUPON_DETAIL_MAPPING_DATA ADD COLUMN DIMENSION VARCHAR(15);

#FOR MALL OF INDIA
INSERT INTO PARTNER_ATTRIBUTES (PARTNER_ID,MAPPING_TYPE,MAPPING_VALUE,PARTNER_TYPE)
VALUES
( '12018','TENANT_ID','','SALES_REPORT'),
( '12018','FTP_SERVER','14.141.54.43','SALES_REPORT'),
( '12018','FTP_PORT','21','SALES_REPORT'),
( '12018','USERNAME','dlfposuser','SALES_REPORT'),
( '12018','PASSWORD','dlfposuser','SALES_REPORT'),
( '12018','STATUS','IN_ACTIVE','SALES_REPORT');


UPDATE COUPON_DETAIL_MAPPING_DATA SET MAPPING_VALUE='10', DIMENSION='Regular' WHERE COUPON_DETAIL_MAPPING_ID='57';
UPDATE COUPON_DETAIL_MAPPING_DATA SET MAPPING_VALUE='10', DIMENSION='Regular' WHERE COUPON_DETAIL_MAPPING_ID='58';
UPDATE COUPON_DETAIL_MAPPING_DATA SET MAPPING_VALUE='651', DIMENSION='Single' WHERE COUPON_DETAIL_MAPPING_ID='59';
UPDATE COUPON_DETAIL_MAPPING_DATA SET MAPPING_VALUE='652', DIMENSION='Single' WHERE COUPON_DETAIL_MAPPING_ID='60';
UPDATE COUPON_DETAIL_MAPPING_DATA SET MAPPING_VALUE='651', DIMENSION='Double' WHERE COUPON_DETAIL_MAPPING_ID='61';
UPDATE COUPON_DETAIL_MAPPING_DATA SET MAPPING_VALUE='652', DIMENSION='Double' WHERE COUPON_DETAIL_MAPPING_ID='62';
UPDATE COUPON_DETAIL_MAPPING_DATA SET MAPPING_VALUE='10', DIMENSION='Regular' WHERE COUPON_DETAIL_MAPPING_ID='80';

ALTER TABLE UNIT_CLOSURE_DETAILS 
ADD COLUMN START_ORDER_ID INTEGER NULL;

update UNIT_CLOSURE_DETAILS ucd1,
(
select UNIT_ID,LAST_ORDER_ID from UNIT_CLOSURE_DETAILS where BUSINESS_DATE = '2016-02-18') ucd2
set ucd1.START_ORDER_ID = ucd2.LAST_ORDER_ID
where ucd1.UNIT_ID = ucd2.UNIT_ID and ucd1.BUSINESS_DATE = '2016-02-19';
update UNIT_CLOSURE_DETAILS ucd1,
(
select UNIT_ID,LAST_ORDER_ID from UNIT_CLOSURE_DETAILS where BUSINESS_DATE = '2016-02-11') ucd2
set ucd1.START_ORDER_ID = ucd2.LAST_ORDER_ID
where ucd1.UNIT_ID = ucd2.UNIT_ID and ucd1.BUSINESS_DATE = '2016-02-18';

#FOR TAKEAWAY
ALTER TABLE UNIT_DETAIL ADD COLUMN NO_OF_TA_TERMINALS int DEFAULT 0;
INSERT INTO BUSINESS_DIVISION (BUSINESS_DIV_ID, BUSINESS_DIV_NAME, BUSIENSS_DIV_DESC, BUSIENSS_DIV_CATEGORY, COMPANY_ID)
	VALUES ('1200', 'Chaayos Takeaway', 'Chaayos Takeaway', 'Takeaway', '1000');

