/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 17 Jul, 2015 3:28:36 PM by Hibernate Tools 4.0.0

import com.stpl.tech.master.core.CreationTime;
import com.stpl.tech.master.core.listeners.SaveOrUpdateDateListener;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * EmployeeSessionDetails generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@EntityListeners({ SaveOrUpdateDateListener.class })
@Table(name = "EMPLOYEE_SESSION_DETAILS")
public class EmployeeSessionDetails implements java.io.Serializable, CreationTime {

	private Integer sessionDetailId;
	private int employeeId;
	private int unitId;
	private Integer terminalId;
	private Date creationTime;
	private String loginAttempt;
	private Date logoutTime;
	private String sessionId;
	private String ipAddress;
	private String macAddress;
	private String userAgent;
	private String appVersion;
	private String osVersion;
	private String deviceModel;
	private String applicationName;
	private String moduleName;

	public EmployeeSessionDetails() {
	}

	public EmployeeSessionDetails(int employeeId, int unitId, Integer terminalId, Date creationTime, Date logoutTime,
			String sessionId) {
		this.employeeId = employeeId;
		this.unitId = unitId;
		this.creationTime = creationTime;
		this.logoutTime = logoutTime;
		this.sessionId = sessionId;
	}

	public EmployeeSessionDetails(int employeeId, int unitId, Integer terminalId, Date creationTime,
			String loginAttempt, Date logoutTime, String sessionId) {
		this.employeeId = employeeId;
		this.unitId = unitId;
		this.creationTime = creationTime;
		this.loginAttempt = loginAttempt;
		this.logoutTime = logoutTime;
		this.sessionId = sessionId;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SESSION_DETAIL_ID", unique = true, nullable = false)
	public Integer getSessionDetailId() {
		return this.sessionDetailId;
	}

	public void setSessionDetailId(Integer sessionDetailId) {
		this.sessionDetailId = sessionDetailId;
	}

	@Column(name = "EMPLOYEE_ID", nullable = false)
	public int getEmployeeId() {
		return this.employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return this.unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "TERMINAL_ID", nullable = true)
	public Integer getTerminalId() {
		return terminalId;
	}

	public void setTerminalId(Integer terminalId) {
		this.terminalId = terminalId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LOGIN_TIME", nullable = false, length = 19)
	public Date getCreationTime() {
		return this.creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	@Column(name = "LOGIN_ATTEMPT", length = 30)
	public String getLoginAttempt() {
		return this.loginAttempt;
	}

	public void setLoginAttempt(String loginAttempt) {
		this.loginAttempt = loginAttempt;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LOGOUT_TIME", nullable = true, length = 19)
	public Date getLogoutTime() {
		return this.logoutTime;
	}

	public void setLogoutTime(Date logoutTime) {
		this.logoutTime = logoutTime;
	}

	@Column(name = "SESSION_ID", nullable = true, length = 150)
	public String getSessionId() {
		return this.sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	@Column(name = "IP_ADDRESS", nullable = true, length = 20)
	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	@Column(name = "MAC_ADDRESS", nullable = true, length = 20)
	public String getMacAddress() {
		return macAddress;
	}

	public void setMacAddress(String macAddress) {
		this.macAddress = macAddress;
	}

	@Column(name = "USER_AGENT", nullable = true, length = 200)
	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	@Column(name = "APP_VERSION", nullable = true, length = 200)
	public String getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}

	@Column(name = "OS_VERSION", nullable = true, length = 200)
	public String getOsVersion() {
		return osVersion;
	}

	public void setOsVersion(String osVersion) {
		this.osVersion = osVersion;
	}

	@Column(name = "DEVICE_MODEL", nullable = true, length = 200)
	public String getDeviceModel() {
		return deviceModel;
	}

	public void setDeviceModel(String deviceModel) {
		this.deviceModel = deviceModel;
	}

	@Column(name = "APPLICATION_NAME", nullable = true, length = 200)
	public String getApplicationName() {
		return applicationName;
	}

	public void setApplicationName(String applicationName) {
		this.applicationName = applicationName;
	}

	@Column(name = "MODULE_NAME", nullable = true, length = 200)
	public String getModuleName() {
		return moduleName;
	}

	public void setModuleName(String moduleName) {
		this.moduleName = moduleName;
	}
}
