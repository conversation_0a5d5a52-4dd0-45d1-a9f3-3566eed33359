package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "name",
        "contact_number",
        "alt_contact_number"
})
public class SwiggyRiderDetail {

    @JsonProperty("name")
    private String name;
    @JsonProperty("contact_number")
    private String contactNumber;
    @JsonProperty("alt_contact_number")
    private String altContactNumber;

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("contact_number")
    public String getContactNumber() {
        return contactNumber;
    }

    @JsonProperty("contact_number")
    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    @JsonProperty("alt_contact_number")
    public String getAltContactNumber() {
        return altContactNumber;
    }

    @JsonProperty("alt_contact_number")
    public void setAltContactNumber(String altContactNumber) {
        this.altContactNumber = altContactNumber;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        SwiggyRiderDetail that = (SwiggyRiderDetail) o;

        return new EqualsBuilder()
                .append(name, that.name)
                .append(contactNumber, that.contactNumber)
                .append(altContactNumber, that.altContactNumber)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(name)
                .append(contactNumber)
                .append(altContactNumber)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "SwiggyRiderDetail{" +
                "name='" + name + '\'' +
                ", contactNumber='" + contactNumber + '\'' +
                ", altContactNumber='" + altContactNumber + '\'' +
                '}';
    }
}