/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.model;

import com.google.gson.JsonArray;

public class ReportResponse {

	private JsonArray header;

	private JsonArray data;

	public ReportResponse() {

	}

	public ReportResponse(JsonArray header, JsonArray data) {
		super();
		this.header = header;
		this.data = data;
	}

	public JsonArray getHeader() {
		return header;
	}

	public void setHeader(JsonArray header) {
		this.header = header;
	}

	public JsonArray getData() {
		return data;
	}

	public void setData(JsonArray data) {
		this.data = data;
	}

}
