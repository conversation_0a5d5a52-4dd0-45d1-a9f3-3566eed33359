import appUtil from "../AppUtil";
import {w3cwebsocket as W3CWebSocket} from "websocket";
import axios from "axios";
import apis from "../APIs";
const symbol_tilde = '~';
const symbol_hyphen = '-';

export function makeSaleRequest(generatedOrderId, amount, printFlag) {
    return new Promise(function(resolve, reject) {
        try{
            if(appUtil.checkEmpty(generatedOrderId)){
                reject(buildJSON(false, 1, "Order Details Missing.", "Invalid order! order details are missing.", null));
            }
            if(appUtil.checkEmpty(amount)){
                reject(buildJSON(false, 2, "Amount Missing.", "Invalid order! Amount not found for transaction", null));
            }
            let saleTrnType = 2;
            let tPInvoiceNumber = generatedOrderId;
            let trnAmount = amount;
            //let tPInvoiceNumber = 123456;
            //let trnAmount = 11.11;
            let additionalData = '';
            let tipAmount = '';
            let messageForAGS = "<STXN>" + saleTrnType + "|" + tPInvoiceNumber + "|" + (trnAmount * 100);
            if(!appUtil.checkEmpty(additionalData)){
                messageForAGS = messageForAGS + "|" + additionalData;
            }
            if(appUtil.checkEmpty(tipAmount)){
                messageForAGS = messageForAGS + "|" + (tipAmount * 100);
            }
            if(appUtil.checkEmpty(printFlag)){
                printFlag = 0;
            }
            if(!appUtil.checkEmpty(printFlag) && (printFlag!==0 && printFlag!==1)){
                printFlag = 0;
            }

            let s2sCheckInMilli = 45000;
            console.log("Will run the server status check after "+ s2sCheckInMilli + " milli secs");
            let serverCallTimerId = setTimeout(function() {
                console.log("Checking for status on server");
                checkAGSPayment(generatedOrderId).then(serverResponse => {
                    console.log("Received response from server");
                    if(!appUtil.checkEmpty(serverResponse.data)){
                        let data = serverResponse.data;
                        if(data.status === true && data.code === 6){
                            console.log("Status received from server: payment success!");
                            resolve(JSON.stringify(data));
                        }
                        if(data.status === false && data.code === 7){
                            console.log("Status received from server: payment failed!");
                            reject(JSON.stringify(data));
                        }
                    }
                }).catch(serverError => {
                    console.log("Error occurred while checking status on server ", serverError);
                    reject(buildJSON(false, 7, "Payment Failed.", "Payment has failed, Please try again.", null));
                });
            }, s2sCheckInMilli);
            messageForAGS = messageForAGS + "|" + printFlag + "<ETXN>";
            callAGSPOSGateway(messageForAGS, generatedOrderId)
                .then(responseString => {
                    console.log('Machine call responseString:: ' + responseString);
                    clearTimeout(serverCallTimerId);
                    resolve(responseString);
                }).catch(errorString => {
                console.log('Machine call errorString:: ' + errorString);
                clearTimeout(serverCallTimerId);
                reject(errorString);
            });
        }catch (e) {
            reject(buildJSON(false, 15, "Error Occurred.", "Some error occurred while making sale request.", null));
        }
    });
}

export function checkAGSPayment(externalOrderId) {
    return new Promise(function (resolve, reject) {
        try {
            console.log("checkAGSPayment request ! for externalOrderId: ", externalOrderId);
            axios({
                method: "POST",
                url: apis.getUrls().payment.agsCheckStatus,
                data: JSON.stringify(externalOrderId),
                headers: {'Content-Type': 'application/json'}
            }).then((response) => {
                console.log('check AGS payment status response', response);
                resolve(response);
            }).catch((error) => {
                console.log('check AGS payment status error', error);
                reject(error)
            });
        } catch (e) {
            console.log('check AGS payment status error', error);
            reject(e)
        }
    });
}

function callAGSPOSGateway(msgToSend, generatedOrderId) {
    return new Promise(function(resolve, reject) {
        console.log("Sale request to be sent:  ", msgToSend);
        let client = new W3CWebSocket('ws://localhost:8088');
        client.onopen = function () {
            if (client.readyState === client.OPEN) {
                client.send(msgToSend.toString());
            }
        };
        client.onerror = function (e) {
            reject(buildJSON(false, 4, "Not connected", "Card Machine is not connected properly, Please check its connection.", null));
        };
        client.onclose = function (e) {
            reject(buildJSON(false, 3, "Gateway Down", "AGS Gateway service is not running in background.", null));
        };
        client.onmessage = function (e) {
            if (typeof e.data === 'string') {
                let resFromMachine = e.data;
                if(isExists(resFromMachine, 'ERROR:: The port is closed.')){
                    reject(buildJSON(false, 4, "Not connected", "Card Machine is not connected properly, Please check its connection.", null));
                }
                resolve(parseAGSResponse(e.data, generatedOrderId));
            }
        };
    });
}


function parseAGSResponse(agsRawData, generatedOrderId) {
    let agsResponse = {};
    if (appUtil.checkEmpty(agsRawData)) {
        return buildJSON(false, 5, "Blank Response", "Card Machine did not return a response.", null);
    }
    if (!appUtil.checkEmpty(agsRawData)) {

        agsRawData = removeTags(agsRawData);

        if(isExists(agsRawData, symbol_tilde)){
            let rawDataArray = agsRawData.split(symbol_tilde);
            console.log("ResponseArray:: " + rawDataArray);
            if(!appUtil.checkEmpty(rawDataArray)) {
                let statusCode = rawDataArray[0];
                if(statusCode === '00' || statusCode === '01'){
                    let trnData = buildTransObject(statusCode, generatedOrderId, rawDataArray);
                    if(statusCode === '00'){
                        return buildJSON(true, 6, "Payment Success.", "Payment was successful, Now placing your order.", trnData);
                    }
                    if(statusCode === '01'){
                        return buildJSON(false, 7, "Payment Failed.", "Payment has failed, Please try again.", trnData);
                    }
                }
                if(isExists(agsRawData, 'TRANSACTION CANCELLED')){
                    return buildJSON(false, 8, "Payment Cancelled.", "Payment has been canceled by the user, Failed to place an order, Please try again.", null);
                }
                if(isExists(agsRawData, 'VOID Failed')){
                    return buildJSON(false, 9, "Payment void.", "Payment was void, Failed to place an order, Please try again.", null);
                }
            }
        }
        if(isExists(agsRawData, symbol_hyphen) && isExists(agsRawData, 'Error')){
            if(isExists(agsRawData, 'TRANSACTION FAILED') || isExists(agsRawData, 'TRANSACTION DECLINED')){
                return buildJSON(false, 10, "Payment failed.", "Payment has failed, if money is deducted, it will be refunded soon, Please try again.", null);
            }
            if(isExists(agsRawData, 'CHIP MALFUNCTION')){
                return buildJSON(false, 11, "Card Error.", "Could not read the card, Please try again and insert the card properly.", null);
            }
            if(isExists(agsRawData, 'Already refunded')){
                return buildJSON(false, 12, "Already Refunded", "Amount had been already refunded, Please place the order again.", null);
            }
            if(isExists(agsRawData, 'No RecORd Found')){
                return buildJSON(false, 13, "No record found.", "No transaction records found.", null);
            }
            if(isExists(agsRawData, 'detail not found')){
                return buildJSON(false, 14, "No details found", "No transaction details found", null);
            }
        }
    }
}

function buildTransObject(statusCode, generatedOrderId, rawDataArray) {
    if(appUtil.checkEmpty(rawDataArray)){
        return null;
    }
    return {
        statusCode: statusCode,
        orderId: generatedOrderId,
        txnId: rawDataArray[6],
        reverseReferenceNumber: rawDataArray[3],
        authCode: rawDataArray[4],
        batchNo: rawDataArray[5],
        invoice: rawDataArray[9],
        card: rawDataArray[7],
        cardName: rawDataArray[8],
        amount: rawDataArray[10],
        terminalId: rawDataArray[11],
        merchantId: rawDataArray[12],
        tipAmount: rawDataArray[13],
        applicationName: rawDataArray[14],
        AID: rawDataArray[15],
        TVR: rawDataArray[16],
        TSI: rawDataArray[17]
    };
}

function removeTags(agsRawData) {
    if (isExists(agsRawData, '<STXN>')) {
        agsRawData = agsRawData.replace('<STXN>', '');
    }
    if (isExists(agsRawData, '<ETXN>')) {
        agsRawData = agsRawData.replace('<ETXN>', '');
    }
    return agsRawData;
}

function buildJSON(status, code, title,  msg, trnData) {
    if(appUtil.checkEmpty(trnData)){
        trnData = {};
    }
    let data = {'status' : status, 'code' : code, 'title' : title, 'msg' : msg , 'trnData': trnData};
    return JSON.stringify(data);
}

function isExists(value, status){
    if(!appUtil.checkEmpty(value) && !appUtil.checkEmpty(status)){
        value = value.toLowerCase();
        status = status.toLowerCase();
        if((value.indexOf(status) !== -1)){
            return true;
        }
    }
    return false;
}