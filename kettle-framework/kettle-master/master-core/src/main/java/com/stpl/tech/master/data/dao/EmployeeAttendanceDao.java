package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.EmployeeAttendance;
import com.stpl.tech.master.domain.model.EligibilityType;
import com.stpl.tech.master.domain.model.MappingType;
import com.stpl.tech.master.domain.model.SystemStatus;

import java.util.List;

/**
 * DAO interface for Employee Eligibility Mapping operations
 */
public interface EmployeeAttendanceDao extends AbstractMasterDao {
    /**
     * Find mappings by employee ID
     * @param empId employee ID
     * @return list of mappings
     */
    List<EmployeeAttendance> findByEmpId(String empId);
    List<EmployeeAttendance> findByEmpIdValueAndType(String empId, String value , MappingType mappingType , EligibilityType eligibilityType);
    List<EmployeeAttendance> findByEmpIdIn(List<Long> empIds);
}
