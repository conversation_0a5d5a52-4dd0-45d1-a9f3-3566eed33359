//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.02.22 at 07:12:15 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PriceUpdateEventType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="PriceUpdateEventType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="SKU_PRICE_UPDATE"/&gt;
 *     &lt;enumeration value="PRODUCT_PRICE_UPDATE"/&gt;
 *     &lt;enumeration value="RECIPE_PRICE_UPDATE"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "PriceUpdateEventType")
@XmlEnum
public enum PriceUpdateEventType {

    SKU_PRICE_UPDATE,
    PRODUCT_PRICE_UPDATE,
    RECIPE_PRICE_UPDATE;

    public String value() {
        return name();
    }

    public static PriceUpdateEventType fromValue(String v) {
        return valueOf(v);
    }

}
