ALTER TABLE KETTLE_MASTER_DEV.APP_OFFER_DETAIL_DATA
ADD COLUMN TITLE VARCHAR(45);



CREATE TABLE KETTLE_MASTER_DEV.BANNER_METADATA(
BANNER_METADATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
NAME VARCHAR(50) NOT NULL,
VALUE VARCHAR(200) NULL,
TYPE VARCHAR(20)  NULL
);

ALTER TABLE KETTLE_MASTER_DEV.BANNER_METADATA
ADD COLUMN DESCRIPTION VARCHAR(200) NULL;

ALTER TABLE  KETTLE_MASTER_DEV.APP_OFFER_DETAIL_DATA
ADD COLUMN ACTION_CATEGORY VARCHAR(45);



INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL`
(`ACTION_DETAIL_ID`, `ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('264', 'CSCMRM', '5', 'ACTION', 'VIEW', 'ADMIN-> SCM RECIPE MANAGEMENT -> CREATE RECIPE ->  -> SHOW', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL`
(`ACTION_DETAIL_ID`, `ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('265', 'ASCMRM', '5', 'ACTION', 'VIEW', 'ADMIN-> SCM RECIPE MANAGEMENT -> APPOVE ->  -> SHOW', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING`
 (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES ('1', '264', 'ACTIVE', '120063', '2020-10-21 00:00:00');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING`
 (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES ('1', '265', 'ACTIVE', '120063', '2020-10-21 00:00:00');



INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`)
VALUES ('Force Pr  ', 'Access to do force Pr', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('FPRA', '7', 'ACTION', 'APPROVE', 'SUMO -> CREATE PAYMENT REQUEST -> FORCE PR', 'ACTIVE');


INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Force Pr'), (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FPRA'), 'ACTIVE', '120063', '2020-11-02 00:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`)
VALUES ('Force Day Close', 'Access to do force Dc', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('FKSDC', '1', 'ACTION', 'APPROVE', 'Kettle-Service -> FORCE DAY CLOSE', 'ACTIVE');

 INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Force Day Close'), (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FKSDC'), 'ACTIVE', '120063', '2020-11-30 00:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('SUMO_FUPRI', '7', 'ACTION', 'SHOW', 'Sumo -> force update payement request invoice -> show', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Approve Regular Vendor Gr'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VRECAGR'), 'ACTIVE', '120063', '2021-05-10 00:00:00');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VRECAGR', '7', 'SUBMENU', 'SHOW', 'SuMo -> Vendor Receivings -> APPROVE QUALITY', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Approve Regular Vendor Gr', 'Access to Approve Reguar Vendor Gr', 'ACTIVE', '7');





INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Approve Purchase Order L1'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VAPOL1'), 'ACTIVE', '120063', '2021-05-26 00:00:00');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VAPOL1', '7', 'ACTION', 'APPROVE', 'SuMo -> Vendor Purcharse Order-> APPROVE PO L1', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Approve Purchase Order L1', 'Access to Approve Purchase Order L1', 'ACTIVE', '7');






INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Approve Purchase Order L2'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VAPOL2'), 'ACTIVE', '120063', '2021-05-26 00:00:00');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VAPOL2', '7', 'ACTION', 'APPROVE', 'SuMo -> Vendor Purcharse Order-> APPROVE PO L2', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Approve Purchase Order L2', 'Access to Approve Purchase Order L2', 'ACTIVE', '7');




INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Approve Purchase Order L3'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VAPOL3'), 'ACTIVE', '120063', '2021-05-26 00:00:00');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VAPOL3', '7', 'ACTION', 'APPROVE', 'SuMo -> Vendor Purcharse Order-> APPROVE PO L3', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Approve Purchase Order L3', 'Access to Approve Purchase Order L3', 'ACTIVE', '7');


INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VEREQC', '7', 'ACTION', 'APPROVE', 'SuMo -> scm-service -> Approve Gr Quality Check-> APPROVE GR QUALITY', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Approve Gr Quality', 'Access to Approve Gr Quality', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Approve Gr Quality'),
(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VEREQC'), 'ACTIVE', '120063', '2021-06-02 00:00:00');




INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VIDAIC', '7', 'ACTION', 'VIEW', 'SUMO -> SCM-SERVICE ->  VENDOR INVOICING -> CANCEL DISPATCHED INVOICE ' , 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('CANCEL DISPATCHED INVOICE', 'Access to Cancel Invoice After Dispatch', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'CANCEL DISPATCHED INVOICE'),
(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VIDAIC'), 'ACTIVE', '120063', '2021-06-30 00:00:00');



