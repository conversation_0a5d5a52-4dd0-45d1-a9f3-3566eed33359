/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2020] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("AppNotificationController", function ($rootScope, $scope, $http, $location, $cookieStore, AppUtil, fileService) {

    $scope.init = function () {
         $scope.userDetails=AppUtil.getUserValues();
        $scope.companyId = 1000;
        $scope.selectedCompanyId = 1000;
        $scope.showTable = false;
        //  console.log(AppUtil.formatDate(AppUtil.getDate(),"dd-MM-yyyy-hh-mm-ss"));
        $scope.isSilent = false;
        $scope.isCustomer = false;
        $scope.startDate = $scope.dateformatting(yesterday())
        $scope.endDate = $scope.dateformatting(AppUtil.getDate())
        console.log($scope.dateformatting(yesterday()));


        function yesterday() {
            var currentDate = new Date();
            // //console.log(currentDate);
            currentDate = currentDate.setDate(currentDate.getDate() - 1);
            return currentDate;
        }

        $http({
            method: 'GET',
            url: AppUtil.restUrls.appNotification.notificationType + "/" + $scope.companyId
        }).then(function success(response) {
            $scope.notificationType = response.data;
            console.log($scope.notificationType);
        }, function error(response) {
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.companies
        }).then(function success(response) {
            $scope.companiesList = response.data;
            console.log($scope.companiesList);
        }, function error(response) {
            console.log("error:" + response);
        });


        $http({
            method: 'GET',
            url: AppUtil.restUrls.appNotification.notificationCategory + "/" + $scope.companyId
        }).then(function success(response) {
            $scope.notificationCategory = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.appNotification.notificationActionType + "/" + $scope.companyId
        }).then(function success(response) {
            $scope.notificationActionType = response.data;

        }, function error(response) {
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.appNotification.notificationTopic + "/" + $scope.companyId
        }).then(function success(response) {
            $scope.notificationTopic = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });

        $http({
            method: 'GET',
            url: AppUtil.restUrls.appNotification.getTestAccounts + "/" + $scope.companyId
        }).then(function success(response) {
            $scope.alreadyPresentAccounts = response.data.contact.map(function (item) {
                return item;

            });
        }, function error(response) {
            console.log("error:" + response);
        });

    }

    $scope.addNewAppNotificationDialog = function () {
        $scope.reset();
        $("#AddNewAppNotificationModalData").modal("show");
    }

    $scope.submitNotification = function (category, type, company, campaignName, description, title, message, actionType, topic,
                                          actionButtonText, appBlockerAction, appBlockerBanner, appBlockerSubTitle, appBlockerTitle) {
        if (!category) {
            alert("please provide notification category")
            return;
        }
        if (!type) {
            alert("please provide notification type")
            return;
        }

        if(type.code==="APP_BLOCKER"){
            if (!actionButtonText) {
                alert("please provide app blocker action button name");
                return;
            }

            if (!appBlockerAction) {
                alert("please provide app blocker action");
                return;
            }
            if (!appBlockerBanner) {
                alert("please provide app blocker image path");
                return;
            }

            if (!appBlockerSubTitle) {
                alert("please provide app blocker subtitle");
                return;
            }
            if (!appBlockerTitle) {
                alert("please provide app blocker title");
                return;
            }

        }
        if (!company) {
            alert("please provide company details")
            return;
        }
        if (!campaignName) {
            alert("please enter campaign name")
            return;
        }
        if (!description) {
            alert("please enter campaign description")
            return;
        }
        if (!title && !$scope.inputExcel) {
            alert("please enter  notification title ")
            return;
        }
        if (!message && !$scope.inputExcel) {
            alert("please enter  notification body or messsage")
            return;
        }
        // if (!$scope.imagePath && !$scope.inputExcel) {
        //     alert("please provide image path")
        //     return;
        // }
        if (!actionType) {
            alert("please provide notification action type")
            return;
        }
        if (!topic) {
            alert("please provide notification topic")
            return;
        }
        // if ($scope.isSingleCustomer && !singleCustomer) {
        //     alert("please provide contact number")
        //     return;
        // }
        var payload = {
            category: category.code,
            categoryName: category.name,
            notificationType: type.code,
            notificationDesc: type.name,
            companyId: company.id,
            campaignName: campaignName,
            campaignDescription: description,
            title: title,
            body: message,
            image: $scope.imagePath,
            action: actionType.code,
            actionName: actionType.type,
            topic: topic.code,
            topicSuffix: topic.topicSuffix,
            requestTime: AppUtil.getDate(),
            notificationTime: null,
            status: "INACTIVE",
            createdBy: $scope.userDetails.user.name + "[" + $scope.userDetails.user.id + "]",
            tested: null,
            bulkFilePath: null,
            testCount: null,
            requestedCount: null,
            sendCount: null,
            customer: category.status,
            scheduled: type.status,
            actionButtonText: null,
            appBlockerAction: null,
            appBlockerBanner: null,
            appBlockerSubTitle: null,
            appBlockerTitle: null,
        }
        if ($scope.inputExcel) {
            payload.bulkFilePath = $scope.bulkfilePath;
        }
        if (type.code == "APP_BLOCKER") {
            payload.actionButtonText = actionButtonText;
            payload.appBlockerAction = appBlockerAction;
            payload.appBlockerBanner = appBlockerBanner;
            payload.appBlockerSubTitle = appBlockerSubTitle;
            payload.appBlockerTitle = appBlockerTitle;
        }
        // if($scope.isSingleCustomer){
        //     payload.singleCustomerContact=singleCustomer;
        // }
        $rootScope.showDetailLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.appNotification.addNotificationRequest + "/" + $scope.companyId,
            data: payload
        }).then(function success(response) {
            $rootScope.showDetailLoader = false;
            alert("Notification Added");
            $scope.isCustomer = false;
            $scope.isSilent = false;
            $scope.showTable = false;
            $("#AddNewAppNotificationModalData").modal("hide");
            $scope.reset();
        }, function error(response) {
            console.log("error:" + response);
        });

    }

    $scope.onSelectCategory = function (category) {
        $scope.inputExcel = false;
        // $scope.isSingleCustomer = false;
        if (category.status) {
            $scope.isCustomer = true;
        }
        else {
            $scope.isCustomer = false;
        }


        if (category.code == "EXCEL_BASED_NOTIFICATION") {
            $scope.inputExcel = true;
        }
        // if (category.code == "SINGLE_CUSTOMER") {
        //     $scope.isSingleCustomer = true;
        // }

    }
    $scope.onSelectType = function (type) {
        console.log(type);
        if (type.status) {
            $scope.isSilent = true;
        } else {
            $scope.isSilent = false;
        }
    }

    $scope.reset = function () {
        $scope.selectedNotificationCategory = null;
        $scope.selectedNotificationType = null;
        $scope.campaignName = null;
        $scope.description = null;
        $scope.title = null;
        $scope.message = null;
        $scope.imagePath = null;
        $scope.selectedCompanyId = null;
        $scope.selectedActionType = null;
        $scope.selectedTopic = null;
        angular.element("input[type='file']").val(null);
        fileService.push(null);
        $scope.inputExcel = false;
        $scope.appBlockerActionButton = null,
        $scope.appBlockerActionType = null,
        $scope.appBlockerBanner = null,
        $scope.appBlockerSubTitle = null,
        $scope.appBlockerTitle = null
    }

    $scope.dateformatting = function (startDate) {
        var year = new Date(startDate).getFullYear();
        var month = new Date(startDate).getMonth() + 1;
        var day = new Date(startDate).getDate();
        if (day >= 1 && day < 10)
            day = '0' + day;
        if (month >= 1 && month < 10)
            month = '0' + month;
        return year + "-" + month + "-" + day;
    }

    $scope.findAll = function (start, end) {
        $scope.showTable = true;
        $rootScope.showDetailLoader = true;
        $http({
            method: 'GET',
            url: AppUtil.restUrls.appNotification.getNotificationRequest + "/" + $scope.companyId + "?startDate=" + start + "&endDate=" + end
        }).then(function success(response) {
            $scope.notificationList = response.data;
            $rootScope.showDetailLoader = false;
            console.log($scope.notificationList);
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.openTestModal = function (notification) {
        $scope.requestNotification = notification;
        $("#TestApplicationModal").modal("show");

    }

    $scope.testApplication = function (additional, isUsingAdditional) {
        if (!$scope.additionalAccounts && !$scope.alreadyPresentAccounts) {
            bootbox.alert("please enter one of the account");
            return;
        }
        if (isUsingAdditional == true) {
            $scope.additionalAccountList = additional.split(",").map(function (item) {
                return item.trim();
            });
        } else {
            $scope.additionalAccountList = null;
        }
        $rootScope.showDetailLoader = true;
        console.log($scope.inputExcel);
        var payload = {
            requestId: $scope.requestNotification.id,
            testAccounts: $scope.alreadyPresentAccounts,
            additionalAccounts: $scope.additionalAccountList,
            testTime: AppUtil.getDate(),
            createdBy: $scope.userDetails.user.name + "[" + $scope.userDetails.user.id + "]",
            testCount: null,
            requestedCount: null,
            topic: $scope.requestNotification.topic,
            status: $scope.requestNotification.status
        }
        $http({
            method: 'POST',
            url: AppUtil.restUrls.appNotification.testNotification + "/" + $scope.companyId,
            data: payload
        }).then(function success(response) {
            console.log(response)
            bootbox.alert("Notification sent to these numbers" + " " + response.data.contact);
            $scope.additionalAccounts = null;
            $scope.useAdditional = false;
            $("#TestApplicationModal").modal("hide");
            $scope.findAll($scope.startDate, $scope.endDate);
            $rootScope.showDetailLoader = false;


        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.arrayToString = function (string) {
        if (Array.isArray(string)) {
            return string.join(", ");
        }
    };

    $scope.sendNotification = function (notification) {
        console.log(notification);
        // var payload= {
        //
        // }
        var result = confirm("Are You Sure You Want To Send Notification");
        if (!result) {
            return;
        }
        $rootScope.showDetailLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.appNotification.sendNotification + "/" + $scope.companyId,
            data: notification.id
        }).then(function success(response) {
            $scope.findAll($scope.startDate, $scope.endDate);
            $rootScope.showDetailLoader = false;
            bootbox.alert("Notification sent")
            console.log(response);

        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.uploadExcelFile = function (campaignName) {
        if (fileService.getFile() == null
            || fileService.getFile() == undefined) {
            bootbox.alert("Please select a .xlsx file");
            return;
        }
        if (campaignName == null) {
            alert("Please select campaign name");
            return;
        }
        // else{
        //     bootbox.alert("you have succesfully input file");
        //     console.log(fileService.getFile())
        //     return;
        // }
        var fileExt = getFileExtension(fileService.getFile().name);
        console.log(typeof fileService.getFile().name);
        console.log(fileExt);
        if (isCorrectFile(fileExt)) {
            var fd = new FormData();
            fd.append("name", campaignName);
            fd.append("extension", fileExt);
            fd.append("file", fileService.getFile());
            console.log(fd);
            $rootScope.showDetailLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.appNotification.saveFile + "/" + $scope.companyId,
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).then(function success(response) {
                alert("file uploaded");
                $rootScope.showDetailLoader = false;
                $scope.bulkfilePath = response.data.path;
                console.warn(response);
                console.log($scope.bulkfilePath);

            }, function error(response) {
                console.log("error:" + response);
            });
        }
    }



    $scope.uploadImage = function () {

        if (fileService.getFile() == null
            || fileService.getFile() == undefined) {
            bootbox.alert("Please select an Image");
            return false;
        }
        var fileExt = getFileExtension(fileService.getFile().name);

        if (isImage(fileExt.toLowerCase())) {
            var fd = new FormData();
            fd.append("mimeType", fileExt.toUpperCase());
            fd.append("couponCode", "");
            fd.append("imageType", "list");
            fd.append("file", fileService.getFile());
            $rootScope.showDetailLoader = true;
            var URL = AppUtil.restUrls.offerManagement.uploadImage;
            $http({
                url: URL,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showDetailLoader = false;
                angular.element("input[type='file']").val(null);
                fileService.push(null);
                console.log(response);
                $scope.imagePath=response.url;
                alert("Image Added");
            }).error(function (response) {
                $rootScope.showDetailLoader = false;
                alert("Error while uploading Image");
            });
        }
        else {
            alert("Cannnot upload image");
            return false;
        }
    }




    function isCorrectFile(fileExt) {
        return fileExt == "xls" || fileExt == "xlsx" || fileExt == "csv";
    }

    function isImage(fileExt) {
        return fileExt == "jpg" || fileExt == "jpeg" || fileExt == "png";
    }

    function getFileExtension(fileName) {
        var re = /(?:\.([^.]+))?$/;
        return re.exec(fileName)[1];
    }
});