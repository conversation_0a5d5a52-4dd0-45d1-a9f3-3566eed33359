package com.stpl.tech.spring.model;

public class FileDetail {

	private String bucket;

	private String key;

	private String name;

	private String url;

	public FileDetail() {
	}

	public FileDetail(String bucket, String key, String url) {
        this.key = key;
        this.url = url;
        this.bucket = bucket;
    }

	public FileDetail(String bucket, String key, String name, String url) {
		this.key = key;
		this.url = url;
		this.name = name;
		this.bucket = bucket;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getBucket() {
		return bucket;
	}

	public void setBucket(String bucket) {
		this.bucket = bucket;
	}

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
	public String toString() {
		return "FileDetail [bucket=" + bucket + ", key=" + key + ", url=" + url +  ", name=" + name + "]";
	}

}