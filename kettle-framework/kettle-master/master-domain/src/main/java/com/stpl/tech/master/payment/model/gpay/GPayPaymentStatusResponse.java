package com.stpl.tech.master.payment.model.gpay;

import java.io.Serializable;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.master.payment.model.PaymentStatus;

public class GPayPaymentStatusResponse implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1731726930686006377L;
	private static final Logger LOG = LoggerFactory.getLogger(GPayPaymentStatusResponse.class);
	private String paymentStatus;

	public String getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(String paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	public PaymentStatus getInternalPaymentStatus() {
		try {
			return GPayPaymentStatusType.valueOf(paymentStatus).getStatus();

		} catch (Exception e) {
			LOG.error("Error in parsing GPAY payment status {}", paymentStatus, e);
			return PaymentStatus.FAILED;
		}
	}
}
