package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import java.util.ArrayList;
import java.util.List;

import com.stpl.tech.master.domain.model.IdValueUnit;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"vendorEntityId", "description", "name", "tags", "inStock", "variants", "is_visible",
        "properties", "imageUrl", "taxGroups", "charges"})
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZomatoCatalogues {

    @JsonProperty("vendorEntityId")
    private String vendorEntityId;
    @JsonProperty("description")
    private String description;
    @JsonProperty("name")
    private String name;
    @JsonProperty("inStock")
    private boolean inStock;
    @JsonProperty("tags")
    private List<String> tags = new ArrayList<>();
    @JsonProperty("is_visible")
    private String isVisible;
    @JsonProperty("variants")
    private List<ZomatoCatalogueVariant> variants = new ArrayList<>();
    @JsonProperty("properties")
    private List<ZomatoCatalogueProperties> properties = new ArrayList<>();
    @JsonProperty("imageUrl")
    private String imageUrl;
    @JsonProperty("taxGroups")
    private List<ZomatoTaxGroup> taxGroups = new ArrayList<>();
    @JsonProperty("charges")
    private List<ZomatoCatalogueCharges> charges = new ArrayList<>();
    @JsonProperty("kind")
    private String kind;
    @JsonProperty("nutritionInfo")
    private ZomatoNutritionInfo nutritionInfo;
    @JsonProperty("preparationTime")
    private String preparationTime;
    @JsonProperty("portionSize")
    private ZomatoPortionSize portionSize;
    @JsonProperty("meatTypes")
    private List<String> meatTypes;

    @JsonProperty("allergenTypes")
    private List<String> allergenTypes;

    @JsonProperty("servingInfo")
    private String servingInfo;

    @JsonProperty("servingSize")
    private IdValueUnit servingSize;

    @JsonProperty("taxGroups")
    public List<ZomatoTaxGroup> getTaxGroups() {
        return taxGroups;
    }

    @JsonProperty("taxGroups")
    public void setTaxGroups(List<ZomatoTaxGroup> taxGroups) {
        this.taxGroups = taxGroups;
    }

    @JsonProperty("charges")
    public List<ZomatoCatalogueCharges> getCharges() {
        return charges;
    }

    @JsonProperty("charges")
    public void setCharges(List<ZomatoCatalogueCharges> charges) {
        this.charges = charges;
    }

    @JsonProperty("description")
    public String getDescription() {
        return description;
    }

    @JsonProperty("description")
    public void setDescription(String description) {
        this.description = description;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("inStock")
    public boolean isInStock() {
        return inStock;
    }

    @JsonProperty("inStock")
    public void setInStock(boolean inStock) {
        this.inStock = inStock;
    }

    @JsonProperty("tags")
    public List<String> getTags() {
        return tags;
    }

    @JsonProperty("tags")
    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    @JsonProperty("vendorEntityId")
    public String getVendorEntityId() {
        return vendorEntityId;
    }

    @JsonProperty("vendorEntityId")
    public void setVendorEntityId(String vendorEntityId) {
        this.vendorEntityId = vendorEntityId;
    }

    @JsonProperty("is_visible")
    public String getIsVisible() {
        return isVisible;
    }

    @JsonProperty("is_visible")
    public void setIsVisible(String isVisible) {
        this.isVisible = isVisible;
    }

    @JsonProperty("variants")
    public List<ZomatoCatalogueVariant> getVariants() {
        return variants;
    }

    @JsonProperty("variants")
    public void setVariants(List<ZomatoCatalogueVariant> variants) {
        this.variants = variants;
    }

    @JsonProperty("properties")
    public List<ZomatoCatalogueProperties> getProperties() {
        return properties;
    }

    @JsonProperty("properties")
    public void setProperties(List<ZomatoCatalogueProperties> properties) {
        this.properties = properties;
    }

    @JsonProperty("imageUrl")
    public String getImageUrl() {
        return imageUrl;
    }

    @JsonProperty("imageUrl")
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    @JsonProperty("kind")
    public String getKind() {
        return kind;
    }

    @JsonProperty("kind")
    public void setKind(String kind) {
        this.kind = kind;
    }

    @JsonProperty("nutritionInfo")
    public ZomatoNutritionInfo getNutritionInfo() {
        return nutritionInfo;
    }

    @JsonProperty("nutritionInfo")
    public void setNutritionInfo(ZomatoNutritionInfo nutritionInfo) {
        this.nutritionInfo = nutritionInfo;
    }

    @JsonProperty("preparationTime")
    public String getPreparationTime() {
        return preparationTime;
    }

    @JsonProperty("preparationTime")
    public void setPreparationTime(String preparationTime) {
        this.preparationTime = preparationTime;
    }

    @JsonProperty("portionSize")
    public ZomatoPortionSize getPortionSize() {
        return portionSize;
    }

    @JsonProperty("portionSize")
    public void setPortionSize(ZomatoPortionSize portionSize) {
        this.portionSize = portionSize;
    }

    @JsonProperty("meatTypes")
    public List<String> getMeatTypes() {
        return meatTypes;
    }

    @JsonProperty("meatTypes")
    public void setMeatTypes(List<String> meatTypes) {
        this.meatTypes = meatTypes;
    }

    @JsonProperty("allergenTypes")
    public List<String> getAllergenTypes() {
        return allergenTypes;
    }

    @JsonProperty("allergenTypes")
    public void setAllergenTypes(List<String> allergenTypes) {
        this.allergenTypes = allergenTypes;
    }

    public String getServingInfo() {
        return servingInfo;
    }

    public void setServingInfo(String servingInfo) {
        this.servingInfo = servingInfo;
    }

    public IdValueUnit getServingSize() {
        return servingSize;
    }

    public void setServingSize(IdValueUnit servingSize) {
        this.servingSize = servingSize;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoCatalogues that = (ZomatoCatalogues) o;

        return new EqualsBuilder()
            .append(inStock, that.inStock)
            .append(vendorEntityId, that.vendorEntityId)
            .append(description, that.description)
            .append(name, that.name)
            .append(tags, that.tags)
            .append(isVisible, that.isVisible)
            .append(variants, that.variants)
            .append(properties, that.properties)
            .append(imageUrl, that.imageUrl)
            .append(taxGroups, that.taxGroups)
            .append(charges, that.charges)
            .append(kind, that.kind)
            .append(nutritionInfo, that.nutritionInfo)
            .append(preparationTime, that.preparationTime)
            .append(portionSize, that.portionSize)
            .append(meatTypes, that.meatTypes)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(vendorEntityId)
            .append(description)
            .append(name)
            .append(inStock)
            .append(tags)
            .append(isVisible)
            .append(variants)
            .append(properties)
            .append(imageUrl)
            .append(taxGroups)
            .append(charges)
            .append(kind)
            .append(nutritionInfo)
            .append(preparationTime)
            .append(portionSize)
            .append(meatTypes)
            .toHashCode();
    }

    @Override
    public String toString() {
        return "ZomatoCatalogues{" +
            "vendorEntityId='" + vendorEntityId + '\'' +
            ", description='" + description + '\'' +
            ", name='" + name + '\'' +
            ", inStock=" + inStock +
            ", tags=" + tags +
            ", isVisible='" + isVisible + '\'' +
            ", variants=" + variants +
            ", properties=" + properties +
            ", imageUrl='" + imageUrl + '\'' +
            ", taxGroups=" + taxGroups +
            ", charges=" + charges +
            ", kind='" + kind + '\'' +
            ", nutritionInfo=" + nutritionInfo +
            ", preparationTime='" + preparationTime + '\'' +
            ", portionSize=" + portionSize +
            ", meatTypes=" + meatTypes +
            '}';
    }
}
