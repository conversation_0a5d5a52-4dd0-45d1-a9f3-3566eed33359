package com.stpl.tech.master.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.SessionCache;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.core.service.MasterCacheManagementService;
import com.stpl.tech.master.core.service.RecipeService;
import com.stpl.tech.master.core.service.SessionCacheService;
import com.stpl.tech.master.core.service.TaxDataCacheService;
import com.stpl.tech.util.EmailGenerationException;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;


@Component
@Log4j2
@ConditionalOnProperty(name = "scheduling.enabled", havingValue = "true", matchIfMissing = false)
public class SchedulerService {

    @Autowired
    private SessionCache cache;
    @Autowired
    private OfferManagementService offerService;
    @Autowired
    private SessionCacheService sessionCacheService;
    @Autowired
    private TaxDataCacheService taxCacheService;
    @Autowired
    private RecipeService recipeService;
    @Autowired
    private MasterCacheManagementService masterCacheManagementService;

    @Scheduled(cron = "0 0 6 * * *", zone = "GMT+05:30")
    public void removeFromQueue() throws InterruptedException {
        cache.getUnitToSessionMap().clear();
    }

    @Scheduled(cron = "0 2 6 * * *", zone = "GMT+05:30")
    public void removeExpiredSessions() throws InterruptedException {
        cache.removeExpiredSessions();
    }

    @Scheduled(cron = "0 15 05 * * *", zone = "GMT+05:30")
    public void archiveOffers() {
        offerService.markExpiredOffersAsArchived();
    }

    @Scheduled(cron = "0 20 05 * * *", zone = "GMT+05:30")
    public void refreshCache() {
        log.info("CRON to refresh all pos session");
        try {
            log.info("Request to remove all pos sessions");
            sessionCacheService.clearAllPosSession();
            log.info("Clear all POS session :: SUCCESSFUL");
        } catch (Exception e) {
            log.error("Clear all POS session :: FAILED", e);
        }
    }

    @Scheduled(cron = "0 15 05 * * *", zone = "GMT+05:30")
    public void refreshCacheOnCron() throws DataNotFoundException, IOException, EmailGenerationException {
        log.info("CRON to refresh master cache");
        refreshCache(true);
    }

    public Boolean refreshCache(boolean withInventory) throws DataNotFoundException{
        try {
            taxCacheService.loadCache();
            int activatedRecipeIds = recipeService.refreshRecipeCache();
            masterCacheManagementService.refreshMasterCache(activatedRecipeIds >0 && withInventory);
            log.info("Refresh Master Cache :: SUCCESSFUL");

        } catch (Exception e) {
            log.error("Refresh Master Cache :: FAILED", e);
            return false;
        }
        return true;
    }


}
