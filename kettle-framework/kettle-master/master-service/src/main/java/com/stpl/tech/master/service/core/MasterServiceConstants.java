/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.service.core;

/**
 * Created by <PERSON><PERSON> on 19-04-2016.
 */
public class MasterServiceConstants {

    public static final String API_VERSION = "v1";

    public static final String SEPARATOR = "/";

    public static final String USER_SERVICES_ROOT_CONTEXT = "users";

    public static final String RECIPE_SERVICES_ROOT_CONTEXT = "recipe";
    
    public static final String SCM_RECIPE_SERVICES_ROOT_CONTEXT = "scm-recipe";

    public static final String USER_MANAGEMENT_SERVICES_ROOT_CONTEXT = "user-management";

    public static final String UNIT_METADATA_ROOT_CONTEXT = "unit-metadata";

    public static final String ENTITY_ALIAS_ROOT_CONTEXT = "alias-management";

    public static final String PRODUCT_METADATA_ROOT_CONTEXT = "product-metadata";
    public static final String GENERIC_EXCEL_ROOT_CONTEXT = "generic-excel-management";
    public static final String PRICE_PROFILE_METADATA_ROOT_CONTEXT = "price-profile";

    public static final String OFFER_MANAGEMENT_ROOT_CONTEXT = "offer-management";

    public static final String ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT = "access-control-management";
    
    public static final String EXTERNAL_PARTNER_ROOT_CONTEXT = "external-partner";

    public static final String MASTER_CACHE_MANAGEMENT_ROOT_CONTEXT = "master-cache-management";

    public static final String SESSION_CACHE_MANAGEMENT_ROOT_CONTEXT = "session-cache-management";

    public static final String KIOSK_MANAGEMENT_ROOT_CONTEXT = "kiosk-management";

    public static final String REDIS_CACHE_ROOT_CONTEXT = "redis-cache";

    public static final String CHARSET = "utf-8";
    
    public static final String TAX_METADATA_ROOT_CONTEXT = "tax-metadata";
    
    public static final String FICO_METADATA_ROOT_CONTEXT = "fico-metadata";
    
    public static final String MASTER_METADATA_ROOT_CONTEXT = "metadata";
    
    public static final String APPLICATION_INSTALLATION_ROOT_CONTEXT = "application-installation";

    public static final String BRAND_METADATA_ROOT_CONTEXT = "brand-metadata";

    public static final String CHANNEL_PARTNER_ROOT_CONTEXT = "channel-partner";

    public static final String BANNER_ROOT_CONTEXT = "banner-management";

    public static final String PAYMENT_MANAGEMENT_ROOT_CONTEXT = "payment-management";

    public static final String EMPLOYEE_MANAGEMENT = "employee-management";

    public static final String EMPLOYEE_ATTENDANCE_ROOT_CONTEXT = "employee-attendance";

    public static final String GOOGLE_CHAT_ROOT_CONTEXT = "google-chat";

    public static final String REPORT_CONTEXT = "report";

    public static final String UNIT_DROOL_RESOURCE_ROOT_CONTEXT = "unit-drool-management";

    public static final String LCD_MENU_IMAGE_ROOT_CONTEXT = "lcd-menu-image";

    public static final String MONK_META_DATA = "monk-metadata";

}