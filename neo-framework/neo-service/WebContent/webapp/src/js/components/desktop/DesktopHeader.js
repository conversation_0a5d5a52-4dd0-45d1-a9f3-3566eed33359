import React from "react";
import {browserHistory} from "react-router";
import {connect} from "react-redux";
import appUtil from "../../AppUtil";
import StorageUtils from "../../utils/StorageUtils";
import * as SidebarActions from "../../actions/SidebarActions";
import * as LocalityActions from "../../actions/LocalityActions";
import * as UtilityActions from "../../actions/UtilityActions";
import * as OutletMenuActions from "../../actions/OutletMenuActions";
import trackUtils from "../../utils/TrackUtils";

@connect((store) => {
    return {
        cart: store.cartManagementReducer.cart,
        showLocationWrapper: store.localityReducer.showLocationWrapper,
        selectedLocality: store.localityReducer.selectedLocality,
        selectedCity: store.localityReducer.selectedCity,
        selectedOutlet: store.localityReducer.selectedOutlet,
        criteria: store.localityReducer.criteria,
        showLocality: store.localityReducer.showLocality,
        showOutlet: store.localityReducer.showOutlet,
        cityStateMap: store.localityReducer.cityStateMap,
        restrictDineIn: store.utilityReducer.restrictDineIn,
        membershipType: store.utilityReducer.membershipType
    };
})
export default class DesktopHeader extends React.Component {

    constructor() {
        super();
        this.state = {
            /*showLocalityWrapper: {display: "none"},
            showOutletWrapper: {display: "none"},
            locality: {value: 0, label: ""},
            city: null,
            outlet: {value: 0, label: ""},*/
        };
        this.init = this.init.bind(this);
        this.processLocationClick = this.processLocationClick.bind(this);
        this.goToCart = this.goToCart.bind(this);
        this.toggleSidebar = this.toggleSidebar.bind(this);
        this.goBack = this.goBack.bind(this);
        this.toggleLocationWrapper = this.toggleLocationWrapper.bind(this);
    }

    init() {
        /*const data = StorageUtils.getLocalityMetadata();
        if (data != null && data.city != null) {
            if (data.criteria === "DELIVERY" && data.locality != null) {
                this.setState({
                    showLocalityWrapper: {display: "block"},
                    showOutletWrapper: {display: "none"},
                    locality: data.locality != null ? data.locality : {value: 0, label: ""},
                    city: data.city != null ? data.city : {value: 0, label: ""},
                    outlet: data.outlet != null ? data.outlet : {value: 0, label: ""}
                });
            }
            if (data.criteria === "TAKE_AWAY" && data.outlet != null) {
                this.setState({
                    showLocalityWrapper: {display: "none"},
                    showOutletWrapper: {display: "block"},
                    locality: data.locality != null ? data.locality : {value: 0, label: ""},
                    city: data.city != null ? data.city : {value: 0, label: ""},
                    outlet: data.outlet != null ? data.outlet : {value: 0, label: ""}
                });
            }
        }*/
    }

    toggleLocationWrapper() {
        var val = this.props.showLocationWrapper;
        if (val) {
            //var localityMetadata = StorageUtils.getLocalityMetadata();
            if (this.props.criteria == null) {
                this.props.dispatch(UtilityActions.showPopup("Please select delivery or pickup.", "info"));
            } else if (this.props.criteria == "DELIVERY" && this.props.selectedLocality == null) {
                this.props.dispatch(UtilityActions.showPopup("Please select delivery location.", "info"));
            } else if ((this.props.criteria == "TAKE_AWAY" || this.props.criteria == "DINE_IN") && this.props.selectedOutlet == null) {
                this.props.dispatch(UtilityActions.showPopup("Please select outlet for dine in/pickup.", "info"));
            } else {
                if (this.props.criteria != null && ((this.props.criteria == "DELIVERY" && this.props.selectedCity != null && this.props.selectedLocality != null) ||
                    (this.props.selectedOutlet != null))) {
                    this.props.dispatch(OutletMenuActions.getUnitProducts(this.props.criteria, this.props.selectedCity,
                        this.props.selectedLocality, this.props.selectedOutlet));
                }
                this.props.dispatch(LocalityActions.toggleLocationWrapper(!val));
            }
        } else {
            this.props.dispatch(LocalityActions.toggleLocationWrapper(!val));
        }
    }

    processLocationClick() {
        if (!this.props.restrictDineIn) {
            trackUtils.trackLocalityChangeClicked(window.location.pathname == "/" ? "home" : window.location.pathname.substring(1));
            //this.props.dispatch(LocalityActions.setCriteria(val));
            this.toggleLocationWrapper();
        }
    }

    goToCart() {
        browserHistory.push("/cart");
    }

    toggleSidebar() {
        this.props.dispatch(SidebarActions.toggleSidebar());
    }

    goBack() {
        if (StorageUtils.getMembershipFlag() == "true") {
            let type = this.props.membershipType;
            browserHistory.push("/membership/"+ type);
        } else {
            if (window.location.pathname == "/paymentModes") {
                trackUtils.trackReturnedFromPayment();
            }
            if (window.location.pathname == "/orderDetail") {
                browserHistory.push("/orders");
            } else {
                browserHistory.push("/menu");
            }
        }
    }

    componentWillMount() {
        this.init();
    }

    render() {
        var cartItems = 0;
        var cart = this.props.cart;
        if (!appUtil.checkEmpty(cart) && cart.orderDetail.orders.length > 0) {
            cartItems = cart.orderDetail.orders.length;
            cart.orderDetail.orders.map((orderItem) => {
                if (orderItem.productId == 1043 || orderItem.productId == 1044) {
                    cartItems--;
                }
            });
        }
        return (
            <div class='headerWrapper'>

                {this.props.menu ? (
                    <div class='headerBtn left' onClick={this.toggleSidebar.bind(this)}>
                        <img class="menuIcon" src="../../../img/menu.svg"/>
                    </div>
                ) : (
                    <div class='headerBtn left' onClick={this.goBack.bind(this)}>
                        <img class="menuIcon" src="../../../img/back.svg"/>
                    </div>:null
                )}
                <div class="headerLogo"><img src="/img/logo.svg"/></div>
                {this.props.showLocationMetadata ? (
                    <div style={{display: "inline-block"}}>
                        {this.props.showLocality ? (
                            <div class="localityWrapper ellipsis"
                                 onClick={this.processLocationClick.bind(this)}>
                                <div class="tagLine">Deliver To</div>
                                {this.props.selectedLocality != null ? this.props.selectedLocality.label + ", " : ""}{this.props.selectedCity.city}
                                <img class="downIcon" src="../../../img/iconDown.png"/>
                            </div>
                        ) : (null)}
                        {this.props.showOutlet ? (
                            <div class="outletWrapper ellipsis"
                                 onClick={this.processLocationClick.bind(this)}>
                                <div class="tagLine">{this.props.criteria == "DINE_IN" ? "Dine In" : "Take Away"}</div>
                                {this.props.selectedOutlet != null ? this.props.selectedOutlet.label : ""}
                                <img class="downIcon" src="../../../img/iconDown.png"/>
                            </div>
                        ) : (null)}
                    </div>
                ) : (null)}
                <span class="right">
                    {this.props.showCartBtn ? (
                        <span class="headerBtn rel" onClick={this.goToCart.bind(this)}>
                            {(cartItems > 0) ? (
                                <span class="cartSizeLabel">{cartItems}</span>
                            ) : (null)}
                            <img src="../../../img/cart.svg"/>
                        </span>
                    ) : (null)}
                </span>
            </div>
        )
    }
}
