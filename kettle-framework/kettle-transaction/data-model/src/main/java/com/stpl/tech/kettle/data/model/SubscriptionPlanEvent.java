package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "SUBSCRIPTION_PLAN_EVENT")
public class SubscriptionPlanEvent implements Serializable {

	private Integer subscriptionPlanEventId;
	private Integer subscriptionPlanId;
	private String subscriptionPlanCode;
	private Integer customerId;
	private String status;
	private Date planStartDate;
	private Date planEndDate;
	private Date renewalTime;
	private String eventType;
	private Integer productId;
	private String dimensionCode;
	private Integer orderId;
	private Integer orderItemId;
	private BigDecimal price;
	private Integer validityInDays;
	private Integer campaignId;
	private String subscriptionSource;
	private BigDecimal subscriptionSavings;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SUBSCRIPTION_PLAN_EVENT_ID", unique = true, nullable = false)
	public Integer getSubscriptionPlanEventId() {
		return subscriptionPlanEventId;
	}

	public void setSubscriptionPlanEventId(Integer subscriptionPlanEventId) {
		this.subscriptionPlanEventId = subscriptionPlanEventId;
	}

	@Column(name = "SUBSCRIPTION_PLAN_ID", nullable = false)
	public Integer getSubscriptionPlanId() {
		return subscriptionPlanId;
	}

	public void setSubscriptionPlanId(Integer subscriptionPlanId) {
		this.subscriptionPlanId = subscriptionPlanId;
	}

	@Column(name = "SUBSCRIPTION_PLAN_CODE", nullable = false, length = 200)
	public String getSubscriptionPlanCode() {
		return subscriptionPlanCode;
	}

	public void setSubscriptionPlanCode(String subscriptionPlanCode) {
		this.subscriptionPlanCode = subscriptionPlanCode;
	}

	@Column(name = "CUSTOMER_ID", nullable = false)
	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "PLAN_STATUS", nullable = false, length = 15)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "PLAN_START_DATE", nullable = true, length = 10)
	public Date getPlanStartDate() {
		return planStartDate;
	}

	public void setPlanStartDate(Date planStartDate) {
		this.planStartDate = planStartDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "PLAN_END_DATE", nullable = true, length = 10)
	public Date getPlanEndDate() {
		return planEndDate;
	}

	public void setPlanEndDate(Date planEndDate) {
		this.planEndDate = planEndDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "RENEWAL_TIME", nullable = false, length = 19)
	public Date getRenewalTime() {
		return renewalTime;
	}

	public void setRenewalTime(Date renewalTime) {
		this.renewalTime = renewalTime;
	}

	@Column(name = "DIMENSION_CODE", nullable = false, length = 100)
	public String getDimensionCode() {
		return dimensionCode;
	}

	public void setDimensionCode(String dimensionCode) {
		this.dimensionCode = dimensionCode;
	}

	@Column(name = "ORDER_ID", nullable = false)
	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Column(name = "ORDER_ITEM_ID", nullable = false)
	public Integer getOrderItemId() {
		return orderItemId;
	}

	public void setOrderItemId(Integer orderItemId) {
		this.orderItemId = orderItemId;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	@Column(name = "EVENT_TYPE", nullable = false, length = 50)
	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	@Column(name = "PRICE", precision = 10)
	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	@Column(name = "VALIDITY_IN_DAYS", nullable = false)
	public Integer getValidityInDays() {
		return validityInDays;
	}

	public void setValidityInDays(Integer validityInDays) {
		this.validityInDays = validityInDays;
	}

	@Column(name = "CAMPAIGN_ID")
	public Integer getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}

	@Column(name = "SUBSCRIPTION_SOURCE")
	public String getSubscriptionSource() {
		return subscriptionSource;
	}

	public void setSubscriptionSource(String subscriptionSource) {
		this.subscriptionSource = subscriptionSource;
	}

	@Column(name = "SUBSCRIPTION_SAVINGS")
	public BigDecimal getSubscriptionSavings(){return subscriptionSavings;}

	public void setSubscriptionSavings(BigDecimal subscriptionSavings){this.subscriptionSavings = subscriptionSavings;}
}
