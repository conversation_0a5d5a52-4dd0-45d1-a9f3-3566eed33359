package com.stpl.tech.kettle.core.service.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import com.corundumstudio.socketio.AckCallback;
import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.SocketIOServer;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.notification.StatusData;
import com.stpl.tech.master.core.external.cache.SessionCache;
import com.stpl.tech.master.core.service.model.ScreenType;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Copyright (C) $today.year, Sunshine Teahouse Private Limited - All Rights
 * Reserved Unauthorized copying of this file, via any medium is strictly
 * prohibited Proprietary and confidential Created by shi<PERSON> on 24-03-2017.
 */

//@Component
public class KettleSocketIOServer {

	private static final Logger LOG = LoggerFactory.getLogger(KettleSocketIOServer.class);
	private static final String ORDER_CHANNEL_EVENT = "order";
	private static final String ORDER_STATUS_CHANNEL_EVENT = "order-status";
	private static Map<UUID, SocketIOClient> CLIENTS = new HashMap<>();
	private static Map<String, UUID> TERMINALS = new HashMap<>();

//	@Autowired
	private SocketIOServer server;

//	@Autowired
	private SessionCache sessionCache;

//	@PostConstruct
	private void start() {
		LOG.info("starting socket io server");
		server.addConnectListener(socketIOClient -> {
			LOG.info("Client connected with UUID ::::::", socketIOClient.getSessionId().toString());
			CLIENTS.put(socketIOClient.getSessionId(), socketIOClient);
		});

		server.addEventListener("register", String.class, (client, sessionKeyId, ackRequest) -> {
			if (ackRequest.isAckRequested()) {
				ackRequest.sendAckData(Boolean.valueOf(true));
			}
			TERMINALS.put(sessionKeyId, client.getSessionId());
		});

		addTestListener();

        server.start();
	}

    private void addTestListener() {
        server.addEventListener("test", String.class, (client, message, ackRequest) -> {
            if (ackRequest.isAckRequested()) {
                ackRequest.sendAckData(Boolean.valueOf(true));
            }
            Map<String,String> messageMap = JSONSerializer.toJSON(message, Map.class);
            String id = "0";
            for(String key : messageMap.keySet()){
                id = key;
            }

            if(!id.equalsIgnoreCase("-1")){
                sendTestMessage(id,message);
            }else{
                sendTestMessageToAll(message);
            }
        });
    }


    public void sendOrderMessage(Integer unitId, OrderInfo order) {
		String sessionKey = sessionCache.getSessionKeyForScreenType(ScreenType.ASSEMBLY, unitId);
		sendToClient(ORDER_CHANNEL_EVENT,sessionKey, order.getOrder().getOrderId(), JSONSerializer.toJSON(order), 0);
	}

    public void sendTestMessageToAll(String message) {
        for(String id : TERMINALS.keySet()){
            sendToClient("test", id, 0, message, 0);
        }
    }

    public void sendTestMessage(String id, String message) {
        sendToClient("test", String.valueOf(id), 0, message, 0);
    }

	public void sendOrderStatus(Integer unitId, StatusData statusData) {
		String sessionKey = sessionCache.getSessionKeyForScreenType(ScreenType.ASSEMBLY, unitId);
		sendToClient(ORDER_STATUS_CHANNEL_EVENT, sessionKey, statusData.getOrderId(), JSONSerializer.toJSON(statusData), 0);
	}

	private void sendToClient(String channel, String sessionKey, int orderId, String message, int count) {

		if (sessionKey != null && count < 5) {
			SocketIOClient client = CLIENTS.get(TERMINALS.get(sessionKey));
			if (client != null) {
				client.sendEvent(channel,  new AckCallback<String>(String.class) {
					@Override
					public void onSuccess(String result) {
						if (result == null || !Boolean.valueOf(result)) {
							LOG.error(String.format(
									":::::::::::::Received the result while publishing the order %d to socket with result %s and count %d::::::::::: ",
									orderId, result, count));
							try {
								Thread.sleep(2000);
							} catch (InterruptedException e) {
								LOG.error(":::::::::::::Error in thread sleep event ::::::::::: " + result);
							}
							sendToClient(channel, sessionKey, orderId, message, count + 1);
						}
					}

					@Override
					public void onTimeout() {
						LOG.error(String.format(
								":::::::::::::Timeout while publishing the order %d to socket with count %d::::::::::: ",
								orderId, count));

						sendToClient(channel, sessionKey, orderId, message, count + 1);
					}

				}, message);
			}
		} else {
			LOG.error(String.format(
					":::::::::::::Failed to send order %d message to the client with count %d::::::::::: ", orderId,
					count));
		}
	}

//	@PreDestroy
	private void stop() {
		LOG.info("stopping socket io server");
		server.stop();
	}

}
