package com.stpl.tech.kettle.test.mongo.dao;

import com.stpl.tech.kettle.test.mongo.model.PartnerOrderDetail;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TestPartnerOrderDao extends MongoRepository<PartnerOrderDetail, String> {

    /*@Query("{'partnerId' : ?0, 'kettleOrder.orderId' : {$gt : ?1}, 'kettleOrder.orderId' : {$lt : ?2}}")
    List<PartnerOrderDetail> getOrdersInRange(String partnerId, int startOrderId , int endOrderId);*/

    @Query("{'partnerName' : ?0, 'kettleOrderId' : {$in : ?1} }")
    List<PartnerOrderDetail> searchAllByPartnerNameAndKettleOrderIdIn(String partnerName , List<String> orderIds );

    @Query("{'kettleOrderId' : ?0}")
    public PartnerOrderDetail searchByKettleOrderId(String id);
}