/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import com.stpl.tech.master.util.MasterUtil;
import org.subtlelib.poi.api.row.RowContext;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.totals.ColumnTotalsDataRange;
import org.subtlelib.poi.api.totals.Formula;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.kettle.reports.model.SettlementReportData;

public class SettlementReportView extends AbstractSettlementReportView<SettlementReportData> {

	public SettlementReportView(String header, String type) {
		super(header, type);
	}

	public void render(WorkbookContext workbookCtx, SettlementReportData data) {
		SheetContext sheetCtx = workbookCtx.createSheet(header);
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);
		RowContext context = sheetCtx.nextRow();
		context.skipCell().setTextStyle(headerStyle).text(type).setColumnWidth(25).text("GMV").setColumnWidth(15).text("Discount")
				.setColumnWidth(15).text("Net Sales Amount").setColumnWidth(18);
		createTaxationHeader(context, data.getTaxations());
		context.text("Round Off").setColumnWidth(15).text("Extra Vouchers").setColumnWidth(15).text("Total")
				.setColumnWidth(15).text("No.Of Bills");

		ColumnTotalsDataRange totalsData = sheetCtx.startColumnTotalsDataRangeFromNextRow();

		for (SettlementReport settlement : data.getSettlements()) {
			RowContext c = sheetCtx.nextRow();
			c.skipCell().text(settlement.getName()).number(settlement.getGrossAmount())
					.number(settlement.getDiscountAmount()).number(settlement.getAmount());
			createTaxationData(c, data.getTaxations(), settlement);
			c.number(settlement.getRoundOff()).number(settlement.getExtraVouchers()).number(settlement.getTotal())
					.number(settlement.getNoOfBills());
		}
		if (data.getSettlements().size() > 0) {
			sheetCtx.nextRow().nextRow().setTotalsDataRange(totalsData).setTextStyle(headerStyle).text("Total:").skipCell().total(Formula.SUM)
					.total(Formula.SUM).total(Formula.SUM).total(Formula.SUM).total(Formula.SUM).total(Formula.SUM)
					.total(Formula.SUM).total(Formula.SUM).total(Formula.SUM).total(Formula.SUM).total(Formula.SUM)
					.total(Formula.SUM).total(Formula.SUM).total(Formula.SUM);
		}

	}

}
