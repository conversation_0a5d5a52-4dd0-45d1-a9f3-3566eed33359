import React from "react";
import {connect} from "react-redux";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import MobileUtilityLayout from "./mobile/MobileUtilityLayout";
import DesktopUtilityLayout from "./desktop/DesktopUtilityLayout";
import * as UtilityActions from "../actions/UtilityActions";
import * as CampaignManagementActions from "../actions/CampaignManagementActions";

///https://github.com/balloob/react-sidebar#installation side bar repo

@connect((store) => {
    return {
        internetError: store.utilityReducer.internetError,
        campaignInitialized: store.campaignReducer.campaignInitialized,
    };
})
export default class MainRoutingLayout extends React.Component {

    constructor() {
        super();
        this.state = {};
        this.tryAgain = this.tryAgain.bind(this);
    }

    tryAgain() {
        window.location.reload();
    }

    componentWillMount() {
        //console.log("Enter MainRoutingLayout");
        //console.log('window.location.pathname is : ' + window.location.pathname);
        if (window.location.pathname.indexOf("/oauth") >= 0) {
            UtilityActions.showFullPageLoader("Loading Dinein Menu...");
        } if (window.location.pathname.indexOf("/10yearsOfChaayos") >= 0) {
            UtilityActions.showFullPageLoader("Preparing game room...");
        } else {
            var authDetail = StorageUtils.getAuthDetail();
            if (!appUtil.checkEmpty(authDetail) && authDetail.deviceKey != null) {
                this.props.dispatch(UtilityActions.stampDevice(authDetail));
            } else {
                this.props.dispatch(UtilityActions.createDevice());
            }
            if (window.location.pathname.indexOf("/delivery") >= 0) {
                UtilityActions.showFullPageLoader("Loading Delivery Menu...");
            }
            if (window.location.pathname.indexOf("/dinein") >= 0) {
                UtilityActions.showFullPageLoader("Loading Dinein Menu...");
            } else if (window.location.pathname.indexOf("/stores") >= 0) {
                UtilityActions.showFullPageLoader("Loading Store Locator...");
            } else {
                //console.log("Enter MainRoutingLayout :: Default flow");
                this.props.dispatch(UtilityActions.setDefaultData());
            }
            if (window.location.pathname == "/menu") {
                this.props.dispatch(CampaignManagementActions.initCampaign(this.props, this.props.campaignInitialized));
            }
        }
    }

    render() {

        return (
            <div>
                {this.props.internetError ? (
                    <div class="internetErrorContainer">
                        <div class="dummyHead"><img src="/img/logo.svg"/></div>
                        {appUtil.isMobile() ? (
                            <img src="../../img/internetErrorPhone.png"/>
                        ) : (
                            <img src="../../img/internetErrorLaptop.png"/>
                        )}
                        <p class="msg">Something went wrong.</p>
                        <div class="btn btn-primary" style={{width: "200px", display: "inline-block"}}
                             onClick={this.tryAgain.bind(this)}>Try again
                        </div>
                    </div>
                ) : (
                    <div id="container">
                        {appUtil.isMobile() ? (
                            <MobileUtilityLayout/>
                        ) : (
                            <DesktopUtilityLayout/>
                        )}
                        {React.cloneElement(this.props.children, {...this.props})}
                    </div>
                )}
            </div>
        )
    }
}
