package com.stpl.tech.kettle.simulator;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.support.ui.Select;

public class LoginHelper {
	
	public static void kettleLogin(WebDriver driver) {
		
		driver.findElement(By.xpath("//*[@id=\"login-pills\"]/div/ul/li[1]/a"));
		// Thread.sleep(3000);
		driver.findElement(By.xpath("//input[@id='userId']")).sendKeys("120057");
		// Thread.sleep(3000);
		driver.findElement(By.xpath("//input[@id='password']")).sendKeys("321321");
		// Thread.sleep(3000);
		Select unit = new Select(driver.findElement(By.xpath("//select[@id='unit']")));
		// Thread.sleep(5000);
		unit.selectByValue("number:26005");
		// Thread.sleep(3000);
		driver.findElement(By.xpath("//button[@type='submit']")).click();
	}
	
	/*
	 * public static void codLogin(WebDriver driver){
	 * driver.findElement(By.xpath("//*[@id=\"login-pills\"]/div/ul/li[1]/a")); //
	 * Thread.sleep(3000);
	 * driver.findElement(By.xpath("//input[@id='userId']")).sendKeys("120057"); //
	 * Thread.sleep(3000);
	 * driver.findElement(By.xpath("//input[@id='password']")).sendKeys("321321");
	 * // Thread.sleep(3000); Select unit = new
	 * Select(driver.findElement(By.xpath("//select[@id='unit']"))); //
	 * Thread.sleep(5000); unit.selectByValue("number:26005"); //
	 * Thread.sleep(3000);
	 * driver.findElement(By.xpath("//button[@type='submit']")).click();
	 * 
	 * 
	 * }
	 */

}
