/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

@charset "utf-8";
/* CSS Document */
@font-face {
    font-family: "AT";
    src: url("../font/american_typewriter.eot");
    src: url("../font/american_typewriter.eot?#iefix") format("embedded-opentype"),
    url("../font/american_typewriter.ttf") format("truetype");
}

@font-face {
    font-family: "AT";
    src: url("../font/american_typewriter.eot");
    src: url("../font/American_Typewriter_Bold.ttf") format("truetype");
    font-weight: 700;
}

/*  basic styles */
html {
    font-family: AT, sans-serif;
}

body {
    /* position:relative; */
    font-family: AT, sans-serif;
    overflow-y: hidden;
    font-size: 22px;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

html {
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
}

input[type="text"], input[type="number"], input[type="email"], select, textarea {
    font-size: 24pt !important;
    font-weight: 300;
    height: 40px;
}

label {
    font-size: 24px !important;
    font-weight: 300;
    width: 100%;
}

select:focus {
    outline: none;
}

select {
    display: block;
    border: none;
    border-bottom: 1px solid #9e9e9e;
    outline: none;
    height: 4rem !important;
}

.btn {
    padding: 0 1rem;
}

input:focus {
    border-left: none;
    border-right: none;
    border-top: none;
    box-shadow: none;
    outline: none;
}

.col {
    margin-top: 20px;
    margin-bottom: 0px;
}

.toast {
    background: #FFFFFF;
    color: #375125;
    font-size: 32px;
    border: #000 1px solid;
    word-break: normal;
    box-shadow: #000 0 0 20px 2px;
}

.character-counter {
    font-size: 21px !important;
}

.btn, .btn-large, .btn-flat {
    text-transform: capitalize;
}

.ellipsis{
	text-overflow:ellipsis;
	overflow:hidden;
	white-space: nowrap;
}

/* login screen styles */
#changePassClose {
    font-weight: 100;
    font-size: 76px;
    line-height: 58px;
    float: right;
    cursor: pointer;
    margin: -30px -10px 0 0;
}

/*   cards view Styles  */

.cardContainer {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    margin: 0px;
}

.newUser {
    position: absolute !important;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.regularUser {
    position: absolute !important;
    top: 50%;
    left: 0;
    right: 0;
    bottom: 0;
}

#emailView {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 145px;
    z-index: 1;
    text-align: center;
    margin: 0px;
    -webkit-transition: all 1s; /* Safari 3.1 to 6.0 */
    transition: all 1s;
    /* border-bottom: #C1C1C1 1px solid; */
    /* background:#fff; */
    color: #fff;
    z-index: 99;
}

.clearEmail {
    float: right;
    margin-top: -54px;
    font-size: 35px;
    font-weight: 100;
    cursor: pointer;
    z-index: 999;
}

/*   user flow view styles  */

#userFlowView {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 3;
    background-color: #fff;
    background: url('../img/regularUser.jpg') #fff center no-repeat;
    background-size: cover;
    -webkit-transition: all 1s; /* Safari 3.1 to 6.0 */
    transition: all 1s;
    transform: translate3d(0, 0, 0);
    overflow: hidden;
    opacity: 0;
}

#userContactScreen {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    -webkit-transition: all 1s; /* Safari 3.1 to 6.0 */
    transition: all 1s;
    transform: translate3d(0, 0, 0);
    opacity: 0;
}

#redemptionScreen, #OTPScreen, #thankYouScreen, #feedbackScreen, #recommendationScreen, #giftCardScreen {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    -webkit-transition: all 1s; /* Safari 3.1 to 6.0 */
    transition: all 1s;
    transform: translate3d(0, 0, 0);
    opacity: 0;
}

#thankYouScreen {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    /* background:url("../img/promotion.jpg") center no-repeat; */
    background-size: cover;
    opacity: 0;
}

#feedbackScreen{
    background-color: #e1f1d1;
    background-image: url('../img/feedbackbg.jpeg');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    color: #5e7e47;
}
#recommendationScreen{
	background-color: #e1f1d1;
    background-image: url('../img/recommendation/background.jpg');
    background-position: center;
    background-repeat: repeat;
    background-size: cover;
    color: #5e7e47;
}
#giftCardScreen{
    background-color: #fff;
    color: #5e7e47;
}

.recommendationTitle{
	font-size: 50px;
    padding: 6px 0 0 0;
   	color: #fff;
}

.recommendationProduct{
	background: #577c3a;
    padding: 10px 6px;
    font-size: 60px;
    color: #fff;
    margin: 4px 10px;
    border-radius: 10px;
}

.recommendationProductName{
	font-size: 55px;
	font-family: 'Helvetica',serif;
}

.recommendationProductDesc{
	font-size: 25px;
    height: 80px;
	font-family: 'Helvetica',serif;
}

.addBtnContainer {
    background: #3f5c2b;
    padding: 20px 0;
    text-align: center;
    /* width: 720px; */
    margin: 4px 10px;
    border-radius: 10px;
}

.addBtn{
    color: #fff;
    background: transparent;
    border: none;
    height: 56px;
}

.recommendationImg {
    margin: 15px auto;
    height: 850px;
    /*width: 720px;*/
    border-radius: 30px;
    border: #d0a208 6px solid;
    overflow: hidden;
    position: relative;
}

.recommendationImg img{
	height:100%;
    width:100%
}

.offerContainer.active {
    left: 65px;
    top: 435px;
}

.offerContainer{
	position: fixed;
    left: 110%;
    top: 0;
    z-index: 1;
    width: 250px;
    height: 250px;
    background: none;
    transition: top 1s, left 1s;
	-moz-transition: top 1s, left 1s; 
	-webkit-transition:top 1s, left 1s; 
	-o-transition: top 1s, left 1s;
}

.skipBtnContainer{
    padding: 30px 0 0 0;
    text-align: center;
}

.skipBtn{
	border-radius: 8px;
    border: #fff 3px solid;
    color: #fff;
    background: transparent;
    padding: 8px 60px;
    font-size: 31px;
}

.welcomeText {
    font-size: 30px;
}

.welcomeText span {
    color: green;
    text-transform: capitalize;
}

.formBox {
    margin: 20px 10px;
    border: #ccc 1px solid;
    padding: 10px;
    border-radius: 10px;
    background: rgba(255, 255, 255, .7);
    box-shadow: #8D8C8C 0 0 8px 0;
}

.editContactLink {
    font-size: 24px;
    color: green;
    margin-left: 20px;
    text-decoration: underline;
    cursor: pointer
}

.contactVerified {
    font-size: 24px;
    color: green;
    margin-left: 20px;
}

.redeemBtn {
    margin-right: 5px;
    margin-bottom: 5px;
    background: #375125;
}

.chaiBtn {
    background: #375125;
}

.promotionHead {
    color: white;
    font-size: 70px;
    text-align: center;
    text-transform: uppercase;
    margin: 35px 0;
    font-weight: bold;
}

.NCR, .NCR_EDU {
    background: url('../img/delhi.jpg') #fff center no-repeat;
}

.MUMBAI {
    background: url('../img/mumbai.jpg') #fff center no-repeat;
}

.CHANDIGARH {
    background: url('../img/chandigarh.jpg') #fff center no-repeat;
}

.BANGALORE {
    background: url('../img/bangalore.jpg') #fff center no-repeat;
}
/* loader view styles  */

.loaderView {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    background: rgba(256, 256, 256, .6);
    z-index: 9999;
}

.shadowBox {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background: rgba(0, 0, 0, .5);
}

.formBox {
    margin-top: 200px;
    padding: 10px 30px;
}

.mailText {
    margin: 0px 10px -15px 10px;
    text-align: left;
    font-size: 15dpi;
    line-height: 18dpi;
    color: white;
}

.emailBtn{
    height: 40px;
}

.feedbackContainer{
    margin: 10px 0;
    padding: 0px;
}

.feedbackQuestionBox{
    margin-top: 30px;
   /* -webkit-transition: all 1s; !* Safari 3.1 to 6.0 *!
    transition: all 1s;
    transform: translate3d(0, 0, 0);*/
}

.feedbackQuestionBox:first-child{
    /*-webkit-transition: all 2s; !* Safari 3.1 to 6.0 *!
    transition: all 2s;
    transform: translate3d(0, 0, 0);*/
}

.feedbackQuestion{
    margin-bottom: 20px;
    display: block;
}

.feedbackOptionBtn,.feedbackOptionBtn:focus,.feedbackOptionBtn:active,.feedbackOptionBtn.active{
    background-color: rgba(0,0,0,0);
    margin: 0 20px 0px 0;
    padding: 5px;
    height: 110px;
    width: 19%;
    border-radius: 2px;
    font-size: 18px;
    color: #5e7e47;
    border: #ccc 1px solid;
    box-shadow: none;
    display: inline-block;
    vertical-align: middle;
}
.feedbackOptionBtn.selected{
    /*background-color: rgba(0,0,0,.07);*/
    background-color: #38cf8e;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    /*-moz-transition: background-color ease-out 100ms 0ms;
    -webkit-transition: background-color ease-out 100ms 0ms;
    -o-transition: background-color ease-out 100ms 0ms;
    transition: background-color ease-out 100ms 0ms;*/
    color: #10442e;
    border-top: 1px solid #249766;
    border-left: 1px solid #249766;
    border-right: 1px solid #249766;
    border-bottom: 1px solid #1b6f4b;
}

#ratingBox span{
    display: inline-block;
    background-image: url("../img/star.png");
    background-position: center;
    background-size: contain;
    width: 95px;
    height: 95px;
    padding: 0 10px;
}

#ratingBox span.selected{
    display: inline-block;
    background: url("../img/star-selected.png");
    background-position: center;
    background-size: contain;
    width: 95px;
    height: 95px;
    padding: 0 10px;
}

.feedbackSubmitBtn{
    background-color: #38cf8e;
    width: 80%;
    height: 64px;
    line-height: 38px;
    padding: 5px 20px 0;
    font-size: 25px;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    text-align: center;
    font-weight: 700;
    overflow: hidden;
    /*-moz-transition: background-color ease-out 100ms 0ms;
    -webkit-transition: background-color ease-out 100ms 0ms;
    -o-transition: background-color ease-out 100ms 0ms;
    transition: background-color ease-out 100ms 0ms;*/
    color: #10442e;
    border-top: 1px solid #249766;
    border-left: 1px solid #249766;
    border-right: 1px solid #249766;
    border-bottom: 1px solid #1b6f4b;
}

.feedbackSubmitBtn:hover{
    /*-moz-transition: background-color ease-out 100ms 0ms;
    -webkit-transition: background-color ease-out 100ms 0ms;
    -o-transition: background-color ease-out 100ms 0ms;
    transition: background-color ease-out 100ms 0ms;*/
    background-color: #61d9a5;
    cursor: pointer;
}

.productBox{
    display: inline-block;
    text-align: center;
    width: 25%;
    margin: 5px 20px;
    vertical-align: top;
}

.productImg{
    /*width: 85%;*/
    margin: auto;
    text-align: center;
    /*height: 151px;*/
    /*background: #000;*/
    margin-bottom: 5px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.productImg img{
    width: 90%;
}

.alert-info {
    color: #31708f !important;
    background-color: #d9edf7;
    border-color: #bce8f1 !important;
}

.alert {
    padding: 8px 10px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.giftCardError{
    color: #cf2d1d;
    font-size: 18px;
    clear: both;
    margin: 0 20% 0 0;
    text-align: right;
}

#giftCardScreen{
    background: url("../img/giftCardCenterBg.png") #fff center no-repeat;
}

#giftCardScreen .giftCardsHeader{
    position: absolute;
    top: 0;
    width: 100%;
    background: url(../img/giftCardHeader.png) center top no-repeat;
    background-size: contain;
    color: #fff;
    height: 218px;
    text-align: center;
}

#giftCardScreen .giftCardsHeader h1{
    font-size: 48px;
    padding-top: 35px;
}

@media only screen and (min-width: 501px) and (max-width: 700px) {
    .recommendationImg {
        height: 540px;
    }
}

@media only screen and (max-width: 500px) {
    body {
        /* position:relative; */
        font-family: 'AT', sans-serif;
        overflow-y: hidden;
        font-size: 16px;
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    input[type="text"], input[type="tel"], input[type="number"], input[type="email"], select, textarea {
        font-size: 22px !important;
        font-weight: 300;
    }

    label {
        font-size: 16px !important;
        font-weight: 300;
    }

    select:focus {
        outline: none;
    }

    select {
        display: block;
        border: none;
        border-bottom: 1px solid #9e9e9e;
        outline: none;
        font-size: 14px !important;
        padding: 5px;
        height: auto !important;
    }

    .card .card-content {
        padding: 0 20px 20px 20px;
    }

    .col {
        margin-top: 20px;
    }

    input {
        font-size: 16px !important;
        padding: 5px !important;
        height: auto !important;
    }

    .row {
        margin-bottom: 10px;
    }

    .btn, .btn-large, .btn-flat {
        padding: 0 1rem;
    }

    .formBox {
        margin-top: 10px;
    }

    .toast {
        background: #FFFFFF;
        color: #375125;
        font-size: 18px;
        border: #000 1px solid;
        word-break: normal;
        box-shadow: #000 0 0 20px 2px;
    }

    .mailText {
        margin: 5px 0 -10px 0px;
        text-align: left;
        font-size: 14px;
        line-height: 18px;
        color: white;
    }

    .emailBtn{
        height: auto !important;
    }
}

.customerInfoLabel {
	color: #5e7e47 !important;
    display: block;
    width:100%;
    font-weight: 700;
}
.otpLabel{
	color: black !important;
    font-weight: 600;
}

#trueCallerLoader{
    position:absolute;
    opacity:0; top:0;
    bottom:0;
    left:0;
    right:0;
    z-index:0;
    -webkit-transition: all 1.5s; /* Safari 3.1 to 6.0 */
    transition: all 1.5s;
}

#trueCallerLoader div#trueCallerBg{
    background-image:url('../img/truecaller.gif');width:100%;height:100%;position:absolute;
}

#trueCallerLoader.show{
    opacity:1;
    z-index:1000;
}

#trueCallerLoader h3{
    text-align:center; color:#fff;margin-top:50%;
}

#skipToOtpBtn{
    cursor:pointer;bottom:7%;position:fixed;left:30%;
}