<div class="row partnerEdcMappingContainer" ng-init="init()">
  <div class="col-lg-12"><br>
    <div class="row">
      <h1 class="page-header"> Partner Edc Mapping </h1>
    </div>
    <div class="row">
      <button style="margin: 10px" class="btn btn-primary pull-right" data-toggle="modal"
      data-target="#offerModal" data-ng-click="toggelEditMode()">{{editMode ? "Hide": "View "}} Edc Mappers List
      </button>
    </div>
  </div>




  <div  data-ng-show="!editMode">
    <div class="form-group">
      <label>Status *</label>
      <select class="form-control" ng-model="edcMappingStatus">
          <option ng-repeat="status in statusList | orderBy" value="{{status}}">
              {{status}}
          </option>
      </select>
    </div>
    <div class="form-group">
      <label>Select Cafe *</label>
      <select class="form-control" ng-model="selectedUnit">
          <option ng-repeat="cafe in cafelist | orderBy" value="{{cafe}}">
              {{cafe.name}}
          </option>
      </select>
    </div>
    <div class="form-group">
      <label>Partner Name *</label>
      <input type="text"
            pattern=".*\S+.*"
            class="form-control"
            ng-model="partnerName"
      required>
    </div>
      <div class="form-group">
        <label>Merchant Id *</label>
        <input type="text"
              pattern=".*\S+.*"
              class="form-control"
              ng-model="merchantId"
        required>
    </div>
    <div class="form-group">
      <label>Terminal Id *</label>
      <input type="text"
            pattern=".*\S+.*"
            class="form-control"
            ng-model="terminalId"
      required>
    </div>
    <div class="form-group">
      <label>Merchant Key *</label>
      <input type="text"
            pattern=".*\S+.*"
            class="form-control"
            ng-model="merchantKey"
      required>
    </div>
    <div class="form-group">
        <label>Version *</label>
        <input type="number"
              step="0.01"
              pattern=".*\S+.*"
              min = "0.00"
              class="form-control"
              ng-model="version"
              ng-change=""
        required>
    </div>
    <div class="form-group">
      <label>T_Id *</label>
      <input type="text"
             pattern=".*\S+.*"
             class="form-control"
             ng-model="tId"
             required>
    </div>
<!--    <div class="form-group">-->
<!--      <label>Secret Key *</label>-->
<!--      <input type="text"-->
<!--             pattern=".*\S+.*"-->
<!--             class="form-control"-->
<!--             ng-model="secretKey">-->
<!--    </div>-->
    <div class="form-group">
      <button style="margin: 10px" class="btn btn-primary pull-right"
              ng-click="savePartnerEdcMapping()">Submit
      </button>
    </div>
  </div>

  <div data-ng-show="editMode">
    <div>
      <label class="col region-card">Select Status * </label>
      <select class="form-control" ng-model="edcMappingStatus">
        <option ng-repeat="status in statusList | orderBy" value="{{status}}">
            {{status}}
        </option>
      </select>
    </div>
    <div style="margin-bottom: 30px;">
      <span class="btn btn-primary" data-ng-click="getPartnerEdcMapperList()" style="margin-top: 20px">
        View List
      </span>
      <span class="btn btn-warning" data-ng-click="updatePartnerEdcMapperList()" data-ng-if="newPartnerEdcMapperList != null && newPartnerEdcMapperList.length > 0" style="margin-top: 20px">
        Update Changes
      </span>
    </div>


    <table class="table table-striped table-bordered"
          style="font-size: 15px" data-ng-if="partnerEdcMapperList != null && partnerEdcMapperList.length > 0">
      <thead>
        <th>Unit Id</th>
        <th><b>PARTNER NAME</b></th>
        <th>Merchant Id</th>
        <th>Terminal Id</th>
        <th>Merchant Key</th>
        <th>Version</th>
        <th>T_ID</th>
        <th>Status</th>
        <th> Change Status</th>
      </thead>
      <tbody>
      <tr data-ng-repeat="partnerEdcMapper in partnerEdcMapperList track by partnerEdcMapper.id" data-ng-model="selectOffer">
          <td>{{partnerEdcMapper.unitId}}</td>
          <td>{{partnerEdcMapper.partnerName}}</td>
          <td>{{partnerEdcMapper.merchantId}}</td>
          <td>{{partnerEdcMapper.terminalId}}</td>
          <td>{{partnerEdcMapper.merchantKey}}</td>
          <td>{{partnerEdcMapper.version}}</td>
          <td>{{partnerEdcMapper.tid}}</td>

          <td>{{partnerEdcMapper.status}}</td>
          <td>
            <button class="btn btn-danger" data-ng-if="partnerEdcMapper.status =='ACTIVE' " data-ng-click="changeStatus(partnerEdcMapper)" style="width: 100px; margin-left: 40px"> IN_ACTIVE
            </button>
            <button class="btn btn-success" data-ng-if="partnerEdcMapper.status =='IN_ACTIVE' " data-ng-click="changeStatus(partnerEdcMapper)" style="width: 100px; margin-left: 10px" > ACTIVE
            </button>
          </td>
        </tr>
        </tbody>
    </table>

  </div>
</div>

