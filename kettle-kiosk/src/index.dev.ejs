<!DOCTYPE html>
<html>
<head>
    <title>Chaayos - Order Chai Online - Experiments With Chai</title>
    <meta name="Keywords" content="Order Chai Online, Order Tea Online, Order Food Online, order breakfast online, Masala Chai, desi chai, indian chai, vada pav" />
    <meta name="Description" content="Chaayos - a Chai adda to order chai online with 1200 personalised chai options. Get the authentic taste delivered in Delhi, Gurgaon, Noida and Mumbai." />
    <meta name="google-site-verification" content="" />
    <link rel="canonical" href="https://cafes.chaayos.com">
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="theme-color" content="#5e7e47" id="themeColor" />
    <meta name="full-screen" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="#5e7e47" />
    <meta name="apple-mobile-web-app-title" content="Chaayos" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <!-- Place favicon.ico and apple-touch-icon.png in the root directory -->
    <link rel="icon" type="image/x-icon" href="img/favicon-96x96.png" />
    <link rel="apple-touch-icon" href="img/favicon-96x96.png" type="image/x-icon" />
    <style type="text/css">
        .splash{position: absolute;top:0;right:0;bottom: 0;left: 0;text-align: center;background: #fff; vertical-align: middle;-webkit-transition: all 0.25s linear;transition: all 0.25s linear;}
        .splash.inactive{opacity: 0;}.dummyHead{height: 52px;background: #5e7e47;}.dummyHead img{height: 27px;margin-top: 12px;}
        .splash.inactive{opacity: 0;}.dummyHead{height: 52px;background: #5e7e47;}.dummyHead img{height: 27px;margin-top: 12px;}
        .load8.loader,.load8.loader:after{border-radius:50%;width:35px;height:35px;text-align:center}
        .load8.loader{margin:35px auto;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(189,189,189,.2);
            border-right:2px solid rgba(189,189,189,.2);border-bottom:2px solid rgba(189,189,189,.2);border-left:2px solid #065904;-webkit-transform:translateZ(0);
            -ms-transform:translateZ(0);transform:translateZ(0);-webkit-animation:load8 .6s infinite linear;animation:load8 .6s infinite linear}
        @-webkit-keyframes load8{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}
        @keyframes load8{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}
    </style>
    <script data-cfasync="false" type="application/javascript" nonce>
        function loadjscssfile(filename, filetype) {
            if (filetype == "js") { //if filename is a external JavaScript file
                var fileref = document.createElement('script');
                fileref.setAttribute("type", "text/javascript");
                fileref.setAttribute("src", filename);
            }
            else if (filetype == "css") { //if filename is an external CSS file
                var fileref = document.createElement("link");
                fileref.setAttribute("rel", "stylesheet");
                fileref.setAttribute("type", "text/css");
                fileref.setAttribute("href", filename);
                fileref.setAttribute("data-cfasync", "false");
            }
            if (typeof fileref != "undefined")
                document.getElementsByTagName("head")[0].appendChild(fileref)
        }
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|BB|PlayBook|IEMobile|Windows Phone|Kindle|Silk|Opera Mini/i.test(navigator.userAgent)) {
            loadjscssfile("./css/style.css", "css");
        }else{
            loadjscssfile("./css/styled.css", "css");
        }
    </script>

    <link rel="manifest" href="manifest.json" />
</head>

<body>
<div id="app"></div>
<div class="splash">
    <div class="dummyHead"><img src="img/logo.svg" /></div>
    <div class="loader load8" style="margin-top: 100px;"></div>
    <p id="splashPay"></p>
</div>
<script type="text/javascript">
    if(window.location.pathname=="/payProcess"){
        document.getElementById("splashPay").innerHTML = "Please wait while we are checking your payment status.";
    }
    if (Object.prototype.toString.call(window.operamini) === "[object OperaMini]") {
        alert("We have limited support for this browser. For best experience please turn of data saving mode or switch to Google Chrome.");
    }
</script>
<link data-cfasync="false" rel="stylesheet" href="css/slick.min.css" />
<link data-cfasync="false" rel="stylesheet" href="css/slick-theme.min.css" />
<link href="https://fonts.googleapis.com/css?family=Varela+Round" rel="stylesheet" />
<link rel="stylesheet" type="text/css" href="css/react-select.min.css" />
<!--<script crossorigin="anonymous" src="https://checkout.razorpay.com/v1/checkout.js"></script>-->
<script crossorigin="anonymous" src="pubnub.4.4.0.min.js"></script>
<script data-cfasync="false" type="text/javascript">
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js').then(function (registration) {
            console.log('ServiceWorker registration successful with scope: ', registration.scope);
        }).catch(function (err) {
            console.error('ServiceWorker registration failed: ', err);
        });
    }
</script>
</body>
</html>