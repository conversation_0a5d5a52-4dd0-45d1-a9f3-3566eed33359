package com.stpl.tech.kettle.inventory.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import com.stpl.tech.kettle.inventory.config.InventoryProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.inventory.dao.mongo.CostDetailWrapperDao;
import com.stpl.tech.kettle.inventory.dao.redis.CriticalProductMapDao;
import com.stpl.tech.kettle.inventory.dao.redis.EmployeeBasicDetailDao;
import com.stpl.tech.kettle.inventory.dao.redis.ProductTrimmedDataDao;
import com.stpl.tech.kettle.inventory.dao.redis.RecipeMapDataDao;
import com.stpl.tech.kettle.inventory.dao.redis.UnitSCMCriticalProductDao;
import com.stpl.tech.kettle.inventory.dao.redis.UnitTrimmedProductMapDataDao;
import com.stpl.tech.kettle.inventory.domain.mongo.CostDetail;
import com.stpl.tech.kettle.inventory.domain.mongo.CostDetailWrapper;
import com.stpl.tech.kettle.inventory.domain.redis.CriticalProductMap;
import com.stpl.tech.kettle.inventory.domain.redis.EmployeeBasicDetail;
import com.stpl.tech.kettle.inventory.domain.redis.UnitProductTrimmedData;
import com.stpl.tech.kettle.inventory.domain.redis.UnitSCMCriticalProductMap;
import com.stpl.tech.kettle.inventory.domain.redis.UnitTrimmedProductMapData;
import com.stpl.tech.kettle.inventory.service.InventoryAggregationServices;
import com.stpl.tech.kettle.inventory.service.MasterDataService;
import com.stpl.tech.kettle.inventory.service.RecipeLocalCacheManagementService;
import com.stpl.tech.kettle.inventory.service.SCMDataService;
import com.stpl.tech.kettle.inventory.util.InventoryUtils;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.TrimmedProductPrice;
import com.stpl.tech.master.domain.model.TrimmedProductVO;
import com.stpl.tech.redis.core.dao.UnitBasicDetailDao;
import com.stpl.tech.redis.core.util.WebServiceHelper;
import com.stpl.tech.redis.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.domain.model.DayCloseEvent;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

/**
 * Aggregation service to collect data form KETTLE, MASTER and SCM services
 *
 * <AUTHOR>
 *
 */
@Service
public class InventoryAggregationServicesImpl implements InventoryAggregationServices {

	private static final Logger LOG = LoggerFactory.getLogger(InventoryAggregationServicesImpl.class);

	@Autowired
	private SCMDataService scmDataService;
	@Autowired
	private MasterDataService masterDataService;

	@Autowired
	private RecipeMapDataDao recipeMapDao;

	@Autowired
	private ProductTrimmedDataDao trimmedProductDataDao;

	@Autowired
	private UnitBasicDetailDao unitBasicDetailDao;
	@Autowired
	private CriticalProductMapDao criticalProductMapDao;
	@Autowired
	private UnitTrimmedProductMapDataDao unitTrimmedProductMapDataDao;
	@Autowired
	private CostDetailWrapperDao costDetailWrapperDao;
	@Autowired
	private UnitSCMCriticalProductDao unitSCMCriticalProductDao;
	@Autowired
	private EmployeeBasicDetailDao employeeBasicDetailDao;;

	@Autowired
	private RecipeLocalCacheManagementService metadataCache;
	@Autowired
	private InventoryProperties properties;
	/**
	 * Method to fetch cost detail for particular unit from SCM service
	 *
	 * @param unitId
	 * @return
	 */
	@Override
	public List<CostDetail> fetchCostDetailData(int unitId) {
		if (unitId == 0) {
			return null;
		}
		try {
			LOG.info("Requesting SCM Cost Data from SCM service for unit: {}", unitId);
			//TODO - Mohit - Might Need a revert
			List<CostDetail> list = scmDataService.getCostDetails(unitId);
			// save data first
			// CostDetailWrapper wrapper = saveCostDetails(list, unitId);
			// collect data which is saved
			return list;
		} catch (Exception e) {
			LOG.info("Error while fetching data from SCM Service", e);
		}
		return null;
	}

	private CostDetailWrapper saveCostDetails(List<CostDetail> list, int unitId) {
		Date businessDate = AppUtils.getBusinessDate();
		CostDetailWrapper wrapper = new CostDetailWrapper();
		wrapper.setUnitId(unitId);
		wrapper.setList(list);
		wrapper.setBusinessDate(businessDate);
		costDetailWrapperDao.deleteCostDetailWrapperByUnitIdAndBusinessDate(unitId, businessDate);
		return costDetailWrapperDao.save(wrapper);
	}

	@Override
	public UnitProductTrimmedData getAllCafeTrimmedProducts(int unitId) {
		Stopwatch watch = Stopwatch.createUnstarted();
		Optional<UnitProductTrimmedData> updOptional = null;
		UnitProductTrimmedData upd = null;
		try {
			watch.start();
			updOptional = trimmedProductDataDao.findById(unitId);
			LOG.info(
					"Inside refreshCafeProductsInventoryOptimized : getAllCafeTrimmedProducts - trimmedProductDataDao.findOne unitId: {} took {} ms",
					unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
		} catch (Exception e) {
			LOG.error("IGNORE#######", e);
		}
		if (updOptional.isEmpty() || updOptional.get().getProducts() == null || updOptional.get().getProducts().isEmpty()) {
			watch.reset();
			watch.start();
			LOG.info("Requesting All Products data from Master for unitId: {}", unitId);
			List<TrimmedProductVO> products = masterDataService.getAllCafeTrimmedProducts(unitId);
			LOG.info(
					"Inside refreshCafeProductsInventoryOptimized : getAllCafeTrimmedProducts - masterDataService.getAllCafeTrimmedProducts unitId: {} took {} ms",
					unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
			// TODO MOhit - Check with Rahul and see what needs to be changed to fix it
			/*
			 * if(products == null || products.isEmpty()) {
			 * LOG.error("Unit trimmed products not found for unit " + unitId); throw new
			 * DataUpdationException("Unit trimmed products not found for unit " + unitId);
			 * } if (products.stream().anyMatch(trimmedProductVO ->
			 * Boolean.TRUE.equals(trimmedProductVO.getInventoryTracked()) &&
			 * (trimmedProductVO.getPrices() == null ||
			 * trimmedProductVO.getPrices().isEmpty()))) { StringBuilder errorProducts = new
			 * StringBuilder(); products.stream().filter(trimmedProductVO ->
			 * Boolean.TRUE.equals(trimmedProductVO.getInventoryTracked()) &&
			 * (trimmedProductVO.getPrices() == null ||
			 * trimmedProductVO.getPrices().isEmpty())).forEach(trimmedProductVO -> {
			 * errorProducts.append(trimmedProductVO.getName()).append(","); });
			 * LOG.error("Product prices object not found for unit " + unitId + " products "
			 * + errorProducts.toString()); throw new
			 * DataUpdationException("Product prices object not found for unit " + unitId +
			 * " products " + errorProducts.toString()); }
			 */
			products.forEach(trimmedProductVO -> {
				if(Objects.isNull(trimmedProductVO.getPrices()) || trimmedProductVO.getPrices().isEmpty()) {
					LOG.error("prices not found for unit: {} and product: {}", unitId, trimmedProductVO.getName());
				}
			});
			upd =new UnitProductTrimmedData();
			upd.setUnitId(unitId);
			upd.setProducts(products);
			// productDataDao.deleteAll();
			watch.reset();
			watch.start();
			upd = trimmedProductDataDao.save(upd);
			LOG.info(
					"Inside refreshCafeProductsInventoryOptimized : getAllCafeTrimmedProducts - trimmedProductDataDao.save unitId: {} took {} ms",
					unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
			watch.reset();
			watch.start();
			updateUnitTrimmedProductMapData(products, unitId);
			LOG.info(
					"Inside refreshCafeProductsInventoryOptimized : getAllCafeTrimmedProducts - updateUnitTrimmedProductMapData unitId: {} took {} ms",
					unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
			watch.reset();
			watch.start();
			updateCriticalTrimmedProducts(products, unitId);
			LOG.info(
					"Inside refreshCafeProductsInventoryOptimized : getAllCafeTrimmedProducts - updateCriticalTrimmedProducts unitId: {} took {} ms",
					unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
		}
		return updOptional.orElse(upd);
	}

	@Override
	public void refreshCafeProducts(int unitId) {
		Stopwatch watch = Stopwatch.createUnstarted();
		watch.start();
		Optional<UnitProductTrimmedData> upd = trimmedProductDataDao.findById(unitId);
		LOG.info("Inside refreshCafeProductsInventoryOptimized : refreshCafeProducts - trimmedProductDataDao.findOne unitId: {} took {} ms", unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
		if (upd.isPresent()) {
			watch.reset();
			watch.start();
			trimmedProductDataDao.delete(upd.get());
			LOG.info("Inside refreshCafeProductsInventoryOptimized : refreshCafeProducts - trimmedProductDataDao.delete unitId: {} took {} ms", unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
			watch.reset();
			watch.start();
			clearUnitTrimmedProductMapData(unitId);
			LOG.info("Inside refreshCafeProductsInventoryOptimized : refreshCafeProducts - clearUnitTrimmedProductMapData unitId: {} took {} ms", unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
			watch.reset();
			watch.start();
			clearUnitSCMCriticalProductMap(unitId);
			LOG.info("Inside refreshCafeProductsInventoryOptimized : refreshCafeProducts - clearUnitSCMCriticalProductMap unitId: {} took {} ms", unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
		}
		watch.reset();
		watch.start();
		getAllCafeTrimmedProducts(unitId);
		LOG.info("Inside refreshCafeProductsInventoryOptimized : refreshCafeProducts - getAllCafeTrimmedProducts unitId: {} took {} ms", unitId, watch.stop().elapsed(TimeUnit.MILLISECONDS));
	}

	private void clearUnitSCMCriticalProductMap(int unitId) {
		List<UnitSCMCriticalProductMap> list = unitSCMCriticalProductDao.findByUnitId(unitId);
		if(list != null && !list.isEmpty()) {
			unitSCMCriticalProductDao.deleteAll(list);
		}

	}

	private void clearUnitTrimmedProductMapData(int unitId) {
		List<UnitTrimmedProductMapData> list = unitTrimmedProductMapDataDao.findByUnitId(unitId);
		if(list != null && !list.isEmpty()) {
			unitTrimmedProductMapDataDao.deleteAll(list);
		}

	}

	private void updateUnitTrimmedProductMapData(List<TrimmedProductVO> products, int unitId) {
		List<UnitTrimmedProductMapData> list = new ArrayList<>();
		for (TrimmedProductVO p : products) {
			UnitTrimmedProductMapData data = new UnitTrimmedProductMapData();
			data.setKey(InventoryUtils.getUnitProductKey(unitId, p.getId()));
			data.setProduct(p);
			data.setUnitId(unitId);
			data.setProductId(p.getId());
			data.setBrandId(p.getBrandId());
			list.add(data);
		}
		unitTrimmedProductMapDataDao.saveAll(list);
	}

	@Deprecated
	private void updateCriticalProducts(List<Product> products, int unitId) {

		Set<Integer> criticalProductKeys;
		Set<Integer> criticalSCMProductKeysForExpiryFilter = new HashSet<>();
		com.stpl.tech.master.recipe.model.RecipeDetail rd;
		Map<Integer, Set<Integer>> map = new HashMap<>();
		for (Product product : products) {
			for (ProductPrice pp : product.getPrices()) {
				criticalProductKeys = new HashSet<>();
				rd = pp.getRecipe();
				InventoryUtils.getCriticalProductId(unitId, rd, criticalProductKeys);
				updateCriticalProductMap(product.getId(), criticalProductKeys, map);
				/*
				 * check to include only active and non merchandise products as we do not show
				 * these products in expiration
				 */
				if (ProductStatus.ACTIVE.equals(product.getStatus())
						&& AppConstants.CATEGORY_MERCHANDISE != product.getType()) {
					criticalSCMProductKeysForExpiryFilter.addAll(criticalProductKeys);
				}
			}
		}

		List<CriticalProductMap> list = new ArrayList<>();
		for (Integer i : map.keySet()) {
			CriticalProductMap cMap = new CriticalProductMap();
			cMap.setKey(InventoryUtils.getCriticalProductKey(i, unitId));
			cMap.setProductId(i);
			cMap.setUnitId(unitId);
			cMap.getProductsAffected().addAll(map.get(i));
			list.add(cMap);
		}
		// criticalProductMapDao.deleteAll();
		criticalProductMapDao.saveAll(list);
		unitSCMCriticalProductDao
				.save(new UnitSCMCriticalProductMap(unitId, new ArrayList<>(criticalSCMProductKeysForExpiryFilter)));

	}

	private void updateCriticalTrimmedProducts(List<TrimmedProductVO> products, int unitId) {

		Set<Integer> criticalProductKeys;
		Set<Integer> criticalSCMProductKeysForExpiryFilter = new HashSet<>();
		com.stpl.tech.master.recipe.model.RecipeDetail rd;
		Map<Integer, Set<Integer>> map = new HashMap<>();
		for (TrimmedProductVO product : products) {
			for (TrimmedProductPrice pp : product.getPrices().values()) {
				criticalProductKeys = new HashSet<>();
				rd = metadataCache.getRecipe(pp.getRecipeId());
				InventoryUtils.getCriticalProductId(unitId, rd, criticalProductKeys);
				updateCriticalProductMap(product.getId(), criticalProductKeys, map);
				/*
				 * check to include only active and non merchandise products as we do not show
				 * these products in expiration
				 */
				if (ProductStatus.ACTIVE.equals(product.getStatus())
						&& AppConstants.CATEGORY_MERCHANDISE != product.getType()) {
					criticalSCMProductKeysForExpiryFilter.addAll(criticalProductKeys);
				}
			}
		}

		List<CriticalProductMap> list = new ArrayList<>();
		for (Integer i : map.keySet()) {
			CriticalProductMap cMap = new CriticalProductMap();
			cMap.setKey(InventoryUtils.getCriticalProductKey(i, unitId));
			cMap.setProductId(i);
			cMap.setUnitId(unitId);
			cMap.getProductsAffected().addAll(map.get(i));
			list.add(cMap);
		}
		// criticalProductMapDao.deleteAll();
		criticalProductMapDao.saveAll(list);
		unitSCMCriticalProductDao
				.save(new UnitSCMCriticalProductMap(unitId, new ArrayList<>(criticalSCMProductKeysForExpiryFilter)));

	}

	private void updateCriticalProductMap(int productId, Set<Integer> criticalProductKeys,
			Map<Integer, Set<Integer>> map) {
		for (Integer i : criticalProductKeys) {
			Set<Integer> productSet = map.get(i);
			if (productSet != null) {
				productSet.add(productId);
			} else {
				productSet = new HashSet<>();
				productSet.add(productId);
				map.put(i, productSet);
			}
		}
	}

	@Override
	public List<UnitBasicDetail> getAllUnitBasicDetails(boolean purge) {
		if (purge) {
			unitBasicDetailDao.deleteAll();
		}
		List<UnitBasicDetail> list = WebServiceHelper.toList(unitBasicDetailDao.findAll());

		if (list == null || list.isEmpty()) {
			List<UnitBasicDetail> ubdList = masterDataService.getAllUnitBasicDetail();
			List<UnitBasicDetail> unitBasicDetails = new ArrayList<>();
			for(UnitBasicDetail detail : ubdList){
				if(detail.getUnitZone().equalsIgnoreCase(properties.getServerZone())) {
					unitBasicDetails.add(detail);
				}
			}
			unitBasicDetailDao.deleteAll();
			unitBasicDetailDao.saveAll(unitBasicDetails);
			list = unitBasicDetails;
		}
		return list;
	}

	@Override
	public List<UnitBasicDetail> getAllUnitBasicDetails() {
		return getAllUnitBasicDetails(false);
	}

	@Override
	public UnitBasicDetail getUnitBasicDetail(int unitId) {
		return getUnitBasicDetail(unitId, false);
	}

	@Override
	public UnitBasicDetail getUnitBasicDetail(int unitId, boolean force) {

		if (unitId == 0) {
			return null;
		}

		Optional<UnitBasicDetail> ubdO = unitBasicDetailDao.findById(unitId);
		if (ubdO.isEmpty() || force) {
			UnitBasicDetail ubd = masterDataService.getUnitBasicDetail(unitId);
			unitBasicDetailDao.save(ubd);
			return ubd;
		}
		return ubdO.get();
	}

	/**
	 * Let the Purge Begin
	 *
	 */
	@Override
	public void purgeData() {
		trimmedProductDataDao.deleteAll();
		unitBasicDetailDao.deleteAll();
		criticalProductMapDao.deleteAll();
		unitTrimmedProductMapDataDao.deleteAll();
		unitSCMCriticalProductDao.deleteAll();
		employeeBasicDetailDao.deleteAll();
	}

	@Override
	public DayCloseEvent getLastScmUnitClosure(int unitId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public EmployeeBasicDetail getEmployeeBasicDetail(int empId) {
		Optional<EmployeeBasicDetail> ebdO = employeeBasicDetailDao.findById(empId);
		if (ebdO.isEmpty()) {
			EmployeeBasicDetail ebd = masterDataService.getEmployeeBasicDetail(empId);
			if (ebd != null) {
				employeeBasicDetailDao.save(ebd);
			}
			return ebd;
		}
		return ebdO.get();
	}

}
