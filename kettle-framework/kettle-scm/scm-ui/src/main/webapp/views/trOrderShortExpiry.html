
<!-- SEMI FINSHED SHORT EXPIRY Update Modal -->
<div
	id="semifinishedItemsModal" data-ng-init="init()">
	<div class="row">
		
		<!-- header -->
		<div class="row margin0">
			<div class="col s10">
				<h5>Semi-Finished Items Inventory</h5>
			</div>
			<div class="col s2">
				<a
					class="waves-effect waves-green btn right"
					data-ng-click="cancelShortExpiryModal()"> <span
					aria-hidden="true">cancel</span>
				</a>
			</div>
		</div>
		
		<!-- Body -->
		<div data-ng-if="currentTab == 1">
		<h5 class="card-panel teal lighten-2 center">
			<span class="white-text center">Fresh and Short Expiry Quantity Distribution</span>
		</h5>
		<table
			data-ng-if="modelData.semiFinishedProducts.length>0"
			class="bordered striped">
			<thead>
				<tr>
					<th class="center-align">Product Name</th>
					<th class="center-align">Unit Of Measure</th>
					<th class="center-align">Requested Qty</th>
					<th class="center-align">Short Expiry Qty</th>
					<th class="center-align">Fresh Qty</th>
					<th class="center-align">Total Qty (SE+F)</th>
				</tr>
			</thead>
			<tbody>
				<tr data-ng-repeat="item in modelData.semiFinishedProducts track by $index">
					<td class="center-align"><a
						data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a></td>
					<td class="center-align">{{item.unitOfMeasure}}</td>
					<td class="center-align">{{item.requestedQuantity != null ? item.requestedQuantity : 0  | number : 4
						}}</td>
					<td class="center-align"><input
						type="number"
						min="0.1"
						placeholder="Enter Quantity"
						data-ng-model="item.shortExpiry"
						data-ng-change="calculateFreshQuantity(item)"/></td>
					<td class="center-align"><input
						type="number"
						min="0.1"
						placeholder="Enter Quantity"
						data-ng-model="item.freshQuantity" /></td>
					<td class="center-align" data-ng-class="(item.shortExpiry + item.freshQuantity) == item.requestedQuantity ? 'data-ok' : 'data-error'">{{
					item.shortExpiry  +  item.freshQuantity 	
					}}</td>
				</tr>
			</tbody>
		</table>
		<div
			data-ng-if="modelData.semiFinishedProducts.length==0"
			class="text-center-disabled">No Items to display.</div>
		</div>
		
		
		<div data-ng-if="currentTab != 1">
			<h5 class="card-panel teal lighten-2 center">
				<span class="white-text center">Expiry dates for transfered Quantity</span>
			</h5>
			<table
			data-ng-if="expiryDisplayData.length > 0"
			class="bordered striped">
			<thead>
				<tr>
					<th class="center-align">Product Name</th>
					<th class="center-align">Unit Of Measure</th>
					<th class="center-align">Expiry Type</th>
					<th class="center-align">Transfer Qty</th>
					<th class="center-align">Expiry Date</th>
				</tr>
			</thead>
			<tbody>
				<tr data-ng-repeat="item in expiryDisplayData track by $index">
					<td class="center-align">
						{{item.productName}}</td>
					<td class="center-align">{{item.uom}}</td>
					<td class="center-align">{{item.expiryType}}</td>
					<td class="center-align">{{item.quantity | number : 4
						}}</td>
					<td class="center-align">{{item.expiryDate | date : 'yyyy-MM-dd HH:mm:ss'}}
					<!-- select data-placeholder="Select Expiry" data-ng-model="item.expiryDate"
						data-ng-options="e as e | date:'dd/MM/yyyy HH:mm:ss' : 'IST' for e in item.availableDates"></select>					
					</td> -->
				</tr>
			</tbody>
		</table>
		</div>
	</div>
</div>
	<!-- footer -->
	<div class="row">
		<div
			data-ng-if="currentTab == 1">
			<button
				class="waves-effect waves-green btn right"
				style="margin-right: 10px"
				data-ng-click="calculateTransferConsumption()">Calculate Expiry</button>
		</div>
		<div
			data-ng-if="currentTab == 2">
			<button
				class="waves-effect waves-green btn left"
				style="margin-left: 10px"
				data-ng-if="currentTab > 1"
				data-ng-click="switchtab(-1)">Back</button>
			<button
				class="waves-effect waves-green btn right red"
				data-ng-if="currentTab == 2"
				style="margin-right: 10px"
				data-ng-click="submitShortExpiryModal()">Submit</button>
		</div>
	</div>
