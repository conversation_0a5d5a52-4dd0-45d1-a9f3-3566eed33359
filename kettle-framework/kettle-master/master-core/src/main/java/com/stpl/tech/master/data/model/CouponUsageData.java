/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "COUPON_CODE_USAGE_DATA")
public class CouponUsageData implements Serializable {

	private static final long serialVersionUID = -7019921328022528762L;

	private int usageId;
	private int couponDetailId;
	private int orderId;
	private Integer customerId;
	private BigDecimal discount;
	private Integer productId;
	private Integer productQuantity;
	private Date addTime;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "COUPON_CODE_USAGE_ID", unique = true, nullable = false)
	public int getUsageId() {
		return usageId;
	}

	public void setUsageId(int usageId) {
		this.usageId = usageId;
	}

	@Column(name = "COUPON_DETAIL_ID", nullable = false, length = 30)
	public int getCouponDetailId() {
		return couponDetailId;
	}

	public void setCouponDetailId(int couponDetailId) {
		this.couponDetailId = couponDetailId;
	}

	@Column(name = "ORDER_ID", nullable = false, length = 30)
	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	@Column(name = "CUSTOMER_ID", nullable = false, length = 30)
	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "DISCOUNT_AMOUNT", nullable = false, length = 30)
	public BigDecimal getDiscount() {
		return discount;
	}

	public void setDiscount(BigDecimal discount) {
		this.discount = discount;
	}

	@Column(name = "COMPLIMENTARY_PRODUCT_ID", nullable = false, length = 30)
	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	@Column(name = "COMPLIMENTARY_PRODUCT_QUANTITY", nullable = false, length = 30)
	public Integer getProductQuantity() {
		return productQuantity;
	}

	public void setProductQuantity(Integer productQuantity) {
		this.productQuantity = productQuantity;
	}

	@Column(name = "ADD_TIME", nullable = false)
	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

}
