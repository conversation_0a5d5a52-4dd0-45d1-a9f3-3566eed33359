@import "../../../less/variables";
@import "../../../less/elements";
@import (reference) "../../../less/bootstrap/bootstrap";

.ui-grid-pager-panel {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    padding-top: 3px;
    padding-bottom: 3px;
}

.ui-grid-pager-container {
  float: left;
}

.ui-grid-pager-control {
  margin-right: 10px;
  margin-left: 10px;
  min-width: 135px;
  float: left;

  button {
    height: 25px;
    min-width: 26px;
    #ui-grid-twbs > .btn;
    #ui-grid-twbs > .button-variant(@paginationButtonColor, @paginationButtonBackgroundColor, @paginationButtonBorderColor);
  }

  input {
    #ui-grid-twbs > .form-control();
    #ui-grid-twbs > .input-sm ();
    display: inline;
    height: 26px;
    width: 50px;
    vertical-align: top;
  }

  .ui-grid-pager-max-pages-number{
    vertical-align: bottom;
    > * {
      vertical-align: middle;
    }
  }

  .first-bar {
    width: 10px;
    border-left: 2px solid #4d4d4d;
    margin-top: -6px;
    height: 12px;
    margin-left: -3px;
  }

  .first-triangle {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 8.7px 5px 0;
    border-color: transparent #4d4d4d transparent transparent;
    margin-left: 2px;
  }

  .next-triangle {
    margin-left: 1px;
  }

  .prev-triangle {
    margin-left: 0;
  }

  .last-triangle {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 0 5px 8.7px;
    border-color: transparent transparent transparent #4d4d4d;
    margin-left: -1px;
  }

  .last-bar {
    width: 10px;
    border-left: 2px solid #4d4d4d;
    margin-top: -6px;
    height: 12px;
    margin-left: 1px;
  }
}

.ui-grid-pager-row-count-picker {
  float: left;

  select {
    #ui-grid-twbs > .form-control;
    #ui-grid-twbs > .input-sm ();
    height: 26px;
    width: 60px;
    display: inline;
  }

  .ui-grid-pager-row-count-label {
    margin-top: 3px;
  }
}

.ui-grid-pager-count-container {
  float: right;
  margin-top: 4px;
  min-width: 50px;

  .ui-grid-pager-count {
    margin-right: 10px;
    margin-left: 10px;
    float: right;
  }
}
