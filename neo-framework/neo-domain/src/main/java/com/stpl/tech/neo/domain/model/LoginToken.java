package com.stpl.tech.neo.domain.model;

public class LoginToken {

	private String token;
	private CustomerLoginRequest data;
	private long endTime;

	public LoginToken() {
		super();
	}

	public LoginToken(String token, CustomerLoginRequest data, long delay) {
		super();
		this.token = token;
		this.data = data;
		this.endTime = System.currentTimeMillis() + delay;
	}

	public boolean isExpired(long time) {
		return endTime < time;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public CustomerLoginRequest getData() {
		return data;
	}

	public void setData(CustomerLoginRequest data) {
		this.data = data;
	}

	public long getEndTime() {
		return endTime;
	}

	public void setEndTime(long endTime) {
		this.endTime = endTime;
	}

}
