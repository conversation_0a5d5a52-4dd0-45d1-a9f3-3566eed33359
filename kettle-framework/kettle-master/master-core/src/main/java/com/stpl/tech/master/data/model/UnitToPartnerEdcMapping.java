package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "UNIT_TO_PARTNER_EDC_MAPPING")
public class UnitToPartnerEdcMapping implements Serializable {


    private static final long serialVersionUID = -7332864696890191719L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MAPPING_ID", unique = true, nullable = false)
    private Integer id;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Column(name = "PARTNER_NAME", nullable = false)
    private String partnerName;

    @Column(name = "STATUS", nullable = false)
    private String status;

    @Column(name = "MERCHANT_ID", nullable = false)
    private String merchantId;

    @Column(name = "T_ID", nullable = false)
    private String tId;

    @Column(name = "TERMINAL_ID", nullable = false)
    private String terminalId;

    @Column(name = "MERCHANT_KEY", nullable = false)
    private  String merchantKey;

    @Column(name = "VERSION", nullable = false)
    private  String version;

    @Column(name = "SECRET_KEY", nullable = false)
    private String secretKey;
}
