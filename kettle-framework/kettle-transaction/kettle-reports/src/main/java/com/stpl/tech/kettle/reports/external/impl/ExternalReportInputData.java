/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.ReportStatusEvent;
import com.stpl.tech.kettle.reports.core.ReportInput;
import com.stpl.tech.master.domain.model.Unit;

public class ExternalReportInputData implements ReportInput<Unit> {

	private List<OrderInfo> orderInfos;
	private Unit unit;
	private Date businessDate;
	private ReportStatusEvent event;
	private EnvironmentProperties props;
	private Map<String, String> configMap;

	public ExternalReportInputData(List<OrderInfo> orderInfos, Unit unit, Date businessDate, ReportStatusEvent event,
			EnvironmentProperties props, Map<String, String> configMap) {
		super();
		this.orderInfos = orderInfos;
		this.unit = unit;
		this.businessDate = businessDate;
		this.event = event;
		this.props = props;
		this.configMap = configMap;
	}

	@Override
	public Unit getContext() {
		return unit;
	}

	public List<OrderInfo> getOrderInfos() {
		return orderInfos;
	}

	public void setOrderInfos(List<OrderInfo> orderInfos) {
		this.orderInfos = orderInfos;
	}

	public Unit getUnit() {
		return unit;
	}

	public void setUnit(Unit unit) {
		this.unit = unit;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	public EnvironmentProperties getProps() {
		return props;
	}

	public void setProps(EnvironmentProperties props) {
		this.props = props;
	}

	public ReportStatusEvent getEvent() {
		return event;
	}

	public void setEvent(ReportStatusEvent event) {
		this.event = event;
	}

	public Map<String, String> getConfigMap() {
		return configMap;
	}

	public void setConfigMap(Map<String, String> configMap) {
		this.configMap = configMap;
	}

}
