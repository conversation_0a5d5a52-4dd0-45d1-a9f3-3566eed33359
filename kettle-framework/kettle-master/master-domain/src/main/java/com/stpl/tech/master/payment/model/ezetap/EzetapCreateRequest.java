package com.stpl.tech.master.payment.model.ezetap;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.payment.model.PaymentRequest;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class EzetapCreateRequest implements Serializable, PaymentRequest {

	/**
	 *
	 */
	private static final long serialVersionUID = 792409328181800049L;
	@JsonProperty("receipt")
	private String orderId;
//	@JsonProperty("currency")
//	private String currency;
	@JsonProperty("amount")
	private int transactionAmount;
//	@JsonProperty("payment_capture")
//	private boolean paymentCapture;
	private String status;


	@JsonProperty("id")
	private String paymentTransactionId;

	public EzetapCreateRequest() {

	}

	public EzetapCreateRequest(String orderId, BigDecimal paidAmount) {

		this.orderId = orderId;
		this.transactionAmount = paidAmount.intValue();
		//this.currency = request.getCurrency();
		//this.paymentCapture = request.getPaymentCapture();
	}

	public EzetapCreateRequest(EzetapServiceRequest request, String transactionId) {

		this.orderId = request.getOrderId();
		this.transactionAmount = request.getTransactionAmount();
//		this.currency = request.getCurrency();
//		this.paymentCapture = request.getPaymentCapture();
		this.paymentTransactionId =  transactionId;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public int getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(int transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Override
	public Map<String, String> getPersistentAttributes() {
		Map<String, String> attributes = new HashMap<>();
		attributes.put("order_id", orderId);
		attributes.put("amount", String.valueOf(transactionAmount));
		return attributes;
	}

	@Override
	public String getStatus() {
		return this.status;
	}/*

	public String getPaymentTransactionId() {
		return paymentTransactionId;
	}

	public void setPaymentTransactionId(String paymentTransactionId) {
		this.paymentTransactionId = paymentTransactionId;
	}*/

	@Override
	public String getPartnerOrderId() {
		return paymentTransactionId;
	}

	
}
