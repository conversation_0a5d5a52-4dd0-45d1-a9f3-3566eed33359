//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.21 at 04:26:33 PM IST 
//


package com.stpl.tech.checklist.model;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.mongodb.core.mapping.Document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="mappingType" type="{http://www.w3schools.com}CheckListMappingType"/&gt;
 *         &lt;element name="mappingValue" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="checkLists" type="{http://www.w3schools.com}CheckList" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "mappingType",
    "mappingValue",
    "status",
    "checkLists"
})
@XmlRootElement(name = "CheckListMapping")
@JsonIgnoreProperties(value = {"handler"})
@Document
public class CheckListMapping {

	@Id
	private String _id;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected CheckListMappingType mappingType;
    @XmlElement(required = true)
    protected String mappingValue;
    @XmlElement(required = true)
    protected String status;
    protected List<CheckList> checkLists;

    
    public String get_id() {
		return _id;
	}

	public void set_id(String id) {
		this._id = id;
	}

	/**
     * Gets the value of the mappingType property.
     * 
     * @return
     *     possible object is
     *     {@link CheckListMappingType }
     *     
     */
    public CheckListMappingType getMappingType() {
        return mappingType;
    }

    /**
     * Sets the value of the mappingType property.
     * 
     * @param value
     *     allowed object is
     *     {@link CheckListMappingType }
     *     
     */
    public void setMappingType(CheckListMappingType value) {
        this.mappingType = value;
    }

    /**
     * Gets the value of the mappingValue property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMappingValue() {
        return mappingValue;
    }

    /**
     * Sets the value of the mappingValue property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMappingValue(String value) {
        this.mappingValue = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the checkLists property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the checkLists property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getCheckLists().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link CheckList }
     * 
     * 
     */
    public List<CheckList> getCheckLists() {
        if (checkLists == null) {
            checkLists = new ArrayList<CheckList>();
        }
        return this.checkLists;
    }

	public void setCheckLists(List<CheckList> checkLists) {
		this.checkLists = checkLists;
	}

}
