/*
 * Created By Shanmukh
 */

package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "ROLE_CHANGE_AUDIT_LOG_DATA")
public class RoleChangeAuditLogData {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ROLE_CHANGE_AUDIT_LOG_ID",nullable = false,unique = true)
    private Integer roleChangeAuditLogId;

    @Column(name = "KEY_ID")
    private Integer keyId;

    @Column(name = "KEY_TYPE")
    private String keyType;

    @Column(name = "ROLE_ID")
    private Integer roleId;

    @Column(name = "UPLOADED_DOCUMENT_ID")
    private Integer uploadedDocumentId;

    @Column(name = "LOGGED_BY")
    private String loggedBy;

    @Column(name = "LOGGED_AT")
    private Date loggedAt;

    public Integer getRoleChangeAuditLogId() {
        return this.roleChangeAuditLogId;
    }

    public void setRoleChangeAuditLogId(Integer roleChangeAuditLogId) {
        this.roleChangeAuditLogId = roleChangeAuditLogId;
    }

    public Integer getKeyId() {
        return this.keyId;
    }

    public void setKeyId(Integer keyId) {
        this.keyId = keyId;
    }

    public String getKeyType() {
        return this.keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public Integer getRoleId() {
        return this.roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public Integer getUploadedDocumentId() {
        return uploadedDocumentId;
    }

    public void setUploadedDocumentId(Integer uploadedDocumentId) {
        this.uploadedDocumentId = uploadedDocumentId;
    }

    public String getLoggedBy() {
        return this.loggedBy;
    }

    public void setLoggedBy(String loggedBy) {
        this.loggedBy = loggedBy;
    }

    public Date getLoggedAt() {
        return loggedAt;
    }

    public void setLoggedAt(Date loggedAt) {
        this.loggedAt = loggedAt;
    }
}
