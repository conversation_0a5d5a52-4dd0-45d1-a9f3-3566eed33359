package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.Filter;

@Entity
@Table(name = "PRICE_UPDATE_DRILLDOWN")
public class PriceUpdateEntryDrillDown {

	private Integer id;
	private PriceUpdateEntryData entryId;
	private PriceUpdateEventData eventId;
	private String drilldownCategory;
	private String drilldownType;
	private String keyType;
	private int keyId;
	private String keyName;
	private String unitOfMeasure;
	private BigDecimal unitPrice;
	private BigDecimal cost;
	private BigDecimal quantity;
	private String hasError;
	private List<PriceUpdateEntryError> errors = new ArrayList<PriceUpdateEntryError>();


	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "DRILLDOWN_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EVENT_ID", nullable = false)
	public PriceUpdateEventData getEventId() {
		return eventId;
	}

	public void setEventId(PriceUpdateEventData eventId) {
		this.eventId = eventId;
	}
	

	/**
	 * @return the entryId
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ENTRY_ID", nullable = false)
	public PriceUpdateEntryData getEntryId() {
		return entryId;
	}

	/**
	 * @param entryId the entryId to set
	 */
	public void setEntryId(PriceUpdateEntryData entryId) {
		this.entryId = entryId;
	}

	@Column(name = "KEY_TYPE", nullable = false)
	public String getKeyType() {
		return keyType;
	}

	public void setKeyType(String keyType) {
		this.keyType = keyType;
	}

	@Column(name = "KEY_ID", nullable = false)
	public int getKeyId() {
		return keyId;
	}

	public void setKeyId(int keyId) {
		this.keyId = keyId;
	}

	@Column(name = "KEY_NAME", nullable = false)
	public String getKeyName() {
		return keyName;
	}

	public void setKeyName(String keyName) {
		this.keyName = keyName;
	}

	@Column(name = "UOM", nullable = false)
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	@Column(name = "UNIT_PRICE", nullable = true)
	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	@Column(name = "COST", nullable = false)
	public BigDecimal getCost() {
		return cost;
	}

	public void setCost(BigDecimal updatedUnitPrice) {
		this.cost = updatedUnitPrice;
	}

	@Column(name = "QUANTITY", nullable = true)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal editedUnitPrice) {
		this.quantity = editedUnitPrice;
	}

	@Column(name = "HAS_ERROR", nullable = true)
	public String getHasError() {
		return hasError;
	}

	public void setHasError(String errorCode) {
		this.hasError = errorCode;
	}

	/**
	 * @return the drilldownCategory
	 */
	@Column(name = "DRILLDOWN_CATEGORY", nullable = true)
	public String getDrilldownCategory() {
		return drilldownCategory;
	}

	/**
	 * @param drilldownCategory the drilldownCategory to set
	 */
	public void setDrilldownCategory(String drilldownCategory) {
		this.drilldownCategory = drilldownCategory;
	}

	/**
	 * @return the drilldownType
	 */
	@Column(name = "DRILLDOWN_TYPE", nullable = true)
	public String getDrilldownType() {
		return drilldownType;
	}

	/**
	 * @param drilldownType the drilldownType to set
	 */
	public void setDrilldownType(String drilldownType) {
		this.drilldownType = drilldownType;
	}

	/**
	 * @return the errors
	 */
	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
	@JoinColumn(name = "ENTRY_ID", nullable = true)
	@Filter(name="drillDown")
	public List<PriceUpdateEntryError> getErrors() {
		return errors;
	}

	/**
	 * @param errors the errors to set
	 */
	public void setErrors(List<PriceUpdateEntryError> errors) {
		this.errors = errors;
	}
	
}
