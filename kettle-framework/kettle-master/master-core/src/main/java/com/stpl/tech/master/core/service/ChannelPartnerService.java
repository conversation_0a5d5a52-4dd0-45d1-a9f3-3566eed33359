package com.stpl.tech.master.core.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.master.domain.model.ChangeMenuStatusRequest;
import org.springframework.web.multipart.MultipartFile;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.ChannelPartner;
import com.stpl.tech.master.data.model.MenuSequenceMappingData;
import com.stpl.tech.master.data.model.ProductGroupData;
import com.stpl.tech.master.data.model.ProductGroupImageData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMappingData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.MenuRecommendation;
import com.stpl.tech.master.domain.model.MenuSequence;
import com.stpl.tech.master.domain.model.MenuSequenceRequestVO;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.PriceProfileDetail;
import com.stpl.tech.master.domain.model.ProductGroup;
import com.stpl.tech.master.domain.model.UnitChannelPartnerMapping;
import com.stpl.tech.master.domain.model.UnitPartnerMenuMapping;

public interface ChannelPartnerService {
    List<IdCodeName> getAllChannelPartners(Date businessDate);

    IdCodeName updateChannelPartner(ChannelPartner channelPartner, Date businessDate)
            throws DataNotFoundException;

    List<UnitChannelPartnerMappingData> getAllUnitChannelPartnerMappings();

    UnitChannelPartnerMappingData addUnitChannelPartnerMapping(UnitChannelPartnerMapping mapping) throws DataUpdationException;

    UnitChannelPartnerMappingData getUnitChannelPartnerMappingByUnitAndPartnerId(Integer unitId, Integer partnerId);

    UnitChannelPartnerMappingData activateUnitChannelPartnerMapping(Integer mappingId, boolean activate);

    UnitPartnerMenuMapping addUnitPartnerMenuMapping(UnitPartnerMenuMapping mapping, Boolean isBulk)
            throws DataUpdationException;

    List<UnitChannelPartnerMenuMappingData> getAllUnitPartnerMenuMapping();

    boolean activateUnitPartnerMenuMapping(Integer mappingId, boolean activate) throws DataUpdationException;

    boolean bulkActivateUnitPartnerMenuMapping(List<Integer> mappingIds, boolean action);


    MenuSequence getMenuSequence(MenuSequenceRequestVO requestVO) throws DataNotFoundException;

    MenuSequence getMenu(Integer menuSequenceId, Integer menuRecommendationSequenceId,Integer priceProfileId ,  Map<Integer, Boolean> productRecommendationMappingMap) throws DataNotFoundException;

    List<ProductGroup> createProductGroups(List<ProductGroup> request) throws DataUpdationException;

    ProductGroupData updateGroup(ProductGroup request) throws DataUpdationException;

    List<ProductGroup> getProductGroups();

    List<ProductGroup> getProductGroupsByFilter(String groupType, String menuAppType);

    List<ProductGroup> mapProductSequence(List<ProductGroup> request) throws DataUpdationException;

    List<ProductGroup> updateProductSequence(List<ProductGroup> request) throws DataUpdationException;

    List<MenuSequence> getMenus() throws DataUpdationException;

    MenuSequence getMenuForSequenceId(Integer menuSequenceId) throws DataUpdationException;

    List<MenuSequence> getMenusShort(String status) throws DataUpdationException;

    Boolean changeMenuStatus(Integer menuSeqId, String status) throws DataUpdationException;

    MenuSequence createMenu(MenuSequence request) throws DataUpdationException;

    MenuSequence addMenuSequenceMapping(MenuSequence request) throws DataUpdationException;

    ProductGroupImageData saveIcon(MimeType mimeType, String iconName, String iconDescription, MultipartFile file, int createdBy, String hostUrl) throws DataUpdationException;

    List<ProductGroupImageData> getIcons();

    List<MenuSequenceMappingData> getGroupMapping();


    List<MenuRecommendation> getRecommendation();

    MenuRecommendation createRecommendation(MenuRecommendation request) throws DataUpdationException;

    Boolean createRecommendationMapping(MenuRecommendation request) throws DataUpdationException;

    Boolean unitRecommendationMapping(List<IdIndex> list);

    Boolean updateRecommendationStatus(Integer id, String status);

    List<Integer> getUnitIdsForMenuSequence(Integer menuSequenceId, Integer kettlePartnerId);

    List<Integer> getMenuSequenceIdsAsPerMenuApp(MenuApp menuApp);

    List<ProductGroupData> getProductGroupDataAsPerIdAndMenuApp(List<Integer> ids, MenuApp menuApp);

    List<Integer> getCategoryIdsMenuSequenceMapping(List<Integer> menuSequenceIds);

    public IdCodeName saveCategoryImage(final MultipartFile file,String hostUrl) throws DataUpdationException;

    PriceProfileDetail getUnitPartnerProfilePrice(Integer priceProfileId);

    List<PriceProfileDetail> getUnitPartnerProfilePrice();

    Boolean unitRecommendationPriceProfileMapping(List<IdIndex> list);

    boolean addProfilePriceMapping(PriceProfileDetail detail);

    List<PriceProfileDetail> getProfilePrice(String PriceProfileStrategy);

    boolean updateProfilePriceMapping(IdCodeName detail);


}
