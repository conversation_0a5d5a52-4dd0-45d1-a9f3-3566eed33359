package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.Map;

public class ChangesDiffNotificationTemplate extends AbstractVelocityTemplate {
    private Map<String, Pair<Object,Object>> diffs;
    private String changedBy;
    private String className;
    private Integer objectId;
    private String basePath;
    private Boolean isNew;


    public ChangesDiffNotificationTemplate(Map<String, Pair<Object, Object>> diffs, String changedBy , String className ,
                                           Integer objectId , String basePath , Boolean isNew) {
        this.diffs = diffs;
        this.changedBy = changedBy;
        this.className = className;
        this.objectId = objectId;
        this.basePath = basePath;
        this.isNew = isNew;

    }

    @Override
    public String getTemplatePath() {
        return "templates/ChangesDiffTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + "ObjectDiffs" + "/" + className + "_" + changedBy + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> diffsMap = new HashMap<>();
        diffsMap.put("diffs",diffs);
        diffsMap.put("keys",diffs.keySet());
        diffsMap.put("Id",objectId);
        diffsMap.put("object",className);
        diffsMap.put("isNew",isNew);

        return diffsMap;
    }


}
