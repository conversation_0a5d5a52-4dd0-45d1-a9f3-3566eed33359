/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.jms.JMSException;

import com.stpl.tech.kettle.core.data.vo.EmailData;
import com.stpl.tech.kettle.domain.model.OrderItemCancellationRequest;
import com.stpl.tech.master.data.model.CouponDetailData;

import com.stpl.tech.analytics.model.PartnerOrderRiderData;
import com.stpl.tech.kettle.commission.MonthlyAOVDetail;
import com.stpl.tech.kettle.commission.PartnerAOVRequest;
import com.stpl.tech.kettle.core.EmailStatus;
import com.stpl.tech.kettle.core.OrderEmailEntryType;
import com.stpl.tech.kettle.core.data.vo.CreateOrderResult;
import com.stpl.tech.kettle.core.data.vo.DelayReason;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.core.data.vo.PartnerDataConsiderRequest;
import com.stpl.tech.kettle.core.data.vo.PartnerDataWithOrderConsideration;
import com.stpl.tech.kettle.core.data.vo.SubscriptionProduct;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.notification.NewOrderNotification;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.notification.UnitOrder;
import com.stpl.tech.kettle.core.notification.UnitOrderOld;
import com.stpl.tech.kettle.data.model.CustomerAdditionalDetail;
import com.stpl.tech.kettle.data.model.CustomerMappingTypes;
import com.stpl.tech.kettle.data.model.GamifiedOfferResponse;
import com.stpl.tech.kettle.data.model.LoyaltyEvents;
import com.stpl.tech.kettle.data.model.MenuProductCogsDrilldown;
import com.stpl.tech.kettle.data.model.MenuProductCostData;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderDetailForFeedback;
import com.stpl.tech.kettle.data.model.OrderInvoiceDetail;
import com.stpl.tech.kettle.data.model.OrderStatusEvent;
import com.stpl.tech.kettle.data.model.SpecialOfferRequest;
import com.stpl.tech.kettle.data.model.SpecialOfferResponse;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.domain.model.CreateNextOfferRequest;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.DayCloseEstimateData;
import com.stpl.tech.kettle.domain.model.ExternalPartnerDetail;
import com.stpl.tech.kettle.domain.model.MyOfferResponse;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderComaplaintZomatoRequest;
import com.stpl.tech.kettle.domain.model.OrderComplaintResponse;
import com.stpl.tech.kettle.channelpartner.core.queue.model.OrderDeliveryStatusUpdate;
import com.stpl.tech.kettle.domain.model.OrderInAppUrlResponse;
import com.stpl.tech.kettle.domain.model.OrderItemConsumable;
import com.stpl.tech.kettle.domain.model.OrderPaymentDetailData;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.OrderStatusDomain;
import com.stpl.tech.kettle.domain.model.RequestInvoiceDetail;
import com.stpl.tech.kettle.domain.model.TestCampaignNotificationRequest;
import com.stpl.tech.kettle.domain.model.WalletEventData;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.CampaignDetailData;
import com.stpl.tech.master.domain.model.*;
import com.stpl.tech.kettle.domain.model.OrderRefund;
import com.stpl.tech.util.TemplateRenderingException;

public interface OrderManagementService {

	/**
	 * Create oder with the given order details
	 *
	 * @param order
	 * @return
	 * @throws DataUpdationException
	 */
	public CreateOrderResult createOrder(Order order) throws DataUpdationException;

	/**
	 * Update the given order. A settled order cannot be deleted
	 *
	 * @param order
	 * @return
	 * @throws DataUpdationException
	 */
	public boolean updateOrder(Order order) throws DataUpdationException;

	/**
	 * Delete an order. This changes the state of the order to be deleted
	 *
	 * @return
	 * @throws DataUpdationException
	 * @throws DataNotFoundException
	 */
	public boolean deleteOrder(int unitId, String generatedOrderId, int cancelledBy, int cancelApprovedBy,
							   String reason, Integer reasonId, String bookWastage)
			throws DataUpdationException, DataNotFoundException, CardValidationException;

	void postOrderCancellationUpdateToDineInServer(int orderId, Integer customerId, Integer partnerId);

	public void postUpdateActions(int orderId, Integer customerId, Integer partnerId, boolean isDelivery, OrderStatus status);

	public boolean changeSettlementMode(int unitId, int orderId, int editedBy, List<Pair<Integer, Integer>> settlements,
										UnitCategory orderSource) throws DataUpdationException, TemplateRenderingException, DataNotFoundException;

	public boolean updateStatus(int orderEmailId, EmailStatus status, String errorMessage);

	public void addReprintRequest(int orderId, int generatedBy, int approvedBy, String reason);

	public void generateOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount, Customer customer,
										boolean isSystemGenerated);

	public OrderStatusEvent updateOrderStatus(Integer orderId, OrderStatus orderStatus, int approvedBy, int generatedBy,
											  int unitId, String reason, Boolean refund, Integer reasonId, String bookWastage);

	public boolean createOrderEnquiry(Order order);

	public boolean updateOrderStatusInCache(Integer orderId, OrderStatus orderStatus, Integer userId, Integer unitId,
											UnitCategory orderSource, String reason, Boolean refund, Integer reasonId, String bookWastage);

	public void generateOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount, String emailId,
										boolean isSystemGenerated, boolean isEmailVerified, Date currentTimestamp);

	public NewOrderNotification fetchNewOrders(UnitOrder unitOrder) throws DataNotFoundException;

	/**
	 * @param o
	 * @param values
	 */
	public int addCost(Order o, Collection<Consumable> values);

	/**
	 * @param orderId
	 * @param sumoId
	 */
	void setWastageSumoId(int orderId, int sumoId);

	public int remainingBillCount(int unitId, int startNo, int endNo);

	public int validateBillBookNo(int unitId, int billBookNo);

	/**
	 * @param unitOrder
	 * @return
	 */
	public Map<Integer, OrderStatus> fetchNewStatuses(UnitOrder unitOrder);

	public boolean updateOrderStatusOnSDPCallback(String riderContactNo, String allotedNo, String reason, int reasonId);

	public Map<String, String> getSDPOrderDetails(String riderContactNo) throws TemplateRenderingException, DataNotFoundException;

	public boolean resetSDPOrderDetails(String riderContactNo) throws TemplateRenderingException, DataNotFoundException;

	public boolean outOfDeliveryNotification(int orderId) throws TemplateRenderingException, DataNotFoundException;

	public boolean getPaymentStatus(String cardtId);

	public List<OrderStatusDomain> getOrderTransitionDetail(int orderId);

	/**
	 * @param unitOrder
	 * @return
	 * @throws DataNotFoundException
	 */
	NewOrderNotification fetchNewOrders(UnitOrderOld unitOrder) throws DataNotFoundException;

	public void saveMonthlyConsumptionData(UnitBasicDetail ubd, int month, int year,
										   Collection<OrderItemConsumable> values, String source);

	public ExternalPartnerDetail getExternalPartnerDetail(String partnerCode);

	public void markNPSfeedbackCancelled(Integer orderId);

	public MenuProductCostData addCogsData(MenuProductCostData cogsData, List<MenuProductCogsDrilldown> drilldowns) throws DataUpdationException;

	public void updateNewCustomerFlagForOrders(int unitId, Date businessDate);

	byte[] getOrderReceipt(String orderId);

	public void updateFeedbackUrl(int orderId, String feedbackUrl);

	public OrderDetail getOrderDetail(int orderId);

	public void saveRiderDetails(PartnerOrderRiderData request);

	String getCustomerOfferLastAppliedTime(int i, String offerCode);

	void invalidateCashBack(Order order);

	void attachFeedback(List<Order> orderDetails) throws DataNotFoundException;

	OrderInAppUrlResponse getOrdersWithDineInNPSFeedback(List<Integer> orderIds) throws DataNotFoundException;

	public PartnerDataWithOrderConsideration setMissionGaramChai(PartnerDataConsiderRequest request);

	PartnerDataWithOrderConsideration setCountsForMissionGaramChai(PartnerDataWithOrderConsideration partner, PartnerDataConsiderRequest request, Integer threshold, Integer maxOrdervalue, PartnerDataWithOrderConsideration cache);

	Long checkDeliveryOrderForCustomer(Integer customerId);

	Long checkDineInOrderForCustomer(Integer customerId);

	boolean sendFreeChaiDeliveryDetails(String contactNumber);

	CustomerAdditionalDetail saveFreeChaiDeliveryDetails(int customerId, String campaignName, Boolean status);

	boolean saveCustomerInfoBrandWise(int customerId, int brandId, int orderId, Date billingServerTime,boolean isSpecialOrder);

	boolean checkIsNewCustomerBrandWise(int customerId, int brandId);

	boolean deleteCogsData(Integer unitId, Integer closureId) throws DataUpdationException;

	void pushDataToThirdPartyAnalytics(OrderInfo info, boolean isOrderCancelled);

	void publishCustomerTransactionViewEvent(int customerId, int brandId, Integer orderId);

	Map<Integer, PartnerDataWithOrderConsideration> getMissionGaramChaiCountForUnit(int unitId);

	OrderDetailForFeedback getOrderDetailForFeedback(FeedbackTokenInfo feedbackTokenInfo) throws DataNotFoundException;

	List<Integer> unsettledKettleOrdersDetailList(List<Integer> ids);

	public OrderInvoiceDetail setInvoiceDetail(RequestInvoiceDetail requestInvoiceDetail);

    CampaignDetailData getCampaignDetailById(Integer campaignId);

	void updateCustomerTransaction(int customerId, int brandId);

	List<CustomerMappingTypes> getCustomerMappingTypes();

	public String generateInvoiceIdDetail(String stateCode);

	Pair<MyOfferResponse, NextOffer> createGeneralOffer(CreateNextOfferRequest request, Customer customer, CampaignDetail campaignDetail, Integer lagDays);
	public void loadPartnerOrderCache();

	Pair<MyOfferResponse, NextOffer> createDeliveryGeneralOffer(CreateNextOfferRequest request, Customer customer, CampaignDetail campaignDetail, Integer lagDays);

	MyOfferResponse noOfferResponse(Customer customer);

    boolean testNotification(TestCampaignNotificationRequest request) throws JMSException, IOException;

	void removeSecondFreeChai(Integer customerId,String signupOfferStatus);

    public List<DayCloseEstimateData> getDayCloseEstimatesData(List<Integer> fountain9UnitIds, Date startDate, Date endDate);

	SubscriptionPlan createSubscription(SubscriptionProduct subscriptionProduct, Customer customer, Integer campaignId, String source, Integer lagDays)
			throws DataUpdationException;

	SpecialOfferResponse getSpecialOffer(SpecialOfferRequest request) throws AuthenticationFailureException, DataUpdationException, JMSException, IOException;

    Boolean isSpecialOfferExist(String contact, String token);

    Boolean isGamifiedOfferExist(String contact, String token, String source);

    OrderComplaintResponse updateOrderComplaintZomato(OrderComaplaintZomatoRequest orderComplaintZomatoRequest);

    void revertSubscriptionSaving(int id, BigDecimal savings);

	Boolean updateOrderStatus(String generatedOrderId);

    List<MonthlyAOVDetail> getPartnerMonthAndBrandWiseAOV(PartnerAOVRequest partnerAOVRequest);

	void resetOverallFrequency(Order orderDetail);

	public void notifyOverWebsocket(OrderInfo info, Boolean isRoutedToAssembly);

	public void updateOrderForDeliveryStatusUpdate(OrderDeliveryStatusUpdate orderDeliveryStatusUpdate) throws DataNotFoundException, TemplateRenderingException;

    Map<String, BigDecimal> getMonthlyOrdersOfRecipe(Integer recipeId, String region);

	boolean saveRiderDelayReason(DelayReason delayReason) throws DataNotFoundException, TemplateRenderingException;

	public void refreshAllListData();

    List<Order> getOrdersWithoutCommission(String startDate, String endDate);

    boolean saveWalletSuggestion(WalletEventData walletEventData);

	public List<LoyaltyEvents> getLoyaltyEvents(String id,Integer limit,String searchType) throws DataNotFoundException;

    public List<OrderPaymentDetailData> getOrderPaymentDetail(Integer customerId, Integer orderId, String paymentSource, String contactNumber, Integer startPosition);

    public List<OrderPaymentDetailData> getOrderPaymentDetailBySourceAndStatus(String paymentSource, String paymentStatus, Date startDate);

    public void getAdditionalOfferData(GamifiedOfferResponse res);

	public void updateSugarVariant(Order order);
	public CouponDetailData getCustomerCouponMapping(Order order);

	public void pushDataToCleverTapForPartner(OrderInfo info,boolean isOrderCancelled);

    boolean resendEmail(EmailData emailData);

	Boolean cancelOrderItem(OrderItemCancellationRequest orderItemCancellationRequest, Integer cancelledBy, Order order) throws DataNotFoundException,
			TemplateRenderingException;

	Boolean createOrderItemStatusEvent(OrderItemCancellationRequest orderItemCancellationRequest ,
									   String fromStatus , String toStatus,
									   String transitionStatus , Integer updatedBy);

    List<OrderStatusEvent> getLastOrderTransactionEvent(int orderId);

	public Map<String, Map<String, String>> fetchOrderItemNewStatus(UnitOrder unitOrder) throws DataNotFoundException;
	boolean isOrderContainHoldItem(OrderInfo info);

	void cancelPendingOrdersOfTestingUnits(Integer unitId) throws DataNotFoundException;

	List<OrderRefund> getServiceChargeRefundOrder(Integer unitId, Date startDate) throws DataNotFoundException;

}
