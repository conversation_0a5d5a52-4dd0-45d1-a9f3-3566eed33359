/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.cache;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.data.dao.DeliveryDao;
import com.stpl.tech.kettle.data.model.DeliveryPartner;
import com.stpl.tech.kettle.data.model.PartnerAttributes;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;

/**
 * Created by shikhar on 19-04-2016.
 */
@Repository
public class DeliveryServiceCache {

    private static final Logger LOG = LoggerFactory.getLogger(DeliveryServiceCache.class);

    @Autowired
    private DeliveryDao deliveryDao;

    @Autowired
    private DeliveryPartnerPriorityMappings unitToDeliveryMappingsCache;

    private List<UnitToDeliveryPartnerMappings> unitPartnerMappings = new ArrayList<UnitToDeliveryPartnerMappings>();
    private Map<Integer, Map<String, String>> apiProfiles = new HashMap<Integer, Map<String, String>>();
    private Map<Integer, String> automatedPartnerMap = new HashMap<Integer, String>();
    private HashMap<Integer, String> cashEligiblePartners = new HashMap<>();


    @PostConstruct
    public void init() {
		LOG.info("POST-CONSTRUCT DeliveryServiceCache - STARTED");
    	Stopwatch watch1 = Stopwatch.createUnstarted();
    	watch1.start();
    	Stopwatch watch = Stopwatch.createUnstarted();
    	watch.start();
    	refreshAutomatedPartnerMap();
    	LOG.info("Inside POSTCONSTRUCT - DeliveryServiceCache refreshAutomatedPartnerMap  : took {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
    	watch.reset();
    	watch.start();
        refreshPartnerAttributes();
    	LOG.info("Inside POSTCONSTRUCT - DeliveryServiceCache refreshPartnerAttributes  : took {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
    	watch.reset();
    	watch.start();
        refreshPriorityCache();
    	LOG.info("Inside POSTCONSTRUCT - DeliveryServiceCache refreshPriorityCache  : took {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
    	LOG.info("Inside POSTCONSTRUCT - DeliveryServiceCache OVERALL: took {} ms", watch1.stop().elapsed(TimeUnit.MILLISECONDS));
    }

    public void refreshPartnerAttributes() {
        List<PartnerAttributes> partnerAttributeObjects = deliveryDao.getObjectMappings();

        if (partnerAttributeObjects != null && partnerAttributeObjects.size() > 0) {
            for (PartnerAttributes mapping : partnerAttributeObjects) {
                LOG.info("mapping  in post construct for each loop:::: {}", mapping.toString());
                Map<String, String> partnerIdMap = apiProfiles.get(mapping.getPartnerId());
                if (partnerIdMap != null && !partnerIdMap.isEmpty()) {
                    partnerIdMap.put(mapping.getMappingType(), mapping.getMappingValue());
                } else {
                    Map<String, String> keyValueMap = new HashMap<String, String>();
                    keyValueMap.put(mapping.getMappingType(), mapping.getMappingValue());
                    apiProfiles.put(mapping.getPartnerId(), keyValueMap);
                }
                LOG.info("api Profile for Partner productId is {} is of size :::: {} ", mapping.getPartnerId(),
                        apiProfiles.get(mapping.getPartnerId()).size());
            }
        }
    }

    private void refreshAutomatedPartnerMap() {
        automatedPartnerMap = deliveryDao.getAutomatedPartnerMap();
        LOG.info("size of automated partnerMap :::::::::: {}", automatedPartnerMap.size());
    }

    public void refreshPriorityCache(){
        if (unitPartnerMappings == null) {
            unitPartnerMappings = new ArrayList<>();
        }
        if (unitPartnerMappings != null && !unitPartnerMappings.isEmpty()) {
            unitPartnerMappings.clear();
        }
        List<UnitToDeliveryPartnerMappings> deliveryPartners = deliveryDao.getDeliveryPartnerPriorityForUnits();
        unitPartnerMappings.addAll(deliveryPartners);
        refreshCashEligibilityCache(deliveryPartners);
        // setting the cache of unit to delivery partner mappings
        unitToDeliveryMappingsCache.setMappings(unitPartnerMappings);
    }

    private void refreshCashEligibilityCache(List<UnitToDeliveryPartnerMappings> deliveryPartners) {
        if (cashEligiblePartners == null) {
            cashEligiblePartners = new HashMap<>();
        }
        for(UnitToDeliveryPartnerMappings partner : deliveryPartners){
            DeliveryPartner deliveryPartner = partner.getDeliveryPartner();
            if(deliveryPartner!=null && !cashEligiblePartners.containsKey(deliveryPartner.getPartnerId())){
                cashEligiblePartners.put(deliveryPartner.getPartnerId(), deliveryPartner.getEligibleForCash());
            }
        }
    }


    public List<UnitToDeliveryPartnerMappings> getUnitPartnerMappings() {
        return unitPartnerMappings;
    }

    public Map<Integer,String> getAutomatedPartnerMap() {
        return automatedPartnerMap;
    }

    public String getCashEligibleStatus(int partnerId) {
        return cashEligiblePartners.get(partnerId);
    }

    public Map<Integer,Map<String,String>> getApiProfiles() {
        return apiProfiles;
    }

    @Override
    public String toString() {
        return "DeliveryServiceCache{" +
                "unitPartnerMappings=" + unitPartnerMappings.size() +
                ", apiProfiles=" + apiProfiles.size() +
                ", automatedPartnerMap=" + automatedPartnerMap.size() +
                ", cashEligiblePartners=" + cashEligiblePartners.size() +
                '}';
    }
}
