package com.stpl.tech.master.payment.model.gpay;

public enum GPayRefundFailureStatus {
    FAILURE_REASON_UNSPECIFIED, MERCHANT_INELIGIBLE, ORIGINAL_TRANSACTION_TOO_OLD,
    OR<PERSON>INAL_TRANSACTION_NOT_SUCCESSFUL, EXCEEDS_ORIGINAL_TRANSACTION_AMOUNT, PAYER_ACCOUNT_ERROR,
    REFUND_EXPIRED, OR<PERSON>INAL_TRANSACTION_DOES_NOT_EXIST, ORIGINAL_TRANSACTION_DOES_NOT_BELONG_TO_MERCHANT,
    REFUND_TRANSACTION_FAILED, REFUND_AMOUNT_EXCEEDS_MAXIMUM_REFUND_TRANSACTION_LIMIT,
    REFUND_AMOUNT_BELOW_MINIMUM_REFUND_TRANSACTION_LIMIT

    }
