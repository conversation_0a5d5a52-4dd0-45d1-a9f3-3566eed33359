package com.stpl.tech.forms.core.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.text.DocumentException;
import com.stpl.tech.forms.core.exception.WarningLetterException;
import com.stpl.tech.forms.core.notification.email.WarningLetterReportNotifiaction;
import com.stpl.tech.forms.core.notification.email.template.WarningLetterNotificationTemplate;
import com.stpl.tech.forms.core.service.AuditService;
import com.stpl.tech.forms.core.service.EnvProperties;
import com.stpl.tech.forms.core.service.WarningService;
import com.stpl.tech.forms.core.templates.WarningLetterPDFTemplate;
import com.stpl.tech.forms.core.util.AuditUtil;
import com.stpl.tech.forms.data.AuditDataConverter;
import com.stpl.tech.forms.data.dao.WarningDao;
import com.stpl.tech.forms.data.model.DocumentDetailData;
import com.stpl.tech.forms.domain.model.Audit;
import com.stpl.tech.forms.domain.model.DocUploadType;
import com.stpl.tech.forms.domain.model.DocumentDetail;
import com.stpl.tech.forms.domain.model.FileType;
import com.stpl.tech.forms.domain.model.IdCodeName;
import com.stpl.tech.forms.domain.model.MimeType;
import com.stpl.tech.master.core.external.cache.EnvironmentPropertiesCache;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.AttachmentData;
import com.stpl.tech.warning.data.model.EmployeeWarningDetail;
import com.stpl.tech.warning.data.model.EmployeeWarningReasonDetail;
import com.stpl.tech.warning.data.model.EmployeeWarningStatusDetail;
import com.stpl.tech.warning.data.model.WarningImageDetailData;
import com.stpl.tech.warning.domain.model.ActionTakenBy;
import com.stpl.tech.warning.domain.model.EmployeeWarning;
import com.stpl.tech.warning.domain.model.EmployeeWarningReason;
import com.stpl.tech.warning.domain.model.EmployeeWarningStatus;
import com.stpl.tech.warning.domain.model.ReasonStatus;
import com.stpl.tech.warning.domain.model.WarningActionType;
import com.stpl.tech.warning.domain.model.WarningImageDetail;
import com.stpl.tech.warning.domain.model.WarningStage;
import com.stpl.tech.warning.domain.model.WarningStatus;

@Service
public class WarningServiceImpl implements WarningService {

	private static final Logger LOG = LoggerFactory.getLogger(WarningServiceImpl.class);

	@Autowired
	private WarningDao dao;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private EnvProperties envProperties;

	@Autowired
	private FileArchiveService fileArchiveService;

	@Autowired
	private AuditService auditService;

	@Autowired
	private EnvironmentPropertiesCache propertiesCache;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Integer addWarningLetter(EmployeeWarning employeeWarning)
			throws WarningLetterException, TemplateRenderingException {
		EmployeeWarningDetail detail = new EmployeeWarningDetail();
		Date currentTimeStamp = AppUtils.getCurrentTimestamp();
		if (isAreaManager(employeeWarning.getInitiator().getAuthorisedPerson().getId())) {
			detail.setWarningStage(WarningStage.PENDING_DGM_APPROVAL.name());
		} else {
			detail.setWarningStage(WarningStage.PENDING_AM_APPROVAL.name());
		}
		if(employeeWarning.getAudit() != null){
			detail.setPqscAuditId(employeeWarning.getAudit().getId());
		}
		detail.setUnitId(employeeWarning.getUnit().getId());
		detail.setManagerOnDuty(employeeWarning.getManagerOnDuty().getId());
		detail.setGulityPersonId(employeeWarning.getGuiltyPerson().getId());
		detail.setGulityPersonName(employeeWarning.getGuiltyPerson().getName());
		detail.setAreaMangerId(masterDataCache.getUnit(employeeWarning.getUnit().getId()).getManagerId());
		if (employeeWarning.getGuiltyPerson().getCode() != null) {
			detail.setGulityPersonDesg(employeeWarning.getGuiltyPerson().getCode());
		}
		detail.setImpactType(employeeWarning.getImpactType().name());
		detail.setWarningStatus(WarningStatus.PENDING.name());
		detail.setCreationTime(currentTimeStamp);
		detail.setDateOfIncidence(AppUtils.getDate(employeeWarning.getDoi()));
		detail = dao.add(detail, true);
		EmployeeWarningStatusDetail statusDetail = null;
		if (detail != null) {
			employeeWarning.getInitiator().setWarningId(detail.getWarningId());
			statusDetail = saveWarningStatusDetail(employeeWarning.getInitiator(),
					(employeeWarning.getAudit() != null ? employeeWarning.getAudit().getId() : null), currentTimeStamp);
		}

		if (statusDetail != null) {
			detail.setLatestStatusId(statusDetail.getStatusId());
			dao.update(detail, true);
		}
		File warningReport = getWarningReportFile(detail);
		sendWarningLetterNotification(detail, warningReport, employeeWarning.getInitiator().getAuthorisedPerson().getId());
		return detail.getWarningId();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<EmployeeWarning> getWarningLetters(Integer unitId, Date startDate, Date endDate, String status,
			Integer amId) {
		List<EmployeeWarningDetail> warningDetails = dao.getWarningLetters(unitId, startDate, endDate, status, amId);
		List<EmployeeWarning> warningList = new ArrayList<EmployeeWarning>();

		for (EmployeeWarningDetail detail : warningDetails) {
			EmployeeWarning warning = AuditDataConverter.convert(detail, masterDataCache);
			setWarningImages(warning);
			getWarningStatus(warning, detail.getWarningId(), false);
			warningList.add(warning);
		}
		return warningList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public EmployeeWarning getWarningLetter(Integer warningId) {
		EmployeeWarningDetail warningDetail = dao.find(EmployeeWarningDetail.class, warningId);
		EmployeeWarning warning = AuditDataConverter.convert(warningDetail, masterDataCache);
		setWarningImages(warning);
		getWarningStatus(warning, warningId, true);
		return warning;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean processWarningAction(EmployeeWarningStatus warningStatus)
			throws TemplateRenderingException, IOException, WarningLetterException {
		boolean result = false;

		Date currentTimeStamp = AppUtils.getCurrentTimestamp();
		EmployeeWarningDetail warningDetail = dao.find(EmployeeWarningDetail.class, warningStatus.getWarningId());
		EmployeeWarningStatusDetail statusDetail = null;

		switch (warningStatus.getActionTakenBy()) {

		case AppConstants.AM_RESPONSE:
			warningStatus.setToStatus(WarningStage.PENDING_DGM_APPROVAL.name());
			break;

		case AppConstants.DGM_RESPONSE:
			if (warningStatus.getActionType() == WarningActionType.ACCEPT) {
				warningStatus.setToStatus(WarningStage.PENDING_HR_APPROVAL_FOR_ACCEPT.name());
			} else if (warningStatus.getActionType() == WarningActionType.REJECT) {
				warningStatus.setToStatus(WarningStage.PENDING_HR_APPROVAL_FOR_REJECT.name());
			}
			break;

		case "hrResponse":
			if (warningStatus.getActionType() == WarningActionType.ACCEPT) {
				warningStatus.setToStatus(WarningStage.HR_ACCEPT.name());
				if (warningStatus.getFromStatus().equals(WarningStage.PENDING_HR_APPROVAL_FOR_REJECT.name())) {
					warningDetail.setHrDGMConflict("Y");
				}
				warningDetail.setWarningStatus(WarningStatus.APPROVED.name());
			} else if (warningStatus.getActionType() == WarningActionType.REJECT) {
				warningStatus.setToStatus(WarningStage.HR_REJECT.name());
				if (warningStatus.getFromStatus().equals(WarningStage.PENDING_HR_APPROVAL_FOR_ACCEPT.name())) {
					warningDetail.setHrDGMConflict("Y");
				}
				warningDetail.setWarningStatus(WarningStatus.REJECTED.name());
			}
			break;
		case "cancelResponse":
			warningStatus.setToStatus(WarningStage.CANCEL.name());
			warningDetail.setWarningStatus(WarningStatus.CANCELLED.name());
			break;
		}

		statusDetail = saveWarningStatusDetail(warningStatus, currentTimeStamp, false);
		if (statusDetail != null) {
			warningDetail.setLatestStatusId(statusDetail.getStatusId());
			warningDetail.setWarningStage(statusDetail.getToStatus());
			warningDetail = dao.update(warningDetail, true);
			if (warningDetail != null) {
				File warningReport = null;
				if (warningDetail.getWarningStatus().equals(WarningStatus.APPROVED.name())
						|| warningDetail.getWarningStatus().equals(WarningStatus.REJECTED.name())) {
					warningReport = uploadWarningReportToS3(warningDetail, warningStatus);
				} else {
					warningReport = getWarningReportFile(warningDetail);
				}
				sendWarningLetterNotification(warningDetail, warningReport, warningStatus.getAuthorisedPerson().getId());
				result = true;
			}
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public void downloadWarningImage(HttpServletResponse response, Integer imageId)
			throws IOException, WarningLetterException {
		if (imageId != null) {

			WarningImageDetailData warningImage = dao.find(WarningImageDetailData.class, imageId);
			FileDetail fileDetail = new FileDetail(warningImage.getS3Bucket(), warningImage.getS3Key(),
					warningImage.getImageUrl());
			File file = fileArchiveService.getFileFromS3(envProperties.getBasePath() + File.separator + "s3",
					fileDetail);
			if (file != null) {
				response.setContentType(warningImage.getMimeType());
				response.addHeader("Content-Disposition", "attachment; filename=" + file.getName());
				byte[] bytesArray = new byte[(int) file.length()];
				response.setContentLength(bytesArray.length);
				try {
					OutputStream outputStream = response.getOutputStream();
					InputStream inputStream = new FileInputStream(file);
					int counter = 0;
					while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
						outputStream.write(bytesArray, 0, counter);
						outputStream.flush();
					}
					outputStream.close();
					inputStream.close();
				} catch (IOException e) {
					LOG.error("Encountered error while writing warning image file to response stream", e);
					throw e;
				} finally {
					response.getOutputStream().flush();
					file.delete(); // delete the temporary file created
									// after completing request
				}
			}
		} else {
			throw new WarningLetterException("Please provide image id");
		}
	}

	
	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public void downloadWarningReport(HttpServletResponse response, Integer warningId)
			throws WarningLetterException, TemplateRenderingException, IOException, DocumentException {
		if (warningId != null) {
			EmployeeWarningDetail warningDetail = dao.find(EmployeeWarningDetail.class, warningId);
			if (warningDetail != null) {
				DocumentDetailData document = dao.find(DocumentDetailData.class, warningDetail.getWarningLetterId());
				FileDetail fileDetail = new FileDetail(document.getS3Bucket(), document.getS3Key(),
						document.getFileUrl());
				File file = fileArchiveService.getFileFromS3(envProperties.getBasePath() + File.separator + "s3",
						fileDetail);
				if (file != null) {
					response.setContentType(document.getMimeType());
					response.addHeader("Content-Disposition", "attachment; filename=" + file.getName());
					byte[] bytesArray = new byte[(int) file.length()];
					response.setContentLength(bytesArray.length);
					try {
						OutputStream outputStream = response.getOutputStream();
						InputStream inputStream = new FileInputStream(file);
						int counter = 0;
						while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
							outputStream.write(bytesArray, 0, counter);
							outputStream.flush();
						}
						outputStream.close();
						inputStream.close();
					} catch (IOException e) {
						LOG.error("Encountered error while writing warning letter file to response stream", e);
						throw e;
					} finally {
						response.getOutputStream().flush();
						file.delete(); // delete the temporary file created
										// after completing request
					}
				}
			} else {
				throw new WarningLetterException("Please provide valid warning id");
			}
		} else {
			throw new WarningLetterException("Please provide warning id");
		}
	}

	
	
	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean saveImages(MultipartFile[] files, Integer warningId) {
		boolean result = false;
		Date currentTimeStamp = AppUtils.getCurrentTimestamp();

		for (MultipartFile file : files) {
			if (file.isEmpty()) {
				continue;
			}
			String fileName = warningId + "_" + System.currentTimeMillis() + "_" + file.getOriginalFilename();
			String baseDir = "WarningImage/" + warningId;
			FileDetail s3File = fileArchiveService.saveFileToS3(envProperties.getS3AuditBucket(), baseDir, fileName,
					file);
			if (s3File != null) {
				WarningImageDetailData data = new WarningImageDetailData();
				data.setImageName(fileName);
				data.setImageUrl(s3File.getUrl());
				data.setWarningId(warningId);
				data.setCreatedOn(currentTimeStamp);
				data.setStatus(ReasonStatus.ACTIVE.name());
				data.setMimeType(MimeType.IMG.name());
				data.setS3Key(s3File.getKey());
				data.setS3Bucket(s3File.getBucket());
				dao.add(data, true);
				result = true;
			}
		}
		return result;
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public DocumentDetail saveImage(MultipartFile file, int userId) {
		if (file.isEmpty()) {
			return null;
		}
		String fileName = System.currentTimeMillis() + "_" + file.getOriginalFilename();
		String baseDir = "auditImage";
		FileDetail s3File = fileArchiveService.saveFileToS3(envProperties.getS3AuditBucket(), baseDir, fileName, file);
		if (s3File != null) {

			DocumentDetailData documentDetailData = new DocumentDetailData();
			documentDetailData.setUpdatedBy(userId);
			documentDetailData.setDocumentLink(fileName);
			documentDetailData.setFileUrl(s3File.getUrl());
			documentDetailData.setDocumentUploadType(DocUploadType.AUDIT_IMAGE.name());
			documentDetailData.setDocumentUploadTypeId(0);
			documentDetailData.setFileType(FileType.AUDIT_IMAGE.name());
			documentDetailData.setMimeType(MimeType.IMG.name());
			documentDetailData.setUpdateTime(AuditUtil.getCurrentTimestamp());
			documentDetailData.setS3Key(s3File.getKey());
			documentDetailData.setS3Bucket(s3File.getBucket());
			documentDetailData = dao.add(documentDetailData, true);
			if (documentDetailData.getDocumentId() != null) {
				DocumentDetail detail = AuditDataConverter.convert(documentDetailData);
				return detail;
			}
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean processWarningActionJobs(String actionBy) {

		LOG.info("processWarningActionJobs : actionBy "+actionBy);
		List<EmployeeWarningDetail> warningDetails = null;
		if (actionBy.equals(AppConstants.AM_RESPONSE)) {
			warningDetails = dao.getWarningLetters(WarningStage.PENDING_AM_APPROVAL.name());
		} else if (actionBy.equals(AppConstants.DGM_RESPONSE)) {
			warningDetails = dao.getWarningLetters(WarningStage.PENDING_DGM_APPROVAL.name());
		}
		Date currentDate = AppUtils.getCurrentDate();

		for (EmployeeWarningDetail detail : warningDetails) {
			EmployeeWarningStatusDetail statusDetail = dao.find(EmployeeWarningStatusDetail.class,
					detail.getLatestStatusId());
			if (envProperties.getEnvType() == EnvType.DEV || AppUtils.getDaysDiff(statusDetail.getActionTakenOn(), currentDate) > 3) {
				LOG.info("Warningresponse processed : "+detail.getWarningId());
				EmployeeWarningStatus warningStatus = new EmployeeWarningStatus();
				warningStatus.setActionTakenBy(actionBy);
				warningStatus.setActionType(WarningActionType.ACCEPT);
				warningStatus.setWarningId(detail.getWarningId());

				if (actionBy.equals(AppConstants.AM_RESPONSE)) {
					warningStatus.setAuthorisedPerson(new IdCodeName(detail.getAreaMangerId(), "", ""));
					warningStatus.setComment("SYSTEM_GENERATED_COMMENT : WARNING LETTER SENT TO DGM FOR ACTION");
				} else if (actionBy.equals(AppConstants.DGM_RESPONSE)) {
					int dgmId = masterDataCache.getEmployeeBasicDetail(detail.getAreaMangerId())
							.getReportingManagerId();
					warningStatus.setAuthorisedPerson(new IdCodeName(dgmId, "", ""));
					warningStatus.setComment("SYSTEM_GENERATED_COMMENT : WARNING LETTER SENT TO HR FOR ACTION");
				}
				warningStatus.setFromStatus(detail.getWarningStage());
				warningStatus.setSystemResponse(true);
				try {
					processWarningAction(warningStatus);
				} catch (TemplateRenderingException | IOException | WarningLetterException e) {
					LOG.info("Error while running waribg action job taken by : " + actionBy + "  : " + e.getMessage());
				}
			}
		}
		return true;
	}

	private void sendWarningLetterNotification(EmployeeWarningDetail warningDetail, File warningReport, int initiatorId)
			throws WarningLetterException, TemplateRenderingException {
		List<String> mailIds = new ArrayList<>();
		String emails = null;
		boolean notDev = !AuditUtil.isDev(envProperties.getEnvType());
		switch (WarningStage.valueOf(warningDetail.getWarningStage())) {
		case PENDING_AM_APPROVAL:
			emails = propertiesCache.getPendingAmMailIds();
			if(notDev){
				mailIds.add(masterDataCache.getEmployeeBasicDetail(warningDetail.getAreaMangerId()).getEmailId());
				mailIds.add(masterDataCache.getEmployeeBasicDetail(initiatorId).getEmailId());
			}
			break;
		case PENDING_DGM_APPROVAL:
			emails = propertiesCache.getPendingDgmMailIds();
			if(notDev){
				mailIds.add(masterDataCache.getEmployeeBasicDetail(warningDetail.getAreaMangerId()).getEmailId());
				mailIds.add(getDgmEmailId(warningDetail.getAreaMangerId()));
			}
			break;
		case PENDING_HR_APPROVAL_FOR_ACCEPT:
			emails = propertiesCache.getPendingHrMailIds();
			if(notDev){
				mailIds.add(getDgmEmailId(warningDetail.getAreaMangerId()));
			}
			break;
		case PENDING_HR_APPROVAL_FOR_REJECT:
			emails = propertiesCache.getPendingHrMailIds();
			if(notDev){
				mailIds.add(getDgmEmailId(warningDetail.getAreaMangerId()));
			}
			break;
		case CANCEL:
			emails = propertiesCache.getWarningCancelledMailIds();
			if(notDev){
				mailIds.add(masterDataCache.getEmployeeBasicDetail(warningDetail.getAreaMangerId()).getEmailId());
				if(warningDetail.getAreaMangerId() != initiatorId){
					mailIds.add(masterDataCache.getEmployeeBasicDetail(initiatorId).getEmailId());
				}else{
					mailIds.add(getDgmEmailId(warningDetail.getAreaMangerId()));
				}
			}
			break;
		case HR_ACCEPT:
			emails = propertiesCache.getWarningApprovedMailIds();
			if(notDev){
				mailIds.add(masterDataCache.getEmployeeBasicDetail(warningDetail.getAreaMangerId()).getEmailId());
				mailIds.add(getDgmEmailId(warningDetail.getAreaMangerId()));
				if(warningDetail.getAreaMangerId() != initiatorId){
					mailIds.add(masterDataCache.getEmployeeBasicDetail(initiatorId).getEmailId());
				}
			}
			break;
		case HR_REJECT:
			emails = propertiesCache.getWarningRejectedMailIds();
			if(notDev){
				mailIds.add(masterDataCache.getEmployeeBasicDetail(warningDetail.getAreaMangerId()).getEmailId());
				mailIds.add(getDgmEmailId(warningDetail.getAreaMangerId()));
				if(warningDetail.getAreaMangerId() != initiatorId){
					mailIds.add(masterDataCache.getEmployeeBasicDetail(initiatorId).getEmailId());
				}
			}
			break;
		default:
			LOG.info(
					"Invalid warning letter stage for sending email notification : " + warningDetail.getWarningStage());
			break;
		}
		if (emails == null && mailIds.size() == 0) {
			LOG.info("Email id not provided sending email notification for satge : " + warningDetail.getWarningStage());
		}else{
			if(emails != null){
				mailIds.addAll(Arrays.asList(emails.split(",")));
			}
			
			WarningLetterNotificationTemplate template = new WarningLetterNotificationTemplate(envProperties.getBasePath(),
					AuditDataConverter.convert(warningDetail, masterDataCache),
					masterDataCache.getEmployeeBasicDetail(warningDetail.getGulityPersonId()), mailIds);
			WarningLetterReportNotifiaction emailNotification = new WarningLetterReportNotifiaction(template,
					envProperties.getEnvType());
			try {
				List<AttachmentData> attachments = new ArrayList<>();
				AttachmentData warningLetterDetail = new AttachmentData();
				warningLetterDetail.setAttachment(IOUtils.toByteArray(new FileInputStream(warningReport)));
				warningLetterDetail.setContentType("application/pdf");
				warningLetterDetail.setFileName(warningReport.getName());
				attachments.add(warningLetterDetail);
				emailNotification.sendRawMail(attachments);
				warningReport.delete();// delete the temp file created on Server
			} catch (Exception e) {
				LOG.error("Error while sending email to for warning report :", e);
				throw new WarningLetterException("Error while sending email to for warning report :");
			}
		}
	}
	
	private String getDgmEmailId(int amId){
		int dgmId = masterDataCache.getEmployeeBasicDetail(amId).getReportingManagerId();
		return masterDataCache.getEmployeeBasicDetail(dgmId).getEmailId();
	}

	private File getWarningReportFile(EmployeeWarningDetail employeeWarningDetail)
			throws TemplateRenderingException, WarningLetterException {
		String auditReportHTMLTemplate = generateWarningLetterTemplate(employeeWarningDetail);
		String path = envProperties.getBasePath() + "/warningLetter/" + employeeWarningDetail.getWarningStage() + "_"
				+ employeeWarningDetail.getWarningId() + ".pdf";
		try (OutputStream outputStream = new FileOutputStream(path)) {
			HtmlConverter.convertToPdf(auditReportHTMLTemplate, outputStream);
			outputStream.flush();
			File file = new File(path);
			return file;
		} catch (IOException e) {
			LOG.info("Error creating warning letter report : ", e);
			throw new WarningLetterException("Error creating warning letter report");
		}
	}

	private File uploadWarningReportToS3(EmployeeWarningDetail employeeWarningDetail,
			EmployeeWarningStatus warningStatus)
			throws TemplateRenderingException, IOException, WarningLetterException {
		String warningReportHTMLTemplate = generateWarningLetterTemplate(employeeWarningDetail);
		String path = envProperties.getBasePath() + "/warningLetter/" + employeeWarningDetail.getWarningId() + ".pdf";
		try (OutputStream outputStream = new FileOutputStream(path)) {
			HtmlConverter.convertToPdf(warningReportHTMLTemplate, outputStream);
			outputStream.flush();
			File file = new File(path);
			String fileName = employeeWarningDetail.getWarningId() + ".pdf";
			String baseDir = "warningLetter/WARNING_LETTER_REPORT";
			try {
				FileDetail s3File = fileArchiveService.saveFileToS3(envProperties.getS3AuditBucket(), baseDir, file,
						true);
				if (s3File != null) {
					DocumentDetailData documentDetailData = new DocumentDetailData();
					documentDetailData.setUpdatedBy(warningStatus.getAuthorisedPerson().getId());
					documentDetailData.setDocumentLink(fileName);
					documentDetailData.setFileUrl(s3File.getUrl());
					documentDetailData.setDocumentUploadType(DocUploadType.WARNING_LETTER.name());
					documentDetailData.setDocumentUploadTypeId(0);
					documentDetailData.setFileType(FileType.WARNING_LETTER.name());
					documentDetailData.setMimeType(MimeType.PDF.name());
					documentDetailData.setUpdateTime(AuditUtil.getCurrentTimestamp());
					documentDetailData.setS3Key(s3File.getKey());
					documentDetailData.setS3Bucket(s3File.getBucket());
					documentDetailData = dao.add(documentDetailData, true);
					if (documentDetailData.getDocumentId() != null) {
						employeeWarningDetail.setWarningLetterId(documentDetailData.getDocumentId());
						employeeWarningDetail = dao.update(employeeWarningDetail, true);
						if (employeeWarningDetail != null) {
							return file;
						} else {
							throw new WarningLetterException("Error updating report id: "
									+ documentDetailData.getDocumentId().toString() + " for warning letter.");
						}
					} else {
						throw new WarningLetterException("Error saving document detail for warning letter : key:"
								+ s3File.getKey() + " url: " + s3File.getUrl());
					}
				} else {
					throw new WarningLetterException("Error uploading warning letter to S3.");
				}
			} catch (Exception e) {
				LOG.error("Encountered error while uploading warning letter to S3", e);
				throw new WarningLetterException("Encountered error while uploading warning letter to S3");
			}
		} catch (IOException e) {
			throw new WarningLetterException("Error creating warning letter report");
		}
	}

	private String generateWarningLetterTemplate(EmployeeWarningDetail employeeWarningDetail)
			throws TemplateRenderingException, WarningLetterException {
		if (employeeWarningDetail != null) {
			EmployeeWarning warning = getWarningLetter(employeeWarningDetail.getWarningId());
			Audit audit = null;
			if (employeeWarningDetail.getPqscAuditId() != null) {
				audit = auditService.getAudit(employeeWarningDetail.getPqscAuditId());
			}
			WarningLetterPDFTemplate pdfTemplate = new WarningLetterPDFTemplate(envProperties.getBasePath(), warning,
					masterDataCache.getEmployeeBasicDetail(warning.getGuiltyPerson().getId()), audit);
			return pdfTemplate.getContent();
		} else {
			throw new WarningLetterException("Please provide valid warning id");
		}
	}

	private EmployeeWarningStatusDetail saveWarningStatusDetail(EmployeeWarningStatus warningStatus, Integer auditId,
			Date currentTimeStamp) {
		EmployeeWarningStatusDetail detail = new EmployeeWarningStatusDetail();
		detail.setWarningId(warningStatus.getWarningId());
		if (isAreaManager(warningStatus.getAuthorisedPerson().getId())) {
			detail.setFromStatus(WarningStage.BY_AM.name());
			detail.setToStatus(WarningStage.PENDING_DGM_APPROVAL.name());
		} else {
			detail.setFromStatus(WarningStage.BY_AUDIT.name());
			detail.setToStatus(WarningStage.PENDING_AM_APPROVAL.name());
		}
		detail.setAuthorityId(warningStatus.getAuthorisedPerson().getId());
		detail.setAuthorityName(
				masterDataCache.getEmployeeBasicDetail(warningStatus.getAuthorisedPerson().getId()).getName());
		detail.setAuthorityDesg(
				masterDataCache.getEmployeeBasicDetail(warningStatus.getAuthorisedPerson().getId()).getDesignation());
		detail.setComment(warningStatus.getComment());
		detail.setActionTakenBy(ActionTakenBy.SELF.name());
		detail.setActionTakenOn(currentTimeStamp);
		detail = dao.add(detail, true);
		if (detail != null) {
			saveWarningReasonDetail(warningStatus, detail, currentTimeStamp);
		}
		return detail;
	}

	private void saveWarningReasonDetail(EmployeeWarningStatus warningStatus, EmployeeWarningStatusDetail statusDetail,
			Date currentTimeStamp) {
		for (EmployeeWarningReason reason : warningStatus.getReasons()) {
			EmployeeWarningReasonDetail detail = new EmployeeWarningReasonDetail();
			detail.setWarningId(statusDetail.getWarningId());
			detail.setWarningStatusId(statusDetail.getStatusId());
			detail.setReasonAddedBy(reason.getReasonAddedBy().name());
			detail.setReasonId(reason.getReasonId());
			detail.setReasonName(reason.getReasonName());
			detail.setReasonCategory(reason.getCategory());
			detail.setStatus(ReasonStatus.ACTIVE.name());
			detail.setLastUpdatedOn(currentTimeStamp);
			dao.add(detail, true);
		}
	}

	private EmployeeWarning getWarningStatus(EmployeeWarning warning, Integer warningId, boolean reasonRequired) {
		List<EmployeeWarningStatusDetail> statusDetails = dao.getWarningStatusDetail(warningId);
		for (EmployeeWarningStatusDetail statusDetail : statusDetails) {
			if (statusDetail.getFromStatus().equals(WarningStage.BY_AM.name())
					|| statusDetail.getFromStatus().equals(WarningStage.BY_AUDIT.name())) {
				EmployeeWarningStatus warningStatus = AuditDataConverter.convert(statusDetail, masterDataCache);
				updateWarningReasons(reasonRequired, warningStatus, statusDetail.getStatusId());
				warning.setInitiator(warningStatus);
			} else if (statusDetail.getFromStatus().equals(WarningStage.PENDING_AM_APPROVAL.name())
					&& !statusDetail.getToStatus().equals(WarningStage.CANCEL.name())) {
				EmployeeWarningStatus warningStatus = AuditDataConverter.convert(statusDetail, masterDataCache);
				updateWarningReasons(reasonRequired, warningStatus, statusDetail.getStatusId());
				warning.setAmResponse(warningStatus);
			} else if (statusDetail.getFromStatus().equals(WarningStage.PENDING_DGM_APPROVAL.name())
					&& !statusDetail.getToStatus().equals(WarningStage.CANCEL.name())) {
				EmployeeWarningStatus warningStatus = AuditDataConverter.convert(statusDetail, masterDataCache);
				updateWarningReasons(reasonRequired, warningStatus, statusDetail.getStatusId());
				warning.setDgmResponse(warningStatus);
			} else if (statusDetail.getToStatus().equals(WarningStage.HR_ACCEPT.name())
					|| statusDetail.getToStatus().equals(WarningStage.HR_REJECT.name())) {
				EmployeeWarningStatus warningStatus = AuditDataConverter.convert(statusDetail, masterDataCache);
				updateWarningReasons(reasonRequired, warningStatus, statusDetail.getStatusId());
				warning.setHrResponse(warningStatus);
			} else if (statusDetail.getToStatus().equals(WarningStage.CANCEL.name())) {
				EmployeeWarningStatus warningStatus = AuditDataConverter.convert(statusDetail, masterDataCache);
				updateWarningReasons(reasonRequired, warningStatus, statusDetail.getStatusId());
				warning.setCancelResponse(warningStatus);
			}

		}
		return warning;
	}

	private void updateWarningReasons(boolean reasonRequired, EmployeeWarningStatus warningStatus, Integer statusId) {
		if (reasonRequired) {
			List<EmployeeWarningReasonDetail> warningReasons = dao.getWarningReasons(statusId);
			for (EmployeeWarningReasonDetail reasonDetail : warningReasons) {
				EmployeeWarningReason reason = AuditDataConverter.convert(reasonDetail, masterDataCache);
				warningStatus.getReasons().add(reason);
			}
		}
	}

	private EmployeeWarningStatusDetail saveWarningStatusDetail(EmployeeWarningStatus warningStatus,
			Date currentTimeStamp, boolean reason) {
		EmployeeWarningStatusDetail detail = new EmployeeWarningStatusDetail();
		detail.setWarningId(warningStatus.getWarningId());
		detail.setFromStatus(warningStatus.getFromStatus());
		detail.setToStatus(warningStatus.getToStatus());
		detail.setAuthorityId(warningStatus.getAuthorisedPerson().getId());
		detail.setAuthorityName(
				masterDataCache.getEmployeeBasicDetail(warningStatus.getAuthorisedPerson().getId()).getName());
		detail.setAuthorityDesg(
				masterDataCache.getEmployeeBasicDetail(warningStatus.getAuthorisedPerson().getId()).getDesignation());
		detail.setComment(warningStatus.getComment());
		if(warningStatus.isSystemResponse()){
			detail.setActionTakenBy(ActionTakenBy.SYSTEM.name());
		}else{
			detail.setActionTakenBy(ActionTakenBy.SELF.name());
		}

		detail.setActionTakenOn(currentTimeStamp);
		detail = dao.add(detail, true);
		if (reason && detail != null) {
			saveWarningReasonDetail(warningStatus, detail, currentTimeStamp);
		}
		return detail;
	}
	
	private void setWarningImages(EmployeeWarning warning) {
		List<WarningImageDetailData> detailDatas = dao.getWarningImages(warning.getId(), ReasonStatus.ACTIVE.name());
		List<WarningImageDetail> imageList = new ArrayList<>();
		for (WarningImageDetailData data : detailDatas) {
			imageList.add(AuditDataConverter.convert(data));
		}
		if (imageList.size() > 0) {
			warning.setImages(imageList);
		}
	}
	
	private boolean isAreaManager(int empId){
		return masterDataCache.getEmployeeBasicDetail(empId)
		.getDesignation().equalsIgnoreCase("Area Manager");
	}
}
