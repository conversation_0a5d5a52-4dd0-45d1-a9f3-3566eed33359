package com.stpl.tech.report.service.controller;

import java.util.Objects;

public class TemperatureLoggerKey {

	private String locationId;
	private String deviceName;
	private String deviceLocation;

	public TemperatureLoggerKey(String locationId, String deviceName, String deviceLocation) {
		super();
		this.locationId = locationId;
		this.deviceName = deviceName;
		this.deviceLocation = deviceLocation;
	}

	public String getLocationId() {
		return locationId;
	}

	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}

	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

	public String getDeviceLocation() {
		return deviceLocation;
	}

	public void setDeviceLocation(String deviceLocation) {
		this.deviceLocation = deviceLocation;
	}

	@Override
	public int hashCode() {
		return Objects.hash(deviceLocation, deviceName, locationId);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		TemperatureLoggerKey other = (TemperatureLoggerKey) obj;
		return Objects.equals(deviceLocation, other.deviceLocation) && Objects.equals(deviceName, other.deviceName)
				&& Objects.equals(locationId, other.locationId);
	}

}
