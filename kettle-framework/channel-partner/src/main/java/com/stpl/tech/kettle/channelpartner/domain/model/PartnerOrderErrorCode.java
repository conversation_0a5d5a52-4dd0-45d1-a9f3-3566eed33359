package com.stpl.tech.kettle.channelpartner.domain.model;

public enum PartnerOrderErrorCode {
	UNIT_MISSING(false, true), UNIT_INVALID(false, true), EMPTY_ORDER(false, true), PRODUCT_MISMATCH(false, true),
	PRICE_MISMATCH(true, false), TAX_MISMATCH(true, false), TRANSACTION_MISMATCH(true, false),
	STOCK_NOT_SUFFICIENT(false, false), STOCK_NOT_AVAILABLE(false, false), STOCK_NOT_FOUND(false, false),
	SIZE_MISMATCH(false, true), OUTSIDE_BUSINESS_HOURS(false, true);

	final boolean canBeProcessed;
	final boolean toBeRejected;

	PartnerOrderErrorCode(boolean canBeProcessed, boolean toBeRejected) {
		this.canBeProcessed = canBeProcessed;
		this.toBeRejected = toBeRejected;
	}

	public boolean isCanBeProcessed() {
		return canBeProcessed;
	}

	public boolean isToBeRejected() {
		return toBeRejected;
	}

}
