/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.util.AppConstants;

import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


/**
 * ChannelPartner generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "LOYALTY_SCORE")
public class LoyaltyScore implements java.io.Serializable {

	private Integer loyaltyPointsId;
	private Integer customerId;
	private Integer acquiredPoints;
	private Integer cumulativePoints;
	private Integer blockedPoints;
	private Date lastOrderTime;
	private Integer lastOrderId;
	private Integer orderCount;
	private String availedSignupOffer;
	private Date lastNPSTime;
	private String signupOfferExpired;
	private Date signupOfferExpiryTime;
	private String signupOfferStatus;
	private Integer redemptionOrderId;
	private Integer totalRedeemedPoints;
	private Integer totalExpiredPoints;

	public LoyaltyScore() {
	}

	public LoyaltyScore(Integer customerId, Integer acquiredPoints) {
		this.customerId = customerId;
		this.acquiredPoints = acquiredPoints;
		this.cumulativePoints = acquiredPoints;
		this.availedSignupOffer = AppConstants.NO;
		this.signupOfferStatus = SignupOfferStatus.AVAILABLE.name();
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "LOYALTY_POINTS_ID", unique = true, nullable = false)
	public Integer getLoyaltyPointsId() {
		return this.loyaltyPointsId;
	}

	public void setLoyaltyPointsId(Integer loyaltyPointsId) {
		this.loyaltyPointsId = loyaltyPointsId;
	}

	@Column(name = "CUSTOMER_ID", nullable = false)
	public Integer getCustomerId() {
		return this.customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "ACQUIRED_POINTS", nullable = true)
	public Integer getAcquiredPoints() {
		return this.acquiredPoints;
	}

	public void setAcquiredPoints(Integer acquiredPoints) {
		this.acquiredPoints = acquiredPoints;
	}

	@Column(name = "CUMULATIVE_POINTS", nullable = true)
	public Integer getCumulativePoints() {
		return this.cumulativePoints;
	}

	public void setCumulativePoints(Integer cumulativePoints) {
		this.cumulativePoints = cumulativePoints;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_ORDER_TIME", nullable = true, length = 19)
	public Date getLastOrderTime() {
		return lastOrderTime;
	}

	public void setLastOrderTime(Date lastOrderTime) {
		this.lastOrderTime = lastOrderTime;
	}

	@Column(name = "LAST_ORDER_ID", nullable = true)
	public Integer getLastOrderId() {
		return lastOrderId;
	}

	public void setLastOrderId(Integer lastOrderId) {
		this.lastOrderId = lastOrderId;
	}

	@Column(name = "ORDER_COUNT", nullable = true)
	public Integer getOrderCount() {
		return orderCount;
	}

	public void setOrderCount(Integer orderCount) {
		this.orderCount = orderCount;
	}
	@Column(name = "AVAILED_SIGNUP_OFFER", nullable = true, length = 1)
	public String getAvailedSignupOffer() {
		return availedSignupOffer;
	}

	public void setAvailedSignupOffer(String availedSignupOffer) {
		this.availedSignupOffer = availedSignupOffer;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_NPS_TIME", nullable = true)
	public Date getLastNPSTime() {
		return lastNPSTime;
	}

	public void setLastNPSTime(Date lastNPSTime) {
		this.lastNPSTime = lastNPSTime;
	}

	@Column(name = "SIGNUP_OFFER_EXPIRED", nullable = true, length = 1)
	public String getSignupOfferExpired() {
		return signupOfferExpired;
	}

	public void setSignupOfferExpired(String signupOfferExpired) {
		this.signupOfferExpired = signupOfferExpired;
	}
	@Column(name = "BLOCKED_POINTS", nullable = true)
	public Integer getBlockedPoints() {
		return blockedPoints;
	}

	public void setBlockedPoints(Integer blockedPoints) {
		this.blockedPoints = blockedPoints;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "SIGNUP_OFFER_EXPIRY_TIME", nullable = true)
	public Date getSignupOfferExpiryTime() {
		return signupOfferExpiryTime;
	}

	public void setSignupOfferExpiryTime(Date signupOfferExpiryTime) {
		this.signupOfferExpiryTime = signupOfferExpiryTime;
	}

	@Column(name = "SIGNUP_OFFER_STATUS")
	public String getSignupOfferStatus() {
		return signupOfferStatus;
	}

	public void setSignupOfferStatus(String signupOfferStatus) {
		this.signupOfferStatus = signupOfferStatus;
	}

	@Column(name = "REDEMPTION_ORDER_ID")
	public Integer getRedemptionOrderId() {
		return redemptionOrderId;
	}

	public void setRedemptionOrderId(Integer redemptionOrderId) {
		this.redemptionOrderId = redemptionOrderId;
	}

	@Column(name = "TOTAL_REDEEMED_POINTS")
	public Integer getTotalRedeemedPoints() {
		return totalRedeemedPoints;
	}

	public void setTotalRedeemedPoints(Integer totalRedeemedPoints) {
		this.totalRedeemedPoints = totalRedeemedPoints;
	}

	@Column(name = "TOTAL_EXPIRED_POINTS")
	public Integer getTotalExpiredPoints() {
		return totalExpiredPoints;
	}

	public void setTotalExpiredPoints(Integer totalExpiredPoints) {
		this.totalExpiredPoints = totalExpiredPoints;
	}
}
