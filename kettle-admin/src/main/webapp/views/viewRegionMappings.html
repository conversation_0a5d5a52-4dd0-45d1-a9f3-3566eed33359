<div class="row" ng-init="init()">
    <div class="col-lg-12"><br>
        <h1 class="page-header"> Region Fulfillment Mappings    </h1>
    </div>
    <div class="col-xs-12">
        <div class="form-group">
            <label>Select Company </label>
            <select class="form-control" ng-model="selectedCompany" ng-options=" company.name for company in allCompanies" data-ng-change="setData()"> </select>
        </div>
    </div>
    <div class="col-xs-12">
        <div class="form-group">
            <label>Select Region</label>
            <select class="form-control" ng-model="selectedRegion" ng-options=" region for region in allRegions" data-ng-change="setData()"> </select>
        </div>
    </div>
    <div class="col-xs-12" data-ng-show="selectedRegion!=null">
        <table  class="table table-hover table-striped">
            <thead>
            <tr>
                <th>Region Name</th>
                <th>Mapped Kitchen</th>
                <th>Mapped Warehouse</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>{{selectedRegion}}</td>
                <td>{{selectedKitchen}}</td>
                <td>{{selectedWarehouse}}</td>
            </tr>
            </tbody>
        </table>
    </div>
</div>
