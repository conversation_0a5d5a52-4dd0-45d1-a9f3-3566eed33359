/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.01.06 at 03:47:58 PM IST 
//


package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * <p>Java class for SFXOrder complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SFXOrder"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="client_order_id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="order_value" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="paid" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="preparation_time" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="allot_time" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="scheduled_time" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SFXOrder", propOrder = {
    "clientOrderId",
    "orderValue",
    "paid",
    "preparationTime",
    "allotTime",
    "scheduledTime"
})
@JsonIgnoreProperties(ignoreUnknown=true)
public class SFXOrder {

    @XmlElement(name = "client_order_id", required = true)
    @JsonProperty("client_order_id")
    protected String clientOrderId;
    @XmlElement(name = "order_value")
    @JsonProperty("order_value")
    protected float orderValue;
    @XmlElement(required = true)
    @JsonProperty("paid")
    protected String paid;
    @XmlElement(name = "preparation_time")
    @JsonProperty("preparation_time")
    protected int preparationTime;
    @XmlElement(name = "allot_time", required = true)
    @JsonProperty("allot_time")
    protected String allotTime;
    @XmlElement(name = "scheduled_time", required = true)
    @JsonProperty("scheduled_time")
    protected String scheduledTime;

    /**
     * Gets the value of the clientOrderId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getClientOrderId() {
        return clientOrderId;
    }

    /**
     * Sets the value of the clientOrderId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClientOrderId(String value) {
        this.clientOrderId = value;
    }
    
    public SFXOrder(){}

    public SFXOrder(String clientOrderId, float orderValue, String paid, int preparationTime, String allotTime,
			String scheduledTime) {
		super();
		this.clientOrderId = clientOrderId;
		this.orderValue = orderValue;
		this.paid = paid;
		this.preparationTime = preparationTime;
		this.allotTime = allotTime;
		this.scheduledTime = scheduledTime;
	}

	/**
     * Gets the value of the orderValue property.
     * 
     */
    public float getOrderValue() {
        return orderValue;
    }

    /**
     * Sets the value of the orderValue property.
     * 
     */
    public void setOrderValue(float value) {
        this.orderValue = value;
    }

    /**
     * Gets the value of the paid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPaid() {
        return paid;
    }

    /**
     * Sets the value of the paid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPaid(String value) {
        this.paid = value;
    }

    /**
     * Gets the value of the preparationTime property.
     * 
     */
    public int getPreparationTime() {
        return preparationTime;
    }

    /**
     * Sets the value of the preparationTime property.
     * 
     */
    public void setPreparationTime(int value) {
        this.preparationTime = value;
    }

    /**
     * Gets the value of the allotTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAllotTime() {
        return allotTime;
    }

    /**
     * Sets the value of the allotTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAllotTime(String value) {
        this.allotTime = value;
    }

    /**
     * Gets the value of the scheduledTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getScheduledTime() {
        return scheduledTime;
    }

    /**
     * Sets the value of the scheduledTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setScheduledTime(String value) {
        this.scheduledTime = value;
    }

}
