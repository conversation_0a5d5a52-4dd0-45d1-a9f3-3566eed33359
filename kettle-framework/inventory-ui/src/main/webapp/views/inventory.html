<div class="row">
    <div class="col-xs-12">
        <h1 class="page-header">
            Inventory Management
            <!--<button type="button" class="btn btn-primary pull-right" style="margin-left: 10px" id="btnAddNewUnitsIDnew" ng-click="open()">
                <i class="fa fa-plus"></i> Add new unit
            </button>-->
        </h1>
    </div>

</div>
<div class="row" data-ng-init="init()">

</div>
<div class="row"style="margin-bottom: 2px;">
        <select
                class="form-control"
                ui-select2="selectedUnit"
                id="inventoryUnit"
                name="inventoryUnit"
                data-placeholder="Select Unit"
                data-ng-model="selectedUnit"
                data-ng-options="unit as unit.name for unit in units track by unit.id"
                data-ng-change="setSelectedUnit(selectedUnit)"></select>
</div>
<div class="row"style="margin-bottom: 2px;">
        <button
                data-ng-click="getCafeInventory()"
                class="btn btn-primary">Cafe Inventory
        </button>
        <button
                data-ng-click="getSCMInventory()"
                class="btn btn-primary">SCM Inventory
        </button>
        <!-- <button
                data-ng-if="isAdmin"
                data-ng-click="refreshCafeInventory(false)"
                class="btn btn-primary">Refresh Cafe Inventory
        </button> -->
        <button
                data-ng-click="refreshScmInventory(false)"
                acl-action="IM_RSCMI"
                class="btn btn-primary">Refresh SCM Inventory
        </button>


        <button
                acl-action="IM_DCIS"
                data-ng-click="downloadSheet()"
                class="btn btn-primary">Download Cafe Inventory Sheet
        </button>

         <button
                data-ng-click="deleteKeys()"
                acl-action="IM_DK"
                class="btn btn-primary">Delete Keys
        </button>
</div>

<div class="row" style="margin-bottom: 2px;">
        <!-- <button
                data-ng-if="isAdmin"
                data-ng-click="refreshCafeInventory(true)"
                class="btn btn-primary">Force Refresh Cafe Inventory
        </button> -->

        <button
                data-ng-click="refreshScmInventory(true)"
                acl-action="IM_FRSCMI"
                class="btn btn-primary">Force Refresh SCM Inventory
        </button>
</div>

<div class="row" style="margin-bottom: 2px;">
        <button
                acl-action="IM_UT"
                data-ng-click="getUnitTimeLine()"
                class="btn btn-primary">Unit Timeline
        </button>
    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal">
        Refresh All Units
    </button>

</div>







<!-- Modal -->
<div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="exampleModalLabel">Refresh Unit By Zone</h3>

                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <label>Select Zone</label>
                <select  style="width: 100%"
                         class="form-control"
                         name="unitZone"
                         ng-options="option as option for option in unitZones"
                         ng-model="selectedZone">{{option}}
                </select>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button style="margin-top: 2px;"
                        data-ng-click="refreshAllUnits()"
                        acl-action="IM_RAU"
                        class="btn btn-primary">Refresh {{selectedZone}} units
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row" style="margin-bottom: 0px;">
    <div class="col-xs-12" data-ng-if="inventory != null">
        <p class="table-header">Inventory</p>
        <table class="table table-bordered striped">
            <tr>
                <div class="form-group">
                    <label>Search</label>
                    <input type="text" class="form-control" data-ng-model="searchAll"
                           placeholder="Enter Product Name to search" style="color: red" />
                </div>

            </tr>
            <tr>
                <th>Product Id</th>
                <th>Product Name</th>
                <th>UOM/Dimension</th>
                <th>Quantity Available</th>
                <th>Expire Quantity</th>
                <th>Price</th>
                <!-- <th>Quantity on Kettle</th> -->
                <th>Action</th>
            </tr>
            <tr data-ng-repeat="item in inventory | filter:searchAll">
                <td>{{item.id}}</td>
                <td>{{item.name}}</td>
                <td>{{item.u}}</td>
                <td>{{item.qty}}</td>
                <td>{{item.exQty}}</td>
                <td>{{item.price}}</td>
                <!-- <td data-ng-class="{'red white-text': (item.kettleQty != 'N/A' && item.qty != item.kettleQty)}">
                    {{item.kettleQty}}
                </td>  -->
                <td data-ng-if="item.isSCM == undefined || item.isSCM == null">
                    <button data-ng-click="getCafeProductRecipeInventory(item)">Check Recipe</button>
                </td>
                <td data-ng-if="(item.isSCM != undefined || item.isSCM !=  null && item.isSCM == 'Y') && isAddInventoryEnabled()" >
                        <button data-ng-click="openSCMDataUpdateUI(item)"  >Add Inventory</button>
                </td>
            </tr>
        </table>
    </div>
    <div class="col-xs-12" data-ng-if="timelineData != null">
        <p class="table-header">Timeline Data</p>
        {{timelineData}}
        <table class="bordered striped">
            <tr>
                <th>Product Id</th>
                <th>Product Name</th>
                <th>Quantity Available</th>
                <th>UOM/Dimension</th>
                <th>Action</th>
            </tr>
            <tr data-ng-repeat="item in inventory">
                <td>{{item.id}}</td>
                <td>{{item.name}}</td>
                <td>{{item.qty}}</td>
                <td>{{item.u}}</td>
                <td data-ng-if="item.isSCM == undefined || item.isSCM == null">
                    <button data-ng-click="getCafeProductRecipeInventory(item)">Check Recipe</button>
                </td>
                <td data-ng-if="item.isSCM != undefined || item.isSCM !=  null && item.isSCM == 'Y'">
                    <button data-ng-click="openSCMDataUpdateUI(item)">Update Inventory</button>
                </td>
            </tr>
        </table>
    </div>
</div>
<script type="text/ng-template" id="checkData.html">
    <div class="row">
        <div class="col-xs-12">
            <h3>{{item}}</h3>
            <p class="table-header">Inventory</p>
            <table class="table table-bordered striped">
                <tr>
                    <th>Product Id</th>
                    <th>Product Name</th>
                    <th>Available Quantity</th>
                    <th>Expiry Quantity</th>
                    <th>UOM/Dimension</th>
                    <th>Recipe Quantity</th>
                    <th>Available Count</th>
                    <th>Expiry Count</th>
                    <th>Critical</th>
                </tr>
                <tr data-ng-repeat="item in inventory">
                    <td>{{item.id}}</td>
                    <td>{{item.name}}</td>
                    <td>{{item.availableQty}}</td>
                    <td>{{item.expiryQty}}</td>
                    <td>{{item.uom}}</td>
                    <td>{{item.recipeQty}}</td>
                    <td>{{item.count}}</td>
                    <td>{{item.expiryCount}}</td>
                    <td>{{item.critical}}</td>
                </tr>
            </table>
            <button class="btn btn-danger" style="float: right;"
                    data-ng-click="cancel()">Close
            </button>
        </div>
    </div>
</script>

<script type="text/ng-template" id="updateSCMData.html">
    <div class="row">
        <div class="col-xs-12">
            <h3 class="table-header">Item</h3>
            <table class="table table-bordered striped">
                <tr>
                    <th>Product Id</th>
                    <th>Product Name</th>
                    <th>Quantity Available</th>
                    <th>UOM/Dimension</th>
                    <th>update value</th>
                </tr>
                <tr>
                    <td>{{item.id}}</td>
                    <td>{{item.name}}</td>
                    <td>{{item.qty}}</td>
                    <td>{{item.u}}</td>
                    <td><input type="number" data-ng-model="item.update"></td>
                </tr>
            </table>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <button class="btn btn-danger" style="float: left;"
                    data-ng-click="submit(item)">Submit
            </button>
            <button class="btn btn-success" style="float: right;"
                    data-ng-click="cancel()">Close
            </button>
        </div>
    </div>
</script>
