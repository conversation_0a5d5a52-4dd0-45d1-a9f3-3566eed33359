package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.UnitPriceProfileMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitPriceProfileMappingDao extends JpaRepository<UnitPriceProfileMapping,Integer> {

    @Query("SELECT u FROM UnitPriceProfileMapping u " +
            "WHERE  u.unitId in :unitIds " +
            "AND (:channelPartnerId IS NULL OR u.channelPartnerId = :channelPartnerId) " +
            "AND (:brandId IS NULL OR u.brandId = :brandId)")
    List<UnitPriceProfileMapping> findAllMappingsByPartnerIdAndBrand(
            @Param("unitIds") List<Integer> unitIds,
            @Param("channelPartnerId") Integer channelPartnerId,
            @Param("brandId") Integer brandId
    );



    @Query("SELECT u FROM UnitPriceProfileMapping u " +
            "WHERE  u.priceProfileId  =  :priceProfileId " +
            "AND (:priceProfileVersion IS NULL OR u.priceProfileVersion = :priceProfileVersion) ")
    List<UnitPriceProfileMapping> findAllByPriceProfileIdAndPriceProfileVersion(@Param("priceProfileId") Integer priceProfileId,
                                                                                @Param("priceProfileVersion") Integer priceProfileVersion);

    @Query("SELECT u FROM UnitPriceProfileMapping u " +
            "WHERE  u.priceProfileVersion  =  :priceProfileVersion and u.mappingStatus  = 'ACTIVE' ")
    List<UnitPriceProfileMapping> findAllByPriceProfileVersion(
            @Param("priceProfileVersion") Integer priceProfileVersion);


}
