/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

clmapp.service('AppUtil', ['$cookieStore','APIJson', function($cookieStore,APIJson){
	
	var service = {};
	
	service.GetRequest = GetRequest;
    service.restUrls = APIJson.urls;
	
	function GetRequest(obj) {
        var requestObj = {};
        if (typeof obj != 'string') {
            requestObj = {
                session: $cookieStore.get('adminglobals').user,
                data: JSON.stringify(obj)
            };
        } else {
            requestObj = {
                session: $cookieStore.get('adminglobals').user,
                data: obj
            };
        }
        return requestObj;
    }
	return service;
}]);