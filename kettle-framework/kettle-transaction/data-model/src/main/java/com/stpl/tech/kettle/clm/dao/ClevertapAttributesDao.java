package com.stpl.tech.kettle.clm.dao;

import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.master.data.dao.AbstractDao;

import java.util.Map;

public interface ClevertapAttributesDao extends AbstractDao {

   public Map<String,Object> calculateEventAttributes(OrderDetail order, CustomerInfo customer);

   public Map<String,Object> calculateUserAttributes(Customer customer);

   public Map<String,Object> calculateSubscriptionAttributes(OrderDetail order, CustomerInfo customer);

   public Map<String,Object> calculateUserAttributes(Integer customerId);

}
