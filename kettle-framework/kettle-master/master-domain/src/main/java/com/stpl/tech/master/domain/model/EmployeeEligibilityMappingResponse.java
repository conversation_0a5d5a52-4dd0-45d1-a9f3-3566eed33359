package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * Response DTO for Employee Eligibility Mapping
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeEligibilityMappingResponse {

    private Long id;
    private Date createdAt;
    private String createdBy;
    private EligibilityType eligibilityType;
    private String empId;
    private MappingType mappingType;
    private SystemStatus status;
    private Date updatedAt;
    private String updatedBy;
    private String value;
}
