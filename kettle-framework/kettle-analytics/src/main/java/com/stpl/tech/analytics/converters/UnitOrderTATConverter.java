package com.stpl.tech.analytics.converters;

import com.stpl.tech.analytics.model.UnitOrderTATDomain;
import com.stpl.tech.analytics.service.impl.AnalyticsProperties;
import com.stpl.tech.kettle.data.model.UnitOrderTATDetail;
import com.stpl.tech.util.AppConstants;

public class UnitOrderTATConverter {

    public static UnitOrderTATDomain convert(UnitOrderTATDetail orderTATDetail, AnalyticsProperties properties) {
        UnitOrderTATDomain orderTATDomain = new UnitOrderTATDomain();
        orderTATDomain.setUnitId(orderTATDetail.getUnitId());
        orderTATDomain.setBrandId(orderTATDetail.getBrandId());
        orderTATDomain.setType(orderTATDetail.getType());
        orderTATDomain.setBusinessDate(orderTATDetail.getBusinessDate());
        orderTATDomain.setTotalOrders(orderTATDetail.getTotalOrders());
        orderTATDomain.setDelayedOrders(orderTATDetail.getDelayedOrders());
        orderTATDomain.setAvgTAT(orderTATDetail.getAverageTAT());
        if (orderTATDetail.getBrandId() == AppConstants.CHAAYOS_BRAND_ID) {
            orderTATDomain.setDelayOrdrsExcd(((100 * orderTATDetail.getDelayedOrders()) / orderTATDetail.getTotalOrders()) > properties.getChaayosOrderDelayThrshld());
            orderTATDomain.setTatExcd(orderTATDetail.getAverageTAT().doubleValue() > properties.getChaayosTATThrshld());
        } else if (orderTATDetail.getBrandId() == AppConstants.GNT_BRAND_ID) {
            orderTATDomain.setDelayOrdrsExcd(((100 * orderTATDetail.getDelayedOrders()) / orderTATDetail.getTotalOrders()) > properties.getGNTOrderDelayThrshld());
            orderTATDomain.setTatExcd(orderTATDetail.getAverageTAT().doubleValue() > properties.getGNTTATThrshld());
        }
        return orderTATDomain;
    }
}
