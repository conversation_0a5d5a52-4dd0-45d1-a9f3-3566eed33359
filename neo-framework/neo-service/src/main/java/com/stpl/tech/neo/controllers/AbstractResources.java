/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.neo.controllers;

import com.stpl.tech.neo.core.exception.CheckoutFailureException;
import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.domain.model.mongo.ErrorInfo;
import org.apache.http.protocol.HTTP;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;

public abstract class AbstractResources {

	@Autowired
	private EnvironmentProperties env;

	public <T> Response createOKResponse(T object) {
		return createResponse(Response.Status.OK, object);
	}

	public <T> Response createResponse(Response.Status status, T object) {
		return Response.status(status).entity(object).build();
	}

	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(WebOrderException.class)
	@ResponseBody
	public ErrorInfo handleWebOrderException(HttpServletRequest req, WebOrderException ex) {
		NeoUtil.slackIt(getUserAgent(req), req.getRequestURL().toString(), env.getEnvType(), ex);
		// return new ErrorInfo(ex.getCode().getErrorValue(), ex.getMessage(),
		// ex);
		return new ErrorInfo(ex.getCode().getErrorValue(), ex.getCode().getMsg(), ex);
	}

	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(CheckoutFailureException.class)
	@ResponseBody
	public ErrorInfo handleCheckoutException(HttpServletRequest req, CheckoutFailureException ex) {
		// return new ErrorInfo(ex.getCode().getCode(), ex.getCode().getMsg(),
		// ex);
		return new ErrorInfo(HttpStatus.INTERNAL_SERVER_ERROR.value(), ex.getCode().getMsg(), ex);
	}

	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(NullPointerException.class)
	@ResponseBody
	public ErrorInfo handleNullPointerException(HttpServletRequest req, Exception ex) {
		NeoUtil.slackIt(getUserAgent(req), req.getRequestURL().toString(), env.getEnvType(), ex);
		return new ErrorInfo(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
	}

	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(Exception.class)
	@ResponseBody
	public ErrorInfo handleAllExceptions(HttpServletRequest req, Exception ex) {
		NeoUtil.slackIt(getUserAgent(req), req.getRequestURL().toString(), env.getEnvType(), ex);
		return new ErrorInfo(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
	}

	private String getUserAgent(HttpServletRequest req) {
		String userAgent = req.getHeader(HTTP.USER_AGENT);
		if (userAgent == null) {
			userAgent = req.getHeader(HTTP.USER_AGENT.toLowerCase());
		}
		return userAgent;
	}
}
