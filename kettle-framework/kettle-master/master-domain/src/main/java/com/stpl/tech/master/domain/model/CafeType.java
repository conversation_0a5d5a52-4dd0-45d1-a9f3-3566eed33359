package com.stpl.tech.master.domain.model;

public enum CafeType {
    KIOSK(false, true, false, true),   // Only TakeAway, Token
    CAFE(true, true, false, true),     // All services except Token
    TABLE_ORDER(true, true, true, false),  // Same as CAFE
    BOTH(true,true,true,true);

    private final boolean dineIn;
    private final boolean takeAway;
    private final boolean tableSelection;
    private final boolean tokenSelection;

    CafeType(boolean dineIn, boolean takeAway, boolean table, boolean token) {
        this.dineIn = dineIn;
        this.takeAway = takeAway;
        this.tableSelection = table;
        this.tokenSelection = token;
    }

    public boolean isDineIn() { return dineIn; }
    public boolean isTakeAway() { return takeAway; }
    public boolean isTableSelection() { return tableSelection; }
    public boolean isTokenSelection() { return tokenSelection; }
}
