angular.module('scmApp')
    .controller('BulkStandaloneTOCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$toastService','metaDataService','previewModalService','Popeye','$alertService','$fileUploadService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService,metaDataService,previewModalService, Popeye,$alertService,$fileUploadService) {
             $scope.init = function (){
                  $scope.gridOptions = {
                      enableGridMenu: true,
                      exporterExcelFilename: 'download.xlsx',
                      exporterExcelSheetName: 'Sheet1',
                      enableColumnMenus: true,
                      saveFocus: false,
                      enableRowSelection: true,
                      enableFiltering: true,
                      saveScroll: true,
                      enableSelectAll: true,
                      multiSelect: true,
                      enableColumnResizing: true,
                      exporterMenuPdf : false,
                      exporterMenuExcel : true,
                      onRegisterApi: function (gridApi) {
                          $scope.gridApi = gridApi;
                          gridApi.edit.on.afterCellEdit($scope, function(rowEntity, colDef, newValue, oldValue) {
                              if(colDef.displayName == "Pkg Quantity"){
                                  rowEntity.uomQuantity =  rowEntity.pkgQuantity *   rowEntity.selectedPackaging.packagingDefinition.conversionRatio;
                              }else if(colDef.displayName == "Packaging"){
                                  var selectedPackaging = newValue;
                                  rowEntity.packaging.forEach(function (packaging){
                                      if(packaging.packagingName === selectedPackaging){
                                          rowEntity.selectedPackaging = packaging;
                                      }
                                  });
                                  rowEntity.uomQuantity =  rowEntity.pkgQuantity *   rowEntity.selectedPackaging.packagingDefinition.conversionRatio;
                              }
                              $scope.$apply();

                          });
                          gridApi.selection.on.rowSelectionChanged($scope, function (row) {
                              if (row.isSelected) {
                                  if(appUtil.isEmptyObject(row.entity.selectedPackaging)){
                                      row.isSelected = false;
                                      $toastService.create("Please Select Packaging!");
                                  }
                                  if(appUtil.isEmptyObject(row.entity.packaging)){
                                      row.isSelected = false;
                                      $toastService.create("Packagings Not Found ");
                                  }
                                  if(appUtil.isEmptyObject(row.entity.selectedSku)){
                                      row.isSelected = false;
                                      $toastService.create("Default Sku Not Mapped");
                                  }

                              }

                          });
                      }
                  }
                  $scope.currentUnit = appUtil.getUnitData();
                  $scope.skuProductMap = appUtil.getSkuProductMap();
                  $scope.packagingMap = appUtil.getPackagingMap();
                  $scope.allProducts = [];
                  $scope.gridItems = [];
                  $scope.getAvailableProducts();
                  $scope.uploadedFile = null;
                  $scope.selectedSkus = [];
                  $scope.upload = false;
                  $scope.downloaded = false;
                  $scope.skuPackagings = {};

                  $scope.unitGridOptions = appUtil.getGridOptions($scope);
             }

            function trimData(gridItems){
                 var result = [];
                 for(var i = 0 ;i< gridItems.length;i++){
                     //conversionRatio as Excess Qty , packaging name as unit Of Measure , packaging id as id
                     var row = {
                         id : gridItems[i].selectedPackaging.packagingDefinition.packagingId,
                         skuName : gridItems[i].selectedSku.skuName,
                         skuId : gridItems[i].selectedSku.skuId,
                         productId: gridItems[i].productId,
                         calculatedAmount : gridItems[i].uomQuantity,
                         unitOfMeasure : gridItems[i].selectedPackaging.packagingDefinition.packagingName,
                         excessQuantity : gridItems[i].selectedPackaging.packagingDefinition.conversionRatio

                     };


                     result.push(row);

                 }
                 return result;
            }



            $scope.getSelectedProducts = function (){
                var rows = $scope.gridApi.selection.getSelectedRows();
                var selectedSkus = [];
                 for(var i = 0;i<rows.length;i++){
                     selectedSkus.push(
                         {
                             skuId : rows[i].selectedSku.skuId,
                             skuName : rows[i].selectedSku.skuName
                     });
                 }
                 $scope.selectedSkus = selectedSkus;
                 return $scope.gridApi.selection.getSelectedRows();
            }

            $scope.setGridItems = function (productList){
                 for(var i = 0;i<productList.length ; i++){
                     var pr = productList[i];
                     var item = {
                         productId:pr.productId,
                         productName:pr.productName,
                         unitOfMeasure: pr.unitOfMeasure,
                         pkgQuantity : 0 ,
                         uomQuantity : 0 ,
                         skuList: setSkuToProduct(pr.productId),
                         selectedSku: null,
                         packaging : null
                     };
                     item.selectedSku = initializeSelectedSku(item.skuList);
                     if(appUtil.isEmptyObject(item.selectedSku)){
                         $scope.gridItems.push(item);
                         continue;
                     }
                     item.selectedPackaging = getDefaultPackaging(item.selectedSku.skuPackagings);
                     var defaultPack = getDefaultPackaging(item.selectedSku.skuPackagings);
                     item.defaultPackaging = appUtil.isEmptyObject(defaultPack) ? "" : !appUtil.isEmptyObject(defaultPack.packagingDefinition) ?
                         angular.copy(defaultPack).packagingDefinition.packagingName : "";
                     item.packaging = trimPackaging(item.selectedSku.skuPackagings);
                     $scope.gridItems.push(item);
                 }

            }


            function  getDefaultPackaging(packaging){
                 for(var i = 0;i<packaging.length;i++){
                     if(packaging[i].isDefault == true){
                         return packaging[i];
                     }
                 }
            }

            function  trimPackaging(packaging){
                for(var i = 0;i<packaging.length;i++){
                    if(!appUtil.isEmptyObject(packaging[i].packagingDefinition)){
                        packaging[i].packagingName = packaging[i].packagingDefinition.packagingName;
                    }
                }
                return packaging;
            }

            $scope.categoryFilter = function(product){
                if(product.categoryDefinition.id != 3 ){
                    return true;
                }

            }

            $scope.getAvailableProducts = function() {
                $http({
                    method : "GET",
                    url : apiJson.urls.transferOrderManagement.getBulkStandAloneProducts,
                    params : {
                        unitId: appUtil.getCurrentUser().unitId
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.availableProductList = response.data;
                        $scope.allProducts  = appUtil.getActiveScmProducts().filter( function (product){
                           return $scope.byProducts(product) && $scope.categoryFilter(product);
                        });
                        $scope.setGridItems($scope.allProducts);
                        $scope.gridOptions.columnDefs = $scope.gridColumns();
                        $scope.gridOptions.data = $scope.gridItems;
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.byProducts = function(product) {
                    return product != null && product != undefined && $scope.availableProductList != undefined && $scope.availableProductList != null
                        && $scope.availableProductList.indexOf(product.productId) > -1;
            };

            function setSkuToProduct(productId){
                var skuList = $scope.skuProductMap[productId];
                skuList.forEach(function(sku){
                    sku.skuPackagings.forEach(function(packaging){
                        packaging.packagingDefinition = $scope.packagingMap[packaging.packagingId];
                    });
                    sku.skuPackagings = filterLoosePackaging(sku.skuPackagings, sku.supportsLooseOrdering);
                });
                //skuList = removeInactive(skuList);
                return skuList;
            }

            function initializeSelectedSku(skuList){
                var ret = null;
                if(skuList.length==1){
                    ret = skuList[0];
                }else{
                    skuList.forEach(function (item) {
                        if(item.isDefault){
                            ret = item;
                        }
                    });
                }
                return ret;
            }

            function filterLoosePackaging(pkgList, looseOrdering){
                var ret = pkgList;
                if(!looseOrdering){
                    var pkgs = [];
                    pkgList.forEach(function (pkg) {
                        if(!appUtil.isEmptyObject(pkg.packagingDefinition)){
                            if(pkg.packagingDefinition.packagingType!="LOOSE"){
                                pkgs.push(pkg);
                            }
                        }

                    });
                    ret = pkgs;
                }
                return ret;
            }

            function removeInactive(skuList){
                var skus = [];
                skuList.forEach(function(sku){
                    if(sku.skuStatus=='ACTIVE'){
                        var pkgs = [];
                        sku.skuPackagings.forEach(function(packaging){
                            if(packaging.mappingStatus=='ACTIVE' && packaging.packagingDefinition.packagingStatus=='ACTIVE'){
                                pkgs.push(packaging);
                            }
                        });
                        sku.skuPackagings = pkgs;
                        skus.push(sku);
                    }
                });
                return skus;
            }

            $scope.gridColumns  = function () {
                return [{
                    field: 'productId',
                    name: 'id',
                    enableCellEdit: false,
                    displayName: 'Product ID'
                }, {
                    field: 'productName',
                    name: 'Product Name',
                    enableCellEdit: false,
                    displayName: 'Product Name'
                }, {
                    field: 'unitOfMeasure',
                    name: 'uom',
                    enableCellEdit: false,
                    displayName: 'UOM'
                }, {
                    field: 'pkgQuantity',
                    name: 'Pkg Quantity',
                    enableCellEdit: true,
                    displayName: 'Pkg Quantity',
                    type: 'number',
                    width: '10%'
                },{
                    field: 'uomQuantity',
                    name: 'Uom Quantity',
                    enableCellEdit: false,
                    displayName: 'Uom Quantity',
                    type: 'number',
                    width: '10%'
                }, {
                    field: 'selectedSku.skuId',
                    name: 'SkuId',
                    enableCellEdit: false,
                    displayName: 'Sku Id'
                },
                    {
                        field: 'selectedSku.skuName',
                        name: 'SkuName',
                        enableCellEdit: false,
                        displayName: 'Sku Name'
                    },
                    { field : 'defaultPackaging',  name: 'Packaging', displayName: 'Packaging',
                        width: '20%',
                        editableCellTemplate: 'ui-grid/dropdownEditor',
                        editDropdownRowEntityOptionsArrayPath: 'packaging',
                        editDropdownIdLabel: 'packagingName',
                        editDropdownValueLabel : 'packagingName',
                        enableCellEdit: true

                    },

                ]
            }

            $scope.unitGridColumns = function (){
                var cols = [
                    {
                        field: 'unitId',
                        name: 'id',
                        enableCellEdit: false,
                        displayName: 'Unit ID',
                        width:100
                    }, {
                        field: 'unitName',
                        name: 'unitName',
                        enableCellEdit: false,
                        displayName: 'Unit Name',
                        width:150
                    }
                ];
                for(var i = 0; i < $scope.selectedSkus.length ; i++ ){
                    cols.push({
                        field: String($scope.selectedSkus[i].skuId),
                        name : String($scope.selectedSkus[i].skuId),
                        enableCellEdit: false,
                        displayName: $scope.selectedSkus[i].skuName,
                        width: 175
                    });
                }
                return cols;
            }

            function getUnitGridData(unitDistribution){
                var data = [];
                Object.keys(unitDistribution).forEach(function (unitId){
                    var tempObj = {};
                    tempObj['unitId'] = unitId;
                    var unit = appUtil.findUnitDetail(parseInt(unitId));
                    if(!appUtil.isEmptyObject(unit)){
                        tempObj['unitName'] = unit.name;
                    }
                    Object.keys(unitDistribution[unitId]).forEach(function (skuId){
                        tempObj[String(skuId)] = unitDistribution[unitId][skuId].packagingDetails[0].numberOfUnitsPacked;
                    });
                    data.push(tempObj);
                });
                return data;
            }



            $scope.getTemplate = function (){
                var selectedSkus = $scope.getSelectedProducts();
                if(selectedSkus.length == 0 ){
                    $toastService.create("Please Select atleast One Product ! ");
                    return;
                }
                var data = trimData(selectedSkus);

                $http({
                    url: apiJson.urls.transferOrderManagement.downloadBulkStandaloneTemplate,
                    method: 'POST',
                    responseType: 'arraybuffer',
                    data: data,
                    params: {unitId : $scope.currentUnit.id},
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                }).success(
                        function (data) {
                            $scope.downloaded = true;
                            var fileName =  "StandAlone_Template"
                                + ".xlsx";
                            var blob = new Blob(
                                [data],
                                {
                                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                }, fileName);
                            saveAs(blob, fileName);
                        }).error(function (err) {
                    console.log("Error during getting data", err);
                });

            }

            $scope.uploadDoc = function () {
                $fileUploadService.openFileModal("Upload unit Distribution Sheet", "Find", function (file) {
                    $scope.uploadedFile = file;
                    $scope.uploadDistributionSheet(file);
                });
            };

            $scope.back = function (){
                $scope.upload = false;
            }


            $scope.createTransfers = function (){
                var file = $scope.uploadedFile;
                var extName = metaDataService.getFileExtension(file.name).toLowerCase();
                if (extName === 'xlsx') {
                    var fd = new FormData();
                    fd.append('file', file);
                    fd.append("unitId",$scope.currentUnit.id)
                    var url = apiJson.urls.transferOrderManagement.bulkStandAloneTransfer;
                    $http({
                        url: url,
                        method: 'POST',
                        data: fd,
                        headers: {'Content-Type': undefined},
                        transformRequest: angular.identity
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (!appUtil.isEmptyObject(response)) {
                            var title = response.length + " Transfers Created Sucessfully";
                            var msg = "TO Ids : " + response;
                            $alertService.alert(title,msg,function (){
                                    $scope.init();
                            },false);
                        } else {
                            $toastService.create("Something Went Wrong");
                        }
                    }).error(function (response) {
                        if(response.errorCode!=null) {
                            $alertService.alert(response.errorTitle, response.errorMsg,function () {}, true);
                        }else{
                            $toastService.create("Could not create transfer. Please try again later");
                            console.log("error:" + response);
                        }
                    });
                } else {
                    $toastService.create("Please upload a valid Excel file.");
                }
            }

            function validateSheet(unitDistribution,skuIds){
                var arr = getIds(skuIds);
                var isValid =  true;
                Object.keys(unitDistribution).forEach(function (unitId){
                    Object.keys(unitDistribution[unitId]).forEach(function (skuId){
                        if(arr.indexOf(parseInt(skuId)) === -1){
                            isValid = false;
                        }
                    })
                });
                return isValid;
            }

            function getIds(skus){
                var arr = [];
                for(var i =0;i<skus.length;i++){
                    arr.push(skus[i].skuId);
                }
                return arr;
            }


            $scope.uploadDistributionSheet = function (file) {
                var selectedSkus = $scope.selectedSkus;
                var extName = metaDataService.getFileExtension(file.name).toLowerCase();
                if (extName === 'xlsx') {
                    var fd = new FormData();
                    fd.append('file', file);
                    fd.append("unitId",$scope.currentUnit.id);
                    var url = apiJson.urls.transferOrderManagement.parseDistributionSheet;
                    $http({
                        url: url,
                        method: 'POST',
                        data: fd,
                        headers: {'Content-Type': undefined},
                        transformRequest: angular.identity
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (!appUtil.isEmptyObject(response)) {
                            if(!validateSheet(response,selectedSkus)){
                                $alertService.alert("Invalid Excel !", "addition Of skus is not Allowed In sheet",function () {}, true);
                            }else{
                                $toastService.create("Upload successful");
                                $scope.upload = true;
                                $scope.unitGridOptions.columnDefs = $scope.unitGridColumns();
                                $scope.unitGridOptions.data = getUnitGridData(response);
                            }

                        } else {
                            $toastService.create("Upload failed");
                        }
                    }).error(function (response) {
                        if(response.errorCode!=null) {
                            $alertService.alert(response.errorTitle, response.errorMsg,function () {}, true);
                        }else{
                            $toastService.create("Could not upload File");
                            console.log("error:" + response);
                        }
                    });
                } else {
                    $toastService.create("Please upload a valid Excel file.");
                }
            };



        }]);
