package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.core.service.model.RecipeData;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.KioskCompanyDetails;
import com.stpl.tech.master.domain.model.ProductImageMappingDetailList;
import com.stpl.tech.master.domain.model.TransactionMetadata;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.readonly.domain.model.UnitProductData;
import com.stpl.tech.master.recipe.model.RecipeDetail;

import java.util.List;
import java.util.Map;

/**
 * Created by Chaayos on 01-10-2016.
 */
public interface RedisCacheService {

	public List<Unit> getAllUnits() throws DataNotFoundException;

    List<com.stpl.tech.master.domain.model.Location> getAllLocations() throws DataNotFoundException;

    public List<KioskCompanyDetails> getAllKioskCompanies();

	public Unit getUnit(int unitId) throws DataNotFoundException;

	public List<RecipeDetail> getRecipe(int productId);

	public List<IdCodeName> getProducts();


	public List<UnitBasicDetail> getAllUnitBasicDetail();

	public UnitBasicDetail getUnitBasicDetail(int unitId);

	public TransactionMetadata getTransactionMetaData(int unitId) throws DataNotFoundException;

	public List<IdCodeName> getWebCategories() throws DataNotFoundException;

	/**
	 * @param unitId
	 * @return
	 */
	public UnitProductData getUnitProducts(int unitId);

	public RecipeDetail getRecipeDetail(int recipeId);

	public RecipeData getRecipe(RecipeData recipes);

	Map<Integer, Map<Integer, List<ProductRecipeKey>>> getAllUnitProductPriceProfiles();

	Map<Integer, List<ProductRecipeKey>> getProductPriceProfilesForUnit(int unitId);

    ProductImageMappingDetailList getAllProductImages();

	ProductImageMappingDetailList getAllProductImages(List<Integer> list);
}