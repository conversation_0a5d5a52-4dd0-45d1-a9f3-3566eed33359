<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:task="http://www.springframework.org/schema/mvc"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/mvc
   http://www.springframework.org/schema/mvc/spring-mvc.xsd


   http://www.springframework.org/schema/beans
   http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
   http://www.springframework.org/schema/context
   http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.stpl.tech.kettle.service.controller"/>
    <context:component-scan base-package="com.stpl.tech.kettle.service.webengage.controller"/>
    <context:component-scan base-package="com.stpl.tech.kettle.service.clevertap.controller"/>
    <mvc:annotation-driven/>
    <task:annotation-driven/>
    <bean class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter">
        <property name="messageConverters">
            <list>
                <bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter"></bean>
            </list>
        </property>
    </bean>

    <bean id="taskExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="5"/>
        <property name="maxPoolSize" value="10"/>
        <property name="WaitForTasksToCompleteOnShutdown" value="true"/>
    </bean>

    <bean id="multipartResolver"
          class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <!-- max upload size in bytes -->
        <property name="maxUploadSize" value="20971520"/> <!-- 20MB -->
        <!-- max size of file in memory (in bytes) -->
        <property name="maxInMemorySize" value="1048576"/> <!-- 1MB -->
    </bean>

    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <mvc:exclude-mapping path="/v1/external/**"/>
            <mvc:exclude-mapping path="/v1/webengage/**"/>
            <bean class="com.stpl.tech.master.core.external.interceptor.SessionAuthInterceptor"/>
        </mvc:interceptor>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <mvc:exclude-mapping path="/v1/external/**"/>
            <mvc:exclude-mapping path="/v1/webengage/**"/>
            <bean class="com.stpl.tech.master.core.external.interceptor.ACLInterceptor"/>
        </mvc:interceptor>
        <mvc:interceptor>
            <mvc:mapping path="/v1/external/**"/>
            <mvc:exclude-mapping path="/v1/webengage/**"/>
            <bean class="com.stpl.tech.master.core.external.partner.service.impl.ExternalAPIAuthInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>

    <!-- <websocket:message-broker
        application-destination-prefix="/app">
        <websocket:stomp-endpoint path="/ws">
            <websocket:sockjs />
        </websocket:stomp-endpoint>
        <websocket:simple-broker prefix="/topic" />
    </websocket:message-broker> -->
</beans>
