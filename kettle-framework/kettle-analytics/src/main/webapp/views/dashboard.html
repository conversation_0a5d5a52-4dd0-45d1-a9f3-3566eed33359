<div id="RokdaModal" data-ng-init="init()">
<div class="row" >
	<div class="col s12 m12 l12 purple lighten-5 navigation-header" >
		<p >Sales Analysis</p>
	</div>
</div>

<div class="row" style="margin-bottom: 0px;">
	<div class="col s12 m6 l6">
	<p class="table-header">Cafe</p>
		<table class="bordered striped">
			<tr>
				<th>Unit Name</th>
				<th>Food Capture</th>
				<th>Customer Capture</th>
				<th>Tickets</th>
				<th>Sales</th>
				<th>Delivery Tickets</th>
				<th>Delivery Sales</th>
				<th>APC</th>
				<th>Delivery APC</th>
			</tr>
			<tr highlighter="item.data.netSale.current"
				data-ng-repeat="item in unitsReportData | filter: cafeFilter">
				<td>{{item.detail.name}}</td>
				<td
					data-ng-class="{upValue: item.data.foodCapture.current >= item.data.foodCapture.target, downValue : item.data.foodCapture.current < item.data.foodCapture.target}">
					{{item.data.foodCapture.current}}
					({{item.data.foodCapture.target}})</td>
				<td
					data-ng-class="{upValue: item.data.customerCapture.current >= item.data.customerCapture.target, downValue : item.data.customerCapture.current < item.data.customerCapture.target}">
					{{item.data.customerCapture.current}}
					({{item.data.customerCapture.target}})</td>
				<td>{{item.data.tickets.current}}
					({{item.data.tickets.target}})</td>
				<td>{{item.data.netSale.current}}
					({{item.data.netSale.target}})</td>
				<td>{{item.data.deliveryTickets.current}}
						({{item.data.deliveryTickets.target}})</td>
					<td>{{item.data.deliveryNetSale.current}}
						({{item.data.deliveryNetSale.target}})</td>
				<td>{{item.data.apc.current}} ({{item.data.apc.target}})</td>
				<td>{{item.data.deliveryApc.current}}
						({{item.data.deliveryApc.target}})</td>
			</tr>
		</table>
	</div>
	<div class="col s12 m6 l6">
	<p class="table-header">Delivery</p>
		<table class="bordered striped">
			<tr>
				<th>Unit Name</th>
				<th>Food Capture</th>
				<th>Tickets</th>
				<th>Sales</th>
				<!-- 				<th>Delivery Tickets</th>
					<th>Delivery Sales</th>
	 -->
				<th>APC</th>
				<!-- 				<th>Delivery APC</th>
	 -->
			</tr>
			<tr data-ng-repeat="item in unitsReportData | filter: deliveryFilter">
				<td>{{item.detail.name}}</td>
				<td
					data-ng-class="{upValue: item.data.foodCapture.current >= item.data.foodCapture.target, downValue : item.data.foodCapture.current < item.data.foodCapture.target}">
					{{item.data.foodCapture.current}}
					({{item.data.foodCapture.target}})</td>
				<td>{{item.data.tickets.current}}
					({{item.data.tickets.target}})</td>
				<td>{{item.data.netSale.current}}
					({{item.data.netSale.target}})</td>
				<!-- 					<td>{{item.data.deliveryTickets.current}}
						({{item.data.deliveryTickets.target}})</td>
					<td>{{item.data.deliveryNetSale.current}}
						({{item.data.deliveryNetSale.target}})</td>
 -->
				<td>{{item.data.apc.current}} ({{item.data.apc.target}})</td>
				<!-- 					<td>{{item.data.deliveryApc.current}}
						({{item.data.deliveryApc.target}})</td>
 -->
			</tr>
		</table>
	</div>
</div>
</div>