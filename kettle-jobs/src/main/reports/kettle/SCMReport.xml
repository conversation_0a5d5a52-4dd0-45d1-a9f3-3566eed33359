<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Item Consumption Reports" type="Cafe Operations"
                  accessCode="Marketing">
			<reports>
				<report name="Live Product Consumption"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    ORDER_ITEM_ID,
    ORDER_ID,
    PRODUCT_ID,
    PRODUCT_NAME,
    QUANTITY,
    PRICE
FROM
    KETTLE.ORDER_ITEM
WHERE
    PRODUCT_ID = :productId 
        AND ORDER_ID IN (SELECT 
            ORDER_ID
        FROM
            ORDER_DETAIL
        WHERE
            ORDER_ID > (SELECT 
                    MAX(LAST_ORDER_ID)
                FROM
                    UNIT_CLOSURE_DETAILS)
                AND UNIT_ID = :unitId);
			]]>
					</content>
					<params>
						<param name="productId" displayName="Product Id"
                                          dataType="INTEGER" />
						<param name="unitId" displayName="Unit Id"
                                          dataType="INTEGER" />
					</params>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>
