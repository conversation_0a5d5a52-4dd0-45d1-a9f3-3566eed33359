package com.stpl.tech.forms.core.mapper;

import com.stpl.tech.forms.core.mapper.annotation.StringToBoolMapper;
import com.stpl.tech.forms.data.model.WalletData;
import com.stpl.tech.forms.domain.model.Wallet;
import com.stpl.tech.util.AppUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WalletMapper {
    WalletMapper INSTANCE = Mappers.getMapper(WalletMapper.class);

    WalletData toDto(Wallet monthlyTargetsDataVO);

    List<WalletData> toDtoList(List<Wallet> monthlyTargetsDataVO);

    @Mapping(source = "canAllocateCostToCafes", target = "canAllocateCostToCafes", qualifiedBy = StringToBoolMapper.class)
    Wallet toDomain(WalletData monthlyTargetsData);

    List<Wallet> toDomainList(List<WalletData> monthlyTargetsData);

    @StringToBoolMapper
    public static boolean stringToBool(String status){
        return AppUtils.getStatus(status);
    }
}
