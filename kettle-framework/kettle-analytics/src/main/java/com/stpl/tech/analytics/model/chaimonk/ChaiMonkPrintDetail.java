/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.model.chaimonk;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stpl.tech.master.domain.model.Adapter2;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.persistence.Entity;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;

@Entity
@Document
public class ChaiMonkPrintDetail{

    @Id
    private String _id;

    @Field
    private Integer orderPrintId;
    @Field
    private int orderDetailId;
    @Field
    private String printReason;
    @Field
    private int generatedBy;

    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2.class)
    @XmlSchemaType(name = "date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Field
    private Date reprintTime;

    public Integer getOrderPrintId() {
        return this.orderPrintId;
    }

    public void setOrderPrintId(Integer orderPrintId) {
        this.orderPrintId = orderPrintId;
    }

    public String getPrintReason() {
        return printReason;
    }

    public void setPrintReason(String printReason) {
        this.printReason = printReason;
    }


    public int getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(int employeeId) {
        this.generatedBy = employeeId;
    }

    public Date getReprintTime() {
        return reprintTime;
    }

    public void setReprintTime(Date billStartTime) {
        this.reprintTime = billStartTime;
    }


    public int getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(int orderDetailId) {
        this.orderDetailId = orderDetailId;
    }



    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }
}
