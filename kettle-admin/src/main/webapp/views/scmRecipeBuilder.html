<style type="text/css">
form.tab-form-demo .tab-pane {
	margin: 20px 20px;
}

.tabDiv {
	margin-top: 20px;
	height: 350px;
	max-height: 350px;
}

.divInnerRow {
	margin-top: 20px;
	height: 50px;
	max-height: 50px;
	width: 100%;
}

.tabPane {
	border: 1px solid #ddd;
	border-radius: 0px 0px 5px 5px;
	padding: 10px;
}
</style>

<div data-ng-init="init()">
<div class="panel panel-info">
<div class="panel-heading">{{recipeDetail.name.length > 0 ? recipeDetail.name : 'Please select recipe'}}</div>
<div class="panel-body">
	<uib-tabset class="tabPane" active="activeTab"> <uib-tab
		index="0" heading="Product Selection">
	<div class="row tabDiv">
		<div class="col-xs-12">
			<div class="row divInnerRow">
				<div class="col-xs-4">
					<label class="pull-right">Select SCM Product</label>
				</div>
				<div class="col-xs-8">
					<select ui-select2 class="form-control"
						style="width: 100% !important" data-ng-model="selectedProductId"
						data-placeholder="Select a product"
						data-ng-change="selectSCMMainProduct(selectedProductId)">
						<option value=""></option>
						<option
							data-ng-repeat="product in scmProductsInfo | filter : filterByCategory"
							value="{{product.productId}}">{{product.productName}}  ({{product.category.code}})  </option>
					</select>
				</div>
			</div>
		</div>
		<div class="col-xs-12">
			<div class="row divInnerRow">
				<div class="col-xs-4">
					<label class="pull-right">Dimension</label>
				</div>
				<div class="col-xs-8">
					<label>{{product.unitOfMeasure}}</label>
				</div>
			</div>
		</div>
		<div class="col-xs-12"
			data-ng-if="existingRecipes != null && existingRecipes.length > 0">
			<div class="row divInnerRow">
				<div class="col-xs-12">
					<table class="table table-striped table-bordered">
						<thead>
							<th>Recipe Id</th>
							<th>Recipe Name</th>
							<th>Actions</th>
						</thead>
						<tbody>
							<tr
								data-ng-repeat="recipe in existingRecipes track by recipe.recipeId">
								<td>{{recipe.recipeId}}</td>
								<td>{{recipe.name}}</td>
								<td align="left"><span
									data-ng-click="selectRecipeForEdit(recipe)"
									style="cursor: pointer" title="Add/Edit Details"><i
										class="fa fa-edit" style="font-size: 24px; margin-right: 5px"></i></span>
									<span data-ng-click="removeRecipe(recipe)"
									style="cursor: pointer" title="Delete Recipe"><i
										class="fa fa-remove"
										style="font-size: 24px; margin-right: 5px; color: red"></i></span></td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div class="col-xs-12" style="text-align: center;">
			<button class="btn btn-primary"
				data-ng-show="selectedProductId != null && ( existingRecipes == null || existingRecipes.length < 1)"
				data-ng-click="addNewSCMRecipe()">
				<i class="fa fa-plus"></i> Add Recipe
			</button>
			<button class="btn btn-primary"
				data-ng-show="selectedProductId != null && ( existingRecipes == null || existingRecipes.length < 1)"
				data-ng-click="cloneFromSCMRecipe()">
				<i class="fa fa-plus"></i> Clone Recipe
			</button>
		</div>
	</div>
	</uib-tab> <uib-tab index="1" heading="Recipe Detail">
	<div class="row tabDiv">
		<div class="col-xs-12">
			<div class="row divInnerRow">
				<div class="col-xs-4">
					<label class="pull-right">Recipe Name</label>
				</div>
				<div class="col-xs-8">
					<input class="form-control" data-ng-model="recipeDetail.name"
						disabled />
				</div>
			</div>
		</div>
		<div class="col-xs-12">
			<div class="row divInnerRow">
				<div class="col-xs-4">
					<label class="pull-right">Start Date</label>
				</div>
				<div class="col-xs-8">
					<div class="datepicker" data-date-min-limit="{{todaysDate}}"
						data-date-format="yyyy-MM-dd">
						<input class="form-control" data-ng-model="recipeDetail.startDate"
							placeholder="Select a start date..." type="text" required />
					</div>
				</div>
			</div>
		</div>
	</div>

	</uib-tab> <uib-tab index="2" heading="Ingredients">
	<div class="row tabDiv">
		<div class="col-xs-12" style="margin-left: 20px; margin-right: 20px">
			<div class="row divInnerRow">
				<div class="col-xs-12">
					<button class="btn btn-primary pull-left"
						data-ng-click="openOtherSCMProduct()">
						<i class="fa fa-plus"></i> Add New Product
					</button>
				</div>
				<div class="col-xs-12" style="margin-top: 20px;">
					<table class="table table-striped table-bordered"
						data-ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.components != null && recipeDetail.ingredient.components.length > 0">
						<thead>
							<th>Product Name</th>
							<th>Unit Of Measure</th>
							<th>Quantity</th>
							<th>Yield in %</th>
							<th>Is Critical</th>
							<th>Actions</th>
						</thead>
						<tbody>
							<tr
								data-ng-repeat="variant in recipeDetail.ingredient.components track by $index">
								<td>{{variant.product.name}}</td>
								<td>{{variant.uom}}</td>
								<td>{{variant.quantity}}</td>
								<td>{{variant.yield}}</td>
								<td>{{variant.critical}}</td>
								<td align="left"><span
									data-ng-click="editOtherSCMProduct(variant)"
									style="cursor: pointer" title="Add/Edit Details"><i
										class="fa fa-edit" style="font-size: 24px; margin-right: 5px"></i></span>&nbsp;&nbsp;
									<span
									data-ng-click="recipeDetail.ingredient.components.splice($index, 1);"
									style="cursor: pointer" title="Remove Variant"><i
										class="fa fa-remove"
										style="font-size: 24px; margin-right: 5px; color: red;"></i></span></td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	</uib-tab> </uib-tabset>
</div>

<div class="row pager" style="margin-top: 10px;">
	<nav>
		<ul class="pager">
			<li data-ng-if="activeTab!=0" data-ng-click="setPrevTab(activeTab)"
				class="previous" style="cursor: pointer"><a><span
					aria-hidden="true">&larr;</span> Previous</a></li>
			<li data-ng-if="activeTab==0" data-ng-click="clearAll(true)"
				style="cursor: pointer"><a>Reset </a></li>
			<li data-ng-if="activeTab==2" data-ng-click="showPreview()"
				style="cursor: pointer"><a>Preview </a></li>
			<li data-ng-if="activeTab > 0 && activeTab < 2"
				data-ng-click="setNextTab(activeTab)" class="next"
				style="cursor: pointer"><a>Next <span aria-hidden="true">&rarr;</span></a>
			</li>
		</ul>
	</nav>
</div>
</div>
</div>

<!-- -- Preview Modal -->
<div class="modal fade previewModal" id="showPreviewModal" role="dialog"
	aria-labelledby="showPreviewModal">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4 class="modal-title" id="showPreviewModalLabel">{{recipeDetail.name}}
					Preview</h4>
			</div>
			<div class="modal-body">
				<div class="row tabDiv">
					<div class="col-xs-12">
						<div class="row divInnerRow">
							<div class="col-xs-6">
								<button class="btn btn-primary pull-right" data-dismiss="modal">Cancel</button>
							</div>
							<div class="col-xs-6">
								<button class="btn btn-primary pull-left"
									data-ng-click="saveRecipe()">Save Recipe</button>
							</div>
						</div>
					</div>
					<div class="col-xs-12" data-ng-if=" recipeCostDetail != null">
						<h3>Recipe Cost Details</h3>
						<div class="row divInnerRow"
							data-ng-if="recipeCostDetail.erroCodes != null && recipeCostDetail.erroCodes.length > 0">
							<div class="col-xs-12" style="margin-top: 10px;">
								<h4>Recipe Common Errors</h4>
								<div class="row"
									data-ng-repeat="error in recipeCostDetail.erroCodes track by $index">
									<div class="col-xs-12">
										<label>{{error}}</label>
									</div>
								</div>
							</div>
						</div>
						<div class="row divInnerRow"
							data-ng-repeat="category in recipeCostDetail.categoryCost track by $index"
							data-ng-if="category.erroCodes != null && category.erroCodes.length > 0">
							<div class="col-xs-12" style="margin-top: 10px;">
								<h4>{{category.costType}} Errors</h4>
								<div class="row"
									data-ng-repeat="error in category.erroCodes track by $index">
									<div class="col-xs-12">
										<label>{{error}}</label>
									</div>
								</div>
							</div>
						</div>
						<div class="row divInnerRow"
							data-ng-if="recipeCostDetail.categoryCost != null && recipeCostDetail.categoryCost.length > 0">
							<div class="col-xs-12" style="margin-top: 10px;">
								<h4>{{recipeCostDetail.recipeName}} Cost Summary</h4>
								<div class="row"
									data-ng-repeat="category in recipeCostDetail.categoryCost track by $index">
									<div class="col-xs-12" data-ng-if="$index == 0">
										<label>Total Cost - {{category.cost}}</label>
									</div>
								</div>
							</div>
						</div>
						<div class="row divInnerRow"
							data-ng-if="recipeCostDetail.categoryCost != null && recipeCostDetail.categoryCost.length > 0">
							<div class="col-xs-12" style="margin-top: 10px;">
								<h4>Detailed Cost Calculation</h4>
								<div class="row" 
									data-ng-repeat="category in recipeCostDetail.categoryCost track by $index">
									<div class="col-xs-12" data-ng-if="$index == 0">
										<div class="row">
											<div class="col-xs-12">
												<h5>{{recipeCostDetail.recipeName}} -
													 Cost Details ({{category.cost}})</h5>
											</div>
											<div class="col-xs-12">
												<table class="table table-striped table-bordered"
													style="margin-top: 10px;">
													<thead>
														<th>Component Type</th>
														<th>Type</th>
														<th>Product Id</th>
														<th>Product Name</th>
														<th>UoM</th>
														<th>Quantity</th>
														<th>Yield in %</th>
														<th>Price</th>
														<th>Cost</th>
													</thead>
													<tbody>
														<tr
															data-ng-repeat="ingredient in recipeCostDetail.commonIngredient track by $index">
															<td>Common</td>
															<td>{{ingredient.type}}</td>
															<td>{{ingredient.productId}}</td>
															<td>{{ingredient.productName}}</td>
															<td>{{ingredient.uom}}</td>
															<td>{{ingredient.quantity}}</td>
															<td>{{ingredient.yield}}</td>
															<td>{{ingredient.price}}</td>
															<td>{{ingredient.cost}}</td>
														</tr>
														<tr
															data-ng-repeat="ingredient in category.ingredients track by $index">
															<td>{{category.costType}}</td>
															<td>{{ingredient.type}}</td>
															<td>{{ingredient.productId}}</td>
															<td>{{ingredient.productName}}</td>
															<td>{{ingredient.uom}}</td>
															<td>{{ingredient.quantity}}</td>
															<td>{{ingredient.yield}}</td>
															<td>{{ingredient.price}}</td>
															<td>{{ingredient.cost}}</td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<h3>Recipe Constituent Details</h3>
						<div class="row divInnerRow">
							<product-view list-label="'Other Products'"
								ng-if="recipeDetail.ingredient != null && recipeDetail.ingredient.components != null && recipeDetail.ingredient.components.length > 0"
								product-list="recipeDetail.ingredient.components"></product-view>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- -- Add Other SCM Products Modal -->
<div class="modal fade" id="addOtherSCMProductModal" role="dialog"
	aria-labelledby="addOtherSCMProductModal">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4 class="modal-title" id="addOtherSCMProductModalLabel">Select
					SCM Product</h4>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="col-xs-12">
						<div class="row divInnerRow">
							<div class="col-xs-4">
								<label>Product</label>
							</div>

							<div class="col-xs-8" data-ng-if="isAdd">
								<select ui-select2 class="form-control"
									style="width: 100% !important" data-ng-model="otherSCMProduct"
									data-ng-change="setSelectedOtherProduct(otherSCMProduct)">
									<option data-ng-repeat="product in scmProductsInfo"
										value="{{product}}">{{product.productName}}  ({{product.category.code}})
								</select>
							</div>
							<div class="col-xs-8" data-ng-if="!isAdd">
								<label class="form-control">{{selectedOtherSCMProduct.productName}}</label>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="row divInnerRow">
							<div class="col-xs-4">
								<label>Unit Of Measure</label>
							</div>
							<div class="col-xs-8">
								<label class="form-control">{{selectedOtherSCMProduct.unitOfMeasure}}</label>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="row divInnerRow">
							<div class="col-xs-4">
								<label>Quantity</label>
							</div>
							<div class="col-xs-8">
								<input class="form-control"
									data-ng-model="selectedOtherProductQuantity" />
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="row divInnerRow">
							<div class="col-xs-4">
								<label>Yield in %</label>
							</div>
							<div class="col-xs-8">
								<input class="form-control"
									data-ng-model="selectedOtherProductYield" />
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<button class="btn btn-primary center-block"
							data-ng-click="addOtherSCMProduct(selectedOtherSCMProduct, selectedOtherProductQuantity, true, selectedOtherProductYield)">Submit</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


<!-- -- Clone Recipe Modal -->
<div class="modal fade" id="cloneSCMRecipeModal" role="dialog"
	aria-labelledby="cloneSCMRecipeModal">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
				<h4 class="modal-title" id="cloneRecipeModalLabel">Select Clone
					Product</h4>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="col-xs-12">
						<div class="row divInnerRow">
							<div class="col-xs-4">
								<label>Product</label>
							</div>
							<div class="col-xs-8">
								<select ui-select2 class="form-control"
									style="width: 100% !important"
									data-ng-model="selectedCloneProductId"
									data-ng-change="onChangeSelectedCloneSCMProduct(selectedCloneProductId);"
									data-placeholder="Search Product...">
									<option value=""></option>
									<option data-ng-repeat="product in scmProductsInfo | filter : filterByCategory"
										value="{{product.productId}}">{{product.productName}}  ({{product.category.code}})  </option>
								</select>
							</div>
						</div>
					</div>
					<div class="col-xs-12"
						data-ng-if="cloneRecipes != null && cloneRecipes.length > 0">
						<div class="row divInnerRow">
							<div class="col-xs-12">
								<table class="table table-striped table-bordered">
									<thead>
										<th>Recipe Id</th>
										<th>Recipe Name</th>
										<th>Actions</th>
									</thead>
									<tbody>
										<tr
											data-ng-repeat="recipe in cloneRecipes track by recipe.recipeId">
											<td>{{recipe.recipeId}}</td>
											<td>{{recipe.name}}</td>
											<td align="left"><span
												data-ng-click="selectSCMRecipeForClone(recipe)"
												style="cursor: pointer" title="Clone"><i
													class="fa fa-copy"
													style="font-size: 24px; margin-right: 5px"></i></span></td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


