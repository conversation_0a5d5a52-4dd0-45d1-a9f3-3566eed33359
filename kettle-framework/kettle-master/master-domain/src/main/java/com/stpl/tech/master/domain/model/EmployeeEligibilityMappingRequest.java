package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Request DTO for Employee Eligibility Mapping
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeEligibilityMappingRequest {

    @NotNull(message = "Eligibility type is required")
    private EligibilityType eligibilityType;

    @NotBlank(message = "Employee ID is required")
    private String empId;

    @NotNull(message = "Mapping type is required")
    private MappingType mappingType;

    @NotBlank(message = "Value is required")
    private String value;
}
