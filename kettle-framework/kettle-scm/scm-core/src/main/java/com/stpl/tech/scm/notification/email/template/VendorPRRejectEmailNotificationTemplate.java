package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorPRRejectEmailNotificationTemplate extends AbstractVelocityTemplate {

    private final static Logger LOG = LoggerFactory.getLogger(VendorPRRejectEmailNotificationTemplate.class);

    private VendorDetail vendorDetail;
    private PaymentRequest paymentRequest;
    private String basePath;
    private List<Integer> grIds;
    private List<Integer> srIds;
    private UnitBasicDetail unitBasicDetail;
    private String rejectedBy;
    private String prCreatedBy;
    private List<String> bcc;


    public VendorPRRejectEmailNotificationTemplate() {

    }

    public VendorPRRejectEmailNotificationTemplate(VendorDetail vendorDetail, PaymentRequest paymentRequest, String basePath,
                                                   List<Integer> grIds, List<Integer> srIds, UnitBasicDetail unitBasicDetail, String rejectedBy, String prCreatedBy, List<String> bcc) {
        this.vendorDetail = vendorDetail;
        this.paymentRequest = paymentRequest;
        this.basePath = basePath;
        this.grIds = grIds;
        this.srIds = srIds;
        this.unitBasicDetail = unitBasicDetail;
        this.rejectedBy = rejectedBy;
        this.prCreatedBy = prCreatedBy;
        this.bcc = bcc;
    }

    @Override
    public String getTemplatePath() {
        return "templates/VendorPRRejectEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/vendor/paymentRequestRejected/" + vendorDetail.getVendorId() + "/" + paymentRequest.getPaymentRequestId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("vendorDetail", vendorDetail);
        stringObjectMap.put("paymentRequest", paymentRequest);
        stringObjectMap.put("grIds", grIds.size() > 0 ? Arrays.toString(grIds.toArray()) : null);
        stringObjectMap.put("srIds", srIds.size() > 0 ? Arrays.toString(srIds.toArray()) : null);
        stringObjectMap.put("rejectedBy", rejectedBy);
        stringObjectMap.put("prCreatedBy", prCreatedBy);
        stringObjectMap.put("bcc", bcc.size() > 0 ? Arrays.toString(bcc.toArray()) : null);
        return stringObjectMap;
    }

    public VendorDetail getVendorDetail() {
        return vendorDetail;
    }

    public PaymentRequest getPaymentRequest() {
        return paymentRequest;
    }

    public UnitBasicDetail getUnitBasicDetail() {
        return unitBasicDetail;
    }
}
