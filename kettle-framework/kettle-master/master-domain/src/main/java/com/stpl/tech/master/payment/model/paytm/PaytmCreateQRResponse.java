
package com.stpl.tech.master.payment.model.paytm;

import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "head",
        "body"
})
public class PaytmCreateQRResponse {

    @JsonProperty("head")
    private PaytmCreateQRResponseHead paytmCreateQRResponseHead;
    @JsonProperty("body")
    private PaytmCreateQRResponseBody paytmCreateQRResponseBody;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("head")
    public PaytmCreateQRResponseHead getPaytmCreateQRResponseHead() {
        return paytmCreateQRResponseHead;
    }

    @JsonProperty("head")
    public void setPaytmCreateQRResponseHead(PaytmCreateQRResponseHead paytmCreateQRResponseHead) {
        this.paytmCreateQRResponseHead = paytmCreateQRResponseHead;
    }

    @JsonProperty("body")
    public PaytmCreateQRResponseBody getPaytmCreateQRResponseBody() {
        return paytmCreateQRResponseBody;
    }

    @JsonProperty("body")
    public void setPaytmCreateQRResponseBody(PaytmCreateQRResponseBody paytmCreateQRResponseBody) {
        this.paytmCreateQRResponseBody = paytmCreateQRResponseBody;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("paytmCreateQRResponseHead", paytmCreateQRResponseHead).append("paytmCreateQRResponseBody", paytmCreateQRResponseBody).append("additionalProperties", additionalProperties).toString();
    }

}
