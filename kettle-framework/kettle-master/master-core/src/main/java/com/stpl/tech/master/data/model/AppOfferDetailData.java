package com.stpl.tech.master.data.model;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "APP_OFFER_DETAIL_DATA")
public class AppOfferDetailData implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1895404398092138192L;
	private Integer id;
    private String offerType;
    private String description;
    private Integer offerIndex;
    private Date startDate;
    private Date endDate;
    private String couponCode;
    private Integer offerId;
    private Integer couponId;
    private String listImage;
    private String listImageUrl;
    private String gridImage;
    private String gridImageUrl;
    private String actionType;
    private String status;
    private String createdBy;
    private Date createTime;
    private String updatedBy;
    private Date updateTime;
    private String redirectionLink;
    private String title;
    private String actionCategory;
    private Integer menuCategoryId;
    private Integer partnerId;
    private String appOfferType;
    private Integer mov;
    private Integer offerValue;
    private Integer maxDiscount;
    private String offerCategory;
    private String offerOrderType;
    private Integer brandId;

    private List<AppOfferApplicabilityData> appOfferApplicabilityDataList;
    public AppOfferDetailData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "OFFER_TYPE")
    public String getOfferType() {
        return offerType;
    }

    public void setOfferType(String offerType) {
        this.offerType = offerType;
    }

    @Column(name = "DESCRIPTION")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "OFFER_INDEX")
    public Integer getOfferIndex() {
        return offerIndex;
    }

    public void setOfferIndex(Integer index) {
        this.offerIndex = index;
    }

    @Column(name = "START_DATE")
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Column(name = "END_DATE")
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Column(name = "COUPON_CODE")
    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    @Column(name = "OFFER_ID")
    public Integer getOfferId() {
        return offerId;
    }

    public void setOfferId(Integer offerId) {
        this.offerId = offerId;
    }

    @Column(name = "COUPON_ID")
    public Integer getCouponId() {
        return couponId;
    }

    public void setCouponId(Integer couponId) {
        this.couponId = couponId;
    }

    @Column(name = "LIST_IMAGE_URL")
    public String getListImageUrl() {
        return listImageUrl;
    }

    public void setListImageUrl(String listImageUrl) {
        this.listImageUrl = listImageUrl;
    }

    @Column(name = "GRID_IMAGE_URL")
    public String getGridImageUrl() {
        return gridImageUrl;
    }

    public void setGridImageUrl(String gridImageUrl) {
        this.gridImageUrl = gridImageUrl;
    }

    @Column(name = "LIST_IMAGE")
    public String getListImage() {
        return listImage;
    }

    public void setListImage(String listImage) {
        this.listImage = listImage;
    }

    @Column(name = "GRID_IMAGE")
    public String getGridImage() {
        return gridImage;
    }

    public void setGridImage(String gridImage) {
        this.gridImage = gridImage;
    }

    @Column(name = "ACTION_TYPE")
    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "CREATED_BY")
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_TIME", nullable = true, length = 19)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Column(name = "UPDATED_BY")
    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATE_TIME", nullable = true, length = 19)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }


    @Column(name = "REDIRECTION_LINK")
    public String getRedirectionLink() {
        return redirectionLink;
    }

    public void setRedirectionLink(String redirectionLink) {
        this.redirectionLink = redirectionLink;
    }

    @Column(name = "TITLE")
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Column(name = "ACTION_CATEGORY")
    public String getActionCategory() {
        return actionCategory;
    }

    public void setActionCategory(String actionCategory) {
        this.actionCategory = actionCategory;
    }

    @Column(name = "MENU_CATEGORY_ID")
    public Integer getMenuCategoryId() {
        return menuCategoryId;
    }

    public void setMenuCategoryId(Integer menuCategoryId) {
        this.menuCategoryId = menuCategoryId;
    }

    @Column(name = "PARTNER_ID")
    public Integer getPartnerId() {
        return partnerId;
    }

    @Column(name = "PARTNER_ID")
    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    @Column(name = "APP_OFFER_TYPE")
    public String getAppOfferType() {
        return appOfferType;
    }

    public void setAppOfferType(String appOfferType) {
        this.appOfferType = appOfferType;
    }

    @Column(name = "MOV")
    public Integer getMov() {
        return mov;
    }
    public void setMov(Integer mov) {
        this.mov = mov;
    }
    @Column(name = "OFFER_VALUE")
    public Integer getOfferValue() {
        return offerValue;
    }
    public void setOfferValue(Integer offerValue) {
        this.offerValue = offerValue;
    }
    @Column(name = "MAX_DISCOUNT")
    public Integer getMaxDiscount() {
        return maxDiscount;
    }

    public void setMaxDiscount(Integer maxDiscount) {
        this.maxDiscount = maxDiscount;
    }
    @Column(name = "OFFER_CATEGORY")
    public String getOfferCategory() {
        return offerCategory;
    }

    public void setOfferCategory(String offerCategory) {
        this.offerCategory = offerCategory;
    }
    @Column(name = "OFFER_ORDER_TYPE")
    public String getOfferOrderType() {
        return offerOrderType;
    }

    public void setOfferOrderType(String offerOrderType) {
        this.offerOrderType = offerOrderType;
    }

    @OneToMany(mappedBy ="appOfferDetailData", cascade = CascadeType.ALL)
    public List<AppOfferApplicabilityData> getAppOfferApplicabilityDataList() {
        return appOfferApplicabilityDataList;
    }

    public void setAppOfferApplicabilityDataList(List<AppOfferApplicabilityData> appOfferApplicabilityDataList) {
        this.appOfferApplicabilityDataList = appOfferApplicabilityDataList;
    }

    @Column(name = "BRAND_ID")
    public Integer getBrandId() { return this.brandId; }

    public void setBrandId(Integer brandId) { this.brandId = brandId; }

}

