/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.util;

import java.io.File;
import java.io.FileFilter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FileNameFilter {

	private static final Logger LOG = LoggerFactory.getLogger(FileNameFilter.class);

	public static File[] listFile(String folder, String ext) {

		GenericExtFilter filter = new GenericExtFilter(ext);

		File dir = new File(folder);

		if (dir.isDirectory() == false) {
			LOG.info("Directory does not exists : " + folder);
			return new File[0];
		}

		// list out all the file name and filter by the extension
		File[] list = dir.listFiles(filter);

		if (list == null || list.length == 0) {
			LOG.info("no files end with : " + ext);
			return new File[0];
		}

		return list;
	}

	// inner class, generic extension filter
	public static class GenericExtFilter implements FileFilter {

		private String ext;

		public GenericExtFilter(String ext) {
			this.ext = ext;
		}

		public boolean accept(File pathname) {
			return (pathname.getName().endsWith(ext));
		}
	}
}
