package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.master.payment.model.PaymentResponse;
import com.stpl.tech.master.payment.model.ingenico.IngenicoQrResponse;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.pubnub.PushNotification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class IngenicoResponseNotification implements PushNotification<Map<String, PaymentResponse>> {

    private static final Logger LOG = LoggerFactory.getLogger(IngenicoResponseNotification.class);

    private String channelName;
    private int retries;
    private Map<String, PaymentResponse> message;

    public IngenicoResponseNotification(EnvType env, IngenicoQrResponse ingenicoResponse, PaymentResponse response) {
        this.channelName = initChannel(env, ingenicoResponse.getMerchantTransactionIdentifier());
        Map<String, PaymentResponse> message = new HashMap<>();
        message.put("INGENICO_PAY_STATUS", response);
        setMessage(message);
    }

    public static String initChannel(EnvType env, String identifier) {
        String unitId = identifier.substring(2, 7);
        char terminalId = identifier.charAt(7);
        LOG.info("ingenico pub nub channel {}", env + "_INGENICO_PAY_" + unitId + "#" + terminalId);
        return env + "_INGENICO_PAY_" + unitId + "#" + terminalId;
    }

    @Override
    public Map<String, PaymentResponse> getMessage() {
        return this.message;
    }

    @Override
    public void setMessage(Map<String, PaymentResponse> message) {
        this.message = message;
    }

    @Override
    public String getChannelName() {
        return this.channelName;
    }

    @Override
    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    @Override
    public int getRetries() {
        return retries;
    }

    @Override
    public void setRetries(int value) {
        this.retries = value;
    }


}
