/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.data.vo;

import java.util.Date;

public class PartnerDataWithOrderConsideration {

    private Integer partnerId;
    private String partnerName;
    private Integer unitId;
    private String unitName;
    private Date businessDate;
    private Integer totalOrders;
    private Integer totalOrderOfConsideration;
    private Integer delayOrders;
    private Date lastUpdateTime;
    private Integer lastOrderId;


    public PartnerDataWithOrderConsideration() {
    }

    public PartnerDataWithOrderConsideration(Integer partnerId) {
        this.partnerId = partnerId;
        this.totalOrders = 0;
        this.totalOrderOfConsideration = 0;
        this.delayOrders = 0;

    }

    public PartnerDataWithOrderConsideration(Integer partnerId, String partnerName, Integer unitId, String unitName, Date businessDate, Integer totalOrders, Integer totalOrderOfConsideration, Integer delayOrders, Date lastUpdateTime, Integer lastOrderId) {
        this.partnerId = partnerId;
        this.partnerName = partnerName;
        this.unitId = unitId;
        this.unitName = unitName;
        this.businessDate = businessDate;
        this.totalOrders = totalOrders;
        this.totalOrderOfConsideration = totalOrderOfConsideration;
        this.delayOrders = delayOrders;
        this.lastUpdateTime = lastUpdateTime;
        this.lastOrderId = lastOrderId;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public Integer getTotalOrders() {
        return totalOrders;
    }

    public void setTotalOrders(Integer totalOrders) {
        this.totalOrders = totalOrders;
    }

    public Integer getTotalOrderOfConsideration() {
        return totalOrderOfConsideration;
    }

    public void setTotalOrderOfConsideration(Integer totalOrderOfConsideration) {
        this.totalOrderOfConsideration = totalOrderOfConsideration;
    }

    public Integer getDelayOrders() {
        return delayOrders;
    }

    public void setDelayOrders(Integer delayOrders) {
        this.delayOrders = delayOrders;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getLastOrderId() {
        return lastOrderId;
    }

    public void setLastOrderId(Integer lastOrderId) {
        this.lastOrderId = lastOrderId;
    }
}
