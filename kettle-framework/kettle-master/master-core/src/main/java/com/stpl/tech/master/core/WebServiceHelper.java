/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.JSONSerializer;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class WebServiceHelper {
	private static final Logger LOG = LoggerFactory.getLogger(WebServiceHelper.class);
	public static final String API_KEY = "api-key";
	private static HttpClient WEB_SERVICE_CLIENT;
	private static HttpClient WEB_SERVICE_CLIENT_300;
	private static ObjectMapper JSON_CONVERTER = new ObjectMapper();

	static {
		JSON_CONVERTER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).getSerializationConfig()
				.getDefaultVisibilityChecker().withFieldVisibility(JsonAutoDetect.Visibility.ANY)
				.withGetterVisibility(JsonAutoDetect.Visibility.NONE)
				.withSetterVisibility(JsonAutoDetect.Visibility.NONE)
				.withCreatorVisibility(JsonAutoDetect.Visibility.NONE);
	}

	public static final String AUTHORIZATION = HttpHeaders.AUTHORIZATION;
	public static final String AUTH_INTERNAL = "auth-internal";
	public static final String AUTH = "auth";
	public static final String CONTENT_TYPE = HttpHeaders.CONTENT_TYPE;
	public static final String ACCEPTS = HttpHeaders.ACCEPT;

	public static final String X_PUBLIC_HEADER = "X-Public";
	public static final String X_HASH_HEADER = "X-Hash";

	private static final int DEFAULT_SOCKET_TIMEOUT = 10;
	private static final int DEFAULT_CONNECTION_TIME_OUT = 10;

	private static final int SOCKET_TIMEOUT_300 = 10;
	private static final int CONNECTION_TIME_OUT_300 = 10;

	public static HttpClient getWebServiceClient() {
		return getHttpClientInstance();
	}

	private static HttpClient getHttpClientInstance() {
		if (WEB_SERVICE_CLIENT == null) {
			RequestConfig config = RequestConfig.custom().setSocketTimeout(DEFAULT_SOCKET_TIMEOUT * 1000)
					.setConnectTimeout(DEFAULT_CONNECTION_TIME_OUT * 1000).setConnectionRequestTimeout(DEFAULT_CONNECTION_TIME_OUT * 1000)
					.build();
			WEB_SERVICE_CLIENT = HttpClientBuilder.create().setDefaultRequestConfig(config).build();
		}
		return WEB_SERVICE_CLIENT;
	}

	private static HttpClient getHttpClientInstance_300(int socketTimeOut, int connectionTimeOut) {
		if (WEB_SERVICE_CLIENT_300 == null) {
			RequestConfig config = RequestConfig.custom().build();
			WEB_SERVICE_CLIENT_300 = HttpClientBuilder.create().setDefaultRequestConfig(config).build();
		}
		return WEB_SERVICE_CLIENT_300;
	}

	public static HttpUriRequest getGenericRequest(HttpMethod method, String endpoint) {
		switch (method) {
		case POST:
			return new HttpPost(endpoint);
		case GET:
			return new HttpGet(endpoint);
		case PUT:
			return new HttpPut(endpoint);
		default:
			return new HttpPost(endpoint);
		}
	}

	public static HttpResponse getRequest(HttpGet requestObject) throws ClientProtocolException, IOException {
		return executeRequest(requestObject);
	}

	public static HttpResponse postRequest(HttpPost requestObject) throws ClientProtocolException, IOException {
		return executeRequest(requestObject);
	}

	public static HttpResponse postRequest(HttpPost requestObject, int socketTimeOut, int connectionTimeOut)
			throws ClientProtocolException, IOException {
		requestObject.setConfig(RequestConfig.custom().setSocketTimeout(socketTimeOut*1000).setConnectTimeout(connectionTimeOut*1000)
				.setConnectionRequestTimeout(connectionTimeOut*1000).build());
		return executeRequestWithTimeouts(requestObject, socketTimeOut, connectionTimeOut);
	}

	public static HttpResponse postRequestWithNoTimeout(HttpPost requestObject)
			throws ClientProtocolException, IOException {
		return executeRequestwithNoTimeOut(requestObject);
	}

	
	
	public static HttpResponse postRequest(String endpoint, String authorizationHeader, StringEntity body)
			throws ClientProtocolException, IOException {
		HttpPost request = new HttpPost(endpoint);
		request.setHeader(AUTHORIZATION, authorizationHeader);
		request.setEntity(body);
		return executeRequest(request);
	}

	public static <T> T getRequestWithAuthAndParamAndHeader(String endpoint, String authorizationHeader,Map<String, String> parameters, Class <T> responseClazz)
			throws ClientProtocolException, IOException, URISyntaxException {
		URIBuilder uriBuilder = new URIBuilder(endpoint);
		if (parameters != null && !parameters.keySet().isEmpty()) {
			for (String p : parameters.keySet()) {
				uriBuilder.setParameter(p, parameters.get(p));
			}
		}
		HttpClient client = HttpClientBuilder.create().build();
		HttpGet request = new HttpGet(uriBuilder.build());
		request.setHeader(AUTH, authorizationHeader);
		request.setHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON.toString());
		HttpResponse response = client.execute(request);
		LOG.info("url before sending request : {}  " ,request);
		return WebServiceHelper.convertResponse(response, responseClazz, true);
	}

	public static HttpResponse postRequestWithAuthInternal(String endpoint, String authorizationHeader, Object body)
			throws ClientProtocolException, IOException {
		HttpPost request = new HttpPost(endpoint);
		request.setHeader(AUTH_INTERNAL, authorizationHeader);
		StringEntity entity = new StringEntity(JSONSerializer.toJSON(body), ContentType.APPLICATION_JSON);
		request.setEntity(entity);
		return executeRequest(request, SOCKET_TIMEOUT_300, CONNECTION_TIME_OUT_300);
	}

	public static HttpResponse putRequest(String endpoint, String authorizationHeader, StringEntity body)
			throws ClientProtocolException, IOException {
		HttpPut request = new HttpPut(endpoint);
		request.setHeader(AUTHORIZATION, authorizationHeader);
		request.setEntity(body);
		return executeRequest(request);
	}

	public static HttpResponse getRequest(String endpoint, String authorizationHeader)
			throws ClientProtocolException, IOException {
		HttpGet request = new HttpGet(endpoint);
		request.setHeader(AUTHORIZATION, authorizationHeader);
		return executeRequest(request);
	}

	public static <T> T getRequestWithParam(String endpoint, String authorizationHeader, Map<String, String> parameters,
			Class<T> responseClazz) throws ClientProtocolException, IOException, URISyntaxException {

		URIBuilder uriBuilder = new URIBuilder(endpoint);
		if (parameters != null && !parameters.keySet().isEmpty()) {
			for (String p : parameters.keySet()) {
				uriBuilder.setParameter(p, parameters.get(p));				
			}
		}
		
		HttpClient client = HttpClientBuilder.create().build();
		HttpGet request = new HttpGet(uriBuilder.build());
		request.setHeader(AUTHORIZATION, authorizationHeader);
		request.setHeader(AUTH_INTERNAL, authorizationHeader);
		
		HttpResponse response = client.execute(request);
		return WebServiceHelper.convertResponse(response, responseClazz, true);
	}

	public static <T> T getRequestWithParamAndHeader(String endpoint, String apiKey, Map<String, String> parameters,
											Class<T> responseClazz,Boolean useJsonSerializer) throws ClientProtocolException, IOException, URISyntaxException {

		URIBuilder uriBuilder = new URIBuilder(endpoint);
		if (parameters != null && !parameters.keySet().isEmpty()) {
			for (String p : parameters.keySet()) {
				uriBuilder.setParameter(p, parameters.get(p));
			}
		}

		HttpClient client = HttpClientBuilder.create().build();
		HttpGet request = new HttpGet(uriBuilder.build());
		request.setHeader(API_KEY, apiKey);
        LOG.info("url before sending request : {}  " ,request);
		HttpResponse response = client.execute(request);
		return WebServiceHelper.convertResponse(response, responseClazz, useJsonSerializer);
	}

	public static <T> T postRequestWithParamAndHeader(String endpoint, String auth, Map<String, String> parameters,
													 Class<T> responseClazz,Boolean useJsonSerializer) throws ClientProtocolException, IOException, URISyntaxException {

		URIBuilder uriBuilder = new URIBuilder(endpoint);
		if (parameters != null && !parameters.keySet().isEmpty()) {
			for (String p : parameters.keySet()) {
				uriBuilder.setParameter(p, parameters.get(p));
			}
		}

		HttpClient client = HttpClientBuilder.create().build();
		HttpPost request = new HttpPost(uriBuilder.build());
		request.setHeader(AUTH, auth);
		LOG.info("url before sending request : {}  " ,request);
		HttpResponse response = client.execute(request);
		return WebServiceHelper.convertResponse(response, responseClazz, useJsonSerializer);
	}

	public static <T> T postRequestWithParamAndAuthInternal(String endpoint, String auth, Map<String, String> parameters,
													  Class<T> responseClazz,Boolean useJsonSerializer) throws ClientProtocolException, IOException, URISyntaxException {

		URIBuilder uriBuilder = new URIBuilder(endpoint);
		if (parameters != null && !parameters.keySet().isEmpty()) {
			for (String p : parameters.keySet()) {
				uriBuilder.setParameter(p, parameters.get(p));
			}
		}

		HttpClient client = HttpClientBuilder.create().build();
		HttpPost request = new HttpPost(uriBuilder.build());
		request.setHeader(AUTH_INTERNAL, auth);
		LOG.info("url before sending request : {}  " ,request);
		HttpResponse response = client.execute(request);
		return WebServiceHelper.convertResponse(response, responseClazz, useJsonSerializer);
	}

	public static HttpResponse putRequest(HttpPut requestObject) throws ClientProtocolException, IOException {
		return executeRequest(requestObject);
	}

	private static HttpResponse executeRequestWithTimeouts(HttpUriRequest requestObject, int socketTimeout,
			int connectionTimeout) throws ClientProtocolException, IOException {
		HttpResponse response = getHttpClientInstance().execute(requestObject);
		// clearing all the resources of the HTTP web service client
		if (response == null || response.getEntity() == null) {
			requestObject.abort();
		}
		return response;
	}

	private static HttpResponse executeRequest(HttpUriRequest requestObject)
			throws ClientProtocolException, IOException {
		return executeRequestWithTimeouts(requestObject, DEFAULT_SOCKET_TIMEOUT, DEFAULT_CONNECTION_TIME_OUT);
	}

	private static HttpResponse executeRequest(HttpUriRequest requestObject, int socketTimeout, int connectionTimeout)
			throws ClientProtocolException, IOException {
		HttpResponse response = getHttpClientInstance_300(socketTimeout, connectionTimeout).execute(requestObject);
		// clearing all the resources of the HTTP web service client
		if (response == null || response.getEntity() == null) {
			requestObject.abort();
		}
		return response;
	}
	
	private static HttpResponse executeRequestwithNoTimeOut(HttpUriRequest requestObject)
			throws ClientProtocolException, IOException {
		HttpResponse response = getHttpClientInstance_300().execute(requestObject);
		// clearing all the resources of the HTTP web service client
		if (response == null || response.getEntity() == null) {
			requestObject.abort();
		}
		return response;
	}
	
	private static HttpClient getHttpClientInstance_300() {
		RequestConfig config = RequestConfig.custom().build();
		WEB_SERVICE_CLIENT_300 = HttpClientBuilder.create().setDefaultRequestConfig(config).build();
		return WEB_SERVICE_CLIENT_300;
	}

	public static <T> T convertResponse(HttpResponse response, Class<T> responseClazz)
			throws IllegalStateException, IOException {
		return convertResponse(response, responseClazz, false);
	}

	public static <T> T convertResponse(HttpResponse response, Class<T> responseClazz, boolean usejsonSerializer)
			throws IllegalStateException, IOException {
		if (response != null) {
			BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
			StringBuffer result = new StringBuffer();
			String line = "";
			while ((line = reader.readLine()) != null) {
				result.append(line);
			}
			//LOG.info("recorded response :::: {}", result.toString());
			EntityUtils.consume(response.getEntity());
			if (usejsonSerializer) {
				return convertUsingSerializer(result.toString(), responseClazz);

			} else {
				return convert(result.toString(), responseClazz);

			}
		}

		return null;

	}

	public static <T> T convert(Object input, Class<T> output) {
		return JSON_CONVERTER.convertValue(input, output);
	}

	public static <T> T convert(String input, Class<T> output) throws IOException {
		return JSON_CONVERTER.readValue(input, output);
	}

	public static <T> T convertUsingSerializer(String input, Class<T> output) throws IOException {
		return JSONSerializer.toJSON(input, output);
	}

	public static <T> String convertToString(T t) throws JsonProcessingException {
		String string = JSON_CONVERTER.writeValueAsString(t);
		// LOG.info("Object value :::: {}", string);
		return string;
	}

	public static <T> T postRequestWithAuthInternalNoTimeout(String url, Object object, Class<T> responseClazz,
			String token) throws ClientProtocolException, IOException {
		HttpPost requestObject = new HttpPost(url);
		String consumptionDataJson = WebServiceHelper.convertToString(object);
		HttpEntity httpEntity = new StringEntity(consumptionDataJson, AppConstants.CHARSET);
		requestObject.setHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON.toString());
		requestObject.setHeader(AUTH_INTERNAL, token);
		requestObject.setEntity(httpEntity);
		HttpResponse response = WebServiceHelper.postRequestWithNoTimeout(requestObject);
		return WebServiceHelper.convertResponse(response, responseClazz, true);
	}
	
	public static <T> T postRequestWithAuthInternalWithTimeout(String url, Object object, Class<T> responseClazz,
			String token) throws ClientProtocolException, IOException {
		HttpPost requestObject = new HttpPost(url);
		String consumptionDataJson = WebServiceHelper.convertToString(object);
		HttpEntity httpEntity = new StringEntity(consumptionDataJson, AppConstants.CHARSET);
		requestObject.setHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON.toString());
		requestObject.setHeader(AUTH_INTERNAL, token);
		requestObject.setEntity(httpEntity);
		HttpResponse response = WebServiceHelper.postRequest(requestObject);
		return WebServiceHelper.convertResponse(response, responseClazz, true);
	}
	
	public static <T> T postRequestWithAuthInternalWithTimeout(String url, Object object, Class<T> responseClazz,
															   String token,int socketTimeOut,int connectionTimeOut) throws ClientProtocolException, IOException {
		HttpPost requestObject = new HttpPost(url);
		String consumptionDataJson = WebServiceHelper.convertToString(object);
		HttpEntity httpEntity = new StringEntity(consumptionDataJson, AppConstants.CHARSET);
		requestObject.setHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON.toString());
		requestObject.setHeader(AUTH_INTERNAL, token);
		requestObject.setEntity(httpEntity);
		HttpResponse response = WebServiceHelper.postRequest(requestObject,socketTimeOut,connectionTimeOut);
		return WebServiceHelper.convertResponse(response, responseClazz, true);

	}

	public static void postRequestForFamePilot(String url, List<NameValuePair> formData) throws IOException {
		HttpPost requestObject = new HttpPost(url);
		requestObject.setEntity(new UrlEncodedFormEntity(formData));
		requestObject.setHeader("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
		HttpResponse response = WebServiceHelper.postRequest(requestObject);
		LOG.info("Response for FamePilotApi : {}",response.toString());
		if(response.getStatusLine().getStatusCode()!=200){
			String st = "{";
			for(NameValuePair pair : formData){
				if(st.equals("{")){
					st = st + pair.toString();
				}else{
					st = st + ","+ pair.toString();
				}
			}
			st = st + "}";
			LOG.info("Request Data is : {}",st);
		}
		if(Objects.nonNull(response) || response.getEntity()!=null){
			requestObject.abort();
		}
	}

	public static HttpResponse postRequestWithAuthInternalTimeout(String endpoint, String authorizationHeader, Object body)
			throws ClientProtocolException, IOException {
		HttpPost request = new HttpPost(endpoint);
		request.setHeader(AUTH_INTERNAL, authorizationHeader);
		StringEntity entity = new StringEntity(JSONSerializer.toJSON(body), ContentType.APPLICATION_JSON);
		request.setEntity(entity);
		return executeRequestwithNoTimeOut(request);
	}

	public static <T> T postWithAuth(String endPoint, String token, Object body, Class<T> clazz) {
		try {
			RestTemplate restTemplate = new RestTemplate();
			org.springframework.http.HttpHeaders requestHeaders = new org.springframework.http.HttpHeaders();
			if(token != null) {
				requestHeaders.set("auth", token);
			}
			requestHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
			org.springframework.http.HttpEntity<?> requestEntity;
			if (body != null) {
				requestEntity = new org.springframework.http.HttpEntity(body, requestHeaders);
			} else {
				requestEntity = new org.springframework.http.HttpEntity(requestHeaders);
			}
			MappingJackson2HttpMessageConverter jackson = new MappingJackson2HttpMessageConverter();
			jackson.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON_UTF8));
			restTemplate.getMessageConverters().add(jackson);
			return restTemplate.postForObject(endPoint, requestEntity, clazz);
		} catch (Exception e) {
			LOG.error("ERROR While Request {}", endPoint, e);
			throw e;
		}
	}

	public static HttpResponse postWithBearer(String endpoint, String token, Object body,Map<String,?> uriVariables) {
		try {
			if (uriVariables != null) {
				endpoint += "?";
				StringBuilder endpointBuilder = new StringBuilder(endpoint);
				for (String key : uriVariables.keySet()) {
					endpointBuilder.append(key).append("=").append(uriVariables.get(key).toString()).append("&");
				}
				endpoint = endpointBuilder.toString();
				endpoint = endpoint.substring(0, endpoint.length() - 1);
			}
			HttpPost request = new HttpPost(endpoint);
			if(token != null) {
				request.setHeader("Authorization", "Bearer "+token);
			}
			StringEntity entity = new StringEntity(JSONSerializer.toJSON(body), ContentType.APPLICATION_JSON);
			request.setEntity(entity);
			HttpResponse response= postRequestWithNoTimeout(request);
			return response;
		}
		catch (Exception e) {
			LOG.error("ERROR While Request {}", endpoint, e);
		}
		return null;
//		try {
//			RestTemplate restTemplate = new RestTemplate();
//			org.springframework.http.HttpHeaders requestHeaders = new org.springframework.http.HttpHeaders();
//			if(token != null) {
//				requestHeaders.set("auth", token);
//			}
//			requestHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
//			org.springframework.http.HttpEntity<?> requestEntity;
//			if (body != null) {
//				requestEntity = new org.springframework.http.HttpEntity(body, requestHeaders);
//			} else {
//				requestEntity = new org.springframework.http.HttpEntity(requestHeaders);
//			}
//			MappingJackson2HttpMessageConverter jackson = new MappingJackson2HttpMessageConverter();
//			jackson.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON_UTF8));
//			restTemplate.getMessageConverters().add(jackson);
//			return restTemplate.postForObject(endPoint, requestEntity, clazz);
//		} catch (Exception e) {
//			LOG.error("ERROR While Request {}", endPoint, e);
//			throw e;
//		}
	}

	public static HttpResponse postRequestWithAuth(String endpoint, String authorizationHeader, Object body)
			throws  IOException {
		HttpPost request = new HttpPost(endpoint);
		request.setHeader(AUTH, authorizationHeader);
		StringEntity entity = new StringEntity(JSONSerializer.toJSON(body), ContentType.APPLICATION_JSON);
		request.setEntity(entity);
		HttpResponse response = executeRequest(request);
	    EntityUtils.consume(response.getEntity());
		return response;
	}
}
