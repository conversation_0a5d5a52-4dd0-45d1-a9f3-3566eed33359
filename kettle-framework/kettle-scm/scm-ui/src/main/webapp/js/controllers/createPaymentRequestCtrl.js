/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('createPaymentRequestCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService, previewModalService) {

            $scope.init = function () {
                $scope.selectView = true;
                $scope.selectedGr = null;
                $scope.itemDeviations = [];
                $scope.invoiceDeviations = [];
                metaDataService.getSCMMetaData(function (metadata) {
                    $scope.prRequestTypes = metadata.paymentRequestTypes.filter(function (prType) {
                        return prType.shortCode == "GR";
                    });
                    $scope.prRequestType = $scope.prRequestTypes == null ? null : $scope.prRequestTypes[0];
                    metadata.paymentDeviations.map(function (deviation) {
                        if (deviation.deviationType == "DEVIATION") {
                            if (deviation.deviationLevel == "INVOICE_ITEM") {
                                $scope.itemDeviations.push(deviation);
                            } else if (deviation.deviationLevel == "INVOICE") {
                                $scope.invoiceDeviations.push(deviation);
                            }
                        }
                    });
                });
                $scope.unitList = [];
                $scope.unitList.push({
                    id: null,
                    name: ""
                });
                $scope.selectedUnit = $scope.unitList[0];
                $scope.vendorList = [];
                $scope.vendorList.push({
                    id: null,
                    name: ""
                });
                $scope.selectedVendor = $scope.vendorList[0];
                $scope.getEmployeeMappedUnits();
                $scope.startDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.showPreview = previewModalService.showPreview;
            };

            $scope.getEmployeeMappedUnits = function () {
                appUtil.getUnitList().map(function (unit) {
                    if ($rootScope.mappedUnits.indexOf(unit.id) >= 0) {
                        $scope.unitList.push(unit);
                    }
                });
                if ($scope.unitList != null && $scope.unitList.length > 0) {
                    $scope.selectedUnit = $scope.unitList[0];
                }
            };

            $scope.selectUnit = function (unit) {
                $scope.selectedUnit = unit;
                $scope.getMappedVendors();
            };

            $scope.getMappedVendors = function () {
                console.log($scope.selectedUnit);
                if ($scope.selectedUnit.id !== null) {
                    $http({
                        method: "GET",
                        url: apiJson.urls.skuMapping.getVendorsForUnitTrimmed + "?unitId=" + $scope.selectedUnit.id
                    }).then(function success(response) {
                        $scope.vendorList = [];
                        response.data.map(function (item) {
                            $scope.vendorList.push(item);
                        });
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                } else {
                    $scope.vendorList = [{
                        id: null,
                        name: ""
                    }];
                    $scope.selectedVendor = $scope.vendorList[0];
                }

            };

            $scope.findGrs = function () {
                $scope.selectedGr = null;
                $scope.showNoGR = false;
                if ($scope.selectedUnit.id == null || $scope.selectedUnit.id == "") {
                    $toastService.create("Please select unit");
                } else if ($scope.startDate == null || $scope.startDate == "") {
                    $toastService.create("Please select start date");
                } else if ($scope.endDate == null || $scope.endDate == "") {
                    $toastService.create("Please select end date");
                } else if ($scope.selectedVendor.id == null || $scope.selectedVendor.id == "") {
                    $toastService.create("Please select vendor");
                } else {
                    $scope.grs = [];
                    $http({
                        method: 'GET',
                        url: apiJson.urls.goodsReceivedManagement.findVendorGRsForPayment,
                        params: {
                            deliveryUnitId: $scope.selectedUnit.id,
                            startDate: $scope.startDate,
                            endDate: $scope.endDate,
                            vendorId: $scope.selectedVendor.id
                        }
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            $scope.getFilteredGrsByType(response.data);
                        } else {
                            $scope.showNoGR = true;
                        }
                    }, function (error) {
                        console.log(error);
                    });
                }
            };

            $scope.getFilteredGrsByType = function (data) {
                $scope.grs = [];
                data.map(function (gr) {
                    if (gr.receiptType === $scope.grType) {
                        $scope.grs.push(gr);
                    }
                });
                if ($scope.grs.length === 0) {
                    $scope.showNoGR = true;
                }
            };

            $scope.backToSelectView = function () {
                $scope.selectView = true;
            };

            $scope.createPR = function (gr, forceCreated) {
                $scope.selectedGr = gr;
                $scope.uploadedDocData = null;
                var invoiceItems = [];
                gr.grItems.map(function (item) {
                    addNewInvoiceItem(invoiceItems, item);
                });
                $scope.createdByUnit = {
                    id: $scope.selectedGr.deliveryUnitId.id,
                    code: "",
                    name: $scope.selectedGr.deliveryUnitId.name
                };
                createPaymentRequestObject(forceCreated);
                addGrDataToInvoice(gr);
                $scope.paymentRequest.paymentInvoice.paymentInvoiceItems = invoiceItems;
                $scope.paymentRequest.paymentInvoice.invoiceNumber = gr.receiptNumber;
                $scope.paymentRequest.paymentInvoice.invoiceDate = appUtil.formatDate(gr.grDocumentDate, "yyyy-MM-dd");
                addGrItemMapping(gr.id);
                $scope.invoiceNumber = gr.receiptNumber;
                $scope.createdByUser = appUtil.createGeneratedBy();
                $scope.invoiceDate = appUtil.formatDate(gr.grDocumentDate, "yyyy-MM-dd");
                $scope.selectView = false;
            };

            $scope.setAvailableDeviations = function (item, type) {
                $scope.availableInvoiceDevs = [];
                $scope.availableItemDevs = [];
                var devList, availableList = [];
                if (type === "INVOICE") {
                    devList = $scope.invoiceDeviations;
                } else {
                    devList = $scope.itemDeviations;
                }
                devList.map(function (iDev) {
                    var found = false;
                    item.deviations.map(function (dev) {
                        if (iDev.paymentDeviationId === dev.paymentDeviation.paymentDeviationId) {
                            found = true;
                        }
                    });
                    if (!found) {
                        availableList.push({data: iDev});
                    }
                });
                if (type === "INVOICE") {
                    $scope.availableInvoiceDevs = availableList;
                } else {
                    $scope.selectedItemForDeviation = item;
                    $scope.availableItemDevs = availableList;
                }
            };

            $scope.addDeviations = function (item, type) {
                var devList;
                if (type === "INVOICE") {
                    devList = $scope.availableInvoiceDevs;
                } else {
                    devList = $scope.availableItemDevs;
                }
                devList.map(function (dev) {
                    if (dev.checked) {
                        item.deviations.push({
                            mappingId: null,
                            paymentDeviation: dev.data,
                            deviationRemark: dev.remark,
                            currentStatus: "CREATED",
                            createdBy: appUtil.createGeneratedBy()
                        });
                    }
                });
            };

            $scope.removeDeviation = function (list, index) {
                list.splice(index, 1);
            };

            $scope.showDeviationInput = function (item) {
                item.showDeviationInput = true;
            };

            $scope.submitPaymentRequest = function () {
                if ($scope.paymentRequest.grDocType == "INVOICE" && $scope.paymentRequest.paymentInvoice.invoiceNumber == null) {
                    $toastService.create("Please provide invoice number.");
                } else if ($scope.paymentRequest.grDocType == "INVOICE" && $scope.paymentRequest.paymentInvoice.invoiceDate == null) {
                    $toastService.create("Please provide invoice date.");
                } else if ($scope.paymentRequest.grDocType == "INVOICE" && $scope.uploadedDocData == null) {
                    $toastService.create("Please attach invoice document.");
                } else {
                    $scope.paymentRequest.deviationCount = $scope.paymentRequest.paymentInvoice.deviations.length;
                    $scope.paymentRequest.paymentInvoice.paymentInvoiceItems.map(function (item) {
                        $scope.paymentRequest.deviationCount += item.deviations.length;
                    });
                    if ($scope.paymentRequest.grDocType == "INVOICE") {
                        $scope.paymentRequest.paymentInvoice.invoiceDocumentHandle = $scope.uploadedDocData.documentId;
                        //$scope.paymentRequest.paymentInvoice.invoiceDate = $scope.invoiceDate;
                    }
                    $http({
                        url: apiJson.urls.paymentRequestManagement.paymentRequest,
                        method: 'POST',
                        data: $scope.paymentRequest,
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response != null) {
                            $alertService.alert("Payment Request creation successful",
                                "Payment request created successfully with request id " + response.paymentRequestId, function () {
                                    $scope.backToSelectView();
                                    $scope.findGrs();
                                })
                            //$toastService.create("Payment Request creation successful.");
                        } else {
                            $toastService.create("Payment Request creation failed.");
                            $scope.selectedGr =  null;
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $scope.selectedGr =  null;
                        $alertService.alert("Payment Request creation failed", response.errorMsg, function () {
                        }, true)
                    });
                }
            };

            $scope.initiatePR = function (forceCreated) {
                var invoiceItems = [];
                $scope.uploadedDocData = null;
                $scope.createdByUnit = {
                    id: $scope.grs[0].deliveryUnitId.id,
                    code: "",
                    name: $scope.grs[0].deliveryUnitId.name
                };
                createPaymentRequestObject(forceCreated);
                $scope.grs.map(function (gr) {
                    if (gr.checked == true) {
                        gr.grItems.map(function (grItem) {
                            //var matched = false;
                            var added = false;
                            invoiceItems.map(function (item) {
                                if (grItem.skuId == item.skuId && grItem.packagingId == item.packagingId && grItem.unitPrice == item.unitPrice && grItem.taxes.length == item.taxes.length) {
                                    //matched = true;
                                    grItem.taxes.map(function (grItemTax) {
                                        item.taxes.map(function (itemTax) {
                                            if(grItemTax.taxCategory == itemTax.taxType && grItemTax.percentage == itemTax.taxPercentage){
                                                grItemTax.matched = true;
                                            }
                                        });
                                    });
                                    var allMatched = true;
                                    grItem.taxes.map(function (grItemTax) {
                                        if(allMatched && grItemTax.matched != true){
                                            allMatched = false;
                                        }
                                    });
                                    if (allMatched) {
                                        addToExistingItem(item, grItem);
                                        added = true;
                                    } else {
                                        //addNewInvoiceItem(invoiceItems, grItem);
                                    }
                                }
                            });
                            if (!added) {
                                addNewInvoiceItem(invoiceItems, grItem);
                            }
                        });
                        addGrDataToInvoice(gr);
                        addGrItemMapping(gr.id);
                    }
                });
                $scope.createdByUser = appUtil.createGeneratedBy();
                $scope.paymentRequest.paymentInvoice.paymentInvoiceItems = invoiceItems;
                $scope.invoiceNumber = null;
                $scope.selectView = false;
            };

            $scope.downloadPRInvoice = function (prInvoice) {
                metaDataService.downloadDocument(prInvoice);
            };

            $scope.previewPRInvoice = function(prInvoice){
                if(!appUtil.isEmptyObject(prInvoice.documentLink)){
                    $http({
                        method:"POST",
                        url:apiJson.urls.vendorManagement.downloadDocument,
                        data: prInvoice,
                        responseType: 'arraybuffer'
                    }).then(function(response){
                        var arrayBufferView = new Uint8Array( response.data );
                        var blob = new Blob( [ arrayBufferView ], { type: appUtil.mimeTypes[prInvoice.mimeType] } );
                        var urlCreator = window.URL || window.webkitURL;
                        var imageUrl = urlCreator.createObjectURL( blob );
                        var preview = document.getElementById("invoicePreview");
                        preview.innerHTML = "";
                        var img = new Image();
                        img.src = imageUrl;
                        preview.appendChild(img);
                    },function(error){
                        $toastService.create("Could not download the document... Please try again");
                    });
                }else{
                    $toastService.create("Not a valid document... Please check");
                }
            };

            /////////////////////document upload methods/////////////////////////////////

            $scope.resetScanModal = function () {
                $scope.imagesScanned = [];
                document.getElementById('images').innerHTML = "";
                var canvas = document.createElement('canvas');
                canvas.id = "scaleCanvas";
                document.getElementById('images').appendChild(canvas);
                $scope.uploadedDocData = null;
            };

            $scope.resetSnapModal = function () {
                $scope.snapRunning = false;
                if ($scope.localstream != null) {
                    $scope.localstream.getTracks()[0].stop();
                }
                $scope.uploadedDocData = null;
                var canvas = document.getElementById('canvas');
                var context = canvas.getContext('2d');
                context.clearRect(0, 0, 640, 480);
            };

            $scope.startSnap = function () {
                var video = document.getElementById('video');
                // Get access to the camera!
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    // Not adding `{ audio: true }` since we only want video now
                    navigator.mediaDevices.getUserMedia({video: true}).then(function (stream) {
                        video.src = window.URL.createObjectURL(stream);
                        $scope.localstream = stream;
                        video.play();
                    });
                }
                $scope.snapRunning = true;
            };

            $scope.snapPicture = function () {
                var canvas = document.getElementById('canvas');
                var context = canvas.getContext('2d');
                var video = document.getElementById('video');
                context.drawImage(video, 0, 0, 640, 480);
                video.pause();
                video.src = "";
                $scope.localstream.getTracks()[0].stop();
                $scope.snapRunning = false;
            };

            function dataURItoBlob(dataURI) {
                var byteString = atob(dataURI.split(',')[1]);
                var ab = new ArrayBuffer(byteString.length);
                var ia = new Uint8Array(ab);
                for (var i = 0; i < byteString.length; i++) {
                    ia[i] = byteString.charCodeAt(i);
                }
                return new Blob([ab], {type: 'image/png'});
            }

            $scope.uploadFile = function () {
                var canvas = document.getElementById('canvas');
                var blob = dataURItoBlob(canvas.toDataURL("image/png"));
                var fd = new FormData(document.forms[0]);
                fd.append("file", blob);
                fd.append('type', "OTHERS");
                fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                fd.append('mimeType', "PNG");
                fd.append('userId', appUtil.getCurrentUser().userId);
                $http({
                    url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Upload successful");
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };

            $scope.uploadDoc = function () {
                $fileUploadService.openFileModal("Upload Invoice Document", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }
                    if(file.size > 5120000){
                        $toastService.create('File size should not be greater than 5 MB.');
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('type', "OTHERS");
                        fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                        fd.append('mimeType', fileExt.toUpperCase());
                        fd.append('userId', appUtil.getCurrentUser().userId);
                        fd.append('file', file);
                        $http({
                            url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!appUtil.isEmptyObject(response)) {
                                $toastService.create("Upload successful");
                                $scope.uploadedDocData = response;
                            } else {
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            $toastService.create("Upload failed");
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                    /*metaDataService.uploadFile("OTHERS","PAYMENT_REQUEST_INVOICE",file, function(doc){
                        $scope.uploadedDocData = doc;
                    });*/
                });
            };

            $scope.scanToPng = function () {
                scanner.scan($scope.displayImagesOnPage,
                    {
                        "output_settings": [
                            {
                                "type": "return-base64",
                                "format": "png"
                            }
                        ]
                    }
                );
            };

            $scope.displayImagesOnPage = function (successful, mesg, response) {
                if (!successful) { // On error
                    console.error('Failed: ' + mesg);
                    return;
                }
                if (successful && mesg != null && mesg.toLowerCase().indexOf('user cancel') >= 0) { // User cancelled.
                    console.info('User cancelled');
                    return;
                }
                var scannedImages = scanner.getScannedImages(response, true, false); // returns an array of ScannedImage
                $scope.imagesScanned = [];
                $scope.processScannedImage(scannedImages[0]);
                /*for(var i = 0; (scannedImages instanceof Array) && i < scannedImages.length; i++) {
                    var scannedImage = scannedImages[i];
                    $scope.processScannedImage(scannedImage);
                }*/
            };

            $scope.processScannedImage = function (scannedImage) {
                $scope.imagesScanned.push(scannedImage);
                scaleImage(scannedImage.src);
            };

            function scaleImage(src) {
                var MAX_WIDTH = 1000;
                var image = new Image();
                var canvas = document.getElementById("scaleCanvas");
                image.onload = function () {
                    //var canvas = document.getElementById("scaleCanvas");
                    if (image.width > MAX_WIDTH) {
                        image.height *= MAX_WIDTH / image.width;
                        image.width = MAX_WIDTH;
                    }
                    var ctx = canvas.getContext("2d");
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    canvas.width = image.width;
                    canvas.height = image.height;
                    ctx.drawImage(image, 0, 0, image.width, image.height);
                };
                image.src = src;
            }

            $scope.uploadScannedFile = function () {
                var canvas = document.getElementById('scaleCanvas');
                var blob = dataURItoBlob(canvas.toDataURL("image/png"));
                var fd = new FormData(document.forms[0]);
                fd.append("file", blob);
                fd.append('type', "OTHERS");
                fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                fd.append('mimeType', "PNG");
                fd.append('userId', appUtil.getCurrentUser().userId);
                $http({
                    url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Upload successful");
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };

            ///////////////////utility functions/////////////////////////////

            function addNewInvoiceItem(invoiceItems, item) {
                var invoiceItem = {
                    paymentInvoiceItemId: null,
                    skuId: item.skuId,
                    skuName: item.skuName,
                    hsn: item.hsn,
                    uom: item.unitOfMeasure,
                    packagingId: item.packagingId,
                    conversionRatio: item.conversionRatio,
                    quantity: item.receivedQuantity,
                    totalAmount: item.amountPaid,
                    totalTax: item.totalTax,
                    totalPrice: item.totalCost,
                    unitPrice: item.unitPrice,
                    packagingPrice: item.unitPrice * item.conversionRatio,
                    packagingName: item.packagingName,
                    taxes: [],
                    deviations: [],
                    showDeviationInput: true
                };
                item.taxes.map(function (tax) {
                    invoiceItem.taxes.push({
                        taxDetailId: null,
                        taxType: tax.taxCategory,
                        taxPercentage: tax.percentage,
                        taxValue: tax.value
                    });
                });
                invoiceItems.push(invoiceItem);
            }

            function addToExistingItem(item, grItem) {
                item.quantity += grItem.receivedQuantity;
                item.totalAmount += grItem.amountPaid;
                item.totalTax += grItem.totalTax;
                item.totalPrice += grItem.totalCost;
            }

            function createPaymentRequestObject(forceCreated) {
                if($scope.selectedGr == null && $scope.selectedGr == undefined){
                    $scope.selectedGr = $scope.grs[0];
                }
                var invoice = {
                    paymentInvoiceId: null,
                    invoiceNumber: null,
                    invoiceDate: null,
                    invoiceDocumentHandle: "",
                    calculatedInvoiceAmount: 0,
                    extraCharges: 0,
                    invoiceAmount: 0,
                    paymentAmount: 0,
                    paymentInvoiceItems: null,
                    deviations: []
                };
                $scope.paymentRequest = {
                    paymentRequestId: null,
                    type: $scope.prRequestType.code,
                    vendorId: {id: $scope.selectedVendor.id},
                    createdBy: {id: appUtil.getCurrentUser().user.id},
                    state: {
                        name: $scope.selectedGr.deliveryUnitId.state,
                        code: $scope.selectedGr.deliveryUnitId.stateCode},
                    creationTime: null,
                    currentStatus: "CREATED",
                    lastUpdated: null,
                    paymentInvoice: invoice,
                    paymentCycle: null,
                    forceCreate : forceCreated,
                    paymentCycleDate: null,
                    proposedAmount: 0,
                    paidAmount: 0,
                    amountsMatch: $scope.amountsMatch,
                    blocked: false,
                    blockedBy: null,
                    requestingUnit: {id: $scope.createdByUnit.id},
                    grDocType: $scope.grType,
                    deviationCount: 0,
                    requestItemMappings: []
                };
            }

            function addGrDataToInvoice(gr) {
                $scope.paymentRequest.proposedAmount += (gr.billAmount + gr.extraCharges);
                $scope.paymentRequest.paidAmount += (gr.billAmount + gr.extraCharges);
                $scope.paymentRequest.paymentInvoice.calculatedInvoiceAmount += gr.billAmount;
                $scope.paymentRequest.paymentInvoice.extraCharges += gr.extraCharges;
                $scope.paymentRequest.paymentInvoice.invoiceAmount += (gr.billAmount + gr.extraCharges);
                $scope.paymentRequest.paymentInvoice.paymentAmount += (gr.billAmount + gr.extraCharges);
            }

            function addGrItemMapping(grId) {
                $scope.paymentRequest.requestItemMappings.push({
                    id: null,
                    paymentRequestId: null,
                    paymentRequestType: $scope.prRequestType.code,
                    paymentRequestItemId: grId
                });
            }

        }]
    );
