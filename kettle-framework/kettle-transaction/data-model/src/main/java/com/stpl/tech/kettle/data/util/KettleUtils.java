package com.stpl.tech.kettle.data.util;

import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.util.AppConstants;
import org.springframework.util.StringUtils;

import java.util.Objects;

public class KettleUtils {
    public static boolean isProfileCompleted(CustomerInfo customer){
        return !StringUtils.isEmpty(customer.getFirstName()) && !StringUtils.isEmpty(customer.getContactNumber())
                && !StringUtils.isEmpty(customer.getEmailId()) && Objects.nonNull(customer.getDateOfBirth()) && !StringUtils.isEmpty(customer.getGender());
    }

    public static Integer getBrandIdForVerificationEmail(Integer brandId){
        switch (brandId){
            case 6:
                return AppConstants.DOHFUL_BRAND_ID;
            default:
                return AppConstants.CHAAYOS_BRAND_ID;
        }
    }
}
