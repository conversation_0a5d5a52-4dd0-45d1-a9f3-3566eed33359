/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.cache;

import java.io.Serializable;

import com.stpl.tech.kettle.domain.model.Customer;

public class UnitSessionDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1013303059478447221L;

	private int unitId;

	private Integer terminalId;

	private String generatedOrderId;

	private Customer customer;

	private boolean newCustomer = false;

	private boolean sentOtp = false;

	private boolean hasRedemption = false;

	private int loyaltyPoints;

	private String loyaltyReason;

	private boolean pointsRedeemedSuccessfully;

	private int redeemedProductId;

	public UnitSessionDetail() {

	}

	/**
	 * @param unitId
	 * @param generatedOrderId
	 */
	public UnitSessionDetail(int unitId, Integer terminalId, String generatedOrderId) {
		super();
		this.unitId = unitId;
		this.generatedOrderId = generatedOrderId;
		this.terminalId = terminalId;
	}

	public String getGeneratedOrderId() {
		return generatedOrderId;
	}

	public Customer getCustomer() {
		return customer;
	}

	public void setCustomer(Customer customer) {
		this.customer = customer;
	}

	public boolean isNewCustomer() {
		return newCustomer;
	}

	public void setNewCustomer(boolean newCustomer) {
		this.newCustomer = newCustomer;
	}

	public boolean isSentOtp() {
		return sentOtp;
	}

	public void setSentOtp(boolean sentOtp) {
		this.sentOtp = sentOtp;
	}

	public boolean isHasRedemption() {
		return hasRedemption;
	}

	public void setHasRedemption(boolean hasRedemption) {
		this.hasRedemption = hasRedemption;
	}

	public int getLoyaltyPoints() {
		return loyaltyPoints;
	}

	public void setLoyaltyPoints(int loyaltyPoints) {
		this.loyaltyPoints = loyaltyPoints;
	}

	public String getLoyaltyReason() {
		return loyaltyReason;
	}

	public void setLoyaltyReason(String loyaltyReason) {
		this.loyaltyReason = loyaltyReason;
	}

	public boolean isPointsRedeemedSuccessfully() {
		return pointsRedeemedSuccessfully;
	}

	public void setPointsRedeemedSuccessfully(boolean pointsRedeemedSuccessfully) {
		this.pointsRedeemedSuccessfully = pointsRedeemedSuccessfully;
	}

	public int getUnitId() {
		return unitId;
	}

	/**
	 * @return the terminalId
	 */
	public Integer getTerminalId() {
		return terminalId;
	}

	public int getRedeemedProductId() {
		return redeemedProductId;
	}

	public void setRedeemedProductId(int redeemedProductId) {
		this.redeemedProductId = redeemedProductId;
	}

}
