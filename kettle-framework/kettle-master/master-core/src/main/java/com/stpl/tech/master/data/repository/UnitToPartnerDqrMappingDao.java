package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.UnitToPartnerDqrMapping;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface UnitToPartnerDqrMappingDao extends JpaRepository<UnitToPartnerDqrMapping,Integer> {

    List<UnitToPartnerDqrMapping> findAllByStatus(String status);
    Optional<UnitToPartnerDqrMapping> findByUnitId(Integer unitId);
}
