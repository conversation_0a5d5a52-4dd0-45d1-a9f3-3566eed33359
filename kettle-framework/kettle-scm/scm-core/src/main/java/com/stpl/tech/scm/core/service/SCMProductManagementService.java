package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PlanOrderItem;
import com.stpl.tech.scm.domain.model.ProductBasicDetail;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPackagingMapping;
import com.stpl.tech.scm.domain.model.SkuAttributeValue;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.UnitProductPackagingMapping;
import com.stpl.tech.scm.domain.model.UnitProductsVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Rahul Singh on 07-05-2016.
 */
public interface SCMProductManagementService {

    public ProductDefinition viewProduct(int productId);

    public List<ProductDefinition> viewAllProducts(Integer unitId, boolean getArchived);

    public Map<Integer, ProductDefinition> getAllProductMaps();

    public Map<Integer, ProductDefinition> getAllVariantProductMaps();

    public ProductDefinition addNewProduct(ProductDefinition productDefinition , Integer userId) throws DataUpdationException, SumoException;

    public ProductDefinition updateProduct(ProductDefinition productDefinition,Integer userId) throws DataUpdationException, SumoException;

    public boolean activateProduct(int productId) throws DataUpdationException;

    public boolean deactivateProduct(int productId);

    public boolean archiveProduct(int productId);

    public List<PackagingDefinition> viewAllPackaging();

    public Map<Integer, PackagingDefinition> getAllPackagingMap();

    public PackagingDefinition viewPackaging(int packagingId);

    public boolean addNewPackaging(List<PackagingDefinition> packagingDefinition) throws SumoException;

    public boolean updatePackaging(PackagingDefinition packagingDefinition);

    public boolean deactivatePackaging(int packagingDefinitionId);

    public boolean activatePackaging(int packagingDefinitionId);

    public Map<String, Set<Map<String, Integer>>> getAllSubPackagingMapping();

    public Map<Integer, List<ProductPackagingMapping>> viewAllProductPackagingMapping();

    public Map<Integer, List<UnitProductPackagingMapping>> viewUnitProductPackagingMapping(int unitId);

    public ProductPackagingMapping viewProductPackagingMapping(int productPackagingMappingId);

    public List<ProductPackagingMapping> addNewProductPackagingMapping(List<ProductPackagingMapping> productPackagingMappings) throws SumoException;

    public boolean updateProductPackagingMapping(List<ProductPackagingMapping> productPackagingMappings);

    public boolean deactivateProductPackagingMapping(int productPackagingMappingId);

    public boolean activateProductPackagingMapping(int productPackagingMappingId);

    public List<ProductPackagingMapping> getProductPackagingMappingByProduct(int productId);

    public boolean setDefaultProductPackaging(int productPackagingMappingId) throws DataUpdationException;

    public SkuDefinition viewSku(int skuId);

    public List<SkuDefinition> viewAllSku();

    public SkuDefinition addNewSku(SkuDefinition skuDefinition , Integer userId) throws SumoException;

    public SkuDefinition updateSku(SkuDefinition skuDefinition , Integer userId) throws SumoException;

    public boolean activateSku(int skuId);

    public boolean deactivateSku(int skuId);

    public Map<Integer, List<SkuPackagingMapping>> viewAllSkuPackagingMapping();

    public SkuPackagingMapping viewSkuPackagingMapping(int skuPackagingMappingId);

    public List<SkuPackagingMapping> addNewSkuPackagingMapping(List<SkuPackagingMapping> skuPackagingMappings) throws SumoException;

    public boolean updateSkuPackagingMapping(List<SkuPackagingMapping> skuPackagingMappings);

    public boolean activateSkuPackagingMapping(int skuPackagingMappingId);

    public boolean deactivateSkuPackagingMapping(int skuPackagingMappingId);

    public boolean setDefaultSkuPackaging(int skuPackagingMappingId) throws DataUpdationException;

    public Map<Integer, List<SkuAttributeValue>> viewAllSkuAttributeValues();

    public SkuAttributeValue viewSkuAttributeValue(int skuAttributeValueId);

    public boolean addNewSkuAttributeValues(List<SkuAttributeValue> skuAttributeValues) throws SumoException;

    public boolean updateSkuAttributeValues(List<SkuAttributeValue> skuAttributeValues);

    public boolean activateSkuAttributeValue(int skuAttributeValueId);

    public boolean deactivateSkuAttributeValue(int skuAttributeValueId);

    public Map<Integer, List<SkuDefinition>> viewAllSkuByProduct();

    public Map<Integer, List<SkuDefinition>> viewAllSkuByProduct(int productId);

    public List<VendorDetail> getAllVendorDetails();

	public List<VendorDetail> getUnitVendorDetails(int unitId);

	/**
	 * @return
	 */
	public List<ProductBasicDetail> viewAllBasicProducts(Integer unitId, boolean getArchived);

	/**
	 * @return
     * @param isScm
     */
	public List<ProductBasicDetail> viewAllBasicProductsForRecipe(Boolean isScm);

    public List<ProductDefinition> viewAllProductsForCafeInventory();

    public Map<Integer, Set<VendorDetail>> getUnitProductVendors(UnitProductsVO request) throws SumoException;

    public String uploadProductImage(MimeType mimeType, Integer userId, MultipartFile file) throws SumoException;

    public String uploadSkuImage(MimeType mimeType, Integer skuId, MultipartFile file) throws SumoException;

    public PlanOrderItem getProductForUnit(int productId, int unitId) throws SumoException;

	List<SkuDefinition> viewAllSku(int productId);

    public Map<Integer, Integer> getUnitSkuPackagingMappings(int unitId);

    public Map<Integer,List<SkuDefinition>> viewAllActiveSkuByUnitId(Integer unitId , List<Integer> productIds) throws SumoException;

    public Boolean updateSubCategoryShelfLife(Integer id , Integer defaultShelfLife , String range);
}
