package com.stpl.tech.kettle.rekognition.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

@Entity
@Table(name = "APP_VERSION_MAPPING_DATA", uniqueConstraints = @UniqueConstraint(name = "unique_version_mapping", columnNames = {
		"UNIT_ID", "TERMINAL_ID", "APP_TYPE" }))
public class AppVersionMappingData {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "APP_VERSION_MAPPING_ID")
	private Integer appVersionMappingId;

	@Column(name = "UNIT_ID")
	private Integer unitId;

	@Column(name = "TERMINAL_ID")
	private Integer terminalId;

	@Column(name = "APP_VERSION")
	private String appVersion;

	@Column(name = "APP_TYPE")
	private String appType;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = false)
	private Date lastUpdateTime;

	@Column(name = "DEVICE_UNIQUE_ID")
	private String deviceUniqueId;

	@Column(name = "MANUFACTURER")
	private String manufacturer;

	@Column(name = "DEVICE_MODEL")
	private String deviceModel;

	@Column(name = "DEVICE_ID")
	private String deviceId;

	@Column(name = "SYSTEM_NAME")
	private String systemName;

	@Column(name = "DEVICE_VERSION")
	private String deviceVersion;

	@Column(name = "BUNDLE_ID")
	private String bundleId;

	@Column(name = "BUILD_NUMBER")
	private String buildNumber;

	@Column(name = "DEVICE_NAME")
	private String deviceName;

	@Column(name = "USER_AGENT")
	private String userAgent;

	@Column(name = "DEVICE_LOCALE")
	private String deviceLocale;

	@Column(name = "DEVICE_COUNTRY")
	private String deviceCountry;

	@Column(name = "MAC_ADDRESS")
	private String macAddress;

	public int getAppVersionMappingId() {
		return appVersionMappingId;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public Integer getTerminalId() {
		return terminalId;
	}

	public void setTerminalId(Integer terminalId) {
		this.terminalId = terminalId;
	}

	public String getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}

	public String getAppType() {
		return appType;
	}

	public void setAppType(String appType) {
		this.appType = appType;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public void setAppVersionMappingId(Integer appVersionMappingId) {
		this.appVersionMappingId = appVersionMappingId;
	}

	public String getDeviceUniqueId() {
		return deviceUniqueId;
	}

	public void setDeviceUniqueId(String deviceUniqueId) {
		this.deviceUniqueId = deviceUniqueId;
	}

	public String getManufacturer() {
		return manufacturer;
	}

	public void setManufacturer(String manufacturer) {
		this.manufacturer = manufacturer;
	}

	public String getDeviceModel() {
		return deviceModel;
	}

	public void setDeviceModel(String deviceModel) {
		this.deviceModel = deviceModel;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getDeviceVersion() {
		return deviceVersion;
	}

	public void setDeviceVersion(String deviceVersion) {
		this.deviceVersion = deviceVersion;
	}

	public String getBundleId() {
		return bundleId;
	}

	public void setBundleId(String bundleId) {
		this.bundleId = bundleId;
	}

	public String getBuildNumber() {
		return buildNumber;
	}

	public void setBuildNumber(String buildNumber) {
		this.buildNumber = buildNumber;
	}

	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public String getDeviceLocale() {
		return deviceLocale;
	}

	public void setDeviceLocale(String deviceLocale) {
		this.deviceLocale = deviceLocale;
	}

	public String getDeviceCountry() {
		return deviceCountry;
	}

	public void setDeviceCountry(String deviceCountry) {
		this.deviceCountry = deviceCountry;
	}

	public String getMacAddress() {
		return macAddress;
	}

	public void setMacAddress(String macAddress) {
		this.macAddress = macAddress;
	}
}
