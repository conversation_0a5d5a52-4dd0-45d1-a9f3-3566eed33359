/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("SCMRecipeDetailsCtrl", function ($rootScope,$scope, $location, $http,
		$cookieStore, AuthService, AppUtil,$state,Popeye,ITERATION_STATUS) {

	var uomMap = {"KG" : "GM" ,"L" : "ML","PC":"PC","SACHET" : "SACHET","PKT" : "PKT"};

	$scope.subUomNameList = [{"name" : "GM" },{"name" : "PC" }];

	$scope.selectOptions = {
            'width': '100%'
        };
	
	$scope.stateList=[
		{name : "iterationDetail" ,currentIndex :0 ,title : "Iteration Detail", buttonText : "Add Iteration" },
		{name : "ingredientList" ,currentIndex :1 ,title : "Ingredients List", buttonText : "Add Ingredient" },
		{name : "productDetails" ,currentIndex :2 ,title : "Product Details", buttonText : "Calculate" },
		{name : "detailsPreview" ,currentIndex :3 ,title : "Details Preview",buttonText : "Submit Recipe" }
		];
	function init(){
		if(!$scope.iterationPageDetails.isShowDetails){
			$state.go("dashboard.SCMRecipeCalculator",{},{reload:true});
		}else{
			$scope.ingredientList=[];
			$scope.isOutputUOMEdiatble = false;
			getAllSCMProducts();
            getAllIngredientInstructions();
			if($scope.iterationDetails.iterationName){
				$scope.isNameAvailable=true;
				isNameEditable=false;
			}else{
				$scope.isNameAvailable=false;
			}
			if(!$scope.iterationDetails.outputUom){
				$scope.isOutputUOMEdiatble = true;
			}
			$scope.activeState=$scope.stateList[0];
			$scope.isNameVerified=false;
			$scope.finalIterationName=undefined;
		}
	}

	init();

	function getAllSCMProducts () {
		$http({
			method: 'GET',
			url: AppUtil.restUrls.scmProductManagement.productBasicDetailsForRecipe,
			params: {
				"isScm" : true
			}
		}).then(function success(response) {
			$scope.scmProductsInfoForRecipe = response.data;
		//	console.log("scmProductsInfoForRecipe:" + $scope.scmProductsInfoForRecipe);
		}, function error(response) {
			console.log("error:" + response);
		});
	};

    function getAllIngredientInstructions () {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.scmRecipeManagement.getAllIngredientInstructions
        }).then(function success(response) {
            $scope.iterationIngredientInstructionList = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    };



    $scope.openInstructionsModal = function(product){
        for(var i=0;i<$scope.iterationIngredientInstructionList.length;i++){
            $scope.iterationIngredientInstructionList[i].checked = false;
        }
        var data = {
            product : product,
            instructions : $scope.iterationIngredientInstructionList
        }
        var mappingModal = Popeye.openModal({
            templateUrl : "iterationIngredientInstructionModal.html",
            controller : "iterationIngredientInstructionModalCtrl",
            resolve : {
                instructionData : function() {
                    return data;
                }
            },
            click : false,
            keyboard : false
        });

        mappingModal.closed.then(function(data) {
        	product.instructions = data.instructions;
            //$scope.ingredientList[index] = product;
			$scope.iterationIngredientInstructionList.concat(data.newInstructs);
			console.log(data);
		});
    };

    $scope.removeInstruction = function (instructions, index) {
		instructions.splice(index, 1);
    };

	$scope.goToParent=function(reload){
		if(!$scope.iterationPageDetails.isViewMode){
			var result;
			if(!reload){
				result=confirm("Your unsaved data will be lost so Please save your unsaved data before going to previous state!")
			}else{
				result=reload;
			}
			if ( !result) {
				return false;
		    }
		}
		$state.go("dashboard.SCMRecipeCalculator",{activeIteration :$scope.activeIteration},{reload:false});
		$scope.iterationPageDetails.isShowDetails=false;
		$scope.iterationPageDetails.isViewMode =false;
	};

	$scope.selectSCMMainProduct = function (selectedProductId) {
		if(!selectedProductId){
			return false;
		}
		var productInfo = getVariantDetail(selectedProductId, $scope.scmProductsInfoForRecipe);
		$scope.selectedProduct = productInfo;
		if (!validateProduct($scope.selectedProduct)) {
			alert("Invalid Product");
			return false;
		}
	};

	$scope.changeOutputUom = function(uom){
		$scope.iterationDetails.outputUom = uom.name;
		if(uom.name == 'PC'){
			$scope.conversionUom = 1;
			$scope.iterationDetails.productUom = uom.name;
			$scope.iterationDetails.outputConversion  =  $scope.conversionUom;
			$scope.iterationDetails.productConversion  =  $scope.conversionUom;
		}else if(uom.name == 'GM'){
			$scope.iterationDetails.productUom  =  'PC';
			$scope.iterationDetails.productConversion  =  1;
		}
	};

	$scope.updateOutputUom=function(conversion){
		$scope.iterationDetails.outputConversion = conversion;
	};

	$scope.validateIterationName = function(name) {
		if($scope.iterationDetails.linkedConstructName){
			$scope.finalIterationName = $scope.iterationDetails.linkedConstructName+":"+name;
		}else if($scope.iterationDetails.linkedProductName){
			$scope.finalIterationName =$scope.iterationDetails.linkedProductName +":"+name;
		}

		$scope.isNameVerified=true;
		$http({
			method: 'GET',
			url: AppUtil.restUrls.scmRecipeManagement.validateIterationName,
			params :{iterationName : $scope.finalIterationName}
		}).then(function success(response) {
			if(response.data==true){
				$scope.isNameAvailable=response.data;
				if($scope.finalIterationName){
					$scope.iterationDetails.iterationName=$scope.finalIterationName;
				}
			}
		}, function error(response) {
			console.log("error:" + response);
		});
	};

	$scope.addNewProduct=function(){
		// for(var i=0;i<$scope.ingredientList.length;i++){
		// 	if($scope.ingredientList[i].productId == $scope.selectedProduct.productId){
		// 		alert("This product is already added");
		// 		return false;
		// 	}
		// }

		var ingredient=createIngredientDetail($scope.selectedProduct);
		$scope.ingredientList.push(ingredient);
	};

	$scope.updateProdQuantity=function(productId,quantity,index){
		console.log($scope.ingredientList);
		$scope.ingredientList.forEach(function (item) {
			if(item.productId == productId && $scope.ingredientList[index] == index){
				item.quantity = quantity;
				item.yieldPercentage = 100;
			}
		});
	};

    $scope.setOrdering = function () {
        $('#SetOrderingModal').modal('show');
    };

    $scope.showRecipeOrNot=function(productId,showRecipe,index){
        console.log("show or not",showRecipe)
        $scope.ingredientList.forEach(function (item) {
            if(item.productId == productId && $scope.ingredientList[index] == index){
                item.showRecipe= showRecipe;
            }
        });
    };

	$scope.updateIterationName=function(){
		$scope.isNameVerified=false;
		$scope.isNameAvailable=false;
	};

	$scope.next=function(){
		var index = $scope.activeState.currentIndex+1;
		$scope.activeState=$scope.stateList[index];

	};

	$scope.previous=function(){
		var index = $scope.activeState.currentIndex-1;
		$scope.activeState=$scope.stateList[index];
	};

	function updateModifiedBy(){

        var data=AppUtil.getUserValues();
		if (data != undefined && data != null) {
			$scope.iterationDetails.lastUpdatedById = data.user.id;
			$scope.iterationDetails.lastUpdatedByName = data.user.name;
		}
	}

	function createIterationForProduct(){
		$scope.iterationDetails.status = ITERATION_STATUS.INITIATED;
		$http({
			method: 'POST',
			url: AppUtil.restUrls.scmRecipeManagement.addIteration,
			data : $scope.iterationDetails
		}).then(function success(response) {
			$scope.iterationDetails = response.data;
			console.log(" iterationDetail  data saved: ");
		}, function error(response) {
			console.log("error:" + response);
		});
	};
	$scope.viewNext=function(currentIndex){
		if($scope.iterationDetails.components && currentIndex==0){
			$scope.ingredientList=$scope.iterationDetails.components;
		}else if(currentIndex==3){
			openRecipeIterationCostModal();
		}
		$scope.next();
	};

	$scope.sumbitIngredientDetails = function(stateName){
		switch (stateName) {
		case $scope.stateList[0].name:
			if(!$scope.isNameAvailable){
				alert("Please verify  name to create unique !");
				return false;
			}
			if($scope.iterationDetails.createdById){
				updateModifiedBy();
				updateRecipeIterationData($scope.iterationDetails).then(function(data){
					console.log(" iterationDetail  data updated: ");
				});
			}else{

                var data=AppUtil.getUserValues();
				if (data != undefined && data != null) {
					$scope.iterationDetails.createdById = data.user.id;
					$scope.iterationDetails.createdByName = data.user.name;
				}
				createIterationForProduct();
			}
			$scope.next();
			if($scope.iterationDetails.components){
				$scope.ingredientList=$scope.iterationDetails.components;
			}
		break;
		case $scope.stateList[1].name:
			var isQuantityRequired=false;
			var productIds = [];
			$scope.ingredientList.forEach(function (item) {
				if (productIds.indexOf(item.productId) === -1 && item.recipeRequired) {
					productIds.push(item.productId);
				}
				if(item.quantity==null && !isQuantityRequired){
					isQuantityRequired=true;
				}
			});
			if(isQuantityRequired){
				alert("Quantity must be filled for a product");
				return false;
			}
			if (productIds.length > 0) {
				$rootScope.showFullScreenLoader = true;
				$http({
					method: 'POST',
					url: AppUtil.restUrls.scmRecipeManagement.checkRecipeForSemiFinishedProducts,
					data: productIds
				}).then(function success(response) {
					var responseData = response.data[0];
					$rootScope.showFullScreenLoader = false;
					if (responseData === "ERROR") {
						bootbox.alert("Something Went Wrong While Verifying the Products For Semi Finished Products...!");
					} else if (responseData === "ALL_RECIPES_FOUND") {
						$scope.next();
						updateModifiedBy();
						updateRecipeIterationData($scope.iterationDetails).then(function (data) {
							console.log(" iterationDetail  data updated: ");
						});
					} else {
						var missingRecipesProductIds = responseData.split("#")[1].split(",");
						var msg = "Recipes Not Found For Products : "
						for (var i = 0; i < missingRecipesProductIds.length; i++) {
							var productId = missingRecipesProductIds[i];
							if (productId != "") {
								for (var j = 0; j < $scope.scmProductsInfoForRecipe.length; j++) {
									if ($scope.scmProductsInfoForRecipe[j].productId === Number(missingRecipesProductIds[i])) {
										msg += $scope.scmProductsInfoForRecipe[j].productName + " (" + $scope.scmProductsInfoForRecipe[j].productId + ") ,";
										break;
									}
								}
							}
						}
						msg = msg.substring(0, msg.length - 1);
						msg+="<br>Please Make SCM Recipe For the Above Proucts First..!";
						bootbox.alert(msg);
					}
				}, function error(response) {
					$rootScope.showFullScreenLoader = false;
					console.log("error:" + response);
				});
			} else {
				$scope.next();
				updateModifiedBy();
				updateRecipeIterationData($scope.iterationDetails).then(function (data) {
					console.log(" iterationDetail  data updated: ");
				});
			}
		break;
		case $scope.stateList[2].name:
			if(!$scope.iterationDetails.outputQuantity){
				alert("Recipe Output Quantity must be filled for a product");
				return false;
			}
			if(!$scope.iterationDetails.notes){
				alert("Recipe Notes must be filled for a product up to this step!");
				return false;
			}
			$scope.ingredientList.forEach(function (item) {
				
				if($scope.iterationDetails.outputUom == 'GM' && $scope.iterationDetails.productUom=='PC'){
					item.quantityPerSubUom = toFixed((item.quantity*$scope.iterationDetails.outputConversion)/ $scope.iterationDetails.outputQuantity);
				}else{
					item.quantityPerSubUom = toFixed(item.quantity / $scope.iterationDetails.outputQuantity);
				}
				if(item.yieldPercentage!=100){
					item.yieldQuantity = toFixed((item.quantityPerSubUom*item.yieldPercentage)/100);
				}else{
					item.yieldQuantity = item.quantityPerSubUom;
				}
			});
			$scope.next();
			updateModifiedBy();
			updateRecipeIterationData($scope.iterationDetails).then(function(data){
				console.log(" iterationDetail  data updated: ");
			});
		break;
		case $scope.stateList[3].name:
			$rootScope.showFullScreenLoader = true;
			$scope.iterationDetails.components = $scope.ingredientList;
			$scope.iterationDetails.status =  ITERATION_STATUS.CREATED;
			updateModifiedBy();
			updateRecipeIterationData($scope.iterationDetails).then(function(data){
				alert("Recipe Iteration Created successfully");
				$rootScope.showFullScreenLoader = false;
				$scope.goToParent(true);
				openRecipeIterationCostModal();
			});
		break;
		}
	};

	function openRecipeIterationCostModal() {
		var mappingModal = Popeye.openModal({
			templateUrl : "views/recipeIterationCostModal.html",
			controller : "recipeIterationCostCtrl",
			resolve : {
				iterationDetails : function() {
					return $scope.iterationDetails;
				}
			},
			click : false,
			keyboard : false
		});

		mappingModal.closed
		.then(function(isSuccessful) {
			$state.go("dashboard.SCMRecipeCalculator", {}, {reload: true});
		});
	}

	function updateRecipeIterationData (iterationDetail) {
		$rootScope.showFullScreenLoader = true;
		var promise=$http({
			method: 'POST',
			url: AppUtil.restUrls.scmRecipeManagement.updateIteration,
			data : iterationDetail
		}).then(function success(response) {
			var data = response.data;
			$rootScope.showFullScreenLoader = false;
			return data;
		}, function error(response) {
			$rootScope.showFullScreenLoader = false;
			console.log("error:" + response);
		});
		return promise;
	};

	$scope.deleteProduct=function(productId){
		for(var i=0;i<$scope.ingredientList.length;i++){
			if($scope.ingredientList[i].productId == productId){
				$scope.ingredientList.splice(i, 1);
			}
		}
	};

	function validateProduct(p) {
		if (p == null) {
			return true;
		}
		if ((p.taxCode == undefined && p.code == undefined) || (p.taxCode != undefined && p.taxCode == null) || (p.code != undefined && p.code == null)) {
			bootbox.alert("Please set the Tax Category Code for " + p.name +
			' before adding its recipe');
			return false;
		}
		return true;
	}

	function toFixed(num,digits){
		if(digits){
			return Number(num).toFixed(10); 
		}
		return Number(num).toFixed(10); 
	}

	function getVariantDetail(id, list) {
		for (var index in list) {
			if (list[index].productId == id) {
				return list[index];
			}
		}
		return null;
	}

	function createIngredientDetail(product) {
		var data = {};
		data.productId = product.productId;
		data.productName = product.productName;
		data.uom = uomMap[product.unitOfMeasure];
		data.type=product.category.id;
		data.subType=product.subCategory.id;
		data.quantity = null;
		data.quantityPerSubUom = null;
		data.yieldQuantity = null;
		data.yieldPercentage = 100;
		data.yieldReason = null;
		data.autoProduction = product.autoProduction;
		data.recipeRequired = product.recipeRequired;
		return data;
	}
});



adminapp.controller('iterationIngredientInstructionModalCtrl',function ($scope,Popeye,instructionData) {
    $scope.product = instructionData.product;
    $scope.instructions = instructionData.instructions;
    $scope.newInstructs = [];

    $scope.init = function () {
		$scope.instructions.map(function (instr) {
			var found = false;
            instr.checked = false;
			$scope.product.instructions.map(function (instrx) {
				if(!found && instrx.instruction == instr.instruction){
					instr.checked = true;
				}
            })
        });
    };

    $scope.addNewInstruction = function () {
    	if($scope.instructionText != null){
            var found = false;
            $scope.instructions.map(function (instr) {
                if(instr.instruction == $scope.instructionText){
                    found = true;
                }
            });
            if(found == false){
                var obj = {
                    instruction : $scope.instructionText,
                    checked: true
                };
                $scope.instructions.push(obj);
                $scope.newInstructs.push({instruction:obj.instruction});
                $scope.instructionText = null;
            } else {
                alert("Instruction is already in the list.");
            }
		} else {
            alert("Please fill instruction.");
		}
    };

    $scope.closeModal =function (){
    	var selectedInstructions = [];
        $scope.instructions.map(function (instr) {
        	if(instr.checked == true){
        		selectedInstructions.push({instruction:instr.instruction});
			}
        });
        Popeye.closeCurrentModal({newInstructs:$scope.newInstructs, instructions:selectedInstructions});
    };

});

