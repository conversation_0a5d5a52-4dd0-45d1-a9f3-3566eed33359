package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import java.util.List;

import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;

@Repository
public interface PartnerUnitMenuDao extends MongoRepository<PartnerUnitMenuDetail, String> {

	public List<PartnerUnitMenuDetail> findAllByActiveAndPartnerIdAndUnitId(Boolean active, Integer partnerId,
			Integer unitId);

	public List<PartnerUnitMenuDetail> findAllByActiveAndPartnerIdAndUnitIdAndIsNew(Boolean active, Integer partnerId,
			Integer unitId, Boolean isNew);

	public Page<PartnerUnitMenuDetail> findByPartnerIdAndRegionOrderByAddTimeDesc(Integer partnerId, String region,
			Pageable request);

	@Query(value = "{ 'partnerId' : ?0, 'region' : ?1, isNew : { $ne: true}}")
	public Page<PartnerUnitMenuDetail> searchLastRegionData(Integer partnerId, String region, Pageable pageRequest);

	@Query(value = "{ 'unitId' : ?0,'partnerId' : ?1,'brandId' : ?2 ,'menuType' : ?2, 'version' : { $exists: true}}")
	public List<PartnerUnitMenuDetail> findAllByUnitIdAndPartnerIdAndBrandIdAndMenuType(Integer unitId,
			Integer partnerId, Integer brandId, String menuType);
	
	public PartnerUnitMenuDetail findAllByUnitIdAndPartnerIdAndBrandIdAndVersionAndMenuType(Integer unitId,
			Integer kettlePartnerId, Integer brandId, String version, MenuType menuType);
}
