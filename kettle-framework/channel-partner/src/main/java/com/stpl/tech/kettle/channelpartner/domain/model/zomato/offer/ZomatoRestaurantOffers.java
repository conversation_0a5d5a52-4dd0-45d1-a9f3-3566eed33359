package com.stpl.tech.kettle.channelpartner.domain.model.zomato.offer;

import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "offerId", "offerType", "startDate", "endDate", "discountType", "discountValue", "minOrderAmount",
		"firstOrderOnly", "isActive", "vendorEntityIds", "timings" })
public class ZomatoRestaurantOffers {

	@JsonProperty("offerId")
	private String offerId;
	@JsonProperty("offerType")
	private String offerType;
	@JsonProperty("startDate")
	private String startDate;
	@JsonProperty("endDate")
	private String endDate;
	@JsonProperty("discountType")
	private String discountType;
	@JsonProperty("discountValue")
	private Float discountValue;
	@JsonProperty("minOrderAmount")
	private Float minOrderAmount;
	@JsonProperty("firstOrderOnly")
	private Integer firstOrderOnly;
	@JsonProperty("isActive")
	private Boolean isActive;
	@JsonProperty("vendorEntityIds")
	private List<String> vendorEntityIds;
	@JsonProperty("timings")
	private List<ZomatoRestOfferTimmings> timings;
	@JsonProperty("buyCount")
	private Integer buyCount;
	@JsonProperty("freeCount")
	private Integer freeCount;
	@JsonProperty("freebieItemName")
	private Float freebieItemName;
	@JsonProperty("freebieLimitCount")
	private Integer freebieLimitCount;

	public String getOfferId() {
		return offerId;
	}

	public void setOfferId(String offerId) {
		this.offerId = offerId;
	}

	public String getOfferType() {
		return offerType;
	}

	public void setOfferType(String offerType) {
		this.offerType = offerType;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getDiscountType() {
		return discountType;
	}

	public void setDiscountType(String discountType) {
		this.discountType = discountType;
	}

	public Float getDiscountValue() {
		return discountValue;
	}

	public void setDiscountValue(Float discountValue) {
		this.discountValue = discountValue;
	}

	public Float getMinOrderAmount() {
		return minOrderAmount;
	}

	public void setMinOrderAmount(Float minOrderAmount) {
		this.minOrderAmount = minOrderAmount;
	}

	public Integer getFirstOrderOnly() {
		return firstOrderOnly;
	}

	public void setFirstOrderOnly(Integer firstOrderOnly) {
		this.firstOrderOnly = firstOrderOnly;
	}

	public List<String> getVendorEntityIds() {
		return vendorEntityIds;
	}

	public void setVendorEntityIds(List<String> vendorEntityIds) {
		this.vendorEntityIds = vendorEntityIds;
	}

	public List<ZomatoRestOfferTimmings> getTimings() {
		return timings;
	}

	public void setTimings(List<ZomatoRestOfferTimmings> timings) {
		this.timings = timings;
	}

	public Boolean getActive() {
		return isActive;
	}

	public void setActive(Boolean active) {
		isActive = active;
	}

	public Integer getBuyCount() {
		return buyCount;
	}

	public void setBuyCount(Integer buyCount) {
		this.buyCount = buyCount;
	}

	public Integer getFreeCount() {
		return freeCount;
	}

	public void setFreeCount(Integer freeCount) {
		this.freeCount = freeCount;
	}

	public Float getFreebieItemName() {
		return freebieItemName;
	}

	public void setFreebieItemName(Float freebieItemName) {
		this.freebieItemName = freebieItemName;
	}

	public Integer getFreebieLimitCount() {
		return freebieLimitCount;
	}

	public void setFreebieLimitCount(Integer freebieLimitCount) {
		this.freebieLimitCount = freebieLimitCount;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;

		if (o == null || getClass() != o.getClass()) return false;

		ZomatoRestaurantOffers that = (ZomatoRestaurantOffers) o;

		return new EqualsBuilder()
			.append(offerId, that.offerId)
			.append(offerType, that.offerType)
			.append(startDate, that.startDate)
			.append(endDate, that.endDate)
			.append(discountType, that.discountType)
			.append(discountValue, that.discountValue)
			.append(minOrderAmount, that.minOrderAmount)
			.append(firstOrderOnly, that.firstOrderOnly)
			.append(isActive, that.isActive)
			.append(vendorEntityIds, that.vendorEntityIds)
			.append(timings, that.timings)
			.append(buyCount, that.buyCount)
			.append(freeCount, that.freeCount)
			.append(freebieItemName, that.freebieItemName)
			.append(freebieLimitCount, that.freebieLimitCount)
			.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37)
			.append(offerId)
			.append(offerType)
			.append(startDate)
			.append(endDate)
			.append(discountType)
			.append(discountValue)
			.append(minOrderAmount)
			.append(firstOrderOnly)
			.append(isActive)
			.append(vendorEntityIds)
			.append(timings)
			.append(buyCount)
			.append(freeCount)
			.append(freebieItemName)
			.append(freebieLimitCount)
			.toHashCode();
	}

	@Override
	public String toString() {
		return "ZomatoRestaurantOffers{" +
			"offerId='" + offerId + '\'' +
			", offerType='" + offerType + '\'' +
			", startDate='" + startDate + '\'' +
			", endDate='" + endDate + '\'' +
			", discountType='" + discountType + '\'' +
			", discountValue=" + discountValue +
			", minOrderAmount=" + minOrderAmount +
			", firstOrderOnly=" + firstOrderOnly +
			", isActive=" + isActive +
			", vendorEntityIds=" + vendorEntityIds +
			", timings=" + timings +
			", buyCount=" + buyCount +
			", freeCount=" + freeCount +
			", freebieItemName=" + freebieItemName +
			", freebieLimitCount=" + freebieLimitCount +
			'}';
	}
}
