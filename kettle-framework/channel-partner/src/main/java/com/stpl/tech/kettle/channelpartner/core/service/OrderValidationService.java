package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.kettle.UnitProductTaxCodeMap;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.master.readonly.domain.model.StateTaxVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface OrderValidationService {

    boolean runCommonValidations(Order order, PartnerOrderDetail partnerOrderDetail, Map<Integer, StateTaxVO> partnerProductTaxMap, UnitPartnerBrandMappingData data,
                                 Unit unit,SwiggyService swiggyService);

    void updateCheckOrderStatus(PartnerOrderDetail partnerOrderDetail, boolean isManual);

    public UnitProductTaxCodeMap getUnitProductTaxCodeMap(UnitPartnerBrandMappingData data);

    void addOrderError(PartnerOrderDetail partnerOrderDetail, PartnerOrderErrorCode errorCode, String errorDescription);

    boolean unitCanDeliver(Integer unitId);

    void checkProductPriceAndTaxes(Order order, UnitProductTaxCodeMap unitProductTaxCodeMap,
                                   PartnerOrderDetail partnerOrderDetail, Map<Integer, StateTaxVO> partnerProductTaxMap, Unit unit);

    boolean checkInventory(Order order, PartnerOrderDetail partnerOrderDetail,SwiggyService swiggyService);

    boolean isValidData(BigDecimal value1, BigDecimal value2);

    void logMetadataDifference(Order order, String type, String param, Integer id, Float expected, Float actual);

    void tagOrder(PartnerOrderDetail partnerOrderDetail, Order order);

    void checkPaidAmount(Order order, PartnerOrderDetail partnerOrderDetail, TransactionDetail td1, BigDecimal paidAmount);

    void updatePartnerNotificationStatus(PartnerOrderDetail partnerOrderDetail, boolean success, Boolean isManual) throws ChannelPartnerException;

    void refreshUnitInventory(Integer unitId,
                              UnitProductsStockEvent stockin, UnitProductsStockEvent stockout);

    void refreshLiveUnitInventory(Integer unitId,
                                  UnitProductsStockEvent stockIn, UnitProductsStockEvent stockOut);

    Map getUnitProductInventoryByProducts(Integer unitId, List<Integer> productIds);

    Map getUnitProductLiveInventoryByProducts(Integer unitId, List<Integer> productIds);

    void cancelOrder(PartnerOrderDetail partnerOrderDetail, boolean isManual);

    void sendMarkOutOfStockRequest(PartnerOrderDetail partnerOrderDetail, Order order, List<OrderItem> outOfStockItems,SwiggyService swiggyService);
}
