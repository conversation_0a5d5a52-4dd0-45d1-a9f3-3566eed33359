package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiRequest;
import com.stpl.tech.kettle.domain.model.SelectedOrderItem;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface FavChaiCustomizationDao extends AbstractDao {
    void saveFavChaiCustomizationDetail(SelectedOrderItem orderItemDetails, CustomerFavChaiMapping latestCustomerFavChaiMapping, SaveCustomerFavChaiRequest customerFavChaiRequest);
}
