package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CorrectedSalesInvoiceItemDetails {


    private Integer itemId;
    private Integer skuId;
    private String skuName;
    private BigDecimal sellingPrice;
    private BigDecimal correctedSellingPrice;
    private BigDecimal sellingAmount;
    private BigDecimal correctedSellingAmount;
    private BigDecimal totalTax;
    private BigDecimal correctedTotalTax;

}
