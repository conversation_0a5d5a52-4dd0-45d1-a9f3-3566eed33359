package com.stpl.tech.scm.data.model;

import javax.persistence.Entity;
import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.JoinColumn;

import java.math.BigDecimal;

@Entity
@Table(name = "SALES_PERFORMA_CORRECTED_INVOICE_ITEM")
public class SalesPerformaCorrectedInvoiceItemDetail {

    private Integer itemId;
    private Integer skuId;
    private String skuName;
    private BigDecimal sellingPrice;
    private BigDecimal correctedSellingPrice;
    private BigDecimal sellingAmount;
    private BigDecimal correctedSellingAmount;
    private BigDecimal totalTax;
    private BigDecimal correctedTotalTax;
    private SalesPerformaCorrectedInvoiceDetail correctedInvoice;


    @Id
    @Column(name = "INVOICE_ITEM_ID", unique = true, nullable = false)
    public Integer getItemId() {
        return itemId;
    }

    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }

    @Column(name="SKU_ID",nullable = false)
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    @Column(name="SKU_NAME",nullable = false)
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    @Column(name="SELLING_PRICE",nullable = false)
    public BigDecimal getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(BigDecimal sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    @Column(name="CORRECTED_SELLING_PRICE",nullable = false)
    public BigDecimal getCorrectedSellingPrice() {
        return correctedSellingPrice;
    }

    public void setCorrectedSellingPrice(BigDecimal correctedSellingPrice) {
        this.correctedSellingPrice = correctedSellingPrice;
    }

    @Column(name="SELLING_AMOUNT",nullable = false)
    public BigDecimal getSellingAmount() {
        return sellingAmount;
    }

    public void setSellingAmount(BigDecimal sellingAmount) {
        this.sellingAmount = sellingAmount;
    }

    @Column(name="CORRECTED_SELLING_AMOUNT",nullable = false)
    public BigDecimal getCorrectedSellingAmount() {
        return correctedSellingAmount;
    }

    public void setCorrectedSellingAmount(BigDecimal correctedSellingAmount) {
        this.correctedSellingAmount = correctedSellingAmount;
    }

    @Column(name="TOTAL_TAX",nullable = false)
    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    @Column(name="CORRECTED_TOTAL_TAX",nullable = false)
    public BigDecimal getCorrectedTotalTax() {
        return correctedTotalTax;
    }

    public void setCorrectedTotalTax(BigDecimal correctedTotalTax) {
        this.correctedTotalTax = correctedTotalTax;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="INVOICE_ID", nullable = false)
    public SalesPerformaCorrectedInvoiceDetail getCorrectedInvoice() {
        return correctedInvoice;
    }

    public void setCorrectedInvoice(SalesPerformaCorrectedInvoiceDetail correctedInvoice) {
        this.correctedInvoice = correctedInvoice;
    }
}
