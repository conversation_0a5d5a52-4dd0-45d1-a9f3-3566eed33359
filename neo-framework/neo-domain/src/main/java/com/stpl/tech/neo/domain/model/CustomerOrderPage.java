package com.stpl.tech.neo.domain.model;

import java.util.ArrayList;
import java.util.List;

import com.stpl.tech.neo.domain.model.mongo.OrderVO;

public class CustomerOrderPage {

	private List<OrderVO> orders;
	private int page;
	private int limit;
	private String customerId;

	public List<OrderVO> getOrders() {
		if (orders == null) {
			orders = new ArrayList<>();
		}
		return orders;
	}

	public int getPage() {
		return page;
	}

	public void setPage(int page) {
		this.page = page;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

}
