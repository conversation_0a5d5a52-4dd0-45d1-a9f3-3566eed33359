export default function reducer(state = {
    customer: null,
    currentStage: null,
    showNBOOffer:false,
    showDNBOOffer:false,
    showChaayosCashOffer:false,
    showMembershipOffer:false,
    showNoOffer:false,
    innerHeight:0,
    innerWidth:0,
    campaignDetail:null,
    utmData:null,
    specialOffer:null,
    isCampaignLoading:false,
    termAndCondition:null,
    enableShowOfferButton:false,
    isButtonLoading:false
}, action) {

    switch (action.type) {
        case "CLEAR_REDUCER":
        {
            return {
                ...state,
                customer: null,
                currentStage: null,
                showNBOOffer:false,
                showDNBOOffer:false,
                showChaayosCashOffer:false,
                showMembershipOffer:false,
                showNoOffer:false,
                innerHeight:0,
                innerWidth:0,
                campaignDetail:null,
                utmData:null,
                specialOffer:null,
                isCampaignLoading:false
            };
        }
        case "SET_SLOT_MACHINE_CUSTOMER":
        {
            return {...state, customer: action.payload};
        }
        case "SET_SLOT_GAME_STAGE":
        {
            return {...state, currentStage: action.payload};
        }
        case "SET_SHOW_NBO_OFFER":
        {
            return {...state, showNBOOffer: action.payload};
        }
        case "SET_SHOW_DNBO_OFFER":
        {
            return {...state, showDNBOOffer: action.payload};
        }
        case "SET_SHOW_CHAAYOS_CASH_OFFER":
        {
            return {...state, showChaayosCashOffer: action.payload};
        }
        case "SET_SHOW_MEMBERSHIP_OFFER":
        {
            return {...state, showMembershipOffer: action.payload};
        }
        case "SET_SHOW_NO_OFFER":
        {
            return {...state, showNoOffer: action.payload};
        }
        case "SET_SCREEN_HEIGHT":
        {
            return {...state, innerHeight: action.payload};
        }
        case "SET_SCREEN_WIDTH":
        {
            return {...state, innerWidth: action.payload};
        }
        case "SET_CAMPAIGN_DETAILS":
        {
            return {...state, campaignDetail: action.payload};
        }
        case "SET_UTM_DATA":
        {
            return {...state, utmData: action.payload};
        }
        case "SET_SPECIAL_OFFER":
        {
            return {...state, specialOffer: action.payload};
        }
        case "SET_TERM_AND_CONDITION":
        {
            return {...state, termAndCondition: action.payload};
        }
        case "ENABLE_SHOW_OFFER_BUTTON":
        {
            return {...state, enableShowOfferButton: action.payload};
        }
        case "SET_BUTTON_LOADER":
        {
            return {...state, isButtonLoading: action.payload};
        }
    }

    return state;

}
