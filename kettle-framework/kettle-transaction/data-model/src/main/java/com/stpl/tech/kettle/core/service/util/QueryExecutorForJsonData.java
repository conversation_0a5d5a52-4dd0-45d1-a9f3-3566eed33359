/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.util;

import java.text.ParseException;

import javax.activation.UnsupportedDataTypeException;
import javax.sql.DataSource;

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import com.google.gson.JsonArray;
import com.stpl.tech.kettle.report.metadata.model.ReportData;
import com.stpl.tech.master.util.QueryExecutor;

public class QueryExecutorForJsonData extends QueryExecutor<JsonArray, JsonArray> {

	public void execute(ReportData reportDefinition, DataSource dataSource)
			throws UnsupportedDataTypeException, ParseException {
		NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
		ResultSetConverter rse = new ResultSetConverter();
		this.data = jdbcTemplate.query(reportDefinition.getContent(), getParamSource(reportDefinition), rse);
		this.header = rse.getHeader();
	}
}
