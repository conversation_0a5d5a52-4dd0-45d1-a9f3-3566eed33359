package com.stpl.tech.kettle.clm.dao.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.clm.dao.ClevertapAttributesDao;
import com.stpl.tech.kettle.core.NamedQueryDefinition;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;

@Repository
public class ClevertapAttributesDaoImpl extends AbstractDaoImpl implements ClevertapAttributesDao {

	private static final Logger LOG = LoggerFactory.getLogger(ClevertapAttributesDaoImpl.class);
	public static final String CUSTOMER_ID = "customerId";

	@Override
	public Map<String, Object> calculateEventAttributes(OrderDetail order, CustomerInfo customer) {
		long startTime = System.currentTimeMillis();
		LOG.info("Query for Clevertap Event Attributes ");
		Query query = manager.createNativeQuery(NamedQueryDefinition.CLEVERTAP_EVENT_ATTRIBUTE_QUERY.getQuery());
		query.setParameter(CUSTOMER_ID, customer.getCustomerId());
		query.setParameter("offerCode", order.getOfferCode());
		query.setParameter("orderId", order.getOrderId());
		query.setParameter("orderSource", order.getOrderSource());
		query.setParameter("brandId", order.getBrandId());
		List<Object[]> resultList = query.getResultList();
		Object[] result = resultList.get(0);
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("offerDetailId", Objects.isNull(result[0]) ? 0 : result[0]);
		resultMap.put("isNewCustomer", Objects.isNull(result[1]) ? 'N' : result[1]);
		resultMap.put("previousOrderId", result[2]);
		resultMap.put("previousSourceOrderId", result[3]);
		resultMap.put("previousBillingServerTime", result[4]);
		resultMap.put("previousOrderTimeDiff", result[5]);
		resultMap.put("previousSourceBillingServerTime", result[6]);
		resultMap.put("previousSourceOrderTimeDiff", result[7]);
		resultMap.put("overallOrderCount", null == result[8] ? BigInteger.ZERO : result[8]);
		resultMap.put("orderSourceOrderCount", null == result[9] ? BigInteger.ZERO : result[9]);
		resultMap.put("offerClass", result[10]);
		resultMap.put("walletPendingAmount",null==result[11] ? BigInteger.ZERO : result[11]);
		LOG.info("Query for Clevertap Event Attributes : took {} ms", System.currentTimeMillis()-startTime);
		return resultMap;
	}

	@Override
	public Map<String, Object> calculateUserAttributes(Customer customer) {
		long startTime = System.currentTimeMillis();
		LOG.info("Query for Clevertap Profile Attributes ");
		Query query = manager.createNativeQuery(NamedQueryDefinition.CLEVERTAP_USER_ATTRIBUTE_QUERY.getQuery());
		query.setParameter(CUSTOMER_ID, customer.getId());
		List<Object[]> resultList = query.getResultList();
		Object[] result = resultList.get(0);
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("nboAvailableFlag", null == result[0] ? BigInteger.ZERO : result[0]);
		resultMap.put("dnboAvailableFlag", null == result[1] ? BigInteger.ZERO : result[1]);
		resultMap.put("gcBalance", null == result[2] ? BigDecimal.ZERO : result[2]);
		resultMap.put("subscriptionActiveFlag", null == result[3] ? BigInteger.ZERO : result[3]);
		resultMap.put("chyCashBalance", null == result[4] ? BigDecimal.ZERO : result[4]);
		resultMap.put("loyaltyPointsBalance", null == result[5] ? 0 : result[5]);
		resultMap.put("loyaltyRedeemedCount", null == result[6] ? BigDecimal.ZERO : result[6]);
		resultMap.put("totalSpent", null == result[7] ? BigDecimal.ZERO : result[7]);
		LOG.info("Query for Clevertap Profile Attributes : took {} ms", System.currentTimeMillis()-startTime);
		return resultMap;
	}

	@Override
	public Map<String, Object> calculateSubscriptionAttributes(OrderDetail order, CustomerInfo customer) {
		long startTime = System.currentTimeMillis();
		LOG.info("Query for Clevertap Subscription Event Attributes ");
		Query query = manager
				.createNativeQuery(NamedQueryDefinition.CLEVERTAP_SUBSCRIPTION_EVENT_ATTRIBUTE_QUERY.getQuery());
		query.setParameter(CUSTOMER_ID, customer.getCustomerId());
		query.setParameter("orderId", order.getOrderId());
		List<Object[]> resultList = query.getResultList();
		Object[] result = resultList.get(0);
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("isNewCustomer", result[0]);
		resultMap.put("planCode", result[1]);
		resultMap.put("planStartDate", result[2]);
		resultMap.put("planEndDate", result[3]);
		resultMap.put("eventType", result[4]);
		LOG.info("Query for Clevertap Subscription Event Attributes : took {} ms",
				System.currentTimeMillis()-startTime);
		return resultMap;
	}

	@Override
	public Map<String,Object> calculateUserAttributes(Integer customerId){
		long startTime = System.currentTimeMillis();
		LOG.info("Query for Clevertap Profile Attributes ");
		Query query = manager.createNativeQuery(NamedQueryDefinition.CLEVERTAP_USER_ATTRIBUTE_QUERY.getQuery());
		query.setParameter(CUSTOMER_ID, customerId);
		List<Object[]> resultList = query.getResultList();
		Object[] result = resultList.get(0);
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("nboAvailableFlag", null == result[0] ? BigInteger.ZERO : result[0]);
		resultMap.put("dnboAvailableFlag", null == result[1] ? BigInteger.ZERO : result[1]);
		resultMap.put("gcBalance", null == result[2] ? BigDecimal.ZERO : result[2]);
		resultMap.put("subscriptionActiveFlag", null == result[3] ? BigInteger.ZERO : result[3]);
		resultMap.put("chyCashBalance", null == result[4] ? BigDecimal.ZERO : result[4]);
		resultMap.put("loyaltyPointsBalance", null == result[5] ? 0 : result[5]);
		resultMap.put("loyaltyRedeemedCount", null == result[6] ? BigDecimal.ZERO : result[6]);
		resultMap.put("totalSpent", null == result[7] ? BigDecimal.ZERO : result[7]);
		LOG.info("Query for Clevertap Profile Attributes : took {} ms", System.currentTimeMillis()-startTime);
		return resultMap;
	}

}
