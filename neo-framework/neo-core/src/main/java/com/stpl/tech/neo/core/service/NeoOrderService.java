package com.stpl.tech.neo.core.service;

import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.domain.model.CustomerOrderPage;
import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import com.stpl.tech.neo.domain.model.mongo.DeviceVO;
import com.stpl.tech.neo.domain.model.mongo.OrderStatus;
import com.stpl.tech.neo.domain.model.mongo.OrderView;
import com.stpl.tech.redis.domain.model.ResponseCode;
import com.stpl.tech.neo.domain.model.mongo.GiftCardOffer;

import java.util.List;

public interface NeoOrderService {

    public String checkout(String cartId) throws WebOrderException;

    public String saveAndCheckoutCart(CartDetail cart) throws WebOrderException;

    public String saveAndCheckoutCartForKIOSKPayByCash(CartDetail cart) throws WebOrderException;

    public ResponseCode syncCart(CartDetail cart) throws WebOrderException;

    public OrderView search(String orderId) throws WebOrderException;

    public CustomerOrderPage searchCustomerOrders(CustomerOrderPage page) throws WebOrderException;

    ;

    public OrderView searchByWebId(String webOrderId) throws WebOrderException;

    public String checkoutByWebOrderId(String woId) throws WebOrderException;

    public OrderStatus fetchOrderStatus(String orderId) throws WebOrderException;

    boolean isFirstOrderForCustomer(String customerId) throws WebOrderException;

    byte[] downloadOrderReceipt(String orderId);

    boolean setOrderSource(CartDetail request) throws WebOrderException;

    public DeviceVO createGiftCardCart(DeviceVO deviceVO) throws WebOrderException;

    public void attachCart(CartDetail cart);

    public List<GiftCardOffer> giftCardOffer(int unitId);
}
