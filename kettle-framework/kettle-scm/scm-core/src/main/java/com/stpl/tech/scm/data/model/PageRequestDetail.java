package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "PAGE_REQUEST_DETAIL")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class PageRequestDetail {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "REQUEST_ID", nullable = false, unique = true)
	private Integer id;
	@Column(name = "EVENT_ID", nullable = false)
	private Integer eventId;
	@Column(name = "EVENT_TYPE", nullable = false)
	private String eventType;
	@Column(name = "CREATED_BY", nullable = false)
	private String createdBy;
	@Column(name = "RECORD_STATUS", nullable = false)
	private String recordStatus;
	@Column(name = "REGISTRATION_UTL", nullable = false)
	private String registrationUrl;
	@Column(name = "AUTH_KEY", nullable = false)
	private String authKey;
	@Column(name = "REQUEST_DATE", nullable = false)
	private Date requestDate;

}
