/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.forms.core.util;

import com.google.gson.Gson;
import com.stpl.tech.forms.core.FormsServiceConstants;
import com.stpl.tech.forms.domain.model.IdCodeName;
import com.stpl.tech.forms.domain.model.ObjectFactory;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.TimeZone;

public class AuditUtil extends AppUtils {

    public static final int COLUMN_WIDTH = 15;
    public static final int NAME_COLUMN_WIDTH = 35;

    private static final TimeZone TIME_ZONE = TimeZone.getTimeZone("Asia/Kolkata");

    private static ObjectFactory objectFactory = new ObjectFactory();

    public static BigDecimal convertToBigDecimal(int value) {
        Float number = Float.valueOf(value);
        return convertToBigDecimal(number);
    }

    public static BigDecimal convertToBigDecimal(Float value) {
        if (value != null) {
            return new BigDecimal(value, new MathContext(6, RoundingMode.HALF_UP));
        } else {
            return new BigDecimal(0.0f, new MathContext(6, RoundingMode.HALF_UP));
        }
    }

    public static BigDecimal convertToBigDecimal(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }

    public static IdCodeName getIdCodeName(Integer id, String code, String name) {
        IdCodeName idCodeName = objectFactory.createIdCodeName();
        idCodeName.setId(id);
        idCodeName.setCode(code);
        idCodeName.setName(name);
        return idCodeName;
    }

    public static IdCodeName getSystemUser() {
        return getIdCodeName(FormsServiceConstants.SYSTEM_USER, "SYSTEM", "SYSTEM");
    }

    public static String write(byte[] bytes, String rootPath, String parentDir, String typeOfFile, String fileName,
                               Logger log) {
        try {
            // Creating the directory to store file
            File dir = new File(rootPath + File.separator + typeOfFile + File.separator + parentDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            // Create the file on server
            File serverFile = new File(dir.getAbsolutePath() + File.separator + fileName);
            BufferedOutputStream stream = new BufferedOutputStream(new FileOutputStream(serverFile));
            stream.write(bytes);
            stream.close();
            log.info("Server File Location=" + serverFile.getAbsolutePath());
            return serverFile.getAbsolutePath();
        } catch (Exception e) {
            log.error("Encountered error while parsing file ::::", e);
            return null;
        }
    }

    public static Date getCurrentBusinessDate() {
        Calendar calendar = Calendar.getInstance(TIME_ZONE);
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h format
        if (hour < 6) {
            calendar.add(Calendar.DATE, -1);
        }
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static void main(String[] args) {

    }

    public static Date getEndOfBusinessDay(Date businessDate) {
        Calendar c = new GregorianCalendar(TIME_ZONE);
        c.setTime(businessDate);
        c.add(Calendar.DATE, 1);
        c.set(Calendar.HOUR_OF_DAY, 5); // anything 0 - 23
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getPreviousBusinessDate(Date businessDate) {
        Calendar calendar = new GregorianCalendar(TIME_ZONE);
        calendar.setTime(businessDate);
        calendar.set(Calendar.HOUR_OF_DAY, 0); // gets hour in 24h format
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.add(Calendar.DATE, -1);
        return calendar.getTime();
    }


    public static String getFormattedDate(Date date) {
        return new DateTime(date.getTime()).toString(AppConstants.DATE_FORMATTER);
    }

    public static Calendar getLastDayOfCurrentMonth(Date businessDate) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(businessDate);
        cal.set(Calendar.DATE, 1);
        cal.add(Calendar.MONTH, 1);
        cal.add(Calendar.DATE, -1);
        return cal;
    }

    public static boolean checkDate(Date date, Date businessDate) {
        System.out.println(date.getTime());
        System.out.println(businessDate.getTime());
        return getDate(businessDate).equals(getDate(date));
    }

    public static <T, E> T clone(E object, Class<T> clazz) {
        Gson gson = new Gson();
        String str = gson.toJson(object);
        return gson.fromJson(str, clazz);
    }

    public static BigDecimal convertToBigDecimal(Double sum) {
        return convertToBigDecimal(sum.floatValue());
    }

    public static boolean isOffice(UnitCategory category) {
        return category != null && category.equals(UnitCategory.OFFICE);
    }

    public static boolean isWareHouse(UnitCategory family) {
        return family != null && family.equals(UnitCategory.WAREHOUSE);
    }

    public static boolean isKitchen(UnitCategory family) {
        return family != null && family.equals(UnitCategory.KITCHEN);
    }

    public static boolean isCafe(UnitCategory family) {
        List<UnitCategory> backEndUnitTypes = Arrays.asList(UnitCategory.KITCHEN, UnitCategory.WAREHOUSE);
        return family != null && !backEndUnitTypes.contains(family);
    }
}
