CREATE TABLE KETTLE_DEV.ORDER_PAYMENT_DETAIL(
ORDER_PAYMENT_DETAIL_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ORDER_SETTLEMENT_ID INTEGER NULL,
ORDER_ID INTEGER NULL,
EXTERNAL_ORDER_ID VARCHAR(20) NULL, 
REQUEST_STATUS VARCHAR(30) NULL,
PAYMENT_MODE_ID INTEGER NULL,
PAYMENT_SOURCE VARCHAR(20) NULL,
PAYMENT_STATUS VARCHAR(30) NULL,
REQUEST_TIME TIMESTAMP NULL,
UPDATE_TIME TIMESTAMP NULL,
RESPONSE_TIME TIMESTAMP NULL,
REFUND_REQUESTED VARCHAR(1) NULL,
REFUND_STATUS VARCHAR(1) NULL,
REFUND_REASON VARCHAR(1) NULL,
REFUND_REQUEST_TIME TIMESTAMP NULL,
REFUND_PROCESS_TIME TIMESTAMP NULL,
PARTNER_TRANSACTION_ID VARCHAR(100) NULL,
PARTNER_PAYMENT_STATUS VARCHAR(50) NULL,
REDIRECT_URL VARCHAR(100) NULL
);

CREATE INDEX ORDER_PAYMENT_DETAIL_EXTERNAL_ORDER_ID ON KETTLE_DEV.ORDER_PAYMENT_DETAIL(EXTERNAL_ORDER_ID) using BTREE;
CREATE INDEX ORDER_PAYMENT_DETAIL_REQUEST_STATUS ON KETTLE_DEV.ORDER_PAYMENT_DETAIL(REQUEST_STATUS) using BTREE;

CREATE TABLE KETTLE_DEV.ORDER_PAYMENT_ATTRIBUTE_DATA(
ORDER_PAYMENT_ATTRIBUTE_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ORDER_PAYMENT_DETAIL_ID INTEGER NULL,
ATTRIBUTE_KEY VARCHAR(200),
ATTRIBUTE_TYPE VARCHAR(20),
ATTRIBUTE_VALUE  VARCHAR(1000)
);
