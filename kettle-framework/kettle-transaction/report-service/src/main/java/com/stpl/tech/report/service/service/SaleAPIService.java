package com.stpl.tech.report.service.service;

import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.xml.model.PartnerSaleRecords;
import com.stpl.tech.kettle.xml.model.SaleRecords;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DuplicateRequestException;

import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.JAXBException;
import java.util.Date;
import java.util.List;

public interface SaleAPIService {

    List<Order> getOrderDetails(int unitId, Date startTime, Date endTime, OrderFetchStrategy strategy);

    PartnerSaleRecords generatePartnerReportForDay(String api,HttpServletRequest request, String businessDate,String byPassValidation) throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException;

    SaleRecords generateHourlyReports(String api,HttpServletRequest request) throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException;

    PartnerSaleRecords generatePartnerHourlyReports(String api,HttpServletRequest request) throws AuthenticationFailureException, DuplicateRequestException, DataNotFoundException, JAXBException;

    SaleRecords generatePathfinderReportForDay(String api,HttpServletRequest request, String businessDate) throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException;

}
