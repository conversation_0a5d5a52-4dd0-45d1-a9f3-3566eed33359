package com.stpl.tech.master.controller;

import com.stpl.tech.master.RevenueCertificateMapping;
import com.stpl.tech.master.core.external.report.service.RevenueCertificateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.REPORT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + REPORT_CONTEXT)
public class RevenueCertificateEmailMappingResource {

    private static final Logger LOG = LoggerFactory.getLogger(RevenueCertificateEmailMappingResource.class);

    @Autowired
    private RevenueCertificateService reportingService;

    @RequestMapping(value = "submit-unitCertificateMapping", method = RequestMethod.POST,  consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean submitUnitCertificate(@RequestParam Integer unitId , @RequestParam String toBeGenerated , @RequestParam(required = false) String emailId ){
        LOG.info("Requesting to save email for unitId:{}",unitId);
        return reportingService.saveEmail(unitId,toBeGenerated,emailId);
    }

    @RequestMapping(value = "get-unitCertificateMapping", method = RequestMethod.GET,  produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public RevenueCertificateMapping getUnitCertificate(@RequestParam Integer unitId){
        LOG.info("Requesting to get mail for unitId :{}",unitId);
        return reportingService.getEmail(unitId);
    }
}
