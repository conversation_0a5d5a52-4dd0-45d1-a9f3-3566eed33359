package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationVersionDetailDomainData {


        private String applicationName;
        private String applicationCurrentVersion;
        private String applicationTargetVersion;
        private String versionStatus;
        private Date lastUpdateTime;

}
