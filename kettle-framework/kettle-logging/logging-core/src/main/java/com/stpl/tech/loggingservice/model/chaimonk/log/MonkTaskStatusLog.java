package com.stpl.tech.loggingservice.model.chaimonk.log;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.persistence.Entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 22-10-2017.
 */

@Entity
@Document
public class MonkTaskStatusLog implements BasicLogDetail{

    @Id
    protected String _id;

    protected String pName;
    protected String d;
    protected int q;
    protected int pid;
    protected int uId;
    protected int taskId;//orderItemId
    protected int orderId; //orderIdAtserver
    protected long taskCreateTime;
    @Indexed
    protected long billServerTime;
    protected Map<Integer,Long> dtimeStatus;
    protected List<ProcessedMonk> pMonkList;
    protected boolean isReassigned;
    protected long ntTmeSum;
    private Date uploadTime;


    public String get_id() {
        return _id;
    }

    public String getpName() {
        return pName;
    }

    public void setpName(String pName) {
        this.pName = pName;
    }

    public String getD() {
        return d;
    }

    public void setD(String d) {
        this.d = d;
    }

    public int getQ() {
        return q;
    }

    public void setQ(int q) {
        this.q = q;
    }

    public int getPid() {
        return pid;
    }

    public void setPid(int pid) {
        this.pid = pid;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public int getuId() {
        return uId;
    }

    public void setuId(int uId) {
        this.uId = uId;
    }

    public int getTaskId() {
        return taskId;
    }

    public void setTaskId(int taskId) {
        this.taskId = taskId;
    }

    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public long getTaskCreationTime() {
        return taskCreateTime;
    }

    public void setTaskCreationTime(long taskCreationTime) {
        this.taskCreateTime = taskCreationTime;
    }


    public Map<Integer, Long> getDtimeStatus() {
        return dtimeStatus;
    }

    public void setDtimeStatus(Map<Integer, Long> dtimeStatus) {
        this.dtimeStatus = dtimeStatus;
    }

    public boolean isReassigned() {
        return isReassigned;
    }

    public void setReassigned(boolean reassigned) {
        isReassigned = reassigned;
    }

    public long getTaskCreateTime() {
        return taskCreateTime;
    }

    public void setTaskCreateTime(long taskCreateTime) {
        this.taskCreateTime = taskCreateTime;
    }

    public long getBillServerTime() {
        return billServerTime;
    }

    public void setBillServerTime(long billServerTime) {
        this.billServerTime = billServerTime;
    }

    public List<ProcessedMonk> getpMonkList() {
        return pMonkList;
    }

    public void setpMonkList(List<ProcessedMonk> pMonkList) {
        this.pMonkList = pMonkList;
    }

    public long getNtTmeSum() {
        return ntTmeSum;
    }

    public void setNtTmeSum(long ntTmeSum) {
        this.ntTmeSum = ntTmeSum;
    }

    @Override
    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Date getUploadTime() {
        return uploadTime;
    }
}
