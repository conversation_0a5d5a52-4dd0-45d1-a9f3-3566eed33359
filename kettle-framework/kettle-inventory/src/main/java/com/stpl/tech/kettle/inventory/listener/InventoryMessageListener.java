package com.stpl.tech.kettle.inventory.listener;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.MessageProducer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.stpl.tech.kettle.inventory.domain.mongo.QuantityResponseData;
import com.stpl.tech.kettle.inventory.service.InventoryManagementService;
import com.stpl.tech.util.JSONSerializer;

public class InventoryMessageListener implements MessageListener {

	/**
	 * static sl4j Logger
	 */
	private static final Logger LOG = LoggerFactory.getLogger(InventoryMessageListener.class);

	private MessageProducer errorQueue;

	private InventoryManagementService service;

	private MessageProducer newInventoryQueue;

	public InventoryMessageListener(MessageProducer newInventoryQueue,MessageProducer errorQueue, InventoryManagementService service) {
		this.errorQueue = errorQueue;
		this.service = service;
		this.newInventoryQueue = newInventoryQueue;
	}

	@Override
	public void onMessage(Message message) {
		try {
			LOG.info("On QuantityResponseData Message " + message.getJMSMessageID());
			if (message instanceof SQSObjectMessage) {
				LOG.info("Message instance of : SQSObjectMessage : " + message.getJMSMessageID());
				SQSObjectMessage object = (SQSObjectMessage) message;
				String response = JSONSerializer.toJSON(object.getObject());
				LOG.info("SQSObjectMessage : {}", response);
				QuantityResponseData event = JSONSerializer.toJSON(response, QuantityResponseData.class);
				service.updateInventory(event, true);
				message.acknowledge();
				newInventoryQueue.send(message);
				LOG.info("Order Acknowledge : SQSObjectMessage : " + message.getJMSMessageID());
			} else if (message instanceof SQSTextMessage) {
				LOG.info("Message instance of : SQSTextMessage : " + message.getJMSMessageID());
				SQSTextMessage object = (SQSTextMessage) message;
				String response = (String) object.getText();
				LOG.info("SQSObjectMessage : {}", response);
				QuantityResponseData event = JSONSerializer.toJSON(response, QuantityResponseData.class);
				service.updateInventory(event, true);
				message.acknowledge();
				newInventoryQueue.send(message);
				LOG.info("Order Acknowledge : SQSTextMessage : " + message.getJMSMessageID());
			} else {
				LOG.info("Order Not Acknowledged" + message.getJMSMessageID());
			}

		} catch (JMSException e) {
			LOG.error("Error while saving the message", e);
			try {
				LOG.info("Publishing Error Message to Error Queue " + message.getJMSMessageID());
				errorQueue.send(message);
			} catch (JMSException e1) {
				LOG.error("Error while saving the message to error queue", e);
			}
			return;
		}
	}

}
