package com.stpl.tech.scm.data.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "FA_STOCK_EVENT_EXTRA_SCANNED_ITEMS")
public class FaStockEventExtraScannedItems {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "FA_STOCK_EVENT_EXTRA_SCANNED_ITEMS_ID")
    private Integer faStockEventExtraScannedItemsId;

    @Column(name = "ASSET_ID")
    private Integer assetId;

    @Column(name = "ASSET_STATUS")
    private String assetStatus;

    @Column(name = "EVENT_ID")
    private Integer eventId;

    @Column(name = "SCANNED_TIME")
    private Date scannedTime;

    @Column(name = "IS_SETTLED")
    private String isSettled;

    @Column(name = "COMMENT")
    private String comment;

    @Column(name = "USER_DESCRIPTION")
    private String userDescription;

    @Column(name = "DOC_ID")
    private Integer docId;

    @Column(name = "OWNER_UNIT_ID")
    private Integer ownerUnitId;

    public Integer getFaStockEventExtraScannedItemsId() {
        return this.faStockEventExtraScannedItemsId;
    }

    public void setFaStockEventExtraScannedItemsId(Integer faStockEventExtraScannedItemsId) {
        this.faStockEventExtraScannedItemsId = faStockEventExtraScannedItemsId;
    }

    public Integer getAssetId() {
        return this.assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    public String getAssetStatus() {
        return this.assetStatus;
    }

    public void setAssetStatus(String assetStatus) {
        this.assetStatus = assetStatus;
    }

    public Integer getEventId() {
        return this.eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Date getScannedTime() {
        return this.scannedTime;
    }

    public void setScannedTime(Date scannedTime) {
        this.scannedTime = scannedTime;
    }

    public String getIsSettled() {
        return this.isSettled;
    }

    public void setIsSettled(String isSettled) {
        this.isSettled = isSettled;
    }

    public String getComment() {
        return this.comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getUserDescription() {
        return this.userDescription;
    }

    public void setUserDescription(String userDescription) {
        this.userDescription = userDescription;
    }

    public Integer getDocId() {
        return this.docId;
    }

    public void setDocId(Integer docId) {
        this.docId = docId;
    }

    public Integer getOwnerUnitId() {
        return ownerUnitId;
    }

    public void setOwnerUnitId(Integer ownerUnitId) {
        this.ownerUnitId = ownerUnitId;
    }
}
