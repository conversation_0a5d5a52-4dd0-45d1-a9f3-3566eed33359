package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SelectedProductDetails")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SelectedProductDetails {

    @JsonProperty(value="id", required = true)
    private int id ;
    @JsonProperty(value="brandId",required = true)
    private int brandId;
    @JsonProperty(value="type")
    private int type ;
    @JsonProperty(value="subType")
    private int subType ;
    @JsonProperty(value="shortCode")
    private String shortCode ;

    @JsonProperty(value="name",required = true)

    private String name ;

    @JsonProperty(value="skuCode")
    private String skuCode ;

    @JsonProperty(value="classification")
    private String classification;

    @JsonProperty(value="customize")
    private boolean customize ;

    @JsonProperty(value="hasAddons")

    private boolean hasAddons;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getBrandId() {
        return brandId;
    }

    public void setBrandId(int brandId) {
        this.brandId = brandId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public String getShortCode() {
        return shortCode;
    }

    public void setShortCode(String shortCode) {
        this.shortCode = shortCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getClassification() {
        return classification;
    }

    public void setClassification(String classification) {
        this.classification = classification;
    }

    public boolean isCustomize() {
        return customize;
    }

    public void setCustomize(boolean customize) {
        this.customize = customize;
    }

    public boolean isHasAddons() {
        return hasAddons;
    }

    public void setHasAddons(boolean hasAddons) {
        this.hasAddons = hasAddons;
    }
}
