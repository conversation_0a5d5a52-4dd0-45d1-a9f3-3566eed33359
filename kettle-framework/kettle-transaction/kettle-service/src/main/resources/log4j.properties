#
# SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
# __________________
#
# [2015] - [2017] Sunshine Teahouse Private Limited
# All Rights Reserved.
#
# NOTICE:  All information contained herein is, and remains
# the property of Sunshine Teahouse Private Limited and its suppliers,
# if any.  The intellectual and technical concepts contained
# herein are proprietary to Sunshine Teahouse Private Limited
# and its suppliers, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material
# is strictly forbidden unless prior written permission is obtained
# from Sunshine Teahouse Private Limited.
#

# Define the root logger with appender file

log4j.rootLogger = DEBUG, CONSOLE, FILE

log4j.logger.com.stpl.tech.kettle.service.controller.UnitHealthResource=DEBUG, HEALTHFILE, CONSOLE
log4j.logger.com.stpl.tech.kettle.core.monitoring.UnitHealthCache=DEBUG, HEALTHFILE, CONSOLE
log4j.additivity.com.stpl.tech.kettle.service.controller.UnitHealthResource=false
log4j.additivity.com.stpl.tech.kettle.core.monitoring.UnitHealthCache=false

log4j.logger.com.stpl.tech.kettle.service.controller.EmailResource=DEBUG, EMAILFILE, CONSOLE
log4j.additivity.com.stpl.tech.kettle.service.controller.EmailResource=false

log4j.logger.com.stpl.tech.kettle.core.cache.OrderInfoCache=DEBUG, ORDERCACHE, CONSOLE
log4j.additivity.com.stpl.tech.kettle.core.cache.OrderInfoCache=false

# Define the file appender
log4j.appender.FILE=org.apache.log4j.DailyRollingFileAppender
log4j.appender.FILE.File=g:/logs/kettle.log
log4j.appender.FILE.ImmediateFlush=true
log4j.appender.FILE.Threshold=INFO
log4j.appender.FILE.Append=true
log4j.appender.FILE.MaxFileSize=500MB
log4j.appender.FILE.DatePattern='.' yyyy-MM-dd
log4j.appender.FILE.layout=org.apache.log4j.PatternLayout
log4j.appender.FILE.layout.conversionPattern=%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %x %-5p %10c: %m%n



log4j.appender.CONSOLE=org.apache.log4j.ConsoleAppender
log4j.appender.CONSOLE.Threshold=INFO
log4j.appender.CONSOLE.layout=org.apache.log4j.PatternLayout
log4j.appender.CONSOLE.layout.conversionPattern=%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %x %-5p %10c: %m%n



log4j.appender.HEALTHFILE=org.apache.log4j.DailyRollingFileAppender
log4j.appender.HEALTHFILE.File=g:/logs/health/kettlehealth.log
log4j.appender.HEALTHFILE.ImmediateFlush=true
log4j.appender.HEALTHFILE.Threshold=DEBUG
log4j.appender.HEALTHFILE.Append=true
log4j.appender.HEALTHFILE.DatePattern='.' yyyy-MM-dd
log4j.appender.HEALTHFILE.layout=org.apache.log4j.PatternLayout
log4j.appender.HEALTHFILE.layout.conversionPattern=%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %x %-5p %10c: %m%n



log4j.appender.EMAILFILE=org.apache.log4j.DailyRollingFileAppender
log4j.appender.EMAILFILE.File=g:/logs/email/kettleemail.log
log4j.appender.EMAILFILE.ImmediateFlush=true
log4j.appender.EMAILFILE.Threshold=DEBUG
log4j.appender.EMAILFILE.Append=true
log4j.appender.EMAILFILE.DatePattern='.' yyyy-MM-dd
log4j.appender.EMAILFILE.layout=org.apache.log4j.PatternLayout
log4j.appender.EMAILFILE.layout.conversionPattern=%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %x %-5p %10c: %m%n



log4j.appender.ORDERCACHE=org.apache.log4j.DailyRollingFileAppender
log4j.appender.ORDERCACHE.File=g:/logs/orderCache/orderCache.log
log4j.appender.ORDERCACHE.ImmediateFlush=true
log4j.appender.ORDERCACHE.Threshold=DEBUG
log4j.appender.ORDERCACHE.Append=true
log4j.appender.ORDERCACHE.DatePattern='.' yyyy-MM-dd
log4j.appender.ORDERCACHE.layout=org.apache.log4j.PatternLayout
log4j.appender.ORDERCACHE.layout.conversionPattern=%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %x %-5p %10c: %m%n

