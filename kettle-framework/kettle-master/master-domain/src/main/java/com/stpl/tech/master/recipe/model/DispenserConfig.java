//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.model;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@XmlAccessorType(XmlAccessType.FIELD)

@Document(collection = "DispenserConfig")
public class DispenserConfig {

	@Id
	private String _id;
	@Field
	private List<String> dispenseTags;

	public List<String> getDispenseTags() {
		return dispenseTags;
	}

	public void setDispenseTags(List<String> dispenseTags) {
		this.dispenseTags = dispenseTags;
	}
}
