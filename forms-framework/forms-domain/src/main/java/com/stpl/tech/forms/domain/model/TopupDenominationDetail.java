package com.stpl.tech.forms.domain.model;

import java.math.BigDecimal;

public class TopupDenominationDetail {
	private Integer id;

	private Integer walletTransactionId;

	private int denominationId;

	private Integer packetCount;

	private Integer looseCurrencyCount;

	private BigDecimal totalAmount;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getWalletTransactionId() {
		return walletTransactionId;
	}

	public void setWalletTransactionId(Integer walletTransactionId) {
		this.walletTransactionId = walletTransactionId;
	}

	public int getDenominationId() {
		return denominationId;
	}

	public void setDenominationId(int denominationId) {
		this.denominationId = denominationId;
	}

	public Integer getPacketCount() {
		return packetCount;
	}

	public void setPacketCount(Integer packetCount) {
		this.packetCount = packetCount;
	}

	public Integer getLooseCurrencyCount() {
		return looseCurrencyCount;
	}

	public void setLooseCurrencyCount(Integer looseCurrencyCount) {
		this.looseCurrencyCount = looseCurrencyCount;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

}
