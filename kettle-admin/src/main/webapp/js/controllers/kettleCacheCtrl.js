/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller(
    "kettleCacheCtrl",
    function ($log, AuthService, $cookieStore, $rootScope, $scope, $http, $location, AppUtil) {

        $scope.init = function () {

        };

        $scope.getCardDetail = function () {
            var url = $scope.byCard ? AppUtil.restUrls.cashCardManagement.getCardByCode :
                AppUtil.restUrls.cashCardManagement.getCardBySerial;
            $http({
                method: 'POST',
                url: url,
                data:$scope.code
            }).then(function success(response) {
                if (response.data !== null) {
                    $scope.cardDetail = response.data;
                    $scope.reason = null;
                } else {
                    $scope.noResultMsg = true;
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }

    });
