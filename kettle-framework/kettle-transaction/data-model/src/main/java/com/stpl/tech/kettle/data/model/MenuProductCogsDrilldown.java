package com.stpl.tech.kettle.data.model;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 07-12-2018.
 */
@Entity
@Table(name = "MENU_PRODUCT_COGS_DRILLDOWN")
public class MenuProductCogsDrilldown {

    private Integer drilldownId;
    private Integer scmProductId;
    private String source;
    private String addOn;
//    private Integer brandId;
//    private String orderType;
    private BigDecimal quantity;
    private BigDecimal price;
    private BigDecimal cost;
    private BigDecimal taxableQuantity;
    private BigDecimal taxableCost;
    private BigDecimal taxPercentage;
    private BigDecimal taxAmount;

    private MenuProductCostData menuProductCostData;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DRILLDOWN_ID", unique = true, nullable = false)
    public Integer getDrilldownId() {
        return drilldownId;
    }

    public void setDrilldownId(Integer drilldownId) {
        this.drilldownId = drilldownId;
    }

    @Column(name = "SCM_PRODUCT_ID", nullable = false)
    public Integer getScmProductId() {
        return scmProductId;
    }

    public void setScmProductId(Integer scmProductId) {
        this.scmProductId = scmProductId;
    }

    @Column(name = "COST_SOURCE", nullable = false)
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Column(name = "ADDON", nullable = true)
    public String getAddOn() {
        return addOn;
    }

    public void setAddOn(String addOn) {
        this.addOn = addOn;
    }

    @Column(name = "QUANTITY", nullable = false)
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Column(name = "PRICE", nullable = false)
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Column(name = "COST", nullable = false)
    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "COGS_DETAIL_ID", nullable = false)
    public MenuProductCostData getMenuProductCostData() {
        return menuProductCostData;
    }

    public void setMenuProductCostData(MenuProductCostData menuProductCostData) {
        this.menuProductCostData = menuProductCostData;
    }

    @Column(name = "TAXABLE_QUANTITY", nullable = false)
    public BigDecimal getTaxableQuantity() {
        return taxableQuantity;
    }

    public void setTaxableQuantity(BigDecimal taxableQuantity) {
        this.taxableQuantity = taxableQuantity;
    }

    @Column(name = "TAXABLE_COST", nullable = false)
    public BigDecimal getTaxableCost() {
        return taxableCost;
    }

    public void setTaxableCost(BigDecimal taxableCost) {
        this.taxableCost = taxableCost;
    }

    @Column(name = "TAXABLE_PERCENTAGE", nullable = false)
    public BigDecimal getTaxPercentage() {
        return taxPercentage;
    }

    public void setTaxPercentage(BigDecimal taxPercentage) {
        this.taxPercentage = taxPercentage;
    }

    @Column(name = "TAXABLE_AMOUNT", nullable = false)
    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

//    @Column(name = "UNIT_ID", nullable = false)
//    public Integer getUnitId() {
//        return unitId;
//    }
//
//    public void setUnitId(Integer unitId) {
//        this.unitId = unitId;
//    }
//
//    @Column(name = "BRAND_ID", nullable = false)
//    public Integer getBrandId() {
//        return brandId;
//    }
//
//    public void setBrandId(Integer brandId) {
//        this.brandId = brandId;
//    }
//
//    @Column(name = "ORDER_TYPE", nullable = false)
//    public String getOrderType() {
//        return orderType;
//    }
//
//    public void setOrderType(String orderType) {
//        this.orderType = orderType;
//    }
}
