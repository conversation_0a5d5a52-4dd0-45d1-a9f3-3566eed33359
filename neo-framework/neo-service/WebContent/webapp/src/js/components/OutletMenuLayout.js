import React from "react";
import appUtil from "../AppUtil";
import MobileOutletMenuLayout from "./mobile/MobileOutletMenuLayout";
import DesktopOutletMenuLayout from "./desktop/DesktopOutletMenuLayout";

export default class OutletMenuLayout extends React.Component {
	render (){
		if(appUtil.isMobile()){
			return(
				<MobileOutletMenuLayout props={this.props} />
			)
		}else{
			return(
				<DesktopOutletMenuLayout props={this.props} />
			)
		}
	}
}