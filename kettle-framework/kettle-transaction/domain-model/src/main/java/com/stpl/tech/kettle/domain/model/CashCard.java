//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.05.17 at 11:48:04 AM IST 
//


package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for CashCard complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="CashCard"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="itemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="cardValue" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="cardNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="isValid" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="error" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="buyerId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="customerId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="empId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="terminalId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CashCard", propOrder = {
    "itemId",
    "productName",
    "cardValue",
    "cardNumber",
    "isValid",
    "error",
    "buyerId",
    "customerId",
    "empId",
    "unitId",
    "terminalId",
    "type"
})
public class CashCard {

    protected int itemId;
    @XmlElement(required = true)
    protected String productName;
    protected int cardValue;
    @XmlElement(required = true)
    protected String cardNumber;
    @XmlElement(required = true, type = Boolean.class, nillable = true)
    protected Boolean isValid;
    @XmlElement(required = true, nillable = true)
    protected String error;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer buyerId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer customerId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer empId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer unitId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer terminalId;
    @XmlElement(required = true)
	protected String type;

    /**
     * Gets the value of the itemId property.
     * 
     */
    public int getItemId() {
        return itemId;
    }

    /**
     * Sets the value of the itemId property.
     * 
     */
    public void setItemId(int value) {
        this.itemId = value;
    }

    /**
     * Gets the value of the productName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProductName() {
        return productName;
    }

    /**
     * Sets the value of the productName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProductName(String value) {
        this.productName = value;
    }

    /**
     * Gets the value of the cardValue property.
     * 
     */
    public int getCardValue() {
        return cardValue;
    }

    /**
     * Sets the value of the cardValue property.
     * 
     */
    public void setCardValue(int value) {
        this.cardValue = value;
    }

    /**
     * Gets the value of the cardNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCardNumber() {
        return cardNumber;
    }

    /**
     * Sets the value of the cardNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCardNumber(String value) {
        this.cardNumber = value;
    }

    /**
     * Gets the value of the isValid property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *
     */
    public Boolean isIsValid() {
        return isValid;
    }

    /**
     * Sets the value of the isValid property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *
     */
    public void setIsValid(Boolean value) {
        this.isValid = value;
    }

    /**
     * Gets the value of the error property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getError() {
        return error;
    }

    /**
     * Sets the value of the error property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setError(String value) {
        this.error = value;
    }

    /**
     * Gets the value of the buyerId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getBuyerId() {
        return buyerId;
    }

    /**
     * Sets the value of the buyerId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setBuyerId(Integer value) {
        this.buyerId = value;
    }

    /**
     * Gets the value of the customerId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * Sets the value of the customerId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setCustomerId(Integer value) {
        this.customerId = value;
    }

    /**
     * Gets the value of the empId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getEmpId() {
        return empId;
    }

    /**
     * Sets the value of the empId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setEmpId(Integer value) {
        this.empId = value;
    }

    /**
     * Gets the value of the unitId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setUnitId(Integer value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the terminalId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getTerminalId() {
        return terminalId;
    }

    /**
     * Sets the value of the terminalId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setTerminalId(Integer value) {
        this.terminalId = value;
    }

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
    
}
