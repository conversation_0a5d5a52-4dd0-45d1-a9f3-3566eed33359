package com.stpl.tech.kettle.data.model;


import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "SUPERU_ORDER_ANALYSIS_DATA")
@Getter
@Setter
public class SuperUOrderAnalysisData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EVENT_ID")
    private Integer eventId;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "UNIT_NAME")
    private String unitName;

    @Column(name = "CUSTOMER_ID")
    private Integer customerId;

    @Column(name = "EMP_ID")
    private Integer empId;

    @Column(name = "ORDER_ID")
    private Integer orderId;

    @Column(name = "BILLING_SERVER_TIME")
    private String billingServerTime;

    @Column(name = "PRODUCTS_AND_QUANTITY", columnDefinition = "TEXT")
    private String productsAndQuantity;

    @Column(name = "REVENUE")
    private Float revenue;

    @Column(name = "CUSTOMER_TYPE")
    private String customerType;

    @Column(name = "APPROPRIATE_UPSELL")
    private String appropriateUpsell;

    @Column(name = "APPROPRIATE_UPSELL_ITEM")
    private String appropriateUpsellItem;

    @Column(name = "STATEMENT_SPEAKER")
    private String statementSpeaker;

    @Column(name = "DRINKS_PRODUCTS_AND_QUANTITY")
    private String drinksProductsAndQuantity;

    @Column(name = "EATABLES_PRODUCTS_AND_QUANTITY")
    private String eatablesProductsAndQuantity;

    @Column(name = "MERCHANDISE_PRODUCTS_AND_QUANTITY")
    private String merchandiseProductsAndQuantity;

    @Column(name = "DAYPART_CONVERSION")
    private Integer daypartConversion;

    @Column(name = "GREETING")
    private Boolean greeting;

    @Column(name = "ASK_FOR_CONTACT_NUMBER")
    private String askForContactNumber;

    @Column(name = "MENTION_LOYALTY_PROGRAM_IF_DENIED_CONTACT")
    private String mentionLoyaltyProgramIfDeniedContact;

    @Column(name = "THANKING")
    private Boolean thanking;

    @Column(name = "SUGGEST_DESI_CHAI_AND_POPULAR_COMBOS_IF_NEW_CUSTOMER")
    private String suggestDesiChaiAndPopularCombosIfNewCustomer;

    @Column(name = "EXPLAIN_LOYALTY_PROGRAM_IF_NEW_CUSTOMER")
    private String explainLoyaltyProgramIfNewCustomer;

    @Column(name = "USE_CUSTOMER_NAME_THRICE")
    private String useCustomerNameThrice;

    @Column(name = "PROVIDE_PRECISE_PRODUCT_DESCRIPTIONS_FOR_QUERIES")
    private String providePreciseProductDescriptionsForQueries;

    @Column(name = "SUGGEST_MERCHANDISE")
    private String suggestMerchandise;

    @Column(name = "SUGGEST_TOP_UP_WALLET_IF_AVAILABLE")
    private String suggestTopUpWalletIfAvailable;


    @Column(name = "IS_PROCESSED")
    private String isProcessed = "N";

    @Transient
    private List<BcxCategorySuperU> categorySuperU;

    @Transient
    private BigDecimal orderRating;

    @Transient
    private boolean isApplicable = true;


}
