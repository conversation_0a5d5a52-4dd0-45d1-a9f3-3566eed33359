package com.stpl.tech.scm.domain.model;

import java.util.List;

public class ProductionPlanningData {
    List<OrdersDetailsShort> orderDetailShort;
    ProductionPlanningSummary  productionPlanningSummary;
    List<UnitWiseSummary> unitWiseSummary;

    public List<OrdersDetailsShort> getOrderDetailShort() {
        return orderDetailShort;
    }

    public void setOrderDetailShort(List<OrdersDetailsShort> orderDetailShort) {
        this.orderDetailShort = orderDetailShort;
    }

    public ProductionPlanningSummary getProductionPlanningSummary() {
        return productionPlanningSummary;
    }

    public void setProductionPlanningSummary(ProductionPlanningSummary productionPlanningSummary) {
        this.productionPlanningSummary = productionPlanningSummary;
    }

    public List<UnitWiseSummary> getUnitWiseSummary() {
        return unitWiseSummary;
    }

    public void setUnitWiseSummary(List<UnitWiseSummary> unitWiseSummary) {
        this.unitWiseSummary = unitWiseSummary;
    }

}
