//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.10 at 07:11:28 PM IST
//

package com.stpl.tech.analytics.model;

import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * Java class for UnitReportData complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="UnitReportData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="businessDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="data" type="{http://www.w3schools.com}AggregateSaleData"/&gt;
 *         &lt;element name="drillDown" type="{http://www.w3schools.com}ProductReportData"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitReportData", propOrder = { "productId", "businessDate", "data", "drillDown" })
@Document(collection = "UnitReportData")
public class UnitReportData implements Serializable, Comparable<UnitReportData> {

	/**
	 *
	 */
	private static final long serialVersionUID = -1669567854038981374L;
	@Id
	private String _id;

	/*
	 * @Version
	 *
	 * @JsonIgnore private Long version;
	 *
	 *//**
		 * Added to avoid a runtime error whereby the detachAll property is
		 * checked for existence but not actually used.
		 *//*
		 * private String detachAll;
		 */
	@Field
	protected int id;
	@XmlElement(required = true)
	@Field
	protected UnitBasicDetail detail;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date businessDate;
	@XmlElement(required = true)
	@Field
	protected AggregateSaleData data = new AggregateSaleData();
	@XmlElement(required = true)
	@Field
	protected AggregatePenetrationData penetration = new AggregatePenetrationData();

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/*
	 * public Long getVersion() { return version; }
	 *
	 * public void setVersion(Long version) { this.version = version; }
	 *
	 * public String getDetachAll() { return detachAll; }
	 *
	 * public void setDetachAll(String detachAll) { this.detachAll = detachAll;
	 * }
	 */
	public UnitBasicDetail getDetail() {
		return detail;
	}

	public void setDetail(UnitBasicDetail detail) {
		this.detail = detail;
	}

	/**
	 * Gets the value of the productId property.
	 *
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the productId property.
	 *
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the businessDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getBusinessDate() {
		return businessDate;
	}

	/**
	 * Sets the value of the businessDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setBusinessDate(Date value) {
		this.businessDate = value;
	}

	/**
	 * Gets the value of the data property.
	 *
	 * @return possible object is {@link AggregateSaleData }
	 *
	 */
	public AggregateSaleData getData() {
		return data;
	}

	/**
	 * Sets the value of the data property.
	 *
	 * @param value
	 *            allowed object is {@link AggregateSaleData }
	 *
	 */
	public void setData(AggregateSaleData value) {
		this.data = value;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + id;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UnitReportData other = (UnitReportData) obj;
		if (id != other.id)
			return false;
		return true;
	}

	@Override
	public int compareTo(UnitReportData o) {
		return Integer.compare(this.id, o.id);
	}

	@Override
	public String toString() {
		return "UnitReportData [productId=" + id + ", detail=" + detail + ", data=" + data + "]";
	}

	public AggregatePenetrationData getPenetration() {
		return penetration;
	}

	public void setPenetration(AggregatePenetrationData penetration) {
		this.penetration = penetration;
	}


}
