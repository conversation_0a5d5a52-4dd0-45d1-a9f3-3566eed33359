/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.process;

import com.google.common.io.Files;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.reports.core.ReportExecutor;
import com.stpl.tech.kettle.reports.core.ReportType;
import com.stpl.tech.kettle.reports.dao.impl.PostClosureReportInputData;
import com.stpl.tech.kettle.reports.dao.impl.ReportFileData;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportOutputData;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.notification.QueryToExcelWriter;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.File;
import java.io.IOException;
import java.util.Date;

public class PostDayClosureReportController
		implements ReportExecutor<Unit, PostClosureReportInputData, SettlementReportOutputData> {

	private final EnvironmentProperties props;
	private static final Logger LOG = LoggerFactory.getLogger(PostDayClosureReportController.class);
	private final WorkbookContextFactory ctxFactory = WorkbookContextFactory.useXlsx();
	private final MasterDataCache masterCache;

	public PostDayClosureReportController(EnvironmentProperties props, MasterDataCache masterCache) {
		super();
		this.props = props;
		this.masterCache = masterCache;
	}

	public SettlementReportOutputData execute(PostClosureReportInputData data) {
		if (data == null) {
			return null;
		}
		WorkbookContext workbookCtx = ctxFactory.createWorkbook();
		if (data.getQueryReports() != null && data.getQueryReports().size() > 0) {
			QueryToExcelWriter writer = new QueryToExcelWriter(workbookCtx);
			for (String category : data.getQueryReports().keySet()) {
				writer.writeToSheet(category, data.getQueryReports().get(category), false);
			}
		}
		SettlementReportOutputData output = getOutput(data.getBusinessDate(), data.getUnit());
		output.getReportFiles().add(generateManagersReport(data.getUnit(), workbookCtx));
		return output;
	}

	private ReportFileData generateManagersReport(Unit unit, WorkbookContext workbookCtx) {
		ReportFileData managersReport = getManagersReportFileData(unit);
		try {
			Files.write(workbookCtx.toNativeBytes(), new File(managersReport.getFilePath()));
		} catch (IOException e) {
			LOG.error("Error in writing managers report for unit " + unit.getName(), e);
			managersReport.setGenerated(false);
		}

		return managersReport;
	}

	private SettlementReportOutputData getOutput(Date businessDate, Unit unit) {
		SettlementReportOutputData output = new SettlementReportOutputData();
		output.setEmailId(unit.getUnitEmail());
		output.setReportGenerated(true);
		output.setReportGenerationTime(AppUtils.getCurrentTimestamp());
		output.setUnit(unit);
		if (unit.getManagerId() != null) {
			output.setManagerEmailId(unit.getManagerEmail());
		}
		if (unit.getCafeManager() != null) {
			EmployeeBasicDetail e = masterCache.getEmployeeBasicDetail(unit.getCafeManager().getId());
			if (e != null && e.getEmailId() != null && !output.getToEmails().contains(e.getEmailId())) {
				output.getToEmails().add(e.getEmailId());
			}
		}
		output.setEnv(props.getEnvironmentType());
		output.setBusinessDate(businessDate);
		output.setReportName("Performance Report");
		return output;
	}

	private ReportFileData getManagersReportFileData(Unit unit) {
		String fileName = "Performance Report-" + unit.getName() + "-" + AppUtils.getCurrentTimeISTStringWithNoColons()
				+ ".xlsx";
		String fileDir = props.getBasePath() + "/" + unit.getId() + "/reports/";
		File file = new File(fileDir);
		if (file != null && !file.exists()) {
			boolean madeDirectories = file.mkdirs();
			LOG.error("made directories for the score report  {} ::: {}", fileDir, madeDirectories);
		}
		String filePath = fileDir + fileName;
		ReportFileData fileData = new ReportFileData(ReportType.PERFORMANCE, AppConstants.EXCEL_MIME_TYPE, fileName,
				filePath, true);
		return fileData;

	}

}
