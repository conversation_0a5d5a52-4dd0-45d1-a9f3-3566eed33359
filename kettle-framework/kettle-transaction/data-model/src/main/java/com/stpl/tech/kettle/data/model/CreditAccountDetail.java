/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * EmployeeDetail generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CREDIT_ACCOUNT_DETAIL")
public class CreditAccountDetail implements java.io.Serializable {

	private Integer creditAccountDetailId;
	private String legalName;
	private String displayName;
	private String address;
	private String tanNumber;
	private String panNumber;
	private String certificateOfIncorporation;
	private String bankDetail;
	private String contactPerson;
	private String contactPersonNumber;
	private String contactPersonEmail;
	private String accountContactPerson;
	private String accountContactPersonNumber;
	private String accountContactPersonEmail;
	private Integer creditDays;
	private String companyContact;
	private String accountStatus;

	public CreditAccountDetail() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "CREDIT_ACCOUNT_DETAIL_ID", unique = true, nullable = false)
	public Integer getCreditAccountDetailId() {
		return this.creditAccountDetailId;
	}

	public void setCreditAccountDetailId(Integer empId) {
		this.creditAccountDetailId = empId;
	}

	@Column(name = "LEGAL_NAME", nullable = false, length = 100)
	public String getLegalName() {
		return this.legalName;
	}

	public void setLegalName(String legalName) {
		this.legalName = legalName;
	}

	@Column(name = "DISPLAY_NAME", nullable = false, length = 100)
	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	@Column(name = "ADDRESS", nullable = true, length = 1000)
	public String getAddress() {
		return this.address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	@Column(name = "TAN_NUMBER", nullable = true, length = 20)
	public String getTanNumber() {
		return this.tanNumber;
	}

	public void setTanNumber(String tanNumber) {
		this.tanNumber = tanNumber;
	}

	@Column(name = "PAN_NUMBER", nullable = true, length = 20)
	public String getPanNumber() {
		return this.panNumber;
	}

	public void setPanNumber(String panNumber) {
		this.panNumber = panNumber;
	}

	@Column(name = "CERTIFICATE_OF_INCORPORATION", nullable = true, length = 20)
	public String getCertificateOfIncorporation() {
		return this.certificateOfIncorporation;
	}

	public void setCertificateOfIncorporation(String certificateOfIncorporation) {
		this.certificateOfIncorporation = certificateOfIncorporation;
	}

	@Column(name = "BANK_DETAIL", nullable = true, length = 200)
	public String getBankDetail() {
		return this.bankDetail;
	}

	public void setBankDetail(String bankDetail) {
		this.bankDetail = bankDetail;
	}

	@Column(name = "CONTACT_PERSON", nullable = false, length = 100)
	public String getContactPerson() {
		return this.contactPerson;
	}

	public void setContactPerson(String contactPerson) {
		this.contactPerson = contactPerson;
	}

	@Column(name = "CONTACT_PERSON_NUMBER", nullable = false, length = 10)
	public String getContactPersonNumber() {
		return contactPersonNumber;
	}

	public void setContactPersonNumber(String contactPersonNumber) {
		this.contactPersonNumber = contactPersonNumber;
	}

	@Column(name = "CONTACT_PERSON_EMAIL", nullable = true, length = 60)
	public String getContactPersonEmail() {
		return contactPersonEmail;
	}

	public void setContactPersonEmail(String contactPersonEmail) {
		this.contactPersonEmail = contactPersonEmail;
	}

	@Column(name = "ACCOUNT_CONTACT_PERSON", nullable = false, length = 100)
	public String getAccountContactPerson() {
		return accountContactPerson;
	}

	public void setAccountContactPerson(String accountContactPerson) {
		this.accountContactPerson = accountContactPerson;
	}

	@Column(name = "ACCOUNT_CONTACT_PERSON_NUMBER", nullable = false, length = 10)
	public String getAccountContactPersonNumber() {
		return accountContactPersonNumber;
	}

	public void setAccountContactPersonNumber(String accountContactPersonNumber) {
		this.accountContactPersonNumber = accountContactPersonNumber;
	}

	@Column(name = "ACCOUNT_CONTACT_PERSON_EMAIL", nullable = true, length = 60)
	public String getAccountContactPersonEmail() {
		return accountContactPersonEmail;
	}

	public void setAccountContactPersonEmail(String accountContactPersonEmail) {
		this.accountContactPersonEmail = accountContactPersonEmail;
	}

	@Column(name = "CREDIT_DAYS")
	public Integer getCreditDays() {
		return creditDays;
	}

	public void setCreditDays(Integer creditDays) {
		this.creditDays = creditDays;
	}

	@Column(name = "COMPANY_CONTACT", nullable = false, length = 100)
	public String getCompanyContact() {
		return companyContact;
	}

	public void setCompanyContact(String companyContact) {
		this.companyContact = companyContact;
	}

	@Column(name = "ACCOUNT_STATUS", nullable = false, length = 15)
	public String getAccountStatus() {
		return accountStatus;
	}

	public void setAccountStatus(String accountStatus) {
		this.accountStatus = accountStatus;
	}

}
