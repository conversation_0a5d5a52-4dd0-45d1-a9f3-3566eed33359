/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Embeddable;

/**
 * ProductRelationshipId generated by hbm2java
 */
@SuppressWarnings("serial")
@Embeddable
public class ProductRelationshipId implements java.io.Serializable {

	private int productId;
	private int constituentProductId;
	private String relationshipType;
	private int quantity;
	private BigDecimal priceMultiplier;

	public ProductRelationshipId() {
	}

	public ProductRelationshipId(int productId, int constituentProductId, String relationshipType, int quantity,
			BigDecimal priceMultiplier) {
		this.productId = productId;
		this.constituentProductId = constituentProductId;
		this.relationshipType = relationshipType;
		this.quantity = quantity;
		this.priceMultiplier = priceMultiplier;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return this.productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "CONSTITUENT_PRODUCT_ID", nullable = false)
	public int getConstituentProductId() {
		return this.constituentProductId;
	}

	public void setConstituentProductId(int constituentProductId) {
		this.constituentProductId = constituentProductId;
	}

	@Column(name = "RELATIONSHIP_TYPE", nullable = false, length = 10)
	public String getRelationshipType() {
		return this.relationshipType;
	}

	public void setRelationshipType(String relationshipType) {
		this.relationshipType = relationshipType;
	}

	@Column(name = "QUANTITY", nullable = false)
	public int getQuantity() {
		return this.quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	@Column(name = "PRICE_MULTIPLIER", nullable = false, precision = 4, scale = 4)
	public BigDecimal getPriceMultiplier() {
		return this.priceMultiplier;
	}

	public void setPriceMultiplier(BigDecimal priceMultiplier) {
		this.priceMultiplier = priceMultiplier;
	}

	public boolean equals(Object other) {
		if ((this == other)) {
			return true;
		}
		if ((other == null)) {
			return false;
		}
		if (!(other instanceof ProductRelationshipId)) {
			return false;
		}
		ProductRelationshipId castOther = (ProductRelationshipId) other;

		return (this.getProductId() == castOther.getProductId())
				&& (this.getConstituentProductId() == castOther.getConstituentProductId())
				&& ((this.getRelationshipType() == castOther.getRelationshipType())
						|| (this.getRelationshipType() != null && castOther.getRelationshipType() != null
								&& this.getRelationshipType().equals(castOther.getRelationshipType())))
				&& (this.getQuantity() == castOther.getQuantity())
				&& ((this.getPriceMultiplier() == castOther.getPriceMultiplier())
						|| (this.getPriceMultiplier() != null && castOther.getPriceMultiplier() != null
								&& this.getPriceMultiplier().equals(castOther.getPriceMultiplier())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result + this.getProductId();
		result = 37 * result + this.getConstituentProductId();
		result = 37 * result + (getRelationshipType() == null ? 0 : this.getRelationshipType().hashCode());
		result = 37 * result + this.getQuantity();
		result = 37 * result + (getPriceMultiplier() == null ? 0 : this.getPriceMultiplier().hashCode());
		return result;
	}

}
