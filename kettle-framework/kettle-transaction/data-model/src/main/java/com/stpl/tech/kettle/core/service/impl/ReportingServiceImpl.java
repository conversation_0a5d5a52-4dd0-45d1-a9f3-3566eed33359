/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * 
 */
package com.stpl.tech.kettle.core.service.impl;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.api.services.drive.model.File;
import com.stpl.tech.kettle.core.ReportStatus;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.data.vo.RevenueCertificateData;
import com.stpl.tech.kettle.core.file.load.ExcelParser;
import com.stpl.tech.kettle.core.file.management.GoogleSheetLoader;
import com.stpl.tech.kettle.core.file.mappers.ExpenseRowMapper;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.service.ReportingService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.ReportStatusDao;
import com.stpl.tech.kettle.data.dao.ReportingDao;
import com.stpl.tech.kettle.data.model.ReportExecutionDetail;
import com.stpl.tech.kettle.domain.model.ExpenseUpdateEvent;
import com.stpl.tech.kettle.domain.model.IterationType;
import com.stpl.tech.kettle.domain.model.ReportDef;
import com.stpl.tech.kettle.domain.model.ReportStatusEvent;
import com.stpl.tech.kettle.domain.model.UnitExpense;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

/**
 * <AUTHOR>
 *
 */
@Service
public class ReportingServiceImpl implements ReportingService {

	private static final Logger LOG = LoggerFactory.getLogger(ReportingServiceImpl.class);

	@Autowired
	private ReportingDao dao;

	@Autowired
	private ReportStatusDao reportStatusdao;

	@Autowired
	private EnvironmentProperties props;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.kettle.core.service.ReportingService#getReportDefinition(
	 * int)
	 */
	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public ReportDef getReportDefinition(int reportId) throws DataNotFoundException {
		return dao.getReportDefinition(reportId);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.kettle.core.service.ReportingService#
	 * createReportExecutionDetail(int, int)
	 */
	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public ReportExecutionDetail createReportExecutionDetail(int unitId, int reportDefId) throws DataUpdationException {
		return dao.createReportExecutionDetail(unitId, reportDefId);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.kettle.core.service.ReportingService#updateStatus(int,
	 * com.stpl.tech.kettle.core.ReportStatus)
	 */
	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateStatus(int executionDetailId, ReportStatus status) throws DataUpdationException {
		dao.updateStatus(executionDetailId, status);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.kettle.core.service.ReportingService#
	 * getReportExecutionDetail(int)
	 */
	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public ReportExecutionDetail getReportExecutionDetail(int id) {
		return dao.getReportExecutionDetail(id);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public ReportStatusEvent addReportStatusEvent(ReportStatusEvent event) {
		return reportStatusdao.addReportStatusEvent(event);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public ReportStatusEvent getPeviousReportStatusEvent(int unitId, int terminalId) {
		return reportStatusdao.getPeviousReportStatusEvent(unitId, terminalId);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public boolean updateReportStatusEvent(ReportStatusEvent event) {
		return reportStatusdao.updateReportStatusEvent(event);
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public ExpenseUpdateEvent loadExpenseReport(IterationType type, Date date, String addedBy) {
		ExpenseUpdateEvent event = getEventData(TransactionConstants.APP_NAME_EXPENSE_REPORT,
				TransactionConstants.PREFIX_EXPENSE_REPORT, type, date, addedBy);
		GoogleSheetLoader loader = new GoogleSheetLoader();
		try {
			LOG.info("Downloading file {} ", event.getInputFileName());
			File file = loader.getFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, event.getInputFileName(),
					TransactionConstants.MIMETYPE_GOOGLE_SHEETS, type.name());
			if (file == null) {
				return createFailedEvent(event, "Could not locate the file with name " + event.getInputFileName(),
						null);
			}
			loader.downloadFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, file.getId(),
					AppConstants.EXCEL_MIME_TYPE, event.getStoredFileName());
		} catch (IOException e) {
			LOG.error("Failed to download the file", e);
			return createFailedEvent(event, e.getMessage(), null);
		}
		ExcelParser<UnitExpense> parser = new ExcelParser<>(new ExpenseRowMapper());
		List<UnitExpense> expenses = null;
		try {
			expenses = parser.parseExcel(event.getStoredFileName(), 0, 1, 0, 1);
			if (parser.hasErrors()) {
				LOG.error("Errors in excel sheet");
				return createFailedEvent(event, "Parsing Errors", parser.getErrors());
			}
		} catch (IOException e) {
			LOG.error("Failed to load the stored excel file ", e);
			return createFailedEvent(event, e.getMessage(), null);
		}
		if (expenses != null) {
			event.setNoOfRows(expenses.size());
			event.setStatus(ReportStatus.SUCCESS.name());
			event = dao.addExpenseUpdateEvent(event);
			List<UnitExpense> list = dao.addUnitExpenses(event, expenses);
			event.getExpenses().addAll(list);
			dao.updateExpenseResultData(event.getEventId());
		}
		return event;
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public List<UnitExpense> getUnitExpensesForEvent(ExpenseUpdateEvent event) {
		return dao.getUnitExpensesForEvent(event);
	}

	private ExpenseUpdateEvent createFailedEvent(ExpenseUpdateEvent event, String error, List<String> errorDetails) {
		new ErrorNotification("Google Sheet Parsing Failure", error, errorDetails, props.getEnvironmentType())
				.sendEmail();
		event.setErrorMessage(error);
		event.setStatus(ReportStatus.FAILED.name());
		event = dao.addExpenseUpdateEvent(event);
		return event;
	}

	private ExpenseUpdateEvent getEventData(String applicationName, String prefix, IterationType type, Date date,
			String addedBy) {
		ExpenseUpdateEvent event = new ExpenseUpdateEvent();
		event.setType(type);
		Date endDay = addOrderIdRange(event, date);
		int iterationNumber = TransactionUtils.getIterationNumber(type, endDay);
		int year = TransactionUtils.getYear(type, endDay);
		String inputFileName = TransactionUtils.getFileName(prefix, type, endDay);
		event.setInputFileName(inputFileName);
		event.setIterationNumber(iterationNumber);
		event.setYear(year);
		event.setStatus(TransactionConstants.ACTIVE);
		event.setAddedByUserId(0);
		event.setAddedByUserName(addedBy);
		event.setUpdatedByUserId(TransactionConstants.ADMIN_USER_ID);
		event.setUpdatedByUserName("System");
		event.setDescription(String.format("%s report for %s for the date %s on date %s", prefix,
				type.name().toLowerCase(), endDay, date));
		event.setEventTimestamp(AppUtils.getCurrentTimestamp());
		event.setStoredFileName(String.format("%s/%s/input/%s/%s/%s_%s.xlsx", props.getBasePath(), applicationName,
				prefix, type.name(), event.getInputFileName(), AppUtils.getCurrentTimeISTStringWithNoColons()));

		return event;
	}

	private Date addOrderIdRange(ExpenseUpdateEvent event, Date date) {
		Date startDay = null;
		Date endDay = null;
		if (IterationType.WOW.equals(event.getType())) {
			endDay = AppUtils.getLastSunday(date);
			startDay = AppUtils.getLastToLastMonday(endDay);
		} else if (IterationType.MOM.equals(event.getType())) {
			endDay = AppUtils.getLastDayOfPreviousMonth(date);
			startDay = AppUtils.getLastDayOfPreviousMonth(endDay);
		}
		// we do not use these order id for calculation
		// procedure runs on order detail
		int startOrdetrId = dao.getLastOrderIdForBusinessDate(startDay);
		int endOrderId = dao.getLastOrderIdForBusinessDate(endDay);
		event.setStartOrderId(startOrdetrId);
		event.setEndOrderId(endOrderId);
		if (IterationType.WOW.equals(event.getType())) {
			event.setStartDate(AppUtils.getLastToLastMonday(date));
			event.setEndDate(AppUtils.getLastSunday(date));
		} else if (IterationType.MOM.equals(event.getType())) {
			event.setStartDate(AppUtils.getFirstDayOfPreviousMonth(date));
			event.setEndDate(AppUtils.getLastDayOfPreviousMonth(date));
		}
		
		return endDay;
	}

	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public void uploadExpenseReport(String filePath, String fileName, String mimeType,
			ExpenseUpdateEvent expenseUpdateEvent) {
		try {
			LOG.info("Uploading file to google Drive filename:{}", fileName);
			String targetDir = IterationType.WOW.equals(expenseUpdateEvent.getType())
					? TransactionConstants.FOLDER_EXPENSE_OUTPUT_WOW : TransactionConstants.FOLDER_EXPENSE_OUTPUT_MOM;

			new GoogleSheetLoader().uploadExpenseReport(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, filePath,
					fileName, mimeType, targetDir, TransactionConstants.FOLDER_EXPENSE_OUTPUT_PARENT);

		} catch (Exception e) {
			LOG.error("Error in uploading file to google drive", e);
		}
	}

	@Override
	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> getUnitsWithSales(int month, int year) {
		return dao.getUnitWithSales(month,year);
	}

	@Override
	@Transactional(rollbackFor=Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<RevenueCertificateData> getRevenueCertificateData(int month, int year,Integer unitId,List<Integer> exclusionList) {
		return dao.getRevenueCertificateData(month, year,unitId,exclusionList);
	}
	
	
	
}
