package com.stpl.tech.kettle.webengage.converter;

import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderItem;
import com.stpl.tech.kettle.data.model.OrderPaymentDenominationDetail;
import com.stpl.tech.kettle.domain.model.OrderPaymentDenomination;
import com.stpl.tech.kettle.webengage.domain.model.WebengageEvent;
import com.stpl.tech.kettle.webengage.domain.model.WebengageOrder;
import com.stpl.tech.kettle.webengage.domain.model.WebengageUser;
import com.stpl.tech.kettle.webengage.domain.model.WebengageUserAttributes;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by Chaayos on 02-05-2017.
 */
public class WebEngageConverter {

    enum OrderMode {
        CAFE("DINE_IN"),
        COD("DELIVERY"),
        TAKE_AWAY("TAKE_AWAY");

        private String value;

        private OrderMode(String value) {
            this.value = value;
        }
    }

    public static WebengageUser convert(CustomerInfo customerInfo, Integer score) {
        WebengageUser webengageUser = new WebengageUser();
        webengageUser.setEmail(customerInfo.getEmailId() != null ? customerInfo.getEmailId() : "");
        webengageUser.setFirstName(customerInfo.getFirstName() != null ? customerInfo.getFirstName() : "");
        //webengageUser.setLastName(customerInfo.getLastName());
        webengageUser.setPhone(customerInfo.getCountryCode() + customerInfo.getContactNumber());
        webengageUser.setUserId(customerInfo.getContactNumber());
        WebengageUserAttributes webengageUserAttributes = new WebengageUserAttributes();
        webengageUserAttributes.setAquisitionSrc(customerInfo.getAcquisitionSource());
        webengageUserAttributes.setBlacklisted(customerInfo.getIsBlacklisted().equals("Y"));
        webengageUserAttributes.setCountryCode(customerInfo.getCountryCode());
        webengageUserAttributes.setEmailSubscriber(customerInfo.getEmailSubscriber().equals("Y"));
        webengageUserAttributes.setEmailVerified(customerInfo.getIsEmailVerified() != null && customerInfo.getIsEmailVerified().equals("Y"));
        webengageUserAttributes.setNumberVerified(customerInfo.getIsNumberVerified() != null && customerInfo.getIsNumberVerified().equals("Y"));
        webengageUserAttributes.setLoyaltea(score);
        webengageUserAttributes.setAddTime(customerInfo.getAddTime() != null ? new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(customerInfo.getAddTime()):"");
        webengageUserAttributes.setNumberVerificationTime(customerInfo.getNumberVerificationTime() != null ?
                new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(customerInfo.getNumberVerificationTime()):"");
        webengageUser.setAttributes(webengageUserAttributes);
        return webengageUser;
    }

    public static WebengageEvent convert(OrderDetail orderDetail, CustomerInfo customerInfo, String unitName, CustomerAddressInfo addressInfo,
                                         List<String> paymentModes, String settlements) {
        WebengageEvent webengageEvent = new WebengageEvent();
        webengageEvent.setUserId(customerInfo.getContactNumber());
        webengageEvent.setEventName("ORDER_SUCCESS_NEW");
        webengageEvent.setEventTime(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(orderDetail.getBillingServerTime()));
        WebengageOrder webengageOrder = new WebengageOrder();
        //webengageOrder.setAcquisitionSrc("");
        if (addressInfo != null) {
            webengageOrder.setCity(addressInfo.getCity());
            webengageOrder.setLocality(addressInfo.getLocality());
        } else {
            webengageOrder.setCity("");
            webengageOrder.setLocality("");
        }

        Boolean containsSignupOffer = orderDetail.getOfferCode() != null && orderDetail.getOfferCode().equalsIgnoreCase("LOYALTEA");
        webengageOrder.setContainsSignupOffer(Boolean.toString(containsSignupOffer));
        //webengageOrder.setEmpId(Integer.toString(orderDetail.getEmpId()));
        webengageOrder.setOfferCode(orderDetail.getOfferCode() != null ? orderDetail.getOfferCode() : "");
        //List<WebengageOrderItem> webengageOrderItems = new ArrayList<>();
        List<Integer> giftCardList = new ArrayList<Integer>();
        giftCardList.add(1026);
        giftCardList.add(1027);
        giftCardList.add(1048);
        giftCardList.add(1056);
        boolean giftCardOnly = true;
        List<String> orderItems = new ArrayList<>();
        for (OrderItem orderItem : orderDetail.getOrderItems()) {
            StringBuilder item = new StringBuilder();
            /*WebengageOrderItem webengageOrderItem = new WebengageOrderItem();
            webengageOrderItem.setAmount(orderItem.getTotalAmount());
            webengageOrderItem.setDimension(orderItem.getDimension());
            webengageOrderItem.setPrice(orderItem.getPrice());
            webengageOrderItem.setProductId(orderItem.getProductId());
            webengageOrderItem.setProductName(orderItem.getProductName());
            webengageOrderItem.setQuantity(orderItem.getQuantity());
            webengageOrderItem.setHasBeenRedeemed(false);
            ComplimentaryDetail complimentaryDetail = new ComplimentaryDetail();
            complimentaryDetail.setIsComplimentary(orderItem.getIsComplimentary()!=null && orderItem.getIsComplimentary().equalsIgnoreCase("Y"));
            complimentaryDetail.setReason(orderItem.getComplimentaryReason());
            complimentaryDetail.setReasonCode(orderItem.getComplimentaryTypeId());
            webengageOrderItem.setComplimentaryDetail(complimentaryDetail);
            webengageOrderItems.add(webengageOrderItem);*/
            item.append(orderItem.getProductName()).append("x").append(orderItem.getQuantity());
            orderItems.add(item.toString());
            if (giftCardOnly) {
                giftCardOnly = giftCardList.contains(orderItem.getProductId());
            }
        }
        webengageOrder.setOrderItems(String.join(",", orderItems));
        webengageOrder.setGiftCardOnly(giftCardOnly);
        webengageOrder.setOrderMode(OrderMode.valueOf(orderDetail.getOrderSource()).value);
        webengageOrder.setOrderSource(orderDetail.getOrderSource());
        //webengageOrder.setOrderSrc("KETTLE");
        webengageOrder.setOutlet(unitName);
        webengageOrder.setPaidAmount(orderDetail.getSettledAmount() != null ? orderDetail.getSettledAmount().floatValue() : null);
        webengageOrder.setTaxableAmount(orderDetail.getTaxableAmount() != null ? orderDetail.getTaxableAmount().floatValue() : null);
        webengageOrder.setTotalAmount(orderDetail.getTotalAmount() != null ? orderDetail.getTotalAmount().floatValue() : null);
        webengageOrder.setTotalDiscount(orderDetail.getTotalDiscount() != null ? orderDetail.getTotalDiscount().floatValue() : null);
        webengageOrder.setUnitId(orderDetail.getUnitId());
        webengageOrder.setUnitName(unitName);
        if (orderDetail.getOrderSourceId() != null && orderDetail.getOrderSourceId().contains("NEO-")) {
            webengageOrder.setExternalOrderId(orderDetail.getOrderSourceId().replace("NEO-", ""));
            webengageOrder.setHost("https://cafes.chaayos.com");
            webengageOrder.setOrderSrc("WEBAPP_MOBILE");
        } else {
            webengageOrder.setExternalOrderId("");
            webengageOrder.setHost("http://prod.kettle.chaayos.com:9797");
            webengageOrder.setOrderSrc("KETTLE");
        }
        //webengageOrder.setDeviceId(orderDetail.getTerminalId()!=null?orderDetail.getTerminalId().toString():"");
        //webengageOrder.setCustomerId("");
        webengageOrder.setGeneratedOrderId(orderDetail.getGeneratedOrderId());
        //List<WebengageOrderMatadata> webengageOrderMatadatas = new ArrayList<>();
        /*for (OrderMetadataDetail orderMetadataDetail:orderMetadataDetails) {
            WebengageOrderMatadata webengageOrderMatadata  = new WebengageOrderMatadata();
            webengageOrderMatadata.setAttributeName(orderMetadataDetail.getAttributeName());
            webengageOrderMatadata.setAttributeValue(orderMetadataDetail.getAttributeValue());
            webengageOrderMatadatas.add(webengageOrderMatadata);
        }*/
        //webengageOrder.setMetadata(webengageOrderMatadatas);
        webengageOrder.setPaymentMode(String.join(",", paymentModes));
        webengageOrder.setSettlements(settlements);
        webengageOrder.setPointsRedeemed(orderDetail.getPointsRedeemed());
        webengageOrder.setBillingServerTime(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(orderDetail.getBillingServerTime()));
        webengageEvent.setEventData(webengageOrder);
        return webengageEvent;
    }

    public static String convertDate(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(date);
    }

    public static OrderPaymentDenomination convert(OrderPaymentDenominationDetail orderPaymentDenominationDetail) {
        OrderPaymentDenomination orderPaymentDenomination = new OrderPaymentDenomination();
        orderPaymentDenomination.setCount(orderPaymentDenominationDetail.getCount());
        orderPaymentDenomination.setDenominationDetailId(orderPaymentDenominationDetail.getDenominationId());
        orderPaymentDenomination.setId(orderPaymentDenominationDetail.getId());
        //orderPaymentDenomination.setOrderId(orderPaymentDenominationDetail.getOrderId());
        orderPaymentDenomination.setSettlementId(orderPaymentDenomination.getSettlementId());
        orderPaymentDenomination.setTotalAmount(orderPaymentDenominationDetail.getTotalAmount());
        return orderPaymentDenomination;
    }

    /*public static void main(String[] args){
        System.out.println(OrderMode.valueOf("CAFE").value);
    }*/

}
