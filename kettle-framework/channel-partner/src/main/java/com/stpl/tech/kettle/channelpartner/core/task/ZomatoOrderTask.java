package com.stpl.tech.kettle.channelpartner.core.task;

import com.stpl.tech.kettle.channelpartner.core.service.ZomatoService;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

public class ZomatoOrderTask implements Runnable {

    private static final Logger LOG = LoggerFactory.getLogger(ZomatoOrderTask.class);

    private ZomatoService zomatoService;
    private volatile PartnerOrderDetail partnerOrderDetail;
    private volatile Order order;
    private volatile boolean isManual;
    private volatile boolean skipInventoryCheck;
    private volatile String requestId;

    public PartnerOrderDetail getPartnerOrderDetail() {
        return partnerOrderDetail;
    }

    public void setPartnerOrderDetail(PartnerOrderDetail partnerOrderDetail) {
        this.partnerOrderDetail = partnerOrderDetail;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public Boolean getManual() {
        return isManual;
    }

    public void setManual(Boolean manual) {
        isManual = manual;
    }

    public Boolean getSkipInventoryCheck() {
        return skipInventoryCheck;
    }

    public void setSkipInventoryCheck(Boolean skipInventoryCheck) {
        this.skipInventoryCheck = skipInventoryCheck;
    }

    public ZomatoService getZomatoService() {
        return zomatoService;
    }

    public void setZomatoService(ZomatoService zomatoService) {
        this.zomatoService = zomatoService;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    @Override
    public void run() {
        try {
            MDC.put("request.id", requestId);
            zomatoService.placeOrder(partnerOrderDetail, order, isManual, skipInventoryCheck);
            zomatoService.notifyOrder(partnerOrderDetail, isManual);
            MDC.clear();
        } catch (Exception ex) {
            LOG.error("Error processing zomato order task ", ex);
        }
    }
}
