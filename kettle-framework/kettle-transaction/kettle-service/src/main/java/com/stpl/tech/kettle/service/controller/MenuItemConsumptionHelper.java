package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.data.model.MenuProductCogsDrilldown;
import com.stpl.tech.kettle.data.model.MenuProductCostData;
import com.stpl.tech.kettle.data.util.DesiChaiConsumptionHelper;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.ConsumptionData;
import com.stpl.tech.master.domain.model.ConsumptionDataDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdNameValue;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.QuantityData;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.model.ResponseData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class MenuItemConsumptionHelper {

    private static final Logger LOG = LoggerFactory.getLogger(MenuItemConsumptionHelper.class);

    @Autowired
    private RecipeCache recipeCache;
    @Autowired
    private MasterDataCache dataCache;
    @Autowired
    private OrderManagementService orderService;

    public void getConsumption(Order o, Map<IdCodeName, Map<IdNameValue, Consumable>> itemMap,
                               Map<Integer, ConsumptionDataDetail> priceMap, Integer deliveryUnitId,
                               Map<IdCodeName, Map<IdNameValue, QuantityData>> soldQuantities) {

        for (OrderItem item : o.getOrders()) {
            Map<Integer, Consumable> consumableMap = new HashMap<>();

            String recipeProfile = getRecipeProfile(item.getProductId(), o.getUnitId(), deliveryUnitId, o.getSource(),
                    item.getDimension(), o.getBrandId());
            // prepare a list of item consumable for a particular item
            boolean taxable = dataCache.getProduct(item.getProductId()).isTaxableCogs();
            boolean pickDineInConsumablesFlag = dataCache.pickDineInConsumablesProduct(o.getUnitId(), item.getProductId(), item.getDimension());
            parseItem(item, o.getSource(), consumableMap, o.getUnitId(), deliveryUnitId, recipeProfile, o.getBrandId(), taxable, pickDineInConsumablesFlag);
            // add all the order item consumables to the order consumables
            String source = (item.getTakeAway() != null && item.getTakeAway()) ? AppConstants.TAKE_AWAY : o.getSource();
            // save to Product Recipe Map
            addToItemRecipeMap(itemMap, item, source, consumableMap, priceMap, soldQuantities, o.getOrderType(), o.getBrandId(), taxable);
        }
    }

    private void addToItemRecipeMap(Map<IdCodeName, Map<IdNameValue, Consumable>> itemMap, OrderItem item,
                                    String source, Map<Integer, Consumable> consumableMap, Map<Integer, ConsumptionDataDetail> priceMap,
                                    Map<IdCodeName, Map<IdNameValue, QuantityData>> soldQuantities, String orderType, Integer brandId, boolean taxable) {
        IdCodeName recipeKey = new IdCodeName(item.getProductId(), item.getDimension(),
                String.valueOf(item.getRecipeId()));
        addToConsumptionMap(itemMap, consumableMap, recipeKey, source, priceMap, item);
        addToQuantityMap(soldQuantities, recipeKey, source, item, orderType, brandId, taxable);
    }

	private void addToQuantityMap(Map<IdCodeName, Map<IdNameValue, QuantityData>> soldQuantities, IdCodeName recipeKey,
			String source, OrderItem item, String orderType, Integer brandId, boolean taxable) {
		Map<IdNameValue, QuantityData> quantities = soldQuantities.get(recipeKey);
		if (quantities == null) {
			quantities = new HashMap<>();
		}

		IdNameValue priceKey = new IdNameValue(item.getProductId(), source, source, item.getPrice());
		Integer sourceQuantity = quantities.get(priceKey) != null ? quantities.get(priceKey).getQuantity() : 0;
		Integer itemQuantity = sourceQuantity + item.getQuantity();

		Map<Integer, Map<String, Pair<Integer, Integer>>> brandWiseQuantity = quantities.containsKey(priceKey)
				? quantities.get(priceKey).getBrandwiseQuantity()
				: new HashMap<>();

		Map<String, Pair<Integer, Integer>> orderTypeQuantity = brandWiseQuantity.containsKey(brandId)
				? brandWiseQuantity.get(brandId)
				: new HashMap<>();

		Pair<Integer, Integer> orderTypeQuantityValue = orderTypeQuantity.containsKey(orderType)
				? orderTypeQuantity.get(orderType)
				: new Pair<Integer, Integer>(0, 0);
		Pair<Integer, Integer> itemOrderTypeQuantityValue = new Pair<>(
				orderTypeQuantityValue.getKey() == 0 ? +item.getQuantity()
						: orderTypeQuantityValue.getKey() + item.getQuantity(),
				taxable ? orderTypeQuantityValue.getValue() == 0 ? +item.getQuantity()
						: orderTypeQuantityValue.getValue() + item.getQuantity() : 0);

		orderTypeQuantity.put(orderType, itemOrderTypeQuantityValue);

		brandWiseQuantity.put(brandId, orderTypeQuantity);
		if (!quantities.containsKey(priceKey)) {
			QuantityData quantityData = new QuantityData(itemQuantity, taxable ? itemQuantity : 0, brandWiseQuantity);
			quantities.put(priceKey, quantityData);
		} else {
			quantities.get(priceKey).setQuantity(itemQuantity);
			quantities.get(priceKey).setTaxableQuantity(taxable ? itemQuantity : 0);
			quantities.get(priceKey).setBrandwiseQuantity(brandWiseQuantity);
			quantities.put(priceKey, quantities.get(priceKey));
		}
		soldQuantities.put(recipeKey, quantities);
	}

    private void addToConsumptionMap(Map<IdCodeName, Map<IdNameValue, Consumable>> itemMap,
                                     Map<Integer, Consumable> consumableMap, IdCodeName recipeKey, String source,
                                     Map<Integer, ConsumptionDataDetail> priceMap, OrderItem item) {
        Map<IdNameValue, Consumable> sourceMap = itemMap.get(recipeKey);
        if (sourceMap == null) {
            consumableMap.values().forEach(c -> c.setPrice(priceMap.get(c.getProductId()).getPrice()));
            consumableMap.values().forEach(c -> c.setTaxPercentage(priceMap.get(c.getProductId()).getTaxPercentage()));
            sourceMap = consumableMap.values().stream()
                    .collect(Collectors.toMap(
                            c -> new IdNameValue(c.getProductId(), source, c.getUom(), item.getQuantity()),
                            Function.identity()));
        } else {
            for (Consumable c : consumableMap.values()) {
                IdNameValue sourceKey = new IdNameValue(c.getProductId(), source, c.getUom());
                Consumable lookup = sourceMap.get(sourceKey);
                if (lookup != null) {
                    lookup.setQuantity(AppUtils.add(c.getQuantity(), lookup.getQuantity()));
                    lookup.setTaxableQuantity(AppUtils.add(c.getTaxableQuantity(), lookup.getTaxableQuantity()));
                } else {
                    lookup = c;
                    lookup.setPrice(priceMap.get(c.getProductId()).getPrice());
                    lookup.setTaxPercentage(priceMap.get(c.getProductId()).getTaxPercentage());
                }
                sourceKey.setValue(AppUtils.add(sourceKey.getValue(), BigDecimal.valueOf(item.getQuantity())));
                sourceMap.put(sourceKey, lookup);
            }
        }
        itemMap.put(recipeKey, sourceMap);
    }

    private void parseItem(OrderItem item, String source, Map<Integer, Consumable> map, int unitId,
                           Integer deliveryUnitId, String recipeProfile, int brandId, boolean taxable, boolean pickDineInConsumablesFlag) {
        // parse recipe composition for the order item from the recipe cache and add to
        // consumable map
        parseComposition(item, source, map, unitId, deliveryUnitId, recipeProfile, brandId, taxable, pickDineInConsumablesFlag);
        // add takeaway consumables to the consumable map for the order item
        addSupplements(item.getTakeAway(), item.getProductId(), item.getDimension(), new BigDecimal(item.getQuantity()),
                source, map, unitId, deliveryUnitId, false, recipeProfile, taxable,Objects.nonNull(item.getMilkVariant()), pickDineInConsumablesFlag);
    }

    private void parseComposition(OrderItem item, String source, Map<Integer, Consumable> map, int unitId,
                                  Integer deliveryUnitId, String recipeProfile, int brandId, boolean taxable, boolean pickDineInConsumablesFlag) {

        Consumable c = null;
        String consumptionHelperProfile = Objects.nonNull(item.getMilkVariant()) ?
                AppUtils.getMilkVariantPaidAddonPrefix(item.getMilkVariant().getProductName()) + recipeProfile : recipeProfile;
        OrderItemComposition composition = item.getComposition();
        if (composition != null) {

            // Variants
            if (composition.getVariants() != null && !composition.getVariants().isEmpty()) {
                if (DesiChaiConsumptionHelper.getInstance(consumptionHelperProfile).getProducts().contains(item.getProductId())) {
                    List<Consumable> consumptions = DesiChaiConsumptionHelper.getInstance(consumptionHelperProfile)
                            .getConsumption(item, composition.getVariants());
                    for (Consumable con : consumptions) {
                        addToConsumableMap(con, map, taxable);
                    }

                } else {
                    for (IngredientVariantDetail detail : composition.getVariants()) {
                        c = createConsumable(detail, item.getQuantity());
                        addToConsumableMap(c, map, taxable);
                    }
                }
            }

            // products
            if (composition.getProducts() != null && !composition.getProducts().isEmpty()) {
                for (IngredientProductDetail detail : composition.getProducts()) {
                    c = createConsumable(detail, item.getQuantity(), false, taxable);
                    addToConsumableMap(c, map, taxable);
                }
            }

            // Add-ons
            if (composition.getAddons() != null && !composition.getAddons().isEmpty()) {
                for (IngredientProductDetail detail : composition.getAddons()) {
                    // Add-ons are menu products
                    addSupplements(item.getTakeAway(), detail.getProduct().getProductId(),
                            detail.getDimension().getCode(),
                            detail.getQuantity().multiply(new BigDecimal(item.getQuantity())), source, map, unitId,
                            deliveryUnitId, true, recipeProfile, taxable,false, pickDineInConsumablesFlag);
                }
            }

            // Menu Products
            if (composition.getMenuProducts() != null && !composition.getMenuProducts().isEmpty()) {
                for (OrderItem i : composition.getMenuProducts()) {
                    String profile = getRecipeProfile(i.getProductId(), unitId, deliveryUnitId, source,
                            i.getDimension(), brandId);
                    boolean taxable1 = dataCache.getProduct(i.getProductId()).isTaxableCogs();
                    parseItem(i, source, map, unitId, deliveryUnitId, profile, brandId, taxable1,pickDineInConsumablesFlag);
                }
            }
        }
    }

    private void addSupplements(Boolean takeAway, int productId, String dimension, BigDecimal qty, String source,
                                Map<Integer, Consumable> consumableMap, int unitId, Integer deliveryUnitId, boolean addon,
                                String recipeProfile, boolean taxable,boolean containsMilkVariant, boolean pickDineInConsumablesFlag) {

        LOG.info("Looking Consumption in MenuItem for pickDineInConsumablesFlag {}, productId {}, dimension {}, qty {}, source {}, profile {}", pickDineInConsumablesFlag, productId, dimension,
                qty, source, recipeProfile);
        RecipeDetail r = recipeCache.getRecipe(productId, dimension, recipeProfile);
        if (r == null) {
            r = recipeCache.getRecipe(productId, dimension, AppConstants.DEFAULT_RECIPE_PROFILE);
        }
        if (r != null) {
            r.getIngredient().getComponents().stream().filter(component -> !containsMilkVariant ||
                    Objects.isNull(component.getProduct()) || AppConstants.SCM_MILK_PRODUCT_ID != component.getProduct().getProductId()
            ).forEach(p -> addToConsumableMap(createConsumable(p, qty, addon, taxable), consumableMap, taxable));
            switch (UnitCategory.valueOf(source)) {
                case COD:
                    addSupplementaryItem(qty, consumableMap, r.getDeliveryConsumables(), addon, taxable);
                    break;
                case CAFE:
                    if (takeAway != null && takeAway) {
                        addSupplementaryItem(qty, consumableMap, r.getTakeawayConsumables(), addon, taxable);
                    } else if(pickDineInConsumablesFlag){
                        addSupplementaryItem(qty, consumableMap, r.getDineInConsumables(), addon, taxable);
                    }
                    break;
                case TAKE_AWAY:
                    addSupplementaryItem(qty, consumableMap, r.getTakeawayConsumables(), addon, taxable);
                    break;
                default:
                    break;
            }
        }
    }

    private String getRecipeProfile(int productId, int unitId, int deliveryUnitId, String source, String dimension,
                                    int brandId) {
        String recipeProfile = null;

        Product product = dataCache.getProduct(productId);
        if (product.getClassification() == ProductClassification.FREE_ADDON
                || product.getClassification() == ProductClassification.PAID_ADDON) {
            // GET default recipe profile in case of ADDON
            recipeProfile = AppConstants.DEFAULT_RECIPE_PROFILE;
        } else {
            if (UnitCategory.COD.equals(UnitCategory.valueOf(source))) {
                // TODO check this if it breaks anything
                if (brandId == AppConstants.CHAAYOS_BRAND_ID) {
                    try {
                        recipeProfile = recipeCache.getUnitProductProfile(unitId, productId, dimension);
                    } catch (Exception e) {
                        recipeProfile = recipeCache.getUnitProductProfile(deliveryUnitId, productId, dimension);
                    }

                } else {
                    try {
                        recipeProfile = recipeCache.getUnitProductProfile(deliveryUnitId, productId, dimension);
                    } catch (Exception e) {
                        recipeProfile = recipeCache.getUnitProductProfile(unitId, productId, dimension);
                    }
                }

            } else {
                recipeProfile = recipeCache.getUnitProductProfile(unitId, productId, dimension);
            }
        }
        return recipeProfile;
    }

    private void addSupplementaryItem(BigDecimal qty, Map<Integer, Consumable> map,
                                      List<IngredientProductDetail> ingredients, boolean addon, boolean taxable) {
        Consumable c = null;
        for (IngredientProductDetail i : ingredients) {
            c = createConsumable(i, qty, addon, taxable);
            addToConsumableMap(c, map, taxable);
        }
    }

    private Consumable createConsumable(IngredientProductDetail detail, int qty, boolean addon, boolean taxable) {
        return createConsumable(detail, new BigDecimal(qty), addon, taxable);
    }

    private Consumable createConsumable(IngredientProductDetail detail, BigDecimal qty, boolean addon, boolean taxable) {
        Consumable c = new Consumable();
        c.setProductId(detail.getProduct().getProductId());
        c.setQuantity(detail.getQuantity().multiply(qty));
        c.setUom(detail.getUom() == null ? null : detail.getUom().name());
        c.setName(detail.getProduct().getName());
        c.setAddOn(addon);
        if (taxable) {
            c.setTaxableQuantity(detail.getQuantity().multiply(qty));
        }
        return c;
    }

    private Consumable createConsumable(IngredientVariantDetail detail, int qty) {
        Consumable c = new Consumable();
        c.setProductId(detail.getProductId());
        BigDecimal consumbaleQty = AppUtils.multiplyWithScale10(detail.getQuantity(), new BigDecimal(qty));
        c.setQuantity(consumbaleQty);
        c.setUom(detail.getUom() == null ? null : detail.getUom().name());
        c.setName(detail.getAlias());
        return c;
    }

    private void addToConsumableMap(Consumable c, Map<Integer, Consumable> map, boolean taxable) {
        Consumable e = map.get(c.getProductId());
        if (e != null) {
            if (taxable) {
                e.setTaxableQuantity(AppUtils.add(e.getTaxableQuantity(), c.getQuantity()));
            }
            e.setQuantity(e.getQuantity().add(c.getQuantity()));
        } else {
            map.put(c.getProductId(), c);
        }
    }

    @Transactional(value = "TransactionDataSourceTM", readOnly = false, rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void processMenuProductCogsForUnit(ResponseData<ConsumptionData> response, List<Order> orders)
            throws DataNotFoundException, DataUpdationException {
        if (response != null && response.getPayload() != null) {
            if (response.getPayload() instanceof ConsumptionData) {
                ConsumptionData data = response.getPayload();
                if (data.getConsumables().isEmpty()) { // in case no COGS found to process, just return
                    LOG.info(
                            "::::::::::::::::::::::: NO COGS found to process MENU COGS for unit {} for businessDate {} :::::::::::::::",
                            data.getUnitId(), data.getBusinessDate());
                    return;
                }
                Map<Integer, ConsumptionDataDetail> priceMap = makePriceMap(data);
                Map<IdCodeName, Map<IdNameValue, QuantityData>> soldQuantities = new HashMap<>();
                Map<IdCodeName, Map<IdNameValue, Consumable>> productMap = makeProductMap(orders, priceMap,
                        soldQuantities);
                Map<IdCodeName, Map<String, List<MenuProductCogsDrilldown>>> drilldowns = getDrilldowns(productMap, soldQuantities);
                saveCogsData(drilldowns, data.getClosureId(), soldQuantities, data.getUnitId());
            } else {
                throw new DataNotFoundException("Could not find price data for Menu Cogs");
            }
        } else {
            if (response != null) {
                throw new DataNotFoundException("Could not find price data: " + response.getMessage());
            }
            throw new DataNotFoundException("Could not find price data for menu COGs");
        }
    }

    private void saveCogsData(Map<IdCodeName, Map<String, List<MenuProductCogsDrilldown>>> drilldowns, int closureId,
                              Map<IdCodeName, Map<IdNameValue, QuantityData>> soldQuantities, Integer unitId) throws DataUpdationException {
        for (IdCodeName recipeProductKey : drilldowns.keySet()) {
            Map<IdNameValue, QuantityData> soldQuantityMap = soldQuantities.get(recipeProductKey);
            saveAggregate(recipeProductKey, drilldowns.get(recipeProductKey), closureId, soldQuantityMap, unitId);
        }
    }

    private void saveAggregate(IdCodeName recipeProductKey, Map<String, List<MenuProductCogsDrilldown>> drilldowns, int closureId,
                               Map<IdNameValue, QuantityData> soldQuantityMap, Integer unitId) throws DataUpdationException {
        List<String> sources = Arrays.asList(UnitCategory.CAFE.name(), UnitCategory.COD.name(),
                UnitCategory.TAKE_AWAY.name());
        for (String source : sources) {
            Product pd = dataCache.getProduct(recipeProductKey.getId());
            MenuProductCostData cogs = new MenuProductCostData(closureId, recipeProductKey);
            cogs.setSource(source);
            cogs.setMenuProductName(pd.getName());
            prepareAndSaveAggregate(source, drilldowns, cogs, soldQuantityMap, recipeProductKey, unitId);
        }
    }

    private void prepareAndSaveAggregate(String source, Map<String, List<MenuProductCogsDrilldown>> drilldowns,
                                         MenuProductCostData cogs, Map<IdNameValue, QuantityData> soldQuantityMap, IdCodeName recipeProductKey,
                                         Integer unitId) throws DataUpdationException {

        IdNameValue priceKey = findPriceKey(soldQuantityMap.keySet(), source, recipeProductKey);

        // in case product is not sold on unit, return without saving menu cogs
        if (priceKey == null) {
            return;
        }

        for (String brandOrderKey : drilldowns.keySet()) {
            BigDecimal addOnCost = BigDecimal.ZERO;
            BigDecimal ingredientCost = BigDecimal.ZERO;

            BigDecimal taxableCost = BigDecimal.ZERO;
            BigDecimal taxAmount = BigDecimal.ZERO;
            BigDecimal totalCost = BigDecimal.ZERO;

            List<MenuProductCogsDrilldown> x = drilldowns.get(brandOrderKey);
            String[] brandOrderArray = brandOrderKey.split("_");
            List<MenuProductCogsDrilldown> filteredList = x.stream().filter(d -> d.getSource().equalsIgnoreCase(source))
                    .collect(Collectors.toList());
            if (filteredList == null || filteredList.size() == 0) {
                continue;
            }
            for (MenuProductCogsDrilldown d : filteredList) {
                if (d.getAddOn().equalsIgnoreCase(AppConstants.YES)) {
                    addOnCost = AppUtils.add(addOnCost, d.getCost());
                } else {
                    ingredientCost = AppUtils.add(ingredientCost, d.getCost());
                }
                taxableCost = AppUtils.add(taxableCost, d.getTaxableCost());
                taxAmount = AppUtils.add(taxAmount, d.getTaxAmount());
                totalCost = AppUtils.add(totalCost, d.getCost());
            }
            MenuProductCostData cogsData = new MenuProductCostData();
            cogsData.setClosureId(cogs.getClosureId());
            cogsData.setMenuProductId(cogs.getMenuProductId());
            cogsData.setRecipeId(cogs.getRecipeId());
            cogsData.setDimension(cogs.getDimension());
            cogsData.setMenuProductName(cogs.getMenuProductName());
            cogsData.setSource(cogs.getSource());
            cogsData.setUnitId(unitId);
            cogsData.setAddOnCost(addOnCost);
            cogsData.setIngredientCost(ingredientCost);
            cogsData.setTotalCost(AppUtils.add(addOnCost, ingredientCost));
            cogsData.setGenerationTime(AppUtils.getCurrentTimestamp());
            cogsData.setTaxableCost(taxableCost);
            cogsData.setTaxableAmount(taxAmount);
            cogsData.setTotalAmount(totalCost);
            cogsData.setBrandId(Integer.parseInt(brandOrderArray[0]));
            cogsData.setOrderType(brandOrderArray[1]);
            cogsData.setQuantity(soldQuantityMap.get(priceKey).getBrandwiseQuantity()
                    .get(Integer.valueOf(brandOrderArray[0])).get(brandOrderArray[1]).getKey());
            cogsData.setTaxableQuantity(soldQuantityMap.get(priceKey).getBrandwiseQuantity()
                    .get(Integer.valueOf(brandOrderArray[0])).get(brandOrderArray[1]).getValue());
            cogsData.setMenuPrice(priceKey.getValue());
            orderService.addCogsData(cogsData, filteredList);
        }
    }

    private IdNameValue findPriceKey(Set<IdNameValue> priceKeySet, String source, IdCodeName recipeProductKey) {
        Optional<IdNameValue> price = priceKeySet.stream().filter(
                idNameValue -> idNameValue.getId() == recipeProductKey.getId() && idNameValue.getName().equals(source))
                .findFirst();
        return price.orElse(null);
    }

    private Map<IdCodeName, Map<String, List<MenuProductCogsDrilldown>>> getDrilldowns(
            Map<IdCodeName, Map<IdNameValue, Consumable>> productMap,
            Map<IdCodeName, Map<IdNameValue, QuantityData>> soldQuantities) {
        Map<IdCodeName, Map<String, List<MenuProductCogsDrilldown>>> drilldownMap = new HashMap<>();


        for (IdCodeName recipeProductKey : productMap.keySet()) {
            Map<IdNameValue, Consumable> sourceMap = productMap.get(recipeProductKey);
            Map<String, List<MenuProductCogsDrilldown>> mapDrillDowns = new HashMap<>();
            for (IdNameValue sourceKey : sourceMap.keySet()) {
                if (soldQuantities.containsKey(recipeProductKey)) {
                    IdNameValue priceKey = findPriceKey(soldQuantities.get(recipeProductKey).keySet(), sourceKey.getName(), recipeProductKey);
                    QuantityData quantityData = soldQuantities.containsKey(recipeProductKey) ? soldQuantities.get(recipeProductKey).containsKey(priceKey) ? soldQuantities.get(recipeProductKey).get(priceKey) : new QuantityData() : new QuantityData();
                    if (quantityData.getBrandwiseQuantity() != null && quantityData.getBrandwiseQuantity().size() > 0) {
                        quantityData.getBrandwiseQuantity().forEach((brandId, map) -> {
                            if (map.size() > 0) {
                                map.forEach((orderType, quantity) -> {
                                    String brandOrderKey = brandId + "_" + orderType;
                                    List<MenuProductCogsDrilldown> drilldowns = mapDrillDowns.getOrDefault(brandOrderKey,
                                            new ArrayList<>());

                                    Consumable c = sourceMap.get(sourceKey);
                                    BigDecimal netQuantity = AppUtils.divideWithScale10(
                                            AppUtils.multiplyWithScale10(new BigDecimal(quantity.getKey()), c.getQuantity()),
                                            new BigDecimal(quantityData.getQuantity()));
                                    BigDecimal netTaxableQuantity = AppUtils.divideWithScale10(
                                            AppUtils.multiplyWithScale10(new BigDecimal(quantity.getKey()), c.getTaxableQuantity()),
                                            new BigDecimal(quantityData.getQuantity()));
                                    MenuProductCogsDrilldown drilldown = new MenuProductCogsDrilldown();
                                    drilldown.setScmProductId(sourceKey.getId());
                                    drilldown.setSource(sourceKey.getName());
                                    drilldown.setAddOn(c.isAddOn() ? AppConstants.YES : AppConstants.NO);
                                    drilldown.setPrice(c.getPrice());
                                    drilldown.setQuantity(netQuantity);
                                    drilldown.setCost(AppUtils.multiplyWithScale10(netQuantity, c.getPrice()));
                                    drilldown.setTaxableQuantity(netTaxableQuantity);
                                    drilldown.setTaxableCost(AppUtils.multiplyWithScale10(netTaxableQuantity, c.getPrice()));
                                    drilldown.setTaxPercentage(c.getTaxPercentage());
                                    drilldown.setTaxAmount(
                                            AppUtils.percentOfWithScale10(drilldown.getTaxableCost(), c.getTaxPercentage()));
                                    drilldowns.add(drilldown);
                                    mapDrillDowns.put(brandOrderKey, drilldowns);
                                });
                            }
                        });
                    }
                }
            }
            drilldownMap.put(recipeProductKey, mapDrillDowns);
        }
        return drilldownMap;
    }

    private Map<IdCodeName, Map<IdNameValue, Consumable>> makeProductMap(List<Order> orders,
                                                                         Map<Integer, ConsumptionDataDetail> priceMap, Map<IdCodeName, Map<IdNameValue, QuantityData>> soldQuantities) {
        Map<IdCodeName, Map<IdNameValue, Consumable>> consumption = new HashMap<>();
        List<Order> nonCancelledOrders = orders.stream().filter(o -> !TransactionUtils.isSkipOrder(o))
                .collect(Collectors.toList());
        for (Order order : nonCancelledOrders) {
            /*
             * int deliveryUnitId = dataCache.getDeliveryUnit(order.getUnitId(),
             * TransactionUtils
             * .isPartnetOrder(masterCache.getChannelPartner(order.getChannelPartner()).
             * getType()));
             */
            int deliveryUnitId = dataCache.getDeliveryUnit(order.getUnitId(), order.getChannelPartner(),
                    order.getBrandId(), TransactionUtils.isCODOrder(order.getSource()));
            getConsumption(order, consumption, priceMap, deliveryUnitId, soldQuantities);
        }
        return consumption;
    }

    private Map<Integer, ConsumptionDataDetail> makePriceMap(ConsumptionData data) {
        Map<Integer, ConsumptionDataDetail> priceMap = new HashMap<>();
        data.getConsumables().forEach(consumable -> priceMap.put(consumable.getProductId(), new ConsumptionDataDetail(consumable.getPrice(), consumable.getTaxPercentage())));
        return priceMap;
    }
    

    @Transactional(value = "TransactionDataSourceTM", readOnly = false, rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
	public void deleteMenuProductCogsForUnit(int unitId, int clousureId) throws DataUpdationException {
         orderService.deleteCogsData(unitId,clousureId);
		
	}
}
