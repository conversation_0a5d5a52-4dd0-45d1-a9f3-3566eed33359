package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UnitBrandMappingDomain implements Serializable {

    private Integer id;
    private Integer unitId;
    private Integer brandId;
    private String status;
    private Date creationTimestamp;
    private Integer createdBy;
    private Date updationTimestamp;
    private Integer updatedBy;

}
