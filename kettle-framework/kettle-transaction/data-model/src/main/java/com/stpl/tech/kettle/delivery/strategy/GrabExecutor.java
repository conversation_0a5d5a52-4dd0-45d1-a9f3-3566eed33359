/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stpl.tech.kettle.core.DeliveryConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.util.RequestSigner;
import com.stpl.tech.kettle.delivery.adapter.GrabRequestAdapter;
import com.stpl.tech.kettle.delivery.adapter.GrabResponseAdapter;
import com.stpl.tech.kettle.delivery.model.AuthorizationObject;
import com.stpl.tech.kettle.delivery.model.DeliveryRequest;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.delivery.model.GrabMerchant;
import com.stpl.tech.kettle.delivery.model.GrabOrderRequest;
import com.stpl.tech.kettle.delivery.model.GrabOrderResponse;
import com.stpl.tech.kettle.delivery.model.GrabRequest;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.domain.model.Unit;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

public class GrabExecutor extends AbstractDeliveryStrategy implements DeliveryExecutionStrategy{

	private static Logger LOG = LoggerFactory.getLogger(GrabExecutor.class);

	private GrabRequest requestObject = new GrabOrderRequest();
	private GrabRequestAdapter requestAdapter = new GrabRequestAdapter();

	private GrabOrderResponse responseObject = new GrabOrderResponse();
	private GrabResponseAdapter responseAdapter = new GrabResponseAdapter();

	//private static final String CALLBACK_URL = "kettle-service/rest/v1/delivery-management/update/"+API_KEY;
	private static final String CLIENT_ID = "62";

	@Override
	public DeliveryResponse createDelivery(String creationEndpoint, OrderInfo order, EnvironmentProperties props) {
		requestObject = requestAdapter.adaptCreate(order);
		return readResponse(createRequest(getHost(props) + creationEndpoint, requestObject), order.getOrder().getGenerateOrderId());
	}

	private String getHost(EnvironmentProperties props) {
		return TransactionUtils.isDev(props.getEnvironmentType())
				? GrabEnv.TEST.getValue():
				GrabEnv.PRODUCTION.getValue();
	}

	@Override
	public DeliveryResponse cancelDelivery(String cancelationEndpoint, String taskId, OrderInfo orderInfo,
										   EnvironmentProperties props) {
		requestObject = requestAdapter.adaptCancel(orderInfo,taskId);
		LOG.info("Cancellation endpoint is :::: {}", cancelationEndpoint);
		return readResponse(createRequest(getHost(props)+cancelationEndpoint, requestObject), orderInfo.getOrder().getGenerateOrderId());
	}

	@Override
	public void setAuthorizationObject(AuthorizationObject authorization) {
		this.authorization = authorization;
	}

	@Override
	public String registerMerchant(String registerEndpoint, Unit unit, EnvironmentProperties props) {
		try {
			GrabMerchant merchantRequest = new GrabMerchant(unit);
			merchantRequest.setClientId(CLIENT_ID);
			HttpResponse responseFromRequest = createRequest(getHost(props)+registerEndpoint, merchantRequest);
			responseObject = WebServiceHelper.convertResponse(responseFromRequest, GrabOrderResponse.class);
			LOG.info("received response for merchant request :::: {}", WebServiceHelper.convertToString(responseObject));
			return WebServiceHelper.convertToString(responseObject);
		} catch (IllegalStateException | IOException e) {
			LOG.error("JSON Parsing exception", e);
		}
		return null;
	}



	@Override
	public DeliveryResponse readResponse(HttpResponse responseFromRequest, String orderId) {
		try {
			responseObject = WebServiceHelper.convertResponse(responseFromRequest, GrabOrderResponse.class);
			if (responseFromRequest != null
					&& responseFromRequest.getStatusLine().getStatusCode() < HttpStatus.BAD_REQUEST.value()
					&& responseObject.getSuccess()!=null && Boolean.parseBoolean(responseObject.getSuccess())){

					return responseAdapter.adapt(responseObject, orderId);
			}else{
				return responseAdapter.adaptError(responseObject, orderId);
			}

		} catch (IllegalStateException e) {
			LOG.error("Illegal state exception ", e);
		} catch (IOException e) {
			LOG.error("I/O exception", e);
		}
		return null;
	}


	public HttpResponse createRequest(String endpoint, DeliveryRequest request) {
		HttpPost requestObject = new HttpPost(endpoint);

		String requestJson = null;
		try {
			requestJson = WebServiceHelper.convertToString(request);
			requestObject = (HttpPost) addHeaders(requestObject, requestJson, getAuthorization());
			HttpEntity orderJson = new StringEntity(requestJson,DeliveryConstants.CHARSET);
			LOG.info("requestJson is ::::::::::::::::::::::::::: {}", requestJson);
			requestObject.setEntity(orderJson);
			LOG.info("inside createRequest of Abstract Strategy before sending request");
			HttpResponse responseFromRequest = WebServiceHelper.postRequest(requestObject);
			LOG.info("HttpResponse after resquest sent ::::: {} :::: {}",
					responseFromRequest.getStatusLine().getStatusCode(),
					responseFromRequest.getStatusLine().getReasonPhrase());

			return responseFromRequest;
		} catch (JsonProcessingException e) {
			LOG.error("Unable to parse json :::", e);
		} catch (ClientProtocolException e) {
			LOG.error("Invalid client protocol :::", e);
		} catch (IOException e) {
			LOG.error("IO Exception encountered :::", e);
		}
		return null;
	}

	public HttpUriRequest addHeaders(HttpUriRequest request, String data, AuthorizationObject token) throws UnsupportedEncodingException {
		LOG.info("Content type ::: {}", ContentType.APPLICATION_JSON.toString());
		request.addHeader(WebServiceHelper.X_PUBLIC_HEADER, URLEncoder.encode(token.getToken(), DeliveryConstants.CHARSET));
		String hash = RequestSigner.calculateHash(data, token.getTokenSecret());
		LOG.info("Hash for this request ::::::::::::::::::::::::::::::::::::::::::::::::::::::::: {}",hash);
		request.addHeader(WebServiceHelper.X_HASH_HEADER, URLEncoder.encode(hash,DeliveryConstants.CHARSET));
		request.addHeader(WebServiceHelper.CONTENT_TYPE, ContentType.APPLICATION_JSON.toString());
		return request;
	}


}
