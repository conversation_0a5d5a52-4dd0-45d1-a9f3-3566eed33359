/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderWiseVariantDataDto implements Serializable {
    private Integer orderWiseVariantDataId;
    private Integer orderItemId;
    private String pattiType;
    private Integer pattiId;
    private BigDecimal pattiQuantity;
    private BigDecimal pattiShots;
    private String sugarType;
    private Integer sugarId;
    private BigDecimal sugarQuantity;
    private BigDecimal sugarShots;
    private String itemRemarks;
}
