package com.stpl.tech.checklist.model;

import javax.xml.bind.annotation.adapters.XmlAdapter;
import java.text.ParseException;
import java.util.Date;

public class Adapter2 extends XmlAdapter<String, Date> {

	public Date unmarshal(String value) throws ParseException {
		return (com.stpl.tech.util.domain.adapter.DateAdapter.parseDate(value));
	}

	public String marshal(Date value) {
		return (com.stpl.tech.util.domain.adapter.DateAdapter.printDate(value));
	}

}
