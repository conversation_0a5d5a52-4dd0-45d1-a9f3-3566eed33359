var inventoryApp = angular.module('inventoryapp', ['ui.router', 'ui.bootstrap', 'ngCookies', 'ui.select2', 'pathgather.popeye'])
    .config(function ($stateProvider, $urlRouterProvider) {

        $urlRouterProvider.otherwise("/login");

        $stateProvider.state('login', {
            url: "/login",
            templateUrl: 'views/login.html',
            params: {
                accessDenied: false
            },
            controller: 'LoginController'
        }).state('dashboard', {
            url: "/dashboard",
            templateUrl: "views/dashboard.html",
            controller: 'DashboardController'
        }).state('dashboard.home', {
            url: "/home",
            templateUrl: "views/home.html",
            controller: 'HomeController'
        }).state('dashboard.inventory', {
            url: "/inventory",
            templateUrl: "views/inventory.html",
            controller: 'InventoryCtrl'
        }).state('dashboard.timeline', {
            url: "/timeline",
            templateUrl: "views/timeline.html",
            controller: 'TimelineCtrl'
        }).state('dashboard.inventorySnapshot',{
            url: "/inventorySnapshot",
            templateUrl: "views/inventorySnapshot.html",
            controller: 'InventorySnapshotCtrl'
        })
    }).service('fileService', function () {
        var service = this;
        service.file = null;
        service.push = function (file) {
            service.file = file;
        };
        service.getFile = function () {
            return service.file;
        };
    }).service('AuthService', function () {
        var service = this;
        service.authorization = null;
        service.getAuthorization = getAuthorization;
        service.setAuthorization = function (authorization) {
            service.authorization = authorization;
        };

        function getAuthorization() {
            return service.authorization;
        }

        return service;
    }).factory('httpAuthInterceptor', function ($q, $rootScope, AuthService, $location) {
        return {
            request: function (config) {
                $rootScope.showSpinner = true;
                config.headers.auth = AuthService.getAuthorization();
                if (config.method == "POST" && config.data == undefined) {
                    config["data"] = {};
                }
                return config;
            },
            requestError: function (rejection) {
                $rootScope.showSpinner = false;
                return $q.reject(rejection);
            },
            response: function (response) {
                $rootScope.showSpinner = false;
                return response || $q.when(response);
            },

            responseError: function (response) {
                $rootScope.showSpinner = false;
                if (response.status === 401) {
                    $cookieStore.remove('inventoryGlobals');
                    AuthService.setAuthorization(null);
                    $location.path('/login');
                }
                return $q.reject(response);
            }
        };
    }).config(['$httpProvider', function ($httpProvider) {
        $httpProvider.interceptors.push('httpAuthInterceptor');
    }]).run(function ($rootScope, $location, AuthService, $cookieStore, AppUtil) {
        $rootScope.$on('$locationChangeStart', function (event, next, current) {
            $cookieStore.get('inventoryGlobals') != null ? AuthService.setAuthorization($cookieStore
                .get('inventoryGlobals').jwtToken) : AuthService.setAuthorization(null);
            $rootScope.aclData = AppUtil.getAcl();
            // redirect to login page if not logged in and trying to access
            // a restricted page
            var restrictedPage = $.inArray($location.path(), ['/login']) === -1;
            var loggedIn = AuthService.getAuthorization() != null;
            if (restrictedPage && !loggedIn) {
                $location.path("/login");
            }
            $rootScope.showFullScreenLoader = false;
            $rootScope.showDetailLoader = false;
            $rootScope.detailLoaderMessage = null;
        });
    }).directive("aclMenu", ['$rootScope', function ($rootScope) {
        /*function link(scope, element, attributes) {
            var aclData = $rootScope.aclData;
            if (aclData != null && aclData.menu != null && (aclData.menu[attributes.aclMenu] != null || aclData.menu["ADMN_ALL"] != null)) {
                element.show();
            } else {
                element.hide();
            }
        }
        return({
            link: link,
            restrict: "A"
        });*/
    }]).directive("aclSubMenu", ['$rootScope', function ($rootScope) {
        /*function link(scope, element, attributes) {
            var aclData = $rootScope.aclData;
            if (aclData != null && aclData.subMenu != null && (aclData.subMenu[attributes.aclSubMenu] != null || aclData.menu["ADMN_ALL"] != null)) {
                element.show();
            } else {
                element.hide();
            }
        }
        return({
            link: link,
            restrict: "A"
        });*/
    }]).directive("aclAction", ['$rootScope', function ($rootScope) {
        function link(scope, element, attributes) {
            var aclData = $rootScope.aclData;
            if (aclData != null && aclData.action != null && (aclData.action[attributes.aclAction] != null || aclData.menu["ADMN_ALL"] != null)) {
                element.show();
            } else {
                element.hide();
            }
        }

        return ({
            link: link,
            restrict: "A"
        });
    }]);
