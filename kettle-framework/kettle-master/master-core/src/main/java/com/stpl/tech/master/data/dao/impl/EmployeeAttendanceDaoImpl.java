package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.data.dao.EmployeeAttendanceDao;
import com.stpl.tech.master.data.model.EmployeeAttendance;
import com.stpl.tech.master.domain.model.EligibilityType;
import com.stpl.tech.master.domain.model.MappingType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DAO implementation for Employee Eligibility Mapping operations
 */
@Repository
public class EmployeeAttendanceDaoImpl extends AbstractMasterDaoImpl implements EmployeeAttendanceDao {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeAttendanceDaoImpl.class);


    @Override
    public List<EmployeeAttendance> findByEmpId(String empId) {
        try {
            Query query = manager.createQuery(
                "FROM EmployeeAttendance ea WHERE ea.empId = :empId");
            query.setParameter("empId", empId);
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error finding mappings by empId: {}", empId, e);
            throw e;
        }
    }

    @Override
    public List<EmployeeAttendance> findByEmpIdValueAndType(String empId, String value , MappingType mappingType , EligibilityType eligibilityType) {
        try {
            Query query = manager.createQuery(
                "FROM EmployeeAttendance ea WHERE ea.empId = :empId AND ea.value = :value AND ea.mappingType = :mappingType AND ea.eligibilityType = :eligibilityType");
            query.setParameter("empId", empId);
            query.setParameter("value", value);
            query.setParameter("mappingType", mappingType);
            query.setParameter("eligibilityType", eligibilityType);
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error finding mappings by empId: {} , value: {} and mappingType: {} and eligibilityType: {}" , empId, value, mappingType,eligibilityType, e);
            throw e;
        }
    }

    @Override
    public List<EmployeeAttendance> findByEmpIdIn(List<Long> empIds) {
        try {
            List<String> empIdStrings = empIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            Query query = manager.createQuery(
                    "FROM EmployeeAttendance ea WHERE ea.empId IN :empIds");
            query.setParameter("empIds", empIdStrings);
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error finding mappings by empIds: {}", empIds, e);
            throw e;
        }
    }


}
