package com.stpl.tech.kettle.customer.dao;

import com.stpl.tech.kettle.data.model.WalletSuggestionEvents;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface WalletSuggestionEventsDao extends JpaRepository<WalletSuggestionEvents,Integer> {
    WalletSuggestionEvents findByCustomerIdAndStatus(Integer customerId,String status);
}
