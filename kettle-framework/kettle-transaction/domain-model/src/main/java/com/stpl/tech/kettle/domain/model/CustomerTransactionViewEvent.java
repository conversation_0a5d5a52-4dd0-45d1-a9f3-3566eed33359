package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.Objects;

public class CustomerTransactionViewEvent implements Serializable {

    private static final long serialVersionUID = -1670220041321677515L;
    private Integer customerId;
    private Integer brandId;

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CustomerTransactionViewEvent that = (CustomerTransactionViewEvent) o;
        return customerId.equals(that.customerId) && brandId.equals(that.brandId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(customerId, brandId);
    }

    @Override
    public String toString() {
        return "CustomerTransactionViewEvent{" +
                "customerId=" + customerId +
                ", brandId=" + brandId +
                '}';
    }
}
