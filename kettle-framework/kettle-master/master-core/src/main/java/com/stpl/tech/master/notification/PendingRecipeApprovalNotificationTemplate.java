/*
 * Created By Shanmukh
 */

package com.stpl.tech.master.notification;

import com.stpl.tech.master.domain.model.RecipeApprovalDTO;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PendingRecipeApprovalNotificationTemplate extends AbstractTemplate {

    private List<RecipeApprovalDTO> recipeApprovalDTOS;
    private String basePath;
    private String emailType;

    public PendingRecipeApprovalNotificationTemplate(List<RecipeApprovalDTO> recipeApprovalDTOS, String basePath, String emailType) {
        this.recipeApprovalDTOS = recipeApprovalDTOS;
        this.basePath = basePath;
        this.emailType  = emailType;
    }

    @Override
    public String getTemplatePath() {
        return "template/PendingRecipeApprovalTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + "Recipes_Pending_For_Approval_" + AppUtils.getCurrentTimeISTStringWithNoColons()  + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> map = new HashMap<>();
        map.put("recipes", recipeApprovalDTOS);
        map.put("emailType", emailType);
        return map;
    }
}
