package com.stpl.tech.analytics.service.impl;

import com.stpl.tech.analytics.dao.OrderItemDao;
import com.stpl.tech.analytics.dao.OrderProcessingDao;
import com.stpl.tech.analytics.dao.SettlementDao;
import com.stpl.tech.analytics.dao.chaimonk.ChaiMonkOrderItemDao;
import com.stpl.tech.analytics.dao.chaimonk.ChaiMonkOrderProcessingDao;
import com.stpl.tech.analytics.dao.chaimonk.ChaiMonkPrintDetailDao;
import com.stpl.tech.analytics.dao.chaimonk.ChaiMonkSettlementDao;
import com.stpl.tech.analytics.model.SyncFailureNotification;
import com.stpl.tech.analytics.model.chaimonk.ChaiMonkOrder;
import com.stpl.tech.analytics.service.OrderProcessingService;
import com.stpl.tech.kettle.domain.model.OrderResponse;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Service
public class OrderProcessingServiceImpl implements OrderProcessingService {
	private static final Logger LOG = LoggerFactory.getLogger(OrderProcessingServiceImpl.class);

	@Autowired
	private OrderProcessingDao orderDao;

	@Autowired
	private OrderItemDao itemDao;

	@Autowired
	private SettlementDao settlementDao;

	@Autowired
	AnalyticsProperties props;


	// Chai Monk Specific autowiring of DAOs
	@Autowired
	private ChaiMonkSettlementDao monkSettlementDao;

	@Autowired
	private ChaiMonkOrderProcessingDao monkOrderDao;

	@Autowired
	private ChaiMonkOrderItemDao monkItemDao;

	@Autowired
	private ChaiMonkPrintDetailDao monkPrintDetailDao;

	private <T,R extends Serializable> List<T> save(MongoRepository<T,R> dao, Iterable<T> list) {
		List<T> finalList = null;
		if (list != null) {
			finalList = new ArrayList<T>();
			Iterable<T> records = dao.saveAll(list);
			for (T record : records) {
				finalList.add(record);
			}
		}
		return finalList;

	}

	@Override
	public List<OrderResponse> findByOrderId(int orderId) {
		return orderDao.findByOrderId(orderId);
	}

	@Override
	public void delete(OrderResponse order) {
		orderDao.delete(order);
	}

	@Override
	public Integer syncChaiMonkOrders(List<ChaiMonkOrder> orderDetails) {
		Integer syncedOrderId = null;
		for(ChaiMonkOrder monkOrder : orderDetails){
			try {
				List<ChaiMonkOrder> chaiMonkOrders =  monkOrderDao.findByOrderId(monkOrder.getOrderId());
				if (chaiMonkOrders != null && chaiMonkOrders.size() > 0) {
					ChaiMonkOrder order = chaiMonkOrders.get(0);
					if (order.getOrderStatus().equals(monkOrder.getOrderStatus())) {
						LOG.error("Order has already been processed {} ", monkOrder.getOrderId());
					} else {
						LOG.info("Order status is changed {}", monkOrder.getOrderId());
						order.setOrderStatus(monkOrder.getOrderStatus());
						monkOrder = monkOrderDao.save(order);
					}
				}else{
					LOG.info("Order not found on the database ::: {}", monkOrder.getOrderId());
					LOG.info("Started syncing order on mongo database");
					monkOrder.setOrderItems(save(monkItemDao, monkOrder.getOrderItems()));
					monkOrder.setOrderSettlements(save(monkSettlementDao, monkOrder.getOrderSettlements()));
					monkOrder.setOrderReprints(save(monkPrintDetailDao, monkOrder.getOrderReprints()));
					monkOrder = monkOrderDao.save(monkOrder);
				}
				syncedOrderId = monkOrder.getOrderId();
			}catch (Exception e){
				String subject = "Failed to sync order from chaimonk " + monkOrder.getOrderId();
				SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Kettle", SlackNotification.MONK_SYNC_ERRORS,
						new SyncFailureNotification(subject, e.getMessage()));
			}
		}
		return syncedOrderId;
	}

	@Override
	public OrderResponse addOrder(OrderResponse order) {
		order.getOrder().setOrders(save(itemDao, order.getOrder().getOrders()));
		order.getOrder().setSettlements(save(settlementDao, order.getOrder().getSettlements()));
		return orderDao.save(order);
	}
}
