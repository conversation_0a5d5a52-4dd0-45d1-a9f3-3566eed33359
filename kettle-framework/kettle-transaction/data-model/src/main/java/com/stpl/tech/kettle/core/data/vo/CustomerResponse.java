/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.data.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.stpl.tech.kettle.domain.model.SubscriptionInfoDetail;
import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;

public class CustomerResponse {

    private int id;
    private String name;
    private String contact;
    private String email;
    private boolean contactVerified;
    private boolean emailVerified;
    private int unitId;
    private boolean eligibleForSignupOffer;
    private boolean newCustomer;
    private String otp;
    private boolean feedbackRequired;
    private boolean otpVerified;
    private FeedbackOrderMetadata feedbackOrderMetadata;
    private Integer tcId;
    private TrueCallerSettings  signInMode;
    private String signUpRefCode;
	private Date lastVisitTime;
    private boolean optOutOfFaceIt = false;
    private Date optOutTime;
    private String faceId;
    private Integer brandId;
    private String gender;
    private Date dateOfBirth;
    private Date anniversary;
    private boolean sourceAcknowledged;
    private SubscriptionInfoDetail subscriptionInfoDetail;

    private BigDecimal walletBalance;
    private Integer orderCount;


    protected int loyalityPoints;
    protected BigDecimal chaayosCash;
    protected Boolean optWhatsapp;

    private Date addTime;

    private boolean isResendOtp;
    private boolean profileCompleted;

    public CustomerResponse() {
        super();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public int getLoyalityPoints() {
        return loyalityPoints;
    }

    public void setLoyalityPoints(int loyalityPoints) {
        this.loyalityPoints = loyalityPoints;
    }

    public boolean isContactVerified() {
        return contactVerified;
    }

    public void setContactVerified(boolean contactVerified) {
        this.contactVerified = contactVerified;
    }

    public boolean isEmailVerified() {
        return emailVerified;
    }

    public void setEmailVerified(boolean emailverified) {
        this.emailVerified = emailverified;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public boolean isNewCustomer() {
        return newCustomer;
    }

    public void setNewCustomer(boolean newCustomer) {
        this.newCustomer = newCustomer;
    }

    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }

    public boolean isEligibleForSignupOffer() {
        return eligibleForSignupOffer;
    }

    public void setEligibleForSignupOffer(boolean eligibleForSignupOffer) {
        this.eligibleForSignupOffer = eligibleForSignupOffer;
    }

    public boolean isFeedbackRequired() {
        return feedbackRequired;
    }

    public void setFeedbackRequired(boolean takeFeedback) {
        this.feedbackRequired = takeFeedback;
    }

    public FeedbackOrderMetadata getFeedbackOrderMetadata() {
        return feedbackOrderMetadata;
    }

    public void setFeedbackOrderMetadata(FeedbackOrderMetadata feedbackOrderMetadata) {
        this.feedbackOrderMetadata = feedbackOrderMetadata;
    }

    public boolean isOtpVerified() {
        return otpVerified;
    }

    public void setOtpVerified(boolean otpVerified) {
        this.otpVerified = otpVerified;
    }

    public Integer getTcId() {
        return tcId;
    }

    public void setTcId(Integer tcId) {
        this.tcId = tcId;
    }

    public TrueCallerSettings getSignInMode() {
        return signInMode;
    }

    public void setSignInMode(TrueCallerSettings signInMode) {
        this.signInMode = signInMode;
    }

	public String getSignUpRefCode() {
		return signUpRefCode;
	}

	public void setSignUpRefCode(String refCode) {
		this.signUpRefCode = refCode;
	}

	public Date getLastVisitTime() {
		return lastVisitTime;
	}

	public void setLastVisitTime(Date lastVisitTime) {
		this.lastVisitTime = lastVisitTime;
	}

    public BigDecimal getChaayosCash() {
        return chaayosCash;
    }

    public void setChaayosCash(BigDecimal chaayosCash) {
        this.chaayosCash = chaayosCash;
    }

	public boolean isOptOutOfFaceIt() {
		return optOutOfFaceIt;
	}

	public void setOptOutOfFaceIt(boolean optOutOfFaceIt) {
		this.optOutOfFaceIt = optOutOfFaceIt;
	}

	public Date getOptOutTime() {
		return optOutTime;
	}

	public void setOptOutTime(Date optOutTime) {
		this.optOutTime = optOutTime;
	}

    public String getFaceId() {
        return faceId;
    }

    public void setFaceId(String faceId) {
        this.faceId = faceId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Date getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public Date getAnniversary() {
        return anniversary;
    }

    public void setAnniversary(Date anniversary) {
        this.anniversary = anniversary;
    }

    public boolean isSourceAcknowledged() {
        return sourceAcknowledged;
    }

    public void setSourceAcknowledged(boolean sourceAcknowledged) {
        this.sourceAcknowledged = sourceAcknowledged;
    }

    public Boolean isOptWhatsapp() {
        return optWhatsapp;
    }

    public void setOptWhatsapp(Boolean optWhatsapp) {
        this.optWhatsapp = optWhatsapp;
    }

    public SubscriptionInfoDetail getSubscriptionInfoDetail() {
        return subscriptionInfoDetail;
    }

    public void setSubscriptionInfoDetail(SubscriptionInfoDetail subscriptionInfoDetail) {
        this.subscriptionInfoDetail = subscriptionInfoDetail;
    }

    public Boolean getOptWhatsapp() {
        return optWhatsapp;
    }

    public BigDecimal getWalletBalance() {
        return walletBalance;
    }

    public void setWalletBalance(BigDecimal walletBalance) {
        this.walletBalance = walletBalance;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public boolean getIsResendOtp() {
        return isResendOtp;
    }

    public void setIsResendOtp(boolean resendOtp) {
        isResendOtp = resendOtp;
    }

    public boolean isProfileCompleted() {
        return profileCompleted;
    }

    public void setProfileCompleted(boolean profileCompleted) {
        this.profileCompleted = profileCompleted;
    }
}
