package com.stpl.tech.kettle.domain.model;

import java.util.Date;

/**
 * Created by shikhar on 31/5/19.
 */
public class AssemblyTAT {

    private int unitId;
    private Date businessDate;
    private TATSummary lastHour;
    private TATSummary today;


    public AssemblyTAT() {}

    public AssemblyTAT(int unitId, Date businessDate) {
        this.unitId = unitId;
        this.businessDate = businessDate;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public TATSummary getLastHour() {
        return lastHour;
    }

    public void setLastHour(TATSummary lastHour) {
        this.lastHour = lastHour;
    }

    public TATSummary getToday() {
        return today;
    }

    public void setToday(TATSummary today) {
        this.today = today;
    }
}
