<#setting locale="en_US">
    <#setting date_format="dd/MM/yyyy HH:mm:ss">
        <#setting number_format="0.##">
            <!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
            <html lang="en">
            <head>
                <title>Chai Receipt No: ${order.generateOrderId}</title>
                <meta http-equiv=Content-Type content="text/html; charset=UTF-8">
            </head>
            <body>

            <div style="margin:0;padding:0;height: 100% !important;width: 100% !important;" bgcolor="#f4f4f4">
                <table cellspacing="0" cellpadding="0" border="0" height="100%" width="100%" style="border-collapse: collapse;
            table-layout: fixed; margin: 0 auto; border-spacing: 0;padding: 0;height:100% !important;width:100% !important;">
                    <tbody>
                    <tr>
                        <td style="border-collapse: collapse; background: #f6f6f6; padding-top: 15px; padding-bottom: 15px;">
                            <#if !isEmailVerified>
                                <div style="text-align:center;padding: 30px 20px;">
                                    <#if (verifyEmailLink)??>
                                        Verify this email address and earn 10 loyaltea points.<br />
                                        <img alt="Chaayos Free Desi Chai" src="${urlBasePath}/img/chaiGlass.png" width="100px" /><br />
                                        <a href="${verifyEmailLink}?token=${token}" style="text-decoration:none;">
                                            <img alt="Verify Email" src="${urlBasePath}/img/verifyBtn.png" />
                                        </a>
                                    </#if>
                                </div>
                            </#if>
                            <!--<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="max-width:600px;margin:auto;border-spacing:0;border-collapse:collapse;">
                                <tbody>
                                    <tr>
                                        <td style="text-align:center;vertical-align:top;font-size:0;border-collapse:collapse">
                                            <a href="https://cafes.chaayos.com">
                                                <img src="${urlBasePath}/img/banner/chaayosBMS2.jpg" width="600px" />
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>-->
                            <table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="max-width:600px;margin:auto;border-spacing:0;border-collapse:collapse;background:white">
                                <tbody>
                                    <tr>
                                        <td style="text-align:center;vertical-align:top;font-size:0;border-collapse:collapse">
                                            <a href="https://cafes.chaayos.com">
                                                <img alt="Chaayos" src="${urlBasePath}/chaayos/logo/chaayos-logo.png"
                                                     style="padding-top: 9px" width="350" />
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="text-align:center;vertical-align:top;font-size:0;border-collapse:collapse;padding-left:15px;padding-right:15px">
                                            <table border="0" width="100%" cellpadding="0" cellspacing="0" bgcolor="#f6f6f6" style="border-spacing:0;border-collapse:collapse">
                                                <tbody>
                                                    <tr>
                                                        <td valign="middle" align="center" style="padding-top:25px;padding-bottom:15px;text-align:center;background-color:white;border-top:1px solid #e6e6e6">
                                                            <span style="font-size:24px;color:#2d2d2d">Thanks for ordering at Chaayos!</span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td valign="middle" align="center" style="padding:10px 15px 20px 15px;text-align:center;background-color:white;border-collapse:collapse">
                                                            <div style="font-size: 12px;color: #383838;">
                                                                Call 1800 120 2424 for delivery or<br>Log in to
                                                                <a href="https://cafes.chaayos.com" target="_blank" style="text-decoration:none;">www.chaayos.com</a>
                                                            </div>

                                                            <div style="text-align: center;margin: 10px 0;">
                                                                <a href="https://twitter.com/chaayos" style="text-decoration: none; margin: 0 5px;">
                                                                    <img src="${urlBasePath}/img/desktop/twitter.png" style="width:25px;">
                                                                </a> &emsp;
                                                                <a href="https://facebook.com/chaayos" style="text-decoration: none; margin: 0 5px;">
                                                                    <img src="${urlBasePath}/img/desktop/facebook.png" style="width:25px;">
                                                                </a> &emsp;
                                                                <a href="https://instagram.com/chaayos" style="text-decoration: none; margin: 0 5px;">
                                                                    <img src="${urlBasePath}/img/desktop/instagram.png" style="width:25px;">
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <#if billPromotion??>
                                                        <tr>
                                                            <td>
                                                                <div style="width: 100%; background: #dcdcdc;">
                                                                    <p style="padding:5px; padding-left: 13px;text-align: center; font-size: 18px; font-style : bold;">
                                                                        ${billPromotion}
                                                                    </p>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </#if>
                                                    <tr>
                                                        <td style="border-collapse: collapse;text-align:left;padding:0 15px;">
                                                            <table border="0" width="100%" cellpadding="0" cellspacing="0"
                                                                   style="margin-top:20px;border-spacing:0;border-collapse:collapse">
                                                                <tbody>
                                                                    <tr>
                                                                        <td style="text-align: center; padding: 0 0 10px 0;">
                                                                            <span style="font-size : 18px;">
                                                                                Chaayos
                                                                                <#if order.source != "COD">
                                                                                    ${unit.referenceName}
                                                                                </#if>
                                                                                <br />
                                                                                <#if order.source != "COD">
                                                                                    ${unit.address.line1},
                                                                                    <#if (unit.address.line2)?? && unit.address.line2 != "">
                                                                                        ${unit.address.line2},
                                                                                    </#if>
                                                                                    <#if (unit.address.line3)?? && unit.address.line3 != "">
                                                                                        ${unit.address.line3},
                                                                                    </#if>
                                                                                    ${unit.address.city}, ${unit.address.state}, ${unit.address.contact1}
                                                                                </#if>
                                                                            </span>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                            <table border="0" width="100%" cellpadding="0" cellspacing="0"
                                                                   style="margin:10px 0;border-spacing:0;border-collapse:collapse">
                                                                <tbody>
                                                                    <tr>
                                                                        <td style="text-transform:uppercase;font-size:15px;line-height: 30px;">Order summary</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">Bill No: ${order.generateOrderId}</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">Order Time: ${order.billCreationTime?date}</td>
                                                                    </tr>
                                                                    <#if (customer.id) gt 5>
                                                                        <#if (customer.firstName)??>
                                                                            <tr>
                                                                                <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">
                                                                                    Customer Name: ${customer.firstName}
                                                                                    <#if (customer.middleName)??>${customer.middleName}</#if>
                                                                                    <#if (customer.lastName)??>${customer.lastName}</#if>
                                                                                </td>
                                                                            </tr>
                                                                        </#if>
                                                                        <tr>
                                                                            <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">Mobile No: ${customer.countryCode}-${customer.contactNumber}</td>
                                                                        </tr>
                                                                        <#if order.source != "COD">
                                                                            <tr>
                                                                                <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">LoyalTea Points: ${customer.loyaltyPoints}</td>
                                                                            </tr>
                                                                        </#if>
                                                                        <tr>
                                                                            <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">Email Address: ${customer.emailId}</td>
                                                                        </tr>
                                                                        <#if order.pointsRedeemed gt 0>
                                                                            <tr>
                                                                                <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">Points Redeemed: ${order.pointsRedeemed}</td>
                                                                            </tr>
                                                                        </#if>
                                                                        <#if (deliveryAddress)??>
                                                                            <tr>
                                                                                <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">Delivery Address:
                                                                                    <#if (deliveryAddress.name)??>${deliveryAddress.name}, </#if>
                                                                                    <#if (deliveryAddress.contact1)??>${deliveryAddress.contact1}, </#if>
                                                                                    <#if (deliveryAddress.email)??>${deliveryAddress.email}</#if>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">
                                                                                    <#if (deliveryAddress.line1)??>${deliveryAddress.line1}, </#if>
                                                                                    <#if (deliveryAddress.line2)??>${deliveryAddress.line2}, </#if>
                                                                                    <#if (deliveryAddress.line3)??>${deliveryAddress.line3}, </#if>
                                                                                    <#if (deliveryAddress.landmark)??>${deliveryAddress.landmark}, </#if>
                                                                                    <#if (deliveryAddress.locality)??>${deliveryAddress.locality}, </#if>
                                                                                    <#if (deliveryAddress.city)??>${deliveryAddress.city}, </#if>
                                                                                    <#if (deliveryAddress.state)??>${deliveryAddress.state}, </#if>
                                                                                    <#if (deliveryAddress.country)??>${deliveryAddress.country}, </#if>
                                                                                    <#if (deliveryAddress.zipCode)??>${deliveryAddress.zipCode} </#if>
                                                                                </td>
                                                                            </tr>
                                                                        </#if>
                                                                        <!-- TODO Remove Channel Partner Id Check -->
                                                                        <#if (channelPartner)?? && (channelPartner.name)?? && channelPartner.id gt 1>
                                                                            <tr>
                                                                                <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">Channel Partner: ${channelPartner.name}</td>
                                                                            </tr>
                                                                        </#if>

                                                                        <!-- TODO Remove Delivery Partner Id Check
                                                                            <#if (deliveryPartner)?? && (deliveryPartner.name)?? && channelPartner.id gt 1>
                                                                            <tr>
                                                                                <td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
                                                                                    <b style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
                                                                                        Delivery Partner:
                                                                                    </b></td><td>
                                                                                    ${deliveryPartner.name}
                                                                                </td><td></td>
                                                                            </tr>
                                                                            </#if> -->

                                                                        <#if (order.orderRemark)??>
                                                                            <tr>
                                                                                <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">Order Remark: ${order.orderRemark}</td>
                                                                            </tr>
                                                                        </#if>
                                                                        <#if (order.offerCode)?? && order.offerCode != "" && order.source != "COD">
                                                                            <tr>
                                                                                <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">Coupon Applied : ${order.offerCode}</td>
                                                                            </tr>
                                                                        </#if>
                                                                        <#if (order.tempCode)?? && order.tempCode != "" && order.source != "COD">
                                                                            <tr>
                                                                                <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">Free Wifi Code **: ${order.tempCode}</td>
                                                                            </tr>
                                                                        </#if>
                                                                        <#if (order.tokenNumber)?? && (order.tokenNumber > 0) && (order.source != "COD")>
                                                                            <tr>
                                                                                <td style="border-collapse:collapse;font-size:12px;color:#7d7d76;line-height:15px;">Token Number : ${order.tokenNumber}</td>
                                                                            </tr>
                                                                        </#if>
                                                                    </#if>
                                                                </tbody>
                                                            </table>
                                                            <table border="0" width="100%" cellpadding="0" cellspacing="0"
                                                                   style="margin:10px 0;border-spacing:0;border-collapse:collapse;border-top:1pt solid #cbcbc8;">
                                                                <tbody>
                                                                    <#list order.orders>
                                                                        <#items as orderItem>
                                                                            <#if orderItem.productId != 1043 && orderItem.productId != 1044>
                                                                                <tr>
                                                                                    <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                        (${orderItem.code}) ${orderItem.productName}
                                                                                        <#if orderItem.dimension != "None"> ${orderItem.dimension}</#if>
                                                                                    <#if (orderItem.complimentaryDetail)?? && (orderItem.complimentaryDetail.isComplimentary)>
                                                                                    (Complimentary)
                                                                                    <#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.discountCode)?? && (orderItem.discountDetail.discountCode > 0))>
                                                                                    *
                                                                                    <#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.promotionalOffer)?? && (orderItem.discountDetail.promotionalOffer > 0))>
                                                                                    *
                                                                                </#if>
                                                                                (${orderItem.quantity} X &#8377;${orderItem.price})
                                                                                </td>
                                                                                <td style="text-align: right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                    &#8377;${orderItem.totalAmount}
                                                                                </td>
                                                                                </tr>
                                                                                <#if orderItem.productCategory.id == 8>
                                                                                    <#list orderItem.composition.menuProducts>
                                                                                        <#items as menuItem>
                                                                                            <tr>
                                                                                                <td colspan="2" style="font-size: 12px; padding: 2pt 0; color: #7d7d76; padding-left: 5px;">
                                                                                                    (${menuItem.code}) ${menuItem.productName}
                                                                                                    <#if menuItem.dimension != "None">${menuItem.dimension}</#if>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </#items>
                                                                                    </#list>
                                                                                </#if>
                                                                            </#if>
                                                                        </#items>
                                                                    </#list>
                                                                </tbody>
                                                            </table>
                                                            <table border="0" width="100%" cellpadding="0" cellspacing="0"
                                                                   style="margin:10px 0;border-spacing:0;border-collapse:collapse;border-top:1pt solid #cbcbc8;">
                                                                <tbody>

                                                                    <#list order.orders>
                                                                        <#items as orderItem>
                                                                            <#if orderItem.productId == 1043 || orderItem.productId == 1044>
                                                                                <tr>
                                                                                    <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;text-transform:uppercase;">
                                                                                        ${orderItem.productName}
                                                                                        <#if (orderItem.complimentaryDetail)?? && (orderItem.complimentaryDetail.isComplimentary)>
                                                                                            (Complimentary)
                                                                                            <#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.discountCode)?? && (orderItem.discountDetail.discountCode > 0))>
                                                                                            *
                                                                                            <#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.promotionalOffer)?? && (orderItem.discountDetail.promotionalOffer > 0))>
                                                                                            *
                                                                                        </#if>
                                                                                    </td>
                                                                                    <td style="text-align: right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                        &#8377;${orderItem.totalAmount}
                                                                                    </td>
                                                                                </tr>
                                                                            </#if>
                                                                        </#items>
                                                                    </#list>

                                                                    <tr>
                                                                        <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                            TOTAL
                                                                        </td>
                                                                        <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                            &#8377;${order.transactionDetail.totalAmount}
                                                                        </td>
                                                                    </tr>
                                                                    <#if promotionalDiscount gt 0>
                                                                        <tr>
                                                                            <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                * PROMOTIONAL OFFER
                                                                            </td>
                                                                            <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                &#8377;${promotionalDiscount}
                                                                            </td>
                                                                        </tr>
                                                                    </#if>
                                                                    <#if discountPercent gt 0>
                                                                        <tr>
                                                                            <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                * DISCOUNT (${discountPercent}%)
                                                                            </td>
                                                                            <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                &#8377;${discountValue}
                                                                            </td>
                                                                        </tr>
                                                                    </#if>
                                                                    <#list order.transactionDetail.taxes> <#items as tax>
																	<tr>
                                                                        <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                            ${tax.code} (${tax.percentage}%)
                                                                        </td>
                                                                        <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                            &#8377;${tax.value}
                                                                        </td>
                                                                    </tr>
																	</#items></#list>
                                                                    <#if order.transactionDetail.roundOffValue != 0>
                                                                        <tr>
                                                                            <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                ROUNDING OFF
                                                                            </td>
                                                                            <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                &#8377;${order.transactionDetail.roundOffValue}
                                                                            </td>
                                                                        </tr>
                                                                    </#if>
                                                                </tbody>
                                                            </table>
                                                            <table border="0" width="100%" cellpadding="0" cellspacing="0"
                                                                   style="margin:10px 0;border-spacing:0;border-collapse:collapse;border-top:1pt solid #cbcbc8;">
                                                                <tbody>
                                                                    <tr>
                                                                        <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                            <strong>BILL TOTAL</strong>
                                                                        </td>
                                                                        <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                            <strong>&#8377;${order.transactionDetail.paidAmount}</strong>
                                                                        </td>
                                                                    </tr>
                                                                    <tr align="right">
																		<td colspan="2" style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
																			<strong>Rs. ${paidAmountInWords}</strong>
																		</td>
																	</tr>
                                                                    <#if (order.transactionDetail.savings)?? && order.transactionDetail.savings != 0>
                                                                        <tr>
                                                                            <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                <strong>NET SAVINGS</strong>
                                                                            </td>
                                                                            <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                <strong>&#8377;${order.transactionDetail.savings}</strong>
                                                                            </td>
                                                                        </tr>
                                                                    </#if>
                                                                    <#if (order.subscriptionDetail.overAllSavings)?? && order.subscriptionDetail.overAllSavings != 0>
                                                                        <tr>
                                                                            <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                <strong>Net Savings till now through Chaayos Select</strong>
                                                                            </td>
                                                                            <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                                                                                <strong>&#8377;${order.subscriptionDetail.overAllSavings}</strong>
                                                                            </td>
                                                                        </tr>
                                                                    </#if>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding:20px; margin: 0 15px; border-bottom: #cbcbc8 1px solid; text-align: center;">
                                            <p style="margin: 0;">
                                                Know more about
                                                <a href=" https://chaayos.com/pages/chaayos-select" target="_blank" style="text-decoration:none;">Chaayos Select</a>.
                                            </p>
                                            <p style="margin: 0;">
                                                Note: This is an electronically generated receipt and does not require signature.For any queries,
                                                please drop an email at <a href="mailto:<EMAIL>" target="_blank"><EMAIL></a>.
                                            </p>
                                            <p style="margin: 0;">^ indicates a zero tax product</p>
                                            <#if (order.tempCode)?? && order.tempCode != "" && order.source != "COD">
                                                <p style="margin: 0;">** Free Wifi is subject to availability.</p>
                                            </#if>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding:10px;text-align:center;">
                                            <table border="0" width="100%" cellpadding="0" cellspacing="0" style="text-align: center;">
                                                <tbody>
                                                <tr>
                                                    <td style="font-size:12px;line-height:15px;color:#7d7d76;">
                                                        Company Identification Number: U55204DL2012PTC304447
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size:12px;line-height:15px;color:#7d7d76;">
                                                        GSTIN: ${unit.tin}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size:12px;line-height:15px;color:#7d7d76;">
                                                        <#if (unit.fssai)??>
                                                            FSSAI: ${unit.fssai}
                                                        </#if>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding:20px; margin: 0 15px; text-align: center; color: #4d4d4a; font-size: 18px;">
                                            Sunshine Teahouse Private Limited, 1st Fl, #382, 100' Rd, Ghitorni, New Delhi 30
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    </tbody>
                </table>

            </div>

            </body>
            </html>
