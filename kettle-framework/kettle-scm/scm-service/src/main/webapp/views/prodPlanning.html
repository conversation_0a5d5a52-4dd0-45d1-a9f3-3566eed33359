<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .table,th,td{
        border: 2px solid black;
        margin-bottom: 20px;
    }





</style>
<div
        class="row white z-depth-3"
        data-ng-init="init()">
    <div class="col s12">
        <h3>Production Planning</h3>
    </div>
    <div class="row searchingCard">
        <!--<div class = "col s3">-->
            <!--<label>Select Type</label>-->
            <!--<select data-ng-model="selectedTab" data-ng-options="type as type for type in orderTypes"></select>-->
        <!--</div>-->
        <div class="col s3">
            <div>
                <label for="inputCreated">Select fulfillment date</label>
                <input
                        input-date
                        type="text"
                        name="created"
                        id="inputCreated"
                        ng-model="selectedDate"
                        data-ng-change="reset()"
                        container=""
                        format="yyyy-mm-dd"
                        select-years="1"
                        min="{{minDate}}"
                        max="{{maxDate}}"/>
            </div>
        </div>
        <div class="col s3">
            <input data-ng-model="isAcknowledged" id = "fetchAcknowledged" data-ng-change="changeStatus(isAcknowledged)" type="checkbox"/>
            <label for="fetchAcknowledged">Fetch Acknowledged</label>
        </div>
        <div class="col s3 margin-top-10">
            <button
                    class="btn btn-small"
                    ng-click="getOrders()"
                    style="margin-top: 14px;" acl-action="TRNPPV">Get Orders
            </button>
            <button
                    class="btn btn-small"
                    ng-click="productionPlanningSummary()"
                    style="margin-top: 14px;" acl-action="TRNPPV">Summary
            </button>
        </div>
    </div>
    <div>
        <div data-ng-if="isShow==true" style="background-color: lightgoldenrodyellow;  padding: 50px;margin: 20px;">
            <span style="border-radius:20px;padding: 0 5px; background-color: green;color: white;white-space: nowrap;font-weight: bold;">  Active Units : {{productionPlanningSummaryData.totalActiveUnit}}  </span>
            <span style="border-radius:20px;padding: 0 5px;margin-left: 40px;background-color: green;color: white;white-space: nowrap;font-weight: bold;">  Units RO Received  : {{productionPlanningSummaryData.totalRoReceived}}</span>
            <span style="border-radius:20px;padding: 0 5px;margin-left: 40px;background-color: red;color: white;white-space: nowrap;font-weight: bold;">  Pending Units RO   : {{productionPlanningSummaryData.pendingRo}}</span>
            <button class="btn btn-primary" data-ng-click="unitWithAllRos() " style="margin-top: 30px;margin-left: 80px;">All Cafe Summary</button>

            <div class="row">
                <div class="col s2" data-ng-repeat="city in cityData" style="font-size: medium; font-style: initial;">
                    <input type="checkbox" style="position:inherit;opacity:1;width: 20px;height: 20px;" data-ng-click="checkCity(city)"> {{city}}
                </div>
            </div>
            <table   >
                <tr>
                    <td> Select <input type="checkbox" style="position:inherit;opacity:1; width: 20px;height: 20px;" data-ng-click="allSelect()"></td>
                    <td>City</td>
                    <td>Unit Name</td>
                </tr>
                <tbody >
                <tr data-ng-repeat="unit in tempList | orderBy:'+city' | orderBy:'+name'"
                    data-ng-click="unit.selected = !unit.selected" style="cursor: pointer;" data-ng-class="{'rowSelected':unit.selected}">
                    <td><input type="checkbox" style="position:inherit;opacity:1;width: 20px;height: 20px;"data-ng-model="unit.selected"  data-ng-click="singleSelect(unit.id) " ></td>
                    <td>{{unit.city}}</td>
                    <td>{{unit.name}}</td>
                </tr>
                </tbody>
            </table>

            <button class="btn btn-primary" data-ng-click="guideMe() " style="margin-top: 20px;">GUIDE ME</button>
        </div>
        <div data-ng-if="allUnitWithRos==true" style="background-color: lightgoldenrodyellow;  padding: 50px;margin: 20px;">
            <span style="border-radius:20px;padding: 0 5px; background-color: green;color: white;white-space: nowrap;font-weight: bold;">  Active Units : {{productionPlanningSummaryData.totalActiveUnit}}  </span>
            <span style="border-radius:20px;padding: 0 5px;margin-left: 40px;background-color: green;color: white;white-space: nowrap;font-weight: bold;">  Total Gnt Count  : {{productionPlanningSummaryData.allGntCount}}</span>
            <span style="border-radius:20px;padding: 0 5px;margin-left: 40px;background-color: green;color: white;white-space: nowrap;font-weight: bold;"> Total Chaayos Count  : {{productionPlanningSummaryData.allChaayosCount}}</span>
            <button class="btn btn-primary" data-ng-click="restToSummary() " style="margin-top: 30px;margin-left: 80px;margin-bottom: 40px;">Back</button>

            <!--            <div class="row">-->
<!--                <div class="col s2" data-ng-repeat="city in cityData" style="font-size: medium; font-style: initial;">-->
<!--                    <input type="checkbox" style="position:inherit;opacity:1;width: 20px;height: 20px;" data-ng-click="checkCity(city)"> {{city}}-->
<!--                </div>-->
<!--            </div>-->

            <table   >
                <tr>
                    <td> Select <input type="checkbox" style="position:inherit;opacity:1; width: 20px;height: 20px;" data-ng-click="allSelect()"></td>
                    <td>City</td>
                    <td>Unit Name</td>
                    <td>GNT </td>
                    <td>CHAAYOS</td>
                </tr>
                <tbody >
                <tr data-ng-repeat="unit in tempList | orderBy:'+name'"
                    data-ng-click="unit.selected = !unit.selected" style="cursor: pointer;" data-ng-class="{'rowSelected':unit.selected}">
                    <td><input type="checkbox" style="position:inherit;opacity:1;width: 20px;height: 20px;"data-ng-model="unit.selected"  data-ng-click="singleSelect(unit.id) " ></td>
                    <td>{{unit.city}}</td>
                    <td>{{unit.name}}</td>
                    <td data-ng-if="unit.gntFlag==true" > GNT </td>
                    <td data-ng-if="unit.gntFlag==false" > </td>
                    <td data-ng-if="unit.chaayosFlag==true">Chaayos</td>
                    <td data-ng-if="unit.chaayosFlag==false"> </td>
                </tr>
                </tbody>
            </table>
            <button class="btn btn-primary" data-ng-click="guideMe() " style="margin-top: 20px;">GUIDE ME</button>
        </div>
    </div>
    <hr>
    <div
            class="row"
            data-ng-if="selectedDate != null && requestOrders.length == 0
                                    && acknowledgedOrders.length == 0">
		<span
                class="flow-text"
                style="margin-left: 10px;"> No request orders found for the
			selected date </span>
    </div>
    <div
            class="row"
            data-ng-if="requestOrders.length>0">
        <div class="col s12">
            <button
                    class="btn left"
                    data-ng-click="acknowledgeAll()">CHECK ALL
            </button>
<!--            <button-->
<!--                    class="btn left"-->
<!--                    style="margin-left: 20px;"-->
<!--                    data-ng-click="selectAllGnt()">CHECK ALL GnT-->
<!--            </button>-->
            <button
                    class="btn right"
                    style="margin-right: 10px;"
                    data-ng-if="isChecked(requestOrders)"
                    data-ng-click="submit(requestOrders)"
                    href="#planningItemsModal"
                    modal acl-action="TRNPPA">START PLANNING
            </button>
            <button
                    class="btn right"
                    style="margin-right: 10px;"
                    data-ng-if="isChecked(requestOrders)"
                    data-ng-click="previewOrders(requestOrders)"
                    acl-action="TRNPPA">PREVIEW ROs
            </button>
        </div>
        <div class="col s12">
            <ul
                    class="collapsible no-border"
                    data-collapsible="accordion"
                    watch>
                <li ng-repeat="ro in requestOrders">
                    <div class="collapsible-header custom-collection-header" data-ng-click="selectOpenRo(ro)">
                        <div class="row margin0">
                            <div class="col s12">

                                <div class="col s9">
                                    <input
                                            id="RO-{{ro.id}}"
                                            ng-disabled="!((selectedRO.roType == null)
                                            || selectedRO.count == 0
                                            || (selectedRO.roType == 'FIXED_ASSET' && ro.assetOrder)
                                            || (selectedRO.roType == 'REGULAR' && !ro.assetOrder))"
                                            data-ng-model="ro.checked"
                                            ng-change="setROType(ro)"
                                            type="checkbox"/>
                                    <label  for="RO-{{ro.id}}">{{ro.requestUnit.name}} (RO
                                        No. : {{ro.id}} Type: {{ro.transferType}} )</label>
                                    <span style="color: green"; data-ng-if="ro.raisedBy=='Y'"> {{ro.orderRaisedBy}} </span>
                                    <span style="color: green"; data-ng-if="ro.raisedBy=='N'"> CAFE  </span>
                                    <span style="color: red"; data-ng-if="ro.gntflag==true">  GNT  </span>
                                    <span style="color: black"; data-ng-if="ro.requestOrderType==true"> Regular Order </span>
                                    <span style="color: black"; data-ng-if="ro.requestOrderType==false">Ad-hoc Order </span>
                                </div>
                                <div class="col s3">
									<span class="right-align">
										<strong>Created On: </strong> {{ro.generationTime |
										date:'dd/MM/yyyy hh:mm:ss a'}}
									</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="collapsible-body">
                        <div class="row">
                            <table
                                    class="bordered striped"
                                    data-ng-if="selectedRo.requestOrderItems.length > 0">
                                <tr>
                                    <th>Product Id</th>
                                    <th>Product Name</th>
                                    <th>Requested Quantity</th>
                                    <th>Unit Of Measure</th>
                                </tr>
                                <tr data-ng-repeat="roi in selectedRo.requestOrderItems track by $index">
                                    <td><a data-ng-click="showPreview($event, roi.productId,'PRODUCT')">{{roi.productId}}</a>
                                    </td>
                                    <td>{{roi.productName}}</td>
                                    <td>{{roi.requestedAbsoluteQuantity}}</td>
                                    <td>{{roi.unitOfMeasure}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div
            class="row"
            data-ng-if="acknowledgedOrders.length>0">
        <div class="col s12">
            <h4>Acknowledged Orders</h4>
            <div
                    class="right"
                    style="line-height: 55px;">
            <button data-ng-click="printRos()"
            class="btn " >
            <i
                    style="line-height: 1rem; margin-right: 0;"
                    class="fa fa-print"></i>
                Bulk Print
            </button>
            <button id="printDiv"  ng-print
                    print-element-id="print-bulk" class="btn"  data-ng-show="false">Print</button>
            <button
                    class="btn "
                    data-ng-click="downloadPlanOrdersBulk()">Download Bulk
            </button>
            </div>
        </div>
        <div class="col s12">
            <ul
                    class="collapsible no-border"
                    data-collapsible="accordion">
                <li ng-repeat="ro in acknowledgedOrders">
                    <div
                            class="collapsible-header custom-collection-header left"
                            style="width: 75%; " data-ng-click="selectOpenRo(ro)">
                        <div class="row margin0">
                            <div class="col s12">
                                <div class="col s6">
                                    <label>{{ro.requestUnit.name}} (RO No.: {{ro.id}})</label>
                                </div>
                                <div class="col s6 right-align">
									<span>
										<strong>Created On: </strong> {{ro.generationTime |
										date:'dd/MM/yyyy'}}
									</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="collapsible-body">
                        <div
                                class="right"
                                style="line-height: 55px;">
                            <button
                                    ng-print
                                    print-element-id="print-{{ro.id}}"
                                    class="btn"
                                    tooltipped
                                    data-tooltip="Print">
                                <i
                                        style="line-height: 1rem; margin-right: 0;"
                                        class="fa fa-print"></i>
                            </button>
                            <button
                                    class="btn"
                                    data-ng-click="downloadRequestOrder(ro)">Download
                            </button>
                        </div>
                        <div class="row">
                            <table
                                    class="bordered striped"
                                    data-ng-if="selectedRo.requestOrderItems.length > 0">
                                <tr>
                                    <th>Product Id</th>
                                    <th>Product Name</th>
                                    <th>Requested Quantity</th>
                                    <th>Unit Of Measure</th>
                                    <th>Total Packaging Qty</th>
                                    <th>Packaging UOM</th>
                                </tr>
                                <tr data-ng-repeat="roi in selectedRo.requestOrderItems track by $index">
                                    <td><a data-ng-click="showPreview($event, roi.productId,'PRODUCT')">{{roi.productId}}</a>
                                    </td>
                                    <td>{{roi.productName}}</td>
                                    <td>{{roi.requestedAbsoluteQuantity}}</td>
                                    <td>{{roi.unitOfMeasure}}</td>
                                    <td>
                                        {{roi.requestedAbsoluteQuantity/productPackagingMappings[roi.productId].conversionRatio}}
                                    </td>
                                    <td>{{productPackagingMappings[roi.productId].packagingName}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <!--   printable All RO section   -->
                    <div  style="padding: 20px 50px; width: 100%; display: none;"  id="print-bulk" >
                        <div class="avoidPageBreak" data-ng-repeat="order in allRos" >
                            <div style="page-break-before: always;"></div>
                               <div class="print-border-table" >
                                   <p
                                           class="center-align"
                                           style="font-size: 24pt; font-weight: 700;">Request Order</p>
                                   <p style="font-size: 20pt;">Request Order ID: {{order.id}}</p>
                                   <p style="font-size: 20pt;">Requesting Unit:
                                       {{order.requestUnit.name}}({{companyMap[order.requestCompany.id].name}}) </p>
                                   <p style="font-size: 20pt;">Fulfillment Unit:
                                       {{order.fulfillmentUnit.name}}({{companyMap[order.fulfillmentCompany.id].name}})</p>
                                   <p style="font-size: 20pt;">Created On: {{order.generationTime |
                                   date:'dd/MM/yyyy'}}</p>
                                   <table
                                           style="border: 1px solid #000; width: 100%"
                                           align="center">
                                       <tr
                                               style="background-color: black; color: white; font-size: 12pt; font-weight: 700;">
                                           <th style="border-radius: 0px; text-align: center;">Product
                                               ID
                                           </th>
                                           <th style="border-radius: 0px; text-align: center;">Product
                                               Name
                                           </th>
                                           <th style="border-radius: 0px; text-align: center;">Requested
                                               Qty
                                           </th>
                                           <th style="border-radius: 0px; text-align: center;">UOM</th>
                                           <th style="border-radius: 0px; text-align: center;">Packaging
                                               Qty
                                           </th>
                                           <th style="border-radius: 0px; text-align: center;">Packaging
                                               UOM
                                           </th>
                                       </tr>
                                       <tr
                                               data-ng-repeat="roi in order.requestOrderItems track by $index"
                                               style="border-bottom: #000 1px solid; font-size: 12pt; page-break-inside: avoid;">
                                           <td style="text-align: center; border-right: 1px dashed #000;">{{roi.productId}}</td>
                                           <td style="text-align: center; border-right: 1px dashed #000;">{{roi.productName}}</td>
                                           <td style="text-align: center; border-right: 1px dashed #000;">
                                               {{roi.requestedAbsoluteQuantity}}
                                           </td>
                                           <td style="text-align: center; border-right: 1px dashed #000;">{{roi.unitOfMeasure}}</td>
                                           <td style="text-align: center;">
                                               {{roi.requestedAbsoluteQuantity/productPackagingMappings[roi.productId].conversionRatio}}
                                           </td>
                                           <td style="text-align: left;">
                                               {{productPackagingMappings[roi.productId].packagingName}}
                                           </td>
                                       </tr>
                                   </table>
                                   <div style="width: 100%; margin-top: 40px;">
                                       <div style="width: 49%; display: inline-block;">
                                           <p>Packed By: ______________________</p>
                                           <p>Security Guard: ______________________</p>
                                           <p>Date of Packing Finished: ______________________</p>
                                           <p>Time of Packing Finished: ______________________</p>
                                       </div>
                                       <div style="width: 49%; display: inline-block;">
                                           <div style="width: 100%;">
                                               <p
                                                       style="text-align: center; margin: 0; border: #000 1px solid; font-size: 12pt; font-weight: 700; padding: 5px; page-break-inside: avoid;">
                                                   Units
                                                   Packed</p>
                                               <p
                                                       style="text-align: left; margin: 0; border: #000 1px solid; font-size: 12pt; padding: 5px; page-break-inside: avoid;">
                                                   Big
                                                   Crate:</p>
                                               <p
                                                       style="text-align: left; margin: 0; border: #000 1px solid; font-size: 12pt; padding: 5px; page-break-inside: avoid;">
                                                   Medium
                                                   Crate:</p>
                                               <p
                                                       style="text-align: left; margin: 0; border: #000 1px solid; font-size: 12pt; padding: 5px; page-break-inside: avoid;">
                                                   Small
                                                   Crate:</p>
                                               <p
                                                       style="text-align: left; margin: 0; border: #000 1px solid; font-size: 12pt; padding: 5px; page-break-inside: avoid;">
                                                   Loose
                                                   Box:</p>

                               </div>
                        </div>
                                   </div>

                    </div>
                            <div style="page-break-after: always;"></div>
                               </div>
                    </div>

                    <!--   printable RO section   -->
                    <div
                            style="padding: 20px 50px; width: 100%; display: none;"
                            id="print-{{selectedRo.id}}">
                        <p
                                class="center-align"
                                style="font-size: 24pt; font-weight: 700;">Request Order</p>
                        <p style="font-size: 20pt;">Request Order ID: {{selectedRo.id}}</p>
                        <p style="font-size: 20pt;">Requesting Unit:
                            {{selectedRo.requestUnit.name}}({{companyMap[selectedRo.requestCompany.id].name}}) </p>
                        <p style="font-size: 20pt;">Fulfillment Unit:
                            {{selectedRo.fulfillmentUnit.name}}({{companyMap[selectedRo.fulfillmentCompany.id].name}})</p>
                        <p style="font-size: 20pt;">Created On: {{selectedRo.generationTime |
                            date:'dd/MM/yyyy'}}</p>
                        <table
                                style="border: 1px solid #000; width: 100%"
                                align="center">
                            <tr
                                    style="background-color: black; color: white; font-size: 12pt; font-weight: 700;">
                                <th style="border-radius: 0px; text-align: center;">Product
                                    ID
                                </th>
                                <th style="border-radius: 0px; text-align: center;">Product
                                    Name
                                </th>
                                <th style="border-radius: 0px; text-align: center;">Requested
                                    Qty
                                </th>
                                <th style="border-radius: 0px; text-align: center;">UOM</th>
                                <th style="border-radius: 0px; text-align: center;">Packaging
                                    Qty
                                </th>
                                <th style="border-radius: 0px; text-align: center;">Packaging
                                    UOM
                                </th>
                            </tr>
                            <tr
                                    data-ng-repeat="roi in selectedRo.requestOrderItems track by $index"
                                    style="border-bottom: #000 1px solid; font-size: 12pt; page-break-inside: avoid;">
                                <td style="text-align: center; border-right: 1px dashed #000;">{{roi.productId}}</td>
                                <td style="text-align: center; border-right: 1px dashed #000;">{{roi.productName}}</td>
                                <td style="text-align: center; border-right: 1px dashed #000;">
                                    {{roi.requestedAbsoluteQuantity}}
                                </td>
                                <td style="text-align: center; border-right: 3px solid #000;">{{roi.unitOfMeasure}}</td>
                                <td style="text-align: center;">
                                    {{roi.requestedAbsoluteQuantity/productPackagingMappings[roi.productId].conversionRatio}}
                                </td>
                                <td style="text-align: left;">
                                    {{productPackagingMappings[roi.productId].packagingName}}
                                </td>
                            </tr>
                        </table>
                        <div style="width: 100%; margin-top: 40px;">
                            <div style="width: 49%; display: inline-block;">
                                <p>Packed By: ______________________</p>
                                <p>Security Guard: ______________________</p>
                                <p>Date of Packing Finished: ______________________</p>
                                <p>Time of Packing Finished: ______________________</p>
                            </div>
                            <div style="width: 49%; display: inline-block;">
                                <div style="width: 100%;">
                                    <p
                                            style="text-align: center; margin: 0; border: #000 1px solid; font-size: 12pt; font-weight: 700; padding: 5px; page-break-inside: avoid;">
                                        Units
                                        Packed</p>
                                    <p
                                            style="text-align: left; margin: 0; border: #000 1px solid; font-size: 12pt; padding: 5px; page-break-inside: avoid;">
                                        Big
                                        Crate:</p>
                                    <p
                                            style="text-align: left; margin: 0; border: #000 1px solid; font-size: 12pt; padding: 5px; page-break-inside: avoid;">
                                        Medium
                                        Crate:</p>
                                    <p
                                            style="text-align: left; margin: 0; border: #000 1px solid; font-size: 12pt; padding: 5px; page-break-inside: avoid;">
                                        Small
                                        Crate:</p>
                                    <p
                                            style="text-align: left; margin: 0; border: #000 1px solid; font-size: 12pt; padding: 5px; page-break-inside: avoid;">
                                        Loose
                                        Box:</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>
<div
        id="planningItemsModal"
        class="modal modal-fixed-footer modal-large">
    <div class="modal-content">
        <div class="row margin0">
            <div class="col s10">
                <h5>Production Planning Items</h5>
            </div>
            <div class="col s2">
                <a
                        class="modal-action modal-close waves-effect waves-green btn right">
                    <span aria-hidden="true">cancel</span>
                </a>
            </div>
        </div>
        <h5 class="card-panel teal lighten-2 center">
            <span class="white-text center">{{itemsMap[openItemType].head}}</span>
        </h5>
        <table
                data-ng-if="itemsMap[openItemType].items.length>0"
                class="bordered striped">
            <thead>
            <tr>
                <th class="center-align">Product Name</th>
                <th class="center-align">Production Unit</th>
                <th class="center-align">Unit Of Measure</th>
                <th class="center-align">Requested Qty</th>
                <th class="center-align">Available Qty</th>
                <th class="center-align">To-be Procured</th>
                <th class="center-align">
                    <table style="width: 100%;">
                        <tr>
                            <td colspan="2" align="center">Buffered Rate</td>
                        </tr>
                        <tr>
                            <td>
                                <input data-ng-disabled="currentTab != 1" type="number" class="form-control" style="width: 40px;"
                                       data-ng-model="checkBoxModal.updatedBuffered"/>
                            </td>
                            <td>
                                <input data-ng-disabled="currentTab != 1"type="checkbox"
                                       style="width: 33px; height: 20px; position:inherit; opacity:1"
                                       data-ng-model="checkBoxModal.checkAllBuffered"
                                       data-ng-click="changeAllBuffered()"/>
                            </td>
                        </tr>
                    </table>
                </th>
                <th class="center-align">Buffered Quantity</th>
                <th class="center-align">Total Quantity</th>
                <th class="center-align">
                    <table style="width: 100%;">
                        <tr>
                            <td colspan="2" align="center">Batch Date</td>
                        </tr>
                        <tr>
                            <td>
                                <!--<select data-ng-disabled="currentTab != 1" data-placeholder="Select Expiry" data-ng-model="checkBoxModal.updatedExpiryDate">-->
                                    <!--<option data-ng-repeat="date in availableDatesForAll">{{date |date:'dd/MM/yyyy HH:mm:ss' : 'IST'}}</option>-->
                                <!--</select>-->
                                <select data-ng-disabled="currentTab > 0" data-placeholder="Select Expiry" data-ng-model="checkBoxModal.updatedExpiryDate"
                                        data-ng-options="e as e|date:'dd/MM/yyyy HH:mm:ss' : 'IST' for e in availableDatesForAll"></select>
                            </td>
                            <td>
                                <input data-ng-disabled="currentTab > 0"type="checkbox"
                                       style="width: 33px; height: 20px; position:inherit; opacity:1"
                                       data-ng-model="checkBoxModal.checkAllUpdatedDates"
                                       data-ng-click="changeAllExpiryDates()"/>
                            </td>
                        </tr>
                    </table>
                </th>
            </tr>
            </thead>
            <tbody>
            <tr
                    data-ng-repeat="item in itemsMap[openItemType].items track by $index">
                <td class="center-align"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a>
                </td>
                <td class="center-align" >{{item.productionName==null?'NA':item.productionName}}</td>
                <td class="center-align">{{item.unitOfMeasure}}</td>
                <td class="center-align">{{item.requestedQuantity | number : 4 }}</td>
                <td class="center-align">{{item.availableQuantity | number : 4 }}</td>
                <td class="center-align">{{item.totalQuantity - item.availableQuantity > 0 ? item.totalQuantity - item.availableQuantity : 0 | number : 4 }}</td>
                <td class="center-align">
                    <input data-ng-disabled="currentTab != 1 || item.recipeRequire != true || item.itemType != 'REQUESTED'" data-ng-change="changeBufferedQuantity(item)"
                           style="width:40px;"  data-ng-model="item.bufferedPercentage" step="0.01" />
                <td class="center-align"> <input  data-ng-disabled="currentTab != 1 || item.recipeRequire != true || item.itemType != 'REQUESTED'"
                    data-ng-model="item.bufferedQuantity" data-ng-change="changeBufferPercentage(item)"  style="width:40px;"   oninput="this.value=(parseInt(this.value)||0)" placeholder="0-9"></td>
                <td class="center-align">{{item.totalQuantity | number : 4 }}</td>
                <td class="center-align" >
                    <div>
                        <span>Current Date:</span>
                        <span>{{item.currentDate | date : 'dd/MM/yyyy'}}</span>
                    </div>
                    <div>
                        <div class="row">
                            <div><select data-ng-disabled="currentTab > 0 || item.recipeRequire != true || item.itemType != 'REQUESTED'" data-placeholder="Select Expiry" data-ng-model="item.expiryDate"
                                               data-ng-options="e as e|date:'dd/MM/yyyy HH:mm:ss' : 'IST' for e in item.availableDates"></select></div>
                        </div>
                    </div>
                </td>
            </tr>
            </tr>
            </tbody>
        </table>
        <div
                data-ng-if="itemsMap[openItemType].items.length==0"
                class="text-center-disabled">No Items to display.
        </div>
    </div>
    <div
            class="modal-footer"
            data-ng-if="currentTab == 1">
        <button
                class="modal-action waves-effect waves-green btn right"
                style="margin-right: 10px"
                data-ng-click="switchtab(1)">Next
        </button>
    </div>
    <div
            class="modal-footer"
            data-ng-if="currentTab == 2">
        <button
                class="modal-action waves-effect waves-green btn left"
                style="margin-left: 10px"
                data-ng-if="currentTab > 1"
                data-ng-click="switchtab(-1)">Previous
        </button>
        <button
                class="modal-action waves-effect waves-green btn right"
                data-ng-if="currentTab < 3"
                style="margin-right: 10px"
                data-ng-click="switchtab(1)">Next
        </button>
    </div>
    <div
            class="modal-footer"
            data-ng-if="currentTab == 3">
        <button
                class="modal-action waves-effect waves-green btn left"
                style="margin-left: 10px"
                data-ng-if="currentTab > 1"
                data-ng-click="switchtab(-1)">Previous
        </button>
        <button
                class="modal-action waves-effect waves-green btn right red"
                data-ng-if="currentTab == 3"
                style="margin-right: 10px"
                data-ng-click="updatePlanItems()">Submit
        </button>
    </div>
</div>
