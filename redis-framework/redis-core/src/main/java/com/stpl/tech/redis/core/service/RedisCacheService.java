package com.stpl.tech.redis.core.service;

import com.stpl.tech.redis.domain.model.*;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

public interface RedisCacheService {

	public Unit getUnit(int id) throws IOException, URISyntaxException;

	public List<ProductRecipes> getProductRecipes(List<Integer> productIds) throws URISyntaxException;

	public UnitBasicDetail getUnitbasicDetails(int primaryUnitId);

	public ProductUnit getProductUnit(String region) throws URISyntaxException;

	public DeliveryUnit getDeliveryUnit(int id);

	public void loadUnitCache() throws IOException, URISyntaxException;

	public List<Location> loadLocationCache() throws IOException, URISyntaxException;

	public void clearUnitCache() throws IOException, URISyntaxException;

	public void removeAllUnits();

	public void removeAllUnitBasicDetails();

    Map<Integer, Map<Integer, List<ProductRecipeKey>>> loadUnitProductPriceProfile() throws URISyntaxException;

    Map<Integer, List<ProductRecipeKey>> loadUnitProductPriceProfileForUnit(int unitId) throws URISyntaxException;

    public List<IdName> getWebCategories();

	public List<UnitBasicDetail> getAllUnitBasicDetails() throws URISyntaxException;
	
	public void loadAll() throws URISyntaxException;

	public ProductRecipes getProductRecipe(int pId) throws URISyntaxException;

	public List<ProductRecipes> getAllProductRecipes();

    Map<Integer, String> getAllProductImages() throws URISyntaxException;

    public UnitBusinessHours getBusinessHours(int unitId);

	public UnitDataVO getUnitData(int id) throws IOException, URISyntaxException;

	public List<ProductBasicDetail> getAllProductBasicDetails() throws URISyntaxException;

	SkuCodeProductImageMappingDetail getProductImagesForSkuCodeByUnitId(int unitId, String skuCode);

	void clearCacheForUnit(int unitId);
	IndexUrl getSingleProductImage (int unitId, int productId) throws IOException, URISyntaxException;

    List<Integer> getSubscriptionProducts(int unitId) throws IOException, URISyntaxException;

    UnitSubscriptionProductsImageMapping getSubscriptionProductImagesForUnit(int unitId) throws IOException, URISyntaxException;
}
