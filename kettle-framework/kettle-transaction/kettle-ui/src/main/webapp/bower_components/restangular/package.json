{"name": "restangular", "description": "Restful Resources service for AngularJS apps", "version": "1.5.1", "filename": "restangular.min.js", "main": "./dist/restangular.min.js", "homepage": "https://github.com/mgonto/restangular", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git://github.com/mgonto/restangular.git"}, "engines": {"node": ">= 0.9"}, "keywords": ["angular", "client", "browser", "restful", "resources", "rest", "api"], "maintainers": [{"name": "<PERSON>", "website": "http://gon.to/"}], "dependencies": {"lodash": ">=1.3.0"}, "devDependencies": {"grunt-cli": ">= 0.1.7", "grunt-contrib-concat": "*", "grunt-contrib-jshint": "*", "grunt-contrib-uglify": "*", "grunt-bower": "*", "grunt-bower-task": "*", "grunt-karma": "latest", "grunt-conventional-changelog": "0.0.12", "grunt-zip": "*", "karma": "~0.12.1", "karma-mocha-reporter": "0.2.8", "karma-jasmine": "~0.1.5", "karma-chrome-launcher": "~0.1.2", "karma-phantomjs-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3"}, "scripts": {"test": "grunt test --verbose"}, "license": "MIT"}