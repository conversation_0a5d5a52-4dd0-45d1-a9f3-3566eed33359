
export const styles = {
    parentContainer: {
        height:window.innerHeight,
        width:window.innerWidth,
        display:'none',
        flexDirection:'row',
    },
    leftChildContainer:{
        flex:'50%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent:'space-around',
        alignItems: 'center'
    },
    rightChildContainer:{
        flex:'50%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent:'flex-start',
        alignItems: 'center',
        backgroundImage: `url("./../../../img/SlotGame/desktopBalloons.webp")`,
        backgroundSize:'contain',
        backgroundRepeat: 'no-repeat'
    },
    slotMachineContainer: {
        width: "70%",
        height: "45%",
        backgroundSize: 'contain',
        backgroundImage: `url("./../../../img/SlotGame/slotMachine.webp")`,
        backgroundRepeat:'no-repeat',
        backgroundPosition:'center',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        marginLeft: '20px',
        flexDirection:'column'
    },
    slotBoxContainer: {
        width:'65%',
        height:'40%',
        marginTop:'10%',
        display:'flex',
        flexDirection:'row',
        justifyContent:'space-around',
        alignItems:'center',
        maxHeight:window.innerHeight*.4*.4,
        paddingLeft:'10px',
        paddingRight:'10px'
    },
    winJackPotText:{
        fontWeight:'bold',
        marginBottom: '10px',
        color:'#f2df95',
        marginRight: '6%'
    },
    formAndButtonContainer: {
        width: "68%",
        height: "50%",
        display:'flex',
        flexDirection:'column',
        justifyContent:'center',
        alignItems:'center'
    }, door: {
        background: '#fafafa',
        boxShadow: '0 0 3px 2px rgba(0, 0, 0, 0.4) inset',
        width: '30%',
        height: '90%',
        overflow: 'hidden',
        borderRadius: '10px',
        margin: '1px'
    },
    boxes: {
        transition: 'transform 1s ease-in-out'
    },
    spinButton: {
        width:'60%',
        height:'50px',
        borderRadius: '100px',
        backgroundColor: 'green',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        marginTop:'50px',
        boxShadow:'1px 1px 10px grey'
    },
    phoneNumberBox: {
        display:'flex',
        flexDirection:'row',
        height : '50px',
        width: '100%',
        borderRadius:'5px',
    },
    phoneNumberBoxChildLeft:{
        flex:2,
        borderRadius:'5px 0px 0px 5px',
        backgroundColor: '#0a6001',
        height:'100%',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        padding: '0 10px'
    },
    phoneNumberBoxChildRight:{
        flex:10,
        borderRadius:'0px 5px 5px 0px',
        backgroundColor: 'white',
        height:'100%',
        borderRight: '2px solid #0a6001',
        borderTop: '2px solid #0a6001',
        borderBottom: '2px solid #0a6001',
    },
    phoneNumberTextField: {
        width: '100%',
        height:'100%',
        marginLeft: 10,
        backgroundColor: 'transparent',
        border: 0,
        fontSize: 15,
        color: '#0a6001',
        fontWeight:'bold',
        fontFamily:'Nunito'
    },
    spinButtonText: {
        color: 'white',
        fontSize: '16px',
        fontWeight:'bold',
        fontFamily:'Nunito'
    },
    smallGreyText: {
        color:'#0a6001',
        fontFamily: 'Nunito',
        fontSize:'13px',
        fontWeight: 'bold',
        textAlign: 'center',
        width:'100%'
    },
    modal: {
        display:'block',
        position:'fixed',
        zIndex: 1,
        left:0,
        top:0,
        width:'100%',
        height:'100%',
        overflow: "auto",
        backgroundColor:'rgb(0,0,0,.7)',
    },
    modalBody: {
        padding: '20px',
        backgroundRepeat:'no-repeat',
        backgroundSize:'contain',
        backgroundPosition: 'center',
        backgroundColor:'rgb(0,0,0,0)',
        display:'flex',
        flexDirection:'column',
        justifyContent:'flex-end',
        alignItems:'center',
        left:0,
        width:'100%',

    },
    downloadImg: {
        width: '100%',
    },
    downloadStoreContainer: {
        width: '49%',

    },
    downloadContainer: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '80%',
        marginLeft: '10%',

    },
    downloadText: {
        marginTop: 20,
        textAlign: 'center',
        marginBottom: 3,
        fontFamily: 'Nunito',
        fontSize: '10px',
        color: '#4a4a4a',
    }, couponCodeBox: {
        width:'100%',
        backgroundColor:'white',
        border: '2px dashed green',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        padding:'5px 25px 5px 25px',
        borderRadius:'100px',

    },
    couponCodeContainer: {
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        flexDirection:'row'
    },
    tncContainer: {
        display:'flex',
        justifyContent:'flex-start',
        alignItems:'center',
        borderRadius:'10px',
        border:'1px green solid',
        flexDirection:'column',
        padding:'5px',
        minWidth:'20%'
    },
    tncText:{
        fontSize:'10px',
        color:'green',
        wordSpacing:'2px',
        textAlign:'center'
    },
    youHaveWonText: {
        fontSize: '15px',
        color: '#093D16',
        fontWeight: 'bold'
    },
    offerTextStyle: {
        fontSize:'45px',
        color:'#C2541A',
        fontWeight:'bold'
    },
    couponCodeText: {
        fontSize:'15px',
        color:'green',
        fontWeight:'bold',
        letterSpacing: '1.5px',
    },
    useCodeText:{
        fontSize:'19px',
        color:'green',
        fontWeight:'bolder'
    },
    tncHeading: {
        fontSize:'10px',
        color:'green',
        wordSpacing:'2px',
        textDecoration:'underline',
        marginBottom:'10px'
    },
    checkBox: {
        height:'20px',
        width:'20px',
        borderRadius:'7px',
        border:'2px solid #008000',
        backgroundSize:'contain',
        backgroundRepeat:'no-repeat',
        backgroundColor:'#e9e9e9',
        boxShadow: '1px 1px 10px #a5a5a5',
        display:'flex',
        justifyContent:'center',
        alignItems:'center'
    },
    exploreMenuButton: {
        width:'10%',
        height:'25px',
        borderRadius:'100px',
        backgroundColor:"#3c8616",
        flexDirection:'column',
        display:'flex',
        justifyContent:'center',
        justifyItems: 'center'
    }

};
