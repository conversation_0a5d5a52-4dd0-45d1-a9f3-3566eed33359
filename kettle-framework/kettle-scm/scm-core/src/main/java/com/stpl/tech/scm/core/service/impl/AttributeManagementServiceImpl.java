package com.stpl.tech.scm.core.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.service.AttributeManagementService;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.AttributeManagementDao;
import com.stpl.tech.scm.data.model.AttributeDefinitionData;
import com.stpl.tech.scm.data.model.AttributeValueData;
import com.stpl.tech.scm.domain.model.AttributeDefinition;
import com.stpl.tech.scm.domain.model.AttributeType;
import com.stpl.tech.scm.domain.model.AttributeValue;
import com.stpl.tech.scm.domain.model.CategoryAttributeMapping;
import com.stpl.tech.scm.domain.model.CategoryAttributeValue;
import com.stpl.tech.scm.domain.model.SwitchStatus;

/**
 * Created by Rahul Singh on 05-05-2016.
 */

@Service
public class AttributeManagementServiceImpl implements AttributeManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(AttributeManagementServiceImpl.class);

    @Autowired
    private AttributeManagementDao attributeManagementDao;

    @Autowired
    private SCMCache scmCache;

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<AttributeType, List<AttributeDefinition>> viewAllAttributeDefinitions() {
        Map<AttributeType, List<AttributeDefinition>> attributeTypeListMap = new TreeMap<AttributeType, List<AttributeDefinition>>();
        for (AttributeDefinition ad : scmCache.getAttributeDefinitions().values()) {
            List<AttributeDefinition> attributeDefinitionList = attributeTypeListMap.get(ad.getAttributeType());
            if (attributeDefinitionList == null) {
                attributeDefinitionList = new ArrayList<AttributeDefinition>();
            }
            if (!attributeDefinitionList.contains(ad)) {
                attributeDefinitionList.add(ad);
            }
            attributeTypeListMap.put(ad.getAttributeType(), attributeDefinitionList);
        }
        return attributeTypeListMap;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, List<AttributeValue>> viewAllAttributeValues() {
        Map<Integer, List<AttributeValue>> attributeDefinitionListMap = new TreeMap<Integer, List<AttributeValue>>();
        for (AttributeValue av : scmCache.getAttributeValues().values()) {
            List<AttributeValue> attributeValues = attributeDefinitionListMap.get(av.getAttributeDefinitionId());
            if (attributeValues == null) {
                attributeValues = new ArrayList<AttributeValue>();
            }
            if (!attributeValues.contains(av)) {
                attributeValues.add(av);
            }
            attributeDefinitionListMap.put(av.getAttributeDefinitionId(), attributeValues);
        }
        return attributeDefinitionListMap;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<AttributeValueData> addNewAttributeValue(List<AttributeValue> attributeValues) throws SumoException {
        boolean flag = false;
        List<AttributeValueData> attributeValueDataList = new ArrayList<>();
        for (AttributeValue attributeValue : attributeValues) {
            AttributeValueData attributeValueData = new AttributeValueData();
            attributeValueData.setAttributeDefinition(attributeManagementDao.find(AttributeDefinitionData.class, attributeValue.getAttributeDefinitionId()));
            attributeValueData.setAttributeValueId(attributeValue.getAttributeValueId());
            attributeValueData.setAttributeValue(attributeValue.getAttributeValue());
            attributeValueData.setAttributeValueStatus(attributeValue.getAttributeValueStatus().value());
            attributeValueData.setAttributeValueShortCode(attributeValue.getAttributeValueShortCode());
            AttributeValueData data = attributeManagementDao.add(attributeValueData,false);
            attributeValueDataList.add(data);
            flag = updateAttributeValueCache(data);
        }
        attributeManagementDao.flush();
        return attributeValueDataList;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateAttributeValue(AttributeValue attributeValue) {
        AttributeValueData attributeValueData = attributeManagementDao.find(AttributeValueData.class, attributeValue.getAttributeValueId());
        if (attributeValueData != null) {
            attributeValueData.setAttributeDefinition(attributeManagementDao.find(AttributeDefinitionData.class, attributeValue.getAttributeDefinitionId()));
            attributeValueData.setAttributeValueId(attributeValue.getAttributeValueId());
            attributeValueData.setAttributeValue(attributeValue.getAttributeValue());
            attributeValueData.setAttributeValueStatus(attributeValue.getAttributeValueStatus().value());
            attributeValueData.setAttributeValueShortCode(attributeValue.getAttributeValueShortCode());
            return updateAttributeValueCache(attributeManagementDao.update(attributeValueData,false));
        }
        attributeManagementDao.flush();
        LOG.info("Attribute Value productId: {} not found!", attributeValue.getAttributeValueId());
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, List<AttributeValue>> getAllAttributeValuesByAttribute(int categoryId, boolean onlyActive) {
        Map<Integer, List<AttributeValue>> attributeToAttributeValueMap = new TreeMap<Integer, List<AttributeValue>>();
        for (CategoryAttributeValue cav : scmCache.getCategoryAttributeValues().values()) {
            CategoryAttributeMapping cam = scmCache.getCategoryAttributeMappings().get(cav.getCategoryAttributeMappingId());
            if (cam.getCategoryDefinition().getId() == categoryId) {
                List<AttributeValue> attributeValues = attributeToAttributeValueMap.get(cam.getAttributeDefinition().getId());
                if (attributeValues == null) {
                    attributeValues = new ArrayList<AttributeValue>();
                }
                if (!attributeValues.contains(scmCache.getAttributeValues().get(cav.getAttributeValue().getId()))) {
                    if (onlyActive) {
                        AttributeValue av = scmCache.getAttributeValues().get(cav.getAttributeValue().getId());
                        if (av.getAttributeValueStatus().equals(SwitchStatus.ACTIVE)) {
                            attributeValues.add(av);
                        }
                    } else {
                        attributeValues.add(scmCache.getAttributeValues().get(cav.getAttributeValue().getId()));
                    }
                    attributeToAttributeValueMap.put(cam.getAttributeDefinition().getId(), attributeValues);
                }
            }
        }
        return attributeToAttributeValueMap;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public AttributeDefinition addAttribute(AttributeDefinition attributeDefinition) throws SumoException {
        AttributeDefinitionData attributeDefinitionData = SCMDataConverter.convert(attributeDefinition);
        attributeDefinitionData = attributeManagementDao.add(attributeDefinitionData, true);
        attributeDefinition = SCMDataConverter.convert(attributeDefinitionData);
        scmCache.getAttributeDefinitions().put(attributeDefinition.getAttributeId(), attributeDefinition);
        return attributeDefinition;
    }

    @Override
    public AttributeDefinition updateAttribute(AttributeDefinition attributeDefinition) throws SumoException {
        AttributeDefinitionData attributeDefinitionData = attributeManagementDao.find(AttributeDefinitionData.class, attributeDefinition.getAttributeId());
        if(attributeDefinitionData != null) {
            attributeDefinitionData = SCMDataConverter.convert(attributeDefinition);
            attributeDefinitionData = attributeManagementDao.update(attributeDefinitionData, true);
            attributeDefinition = SCMDataConverter.convert(attributeDefinitionData);
            scmCache.getAttributeDefinitions().put(attributeDefinition.getAttributeId(), attributeDefinition);
            return attributeDefinition;
        } else {
            throw new SumoException("Attribute updation failure", "Error while updating attribute.");
        }
    }

    private boolean updateAttributeValueCache(AttributeValueData attributeValueData) {
        if (attributeValueData != null) {
            scmCache.getAttributeValues().put(attributeValueData.getAttributeValueId(), SCMDataConverter.convert(attributeValueData));
            return true;
        }
        return false;
    }
}
