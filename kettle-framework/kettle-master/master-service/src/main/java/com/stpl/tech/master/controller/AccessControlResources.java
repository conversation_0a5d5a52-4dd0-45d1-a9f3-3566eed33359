package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.external.cache.ApiTokenCache;
import com.stpl.tech.master.core.service.AccessControlService;
import com.stpl.tech.master.domain.model.AccessControlList;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.PreAuthApi;
import com.stpl.tech.master.domain.model.UserRole;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.stpl.tech.master.service.core.MasterServiceConstants.ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

/**
 * Created by Rahul Singh on 08-07-2016.
 */
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT) // 'v1/access-control-management'
public class AccessControlResources {

    private static final Logger LOG = LoggerFactory.getLogger(AccessControlResources.class);

    @Autowired
    private AccessControlService accessControlService;

    @Autowired
    private ApiTokenCache apiTokenCache;

    @RequestMapping(method = RequestMethod.GET, value = "pre-authenticated-apis", produces = MediaType.APPLICATION_JSON)
    public List<PreAuthApi> getAllPreAuthenticatedApi() {
        LOG.info("Request to get all-pre-authenticated-api");
        return accessControlService.getAllPreAuthenticatedApi();
    }

    @RequestMapping(method = RequestMethod.POST, value = "pre-authenticated-api", consumes = MediaType.APPLICATION_JSON, produces = MediaType.TEXT_PLAIN)
    public String addPreAuthenticatedApi(@RequestBody final PreAuthApi api) {
        LOG.info("Request to add-pre-authenticated-api {}", api);
        return accessControlService.addPreAuthenticatedApi(api);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "deactivate-pre-authenticated-api")
    public boolean deactivatePreAuthenticatedApi(@RequestBody final int apiId) {
        LOG.info("Request to deactivate pre-authenticated-api id {}", apiId);
        return accessControlService.deactivatePreAuthenticatedApi(apiId);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "activate-pre-authenticated-api")
    public boolean activatePreAuthenticatedApi(@RequestBody final int apiId) {
        LOG.info("Request to activate pre-authenticated-api id {}", apiId);
        return accessControlService.activatePreAuthenticatedApi(apiId);
    }

    //TODO move this to metadata
    @RequestMapping(method = RequestMethod.GET, value = "applications", produces = MediaType.APPLICATION_JSON)
    public List<IdName> getApplications(){
        LOG.info("Request to get all applications");
        List<IdName> applications = new ArrayList<>();
        for (ApplicationName applicationName:ApplicationName.values()) {
            applications.add(new IdName(applicationName.id(), applicationName.value()));
        }
        return applications;
    }

    @RequestMapping(method = RequestMethod.GET, value = "access-controls", produces = MediaType.APPLICATION_JSON)
    public List<AccessControlList> getAccessControls(@RequestParam(required = false) final ApplicationName appName) {
        LOG.info("Request to get all access controls");
        return accessControlService.getAccessControls(appName);
    }

    @RequestMapping(method = RequestMethod.POST, value = "access-controls", produces = MediaType.APPLICATION_JSON)
    public boolean addAccessControl(@RequestBody final AccessControlList accessControlList) {
        LOG.info("Request to get all access controls");
        return accessControlService.addAccessControl(accessControlList);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "deactivate-access-control", produces = MediaType.APPLICATION_JSON)
    public boolean deactivateAccessControl(@RequestBody final int aclId) {
        LOG.info("Request to deactivate access controls id {}", aclId);
        return accessControlService.deactivateAccessControl(aclId);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "activate-access-control", produces = MediaType.APPLICATION_JSON)
    public boolean activateAccessControl(@RequestBody final int aclId) {
        LOG.info("Request to activate access controls id {}", aclId);
        return accessControlService.activateAccessControl(aclId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate-api-token", produces = MediaType.TEXT_PLAIN)
    public String generate() {
        return apiTokenCache.generateApiToken();
    }

    @RequestMapping(method = RequestMethod.POST, value = "invalidate-all-api-tokens", produces = MediaType.APPLICATION_JSON)
    public boolean flushAllApiTokens() {
        return apiTokenCache.flushAll();
    }



    @RequestMapping(method = RequestMethod.GET, value = "user-roles/application", produces = MediaType.APPLICATION_JSON)
    public List<UserRole> getUserRolesByApplication(@RequestParam final ApplicationName appName) {
        LOG.info("Request to get user roles for application" + appName);
        return accessControlService.getUserRolesByApplication(appName);
    }
}
