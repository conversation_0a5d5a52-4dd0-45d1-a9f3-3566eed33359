import RestService from "../services/RestService";
import apis from "../services/APIs";
import UtilityService from "../services/UtilityService";

export function getTargets(type, targetType, handlerId, date, week, month, year) {
    return dispatch => {
            let data = {
                        "type": type,
                        "targetType": targetType,
                        "handlerId": handlerId,
                        "businessDate": date,
                        "week": week,
                        "month": month,
                        "year": year
            };
            dispatch({type: "SET_SELECTED_TARGET_TYPE", payload: targetType});
            dispatch({type: "SET_SELECTED_HANDLER_ID", payload: handlerId});
            dispatch({type: "SALES_TARGETS_LOADING", payload: true});
            dispatch({type: "SET_ERROR_TARGETS_MISSING", payload: false});
            RestService.postJSON(apis.getUrls().targets.fetchTargets, data).then((response) => {

                    if (response != null && response.targetType != null) {

                        let targetsResponse = UtilityService.getTargetsResponseObj();
                        targetsResponse.primaryHandler = handlerId;
                        targetsResponse.retailerCount50kg = response.retailerCount50kg;
                        targetsResponse.retailerSale50kg = response.retailerSale50kg;
                        targetsResponse.retailerCount30kg = response.retailerCount30kg;
                        targetsResponse.retailerSale30kg = response.retailerSale30kg;
                        targetsResponse.retailerCount4kg = response.retailerCount4kg;
                        targetsResponse.retailerSale4kg = response.retailerSale4kg;
                        targetsResponse.retailers50Cup = response.retailers50Cup;
                        targetsResponse.distinctHandlers = response.distinctHandlers;
                        targetsResponse.totalSales = response.totalSales;
                        targetsResponse.totalRetailers = response.totalRetailers;
                        targetsResponse.totalKilograms = response.totalKilograms;
                        targetsResponse.totalPacks = response.totalPacks;
                        targetsResponse.targetType = response.targetType;

                        if(type === "TARGET"){
                            dispatch({type: "SET_TARGETS", payload: targetsResponse});
                        }
                        if(type === "ACHIEVED"){
                            dispatch({type: "SET_ACHIEVED_TARGETS", payload: targetsResponse});
                        }
                        dispatch({type: "SALES_TARGETS_LOADING", payload: false});
                    } else {
                        if(type === "ACHIEVED"){
                            dispatch({type: "SET_ACHIEVED_TARGETS", payload: UtilityService.getTargetsResponseObj()});
                        }else{
                            dispatch({type: "SET_TARGETS", payload: null});
                            dispatch({type: "SET_ACHIEVED_TARGETS", payload: null});
                            dispatch({type: "SET_ERROR_TARGETS_MISSING", payload: true});
                        }
                        dispatch({type: "SALES_TARGETS_LOADING", payload: false});
                    }
            }).catch((error) => {
                console.log(error);
                dispatch({type: "SET_TARGETS", payload: null});
                dispatch({type: "SET_ACHIEVED_TARGETS", payload: null});
                dispatch({type: "SALES_TARGETS_LOADING", payload: false});
            });
    }
}


