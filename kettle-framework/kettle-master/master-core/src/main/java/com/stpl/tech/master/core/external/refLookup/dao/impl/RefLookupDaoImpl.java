package com.stpl.tech.master.core.external.refLookup.dao.impl;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.refLookup.dao.RefLookupDao;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.RefLookup;
import com.stpl.tech.master.data.model.RefLookupType;
import com.stpl.tech.master.domain.model.ListData;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;

@Repository
@Log4j2
public class RefLookupDaoImpl extends AbstractMasterDaoImpl implements RefLookupDao {

    @Override
    @Cacheable(value = "allListData", key = "#group")
    public List<ListData> getAllListData(String group, boolean getAll) throws DataNotFoundException {
        try {
            List<ListData> addOns = new ArrayList<>();
            List<RefLookupType> types = getLookupTypes(group);
            for (RefLookupType type : types) {
                addOns.add(MasterDataConverter.convert(type, getAll));
            }
            return addOns;
        } catch (NoResultException e) {
            throw new DataNotFoundException(String.format("Did not find RefLookup Data with type %s", group), e);
        }
    }

    @Override
    @CacheEvict(value = "allListData", allEntries = true)
    @Scheduled(fixedRate = 24 * 60 * 1000)
    public void refreshAllListData() {}

    private List<RefLookupType> getLookupTypes(String groupName) {
        try {
            Query query = manager.createQuery("FROM RefLookupType E where E.rtlGroup = :groupName order by E.rtlId");
            query.setParameter("groupName", groupName);
            return query.getResultList();
        }catch (Exception e){
            log.error("Error while fetching lookup types for groupName : {}", groupName);
        }
        return new ArrayList<>();
    }
}
