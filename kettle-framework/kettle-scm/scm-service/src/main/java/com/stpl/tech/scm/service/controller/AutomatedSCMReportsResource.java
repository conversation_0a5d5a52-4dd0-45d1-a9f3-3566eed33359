
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.service.controller;

import java.io.IOException;
import java.text.ParseException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.stpl.tech.kettle.report.metadata.model.ReportCategories;
import com.stpl.tech.master.core.service.AbstractAutomatedReports;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JaxbUtil;

@Component
public class AutomatedSCMReportsResource extends AbstractAutomatedReports {

	@Autowired
	private EnvProperties props;

	// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 10 * * *", zone = "GMT+05:30")
	public void receivingDetailsMilk() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/ReceivingDetailsMilk.xml");

			executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getCurrentBusinessDate()));
		}
	}
	
	// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 11 * * *", zone = "GMT+05:30")
	public void requestOrderDetailsMilk() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/RequestOrderDetailsMilk.xml");

			executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getCurrentBusinessDate()));
		}
	}

	// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 12 * * *", zone = "GMT+05:30")
	public void giftCardRequestOrders() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/GiftCardRequestOrders.xml");
			executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getCurrentBusinessDate()));
		}
	}
	
	// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 13 * * *", zone = "GMT+05:30")
	public void receivingDetailsBakery() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/ReceivingDetailsBakery.xml");

			executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getCurrentBusinessDate()));
		}
	}

	// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 14 * * *", zone = "GMT+05:30")
	public void requestOrderDetailsBakery() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/RequestOrderDetailsBakery.xml");

			executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getCurrentBusinessDate()));
		}
	}

	// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 18 * * *", zone = "GMT+05:30")
	public void pendingreceivingDetails() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/PendingReceivingDetails.xml");

			executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getCurrentBusinessDate()));
		}
	}
	
	@Scheduled(cron = "0 0 17 * * *", zone = "GMT+05:30")
	public void ReportsFor7PM() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/ReportsFor5PM.xml");

			executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getCurrentBusinessDate()));
		}
	}
	
	@Scheduled(cron = "0 0 10 * * *", zone = "GMT+05:30")
	public void gatepassReturnNotification() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/GatepassReturn.xml");

			executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getCurrentBusinessDate()));
		}
	}


	@Override
	public EnvType getEnvironmentType() {
		return props.getEnvType();
	}

	@Override
	public String getBasePath() {
		return props.getBasePath();
	}

}
