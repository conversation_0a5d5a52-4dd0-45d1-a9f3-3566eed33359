


*{
  font-family: 'Nunito';
}





/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------ChaayosDestopLayout.js---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/

.maincontainer {
  position: relative;
  width: 100%;
  margin: 0vw 0vw -0.1945vw 0vw;
  }
  
.mainChaayosSelectLogo {
  position: absolute;
  height: 13vh;
  width: 20vw;
  left: 75vw;
  }
  
.mainfirstimg  {

    width: 81vw;
    height: 105vh;
    margin-left: 19vw;
  
  
  }
  .mainchaayosSelectText1 {
  position: absolute;
  top: 23vh;
  font-size: 8vh;
  color: #0E8B45;
  font-weight: bolder;
  left: 5vw;
  }
  
  .mainchaayosSelectText2 {
  position: absolute;
  top: 45vh;
  font-size: 3vw;
  color: #0E8B45;
  left: 5vw;
  font-weight: bold;
  }
  
  .mainchaayosSelectText3 {
  position: absolute;
  top: 53vh;
  font-size: 3vmin;
  color: black;
  font-weight: bolder;
  left: 5vw;
  }
  .mainchaayosSelectText4{
    position: absolute;
    top: 57vh;
    font-size: 2vmin;
    left: 5vw;
  }

  .mainbenefits {

    overflow: hidden;
    position: absolute;
    top: 35vh;
    left: 5vw;
    line-height: 12vh;
    height: 4.2vw;
    font-size: 4vw;
    font-weight: bold;
  
  }
  
  .mainbenefits ul {
  
    -webkit-animation: scrollUp 6s linear infinite ;
  
            animation: scrollUp 6s linear infinite ;
   
    
  }
  .mainbenefits ul li {
    opacity: 1;
  
    list-style: none;
  }
  
  
  
  @-webkit-keyframes scrollUp {
    from {
     
        -webkit-transform: translateY(0);
     
                transform: translateY(0);
    }
    to {
        -webkit-transform: translateY(-80%);
                transform: translateY(-80%);
    }
  }
  
  
  
  @keyframes scrollUp {
    from {
     
        -webkit-transform: translateY(0);
     
                transform: translateY(0);
    }
    to {
        -webkit-transform: translateY(-80%);
                transform: translateY(-80%);
    }
  }
  

/*----------------------------------------*/

.mainsecondContainer{
  position: relative;
    height: 200vh;
    width: 100vw;
}

.mainellipse{
  
  position: absolute;
  top: -36vh;
  height: 208vh;
  width: 90vw;   
}

.maincircles {
position: relative;
display: -webkit-box;
display: -ms-flexbox;
display: flex;
-ms-flex-wrap: wrap;
    flex-wrap: wrap;
-ms-grid-columns: auto auto auto;
grid-template-columns: auto auto auto;
top: 28vh;
-webkit-box-pack: justify;
    -ms-flex-pack: justify;
        justify-content: space-between;
margin: 0vh 10vw;
}

.mainCircleWithText{
  text-align: center;
  width: 30vmin;
}
.mainCircle1 {
/* filter: url(drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.15))); */
height: 30vmin;
width: 30vmin;
border-radius: 50%;
background-image: url(./../../../img/ChaayosSelectMembership_images/gradient_image.png);
display: -webkit-box;
display: -ms-flexbox;
display: flex;
border: 1.5vmin solid #FFFFFF;
-webkit-box-shadow: 0px 2px 4px rgb(0 0 0 / 15%);
        box-shadow: 0px 2px 4px rgb(0 0 0 / 15%);
background-repeat: no-repeat;
background-position: center;
-webkit-box-pack: center;
    -ms-flex-pack: center;
        justify-content: center;
}
  
.mainWCCS{
  color: #0E8B45;
  font-size: 8vh;
  text-align: center;
  font-weight: bolder;
  width: 100vw;
  margin: 0vh 0vw 10vh;

}

.maingifs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: space-evenly;
      -ms-flex-pack: space-evenly;
          justify-content: space-evenly;
}
.maingifWithText{
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 16vw;
  justify-content: center;
  align-items: center;
  text-align:center;
   
}
.maingif {
  -webkit-box-shadow: 0px 2px 4px rgb(0 0 0 / 15%);
          box-shadow: 0px 2px 4px rgb(0 0 0 / 15%);
  border-radius: 15px;
  padding: 1vw;
  height: 35vh;
  width: 16vw;
  background: white;
  }


  
.maingifheadings {
  font-weight: bold;
  font-size: 4vmin;
  margin: 5vh 0vw 3vh;
  }


.mainSubscribebtn {
  position: absolute;

  width: 27%;
  height:9vh;

  top: 65%;
  left: 5%;
  z-index:3;

  -o-text-overflow: clip;

     text-overflow: clip;
  background-color: #0E8B45;
  color: white;
  font-size: 2vw;

  border: none;
  cursor: pointer;
  border-radius: 108px;

  
  display: -webkit-box;

  
  display: -ms-flexbox;

  
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-shadow:10px 14px 30px rgba(0, 0, 0, 0.2);
          box-shadow:10px 14px 30px rgba(0, 0, 0, 0.2)

    
}

.mainSubscribebtn:hover,.mainsavingCalcSubscribeBtn:hover{
  background-color: #C6E3BA !important;
}
.mainSubscribebtn:active,.mainsavingCalcSubscribeBtn:active{
  background-color: #383838 !important;
}




.mainSecondarySubscribeBtn{

  width: 18vw;
  top: 123%;
  left: 50%;
  font-size:1.5vw;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%)

}

.mainSecondarySecondSubscribeBtn{
  position: unset;
  margin: 11vh 50vw;
  -webkit-transform: translate(-50%,0%);
      -ms-transform: translate(-50%,0%);
          transform: translate(-50%,0%);
}

.mainAboutUsImg{
  

  width: 30vw;


}

.mainAboutUsHeading,.mainfourthimgText{
  color: #0E8B45;
  font-size: 8vh;
  text-align: center;
  font-weight: bolder;
  width: 100vw;
  margin: 10vh 0vw;
}

.mainfourthimgText{
  color: #0E8B45;
  font-size: 8vh;
  text-align: center;
  font-weight: bolder;
  width: 100vw;
  margin: 20vh 0vw -1vh;
}


.mainAboutUsLeaves{
  position: absolute;
    height: 117vh;
    width: 56vw;
    border-radius: 100%;
  
}
.mainAboutUsDiscription{
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  gap: 30vw;
  height: 82vh;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.mainAboutUsText{
  height: 70vh;
  width: 35vw;
  border: 2px dashed #577C3A;
  -webkit-box-shadow: inset 0px 41px 16px rgb(0 0 0 / 1%), inset 0px 23px 14px rgb(0 0 0 / 5%), inset 0px 10px 10px rgb(0 0 0 / 9%), inset 0px 3px 6px rgb(0 0 0 / 10%);
          box-shadow: inset 0px 41px 16px rgb(0 0 0 / 1%), inset 0px 23px 14px rgb(0 0 0 / 5%), inset 0px 10px 10px rgb(0 0 0 / 9%), inset 0px 3px 6px rgb(0 0 0 / 10%);
  border-radius: 20px;
  font-size: 2.6vmin;
  padding: 4vmin;
  overflow-y: auto;
  -ms-flex-item-align: self-start;
      -ms-grid-row-align: self-start;
      align-self: self-start;
}



.mainfourthimg{ 
  width:100%;
  margin:10vh 0vw 0vh;
  padding: 0vh 2vw 0vh 5vw;
} 

.mainfourthimg.mobileView{
  display: none;
}


.mainOpenModal{
  position: fixed;
  top: 90vh;
  height: 8vh;
  border-radius: 8px;
  left: 90vw;
  z-index: 5;

}

.maintooltip{
  background-color:white;
  border-radius:8px;
  z-index:10;
  height: 7vh;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  color: black;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 18vw;
  position:fixed;
  top:85vh;
  left:71vw;
  -webkit-filter: drop-shadow(-4px -4px 8px rgba(0, 0, 0, 0.26));
          filter: drop-shadow(-4px -4px 8px rgba(0, 0, 0, 0.26));
  

}

 .maintooltip::after{

  content:  "";
  position: absolute;
  top: 51%;
  -webkit-transform: skew(-62deg,47deg);
      -ms-transform: skew(-62deg,47deg);
          transform: skew(-62deg,47deg);
  left: 71%;
  border-width: 3.3vmin;
  border-style: solid;
  border-color: white transparent transparent transparent;
  z-index: -1;
   
    
 }
.maintooltip>p{
  overflow: hidden; /* Ensures the content is not revealed until the animation */
  border-right: .15em solid transparent; /* The typwriter cursor */
  white-space: nowrap; /* Keeps the content on a single line */
  margin: 0 1vw; /* Gives that scrolling effect as the typing happens */
  
  /* animation: typing 3s steps(40, end), blink-caret .75s step-end backwards; */
   
  -webkit-animation: typing 5s steps(60), blink-caret 5s;
   
          animation: typing 5s steps(60), blink-caret 5s;
  
  }
@-webkit-keyframes typing {
    from { width: 0 }
    to { width: 100% }
}
@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}
@-webkit-keyframes blink-caret {
  from, to { border-color: transparent }
  80% { border-color: black; }
 

}
@keyframes blink-caret {
  from, to { border-color: transparent }
  80% { border-color: black; }
 

}





/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Faq Styles---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/


.mainfaqContainer{
    position:relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }

.mainfaqHeading{

  border-bottom: #777;
  padding: 20px 60px;
  font-size: 8vh;
  color: #0E8B45;
  font-weight: bolder;
  text-align:center;
  margin-bottom: 3vh;
}



.mainfaqUpArrow{
  
  margin: 2.5vh 1.5vw;

  height: 1.5vh;
}
.mainfaqDownArrow{
  margin: 2.5vh 1.5vw;
  height: 1.5vh;
}





.mainfaqQues
{
    background-color: #F3F3F3;
    margin: 0vh 8vw 7vh;
    -webkit-box-shadow: -20px -20px 50px #ffffff, 20px 20px 50px #d2d2d2;
            box-shadow: -20px -20px 50px #ffffff, 20px 20px 50px #d2d2d2;
    border-radius: 14px;
   
    
}

.mainfaqOne{
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row
}

.mainfaqPage {
line-height: 3vh;
color: #444;
cursor: pointer;
padding: 2vh 1.5vw 2vh;
width: 100%;
border: none;
outline: none;
-webkit-transition: 0.4s;
-o-transition: 0.4s;
transition: 0.4s;
font-size:3vh; 
}

.mainfaqLeaves{
  position:absolute;
  height:58vmin;
  top: 52vh
}



/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Review.js---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/

.mainreviewtestimonialWrapper::-webkit-scrollbar{
  display: none;
}

.mainreviewtestimonialWrapper{
  display:-webkit-box;
  display:-ms-flexbox;
  display:flex;
  margin:5vmin;
  -webkit-box-orient:horizontal;
  -webkit-box-direction:normal;
      -ms-flex-direction:row;
          flex-direction:row;
  overflow:auto;
  scrollbar-width: none;
  height:60vh;
  padding: 6vh;
  margin-left:-2vmin;
  position:relative



}

.mainreviewTestimonials{  

position: relative;
display: -webkit-box;
display: -ms-flexbox;
display: flex;
-webkit-box-orient: vertical;
-webkit-box-direction: normal;
    -ms-flex-direction: column;
        flex-direction: column;
background: #F3F3F3;
-webkit-box-shadow: -20px -20px 50px #FFFFFF, 20px 20px 50px #D2D2D2;
        box-shadow: -20px -20px 50px #FFFFFF, 20px 20px 50px #D2D2D2;
z-index:1;
padding: 2vh 3vw;
border-radius: 43px;
margin:3vh 5vw;
}

.mainreviewImg{
  position: absolute;
  height: 12vmin;
  width: 12vmin;
  border: 1px;
  border-radius: 50%;
  top: -15%;
  left: 10%;
}

.mainreviewRating{

  /* position: absolute;
  left: 60%;
  top: 6%; */
  text-align: end;
  font-size: 2.5vw; 

}

.mainreviewRemainingPart{
  margin-top:3vh
  }  

.mainreviewName{
  font-weight:bolder;
  font-size: 2vw;
  text-align: center;
  padding: 0vh 0vh 1vh 0vh

}

.mainreviewDiscription {
  font-size: 1.7vmin;
  text-align:center;
  width:15vw;


}
.mainreviewRating img{
  height: 20px;
}

.mainreviewLeaves{
position: absolute;
height: 61vmin;
-webkit-transform: rotate(-92deg);
    -ms-transform: rotate(-92deg);
        transform: rotate(-92deg);
left: 53vw;
top: -17vh

}








/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Footer styles---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/





.mainfooterContainer{
  position:relative;
  background-color:#F7F7F8;
  height:46vh;
  z-index:1


}

.mainfooterRow{
  display:-webkit-box;
  display:-ms-flexbox;
  display:flex;
  -webkit-box-orient:horizontal;
  -webkit-box-direction:normal;
  -ms-flex-direction:row;
  flex-direction:row
}

.mainfooterCol{
  display:-webkit-box;
  display:-ms-flexbox;
  display:flex;
  -webkit-box-orient:vertical;
  -webkit-box-direction:normal;
  -ms-flex-direction:column;
  flex-direction:column
}

.mainfooterHeading{
  font-weight:Bolder;
  font-size: 7vh;
  margin-top: 5vh;
  color:#0E8B45
}

.mainfooterImg{
  /* // position:absolute; */
  height: 100%;
  /* width: 30%; */
  border: 1px;
  border-radius: 50%;
  margin:0 0.5vw
}
.mainfooterAppStores{
  
  height: 6.5vh;
  width: 24vw;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  
}
.mainfooterFirst{
  -webkit-box-align:center;
  -ms-flex-align:center;
  align-items:center;
  font-size:1vw;
  width:40%

}
.mainfooterSecond{
  /* position:absolute; */
 
 width:20%
}


.mainfooterThird{
  /* position:absolute; */

 width:20%
}

.mainfooterFourth{
  /* position:absolute; */

 width:20%
}
.mainfooterSubHeading{
  font-weight:Bolder;
  font-size: 4vh;
  margin-top: 5vh;
  margin-bottom: 2vh;
  color:#0E8B45
}
.mainfooterHeading img{

  width:16vw;

}
.mainfooterSubSubHeading{
  font-size:3vh;
  margin:1vh 1vh 1vh 0;

}

.mainfooterCopyright{
  background-color: #F7F7F8;
  text-align: center;
  padding: 2vh;
  font-size:2vh
}
.mainfooterSocialMediaIcons{
  height:10vh;
  justify-content:center;
  margin-top: 4vh;
}

.mainfooterSocialMediaIconItem{
  width: 12%;
  justify-content: center;
  display: flex;
}
.mainfooterAppStoreItem{
  width: 60%;
  justify-content: center;
  display: flex; 
}
.mainfooterLeaves{
  position: absolute;
  height: 60vmin;
  top: -37vh;
  left: 87vw;
  -webkit-transform: rotate(205deg);
  -ms-transform: rotate(205deg);
  transform: rotate(205deg)
}




/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Saving Calculator Styles---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/


.mainSavingCalcBtn{
  opacity: 0;
 
}
.scrollToTopSavingCalcBtn,.DesktopViewSavingCalcBtn{
  opacity: 1;
 
}



.mainsavingCalcOverlay{
  position:fixed;
  height:100%;
  width:100%;
  background-color:rgba(255,255,255,0.9);
  z-index:10;
  display: flex;
  justify-content: center;
  top:0%
  
}
.mainsavingCalcModalContainer{
  width: 50vw;
  height: 80vh;
  align-self: center;
  background-color: white;
  display: flex;
  flex-direction: column;
  padding: 6vh 2vw;
  border: 26px solid #D9D9D9;
  border-radius: 30px;

}
.mainsavingCalcfirstRow{
  display: flex;
  width: 98%;
  position: relative;

}
.mainSavingCalcLeftSecondRow{
  width: 40vw;
}
.mainSavingCalcLeftSecondRowFirstCol{
  display: flex;

}
.mainSavingCalcSecondRowFirstColFirstBox{
  display: flex;
  flex-direction: column;
  margin: 0vh 0vw 1vh;
  width: 50%;
}
.mainSavingCalcSecondRowFirstColFirstSecondBox
{
  margin-bottom: 5vh;
}

.mainsavingCalcfirstbox{
    margin:0vh 0vw 4vh
}
.mainsavingCalcheading{
  font-size: 3.5vmin;
  font-weight: bolder;
  margin-bottom: 0.5vh;
  width:18vw;
}
.mainsavingCalcheadingDiscription{
  font-size: 1.7vmin;

}
.mainsavingCalcRightColFirstBox
{
  justify-content: flex-end;
}

.mainsavingCalcRightCol{
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
  align-items: flex-end;

}

.mainsavingCalcSelectOptionPlans{
  width: 50%;
  height: 4vmin;
  margin:0vh 0vw 0vh 1vw;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: end;
}

.mainsavingCalcSelectOptionPlans select{
 border: 0.5px solid #0E8B45;
 border-radius: 4px;
 font-size:2.5vmin;
 margin:0vh 1.5vh 0vw 1vw;
 height:4vh;
 
}

.mainsavingCalcSecondbox{
  font-size: 2.5vmin;
  font-weight:bold
  
}

.mainsavingCalcSlider{
  width: 100%;
  padding:2vh 0vw 1vh;
}
.mainSavingCalcInput{
  border-radius: 8px;
  text-align: center;
  border: 1px solid;
  width:35%;
}
 
.mainsavingCalcRightSavingBox{
  padding:3vh 2vw;
  font-size: 2vmin;
  background: #F9FAFE;
  -webkit-box-shadow:18px 39px 17px rgba(0, 0, 0, 0.01), 10px 22px 14px rgba(0, 0, 0, 0.05), 5px 10px 11px rgba(0, 0, 0, 0.09), 1px 2px 6px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
          box-shadow:18px 39px 17px rgba(0, 0, 0, 0.01), 10px 22px 14px rgba(0, 0, 0, 0.05), 5px 10px 11px rgba(0, 0, 0, 0.09), 1px 2px 6px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  width:80%; 
  display: -webkit-box; 
  display: -ms-flexbox; 
  display: flex;
  -webkit-box-orient:vertical;
  -webkit-box-direction:normal;
      -ms-flex-direction:column;
          flex-direction:column;
  text-align: center;
}
.mainsavingCalcSubscribeBtnDiv{
  margin-right: 3vw;
}
.mainsavingCalcSubscribeBtn{
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding:8px 16px;
  background:#0E8B45;
  border-radius: 50px;
  width: 12vw;
  color: white;
  border:none;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size:2.5vmin;
  cursor: pointer;
}
.mainsavingCalcCrossBtn{
  position: absolute;
  right: -2vw;
  top: -4vh;
 
}
  

/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Underline effect---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/

.hover-underline-animation {
  display: inline-block;
  position: relative;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  
}

.hover-underline-animation::after {
  content: '';
  position: absolute;
  width: 100%;
  -webkit-transform: scaleX(0);
      -ms-transform: scaleX(0);
          transform: scaleX(0);
  height: 2px;
  bottom: 0;
  left: 0;
  background-color:black;
  -webkit-transform-origin: bottom right;
      -ms-transform-origin: bottom right;
          transform-origin: bottom right;
  -webkit-transition: -webkit-transform 0.25s ease-out;
  transition: -webkit-transform 0.25s ease-out;
  -o-transition: transform 0.25s ease-out;
  transition: transform 0.25s ease-out;
  transition: transform 0.25s ease-out, -webkit-transform 0.25s ease-out;
}

.hover-underline-animation:hover::after {
  -webkit-transform: scaleX(1);
      -ms-transform: scaleX(1);
          transform: scaleX(1);
  -webkit-transform-origin: bottom left;
      -ms-transform-origin: bottom left;
          transform-origin: bottom left;
}


/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Range slider---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/


input[type=range] {

  margin: 18px 0;
  width: 100%;
  -webkit-appearance: none;
}
input[type=range]:focus {
  outline: none;
}
input[type=range]::-webkit-slider-runnable-track {
  width: 100%;
  height: 8.4px;
  cursor: pointer;
  
  
  background: #CECECE;;
  border-radius: 10px;
  border: 1px solid #B9B9B9;
}

input[type=range]::-webkit-slider-thumb {
 
  border: 1px solid #0E8B45;
  height: 25px;
  width: 25px;
  border-radius: 50px;
  background: #0E8B45;
  cursor: pointer;
  -webkit-appearance: none;
  margin-top: -10px;
}
input[type=range]:focus::-webkit-slider-runnable-track {
  background: #CECECE;;
}


input[type=range]::-moz-range-track {
  width: 100%;
  height: 8.4px;
  cursor: pointer;
  background: #CECECE;;
  border-radius: 10px;
  border: 1px solid #B9B9B9;
  }
  
  input[type=range]::-moz-range-thumb {
  border: 1px solid #0E8B45;
  height: 25px;
  width: 25px;
  border-radius: 50px;
  background: #0E8B45;
  cursor: pointer;
  -moz-appearance: none;
  appearance: none;
  margin-top: -10px;
  }
  
  input[type=range]:focus::-moz-range-track {
  background: #CECECE;
  }



/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Mobile View---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/

/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Mobile View---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/



/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Mobile View---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/



/*---------------------------------------------------------------------------------------------------------------
-----------------------------------------------Mobile View---------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------*/





@media only screen and (max-width: 803px) {

.mainChaayosSelectLogo {
  position: absolute;
  height: 7vh;
  width: 50vw;
  left: 48vw;
}

.mainfirstimg {
  width: 155vw;
  height: 53vh;
  margin-left: -55.5vw;
}

.mainchaayosSelectText1{
  position: absolute;
  top: 105%;
  left: 48%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  font-size: 3vh;
  color: rgb(14, 139, 69);
  font-weight: bolder;
 
}

.mainbenefits ul{
  text-align: center;
}
.mainchaayosSelectText2{
  position: absolute;
  top: 123%;
  font-size: 2.4vh;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);;
  left: 50%;
  font-weight: bold;
}

.mainchaayosSelectText3{
  position: absolute;
  top: 129%;
  font-size: 1.6vh;
  color: black;
  font-weight: bolder;
  left: 45%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}


.mainchaayosSelectText4{
  position: absolute;
  top: 129%;
  font-size: 1.6vh;
  left: 63vw;
 
  -webkit-transform: translate(0%, -50%);

      -ms-transform: translate(0%, -50%);

          transform: translate(0%, -50%);
}

.mainsecondContainer{
  height: 142vh;
}
.mainbenefits{
  overflow: hidden;
  position: absolute;
  top: 114%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  line-height: inherit;
  height: 4vh;
  width: 100vw;
  font-size: 3vh;
  font-weight: bold;
}

.mainSubscribebtn{
  position: absolute;
  width: 85vw;
  height: 7vh;
  text-align: center;
  font-size: 2.4vh;
  top: 77vh;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
      -ms-transform: translate(-50%,-50%);
          transform: translate(-50%,-50%);
  z-index: 3;
  -o-text-overflow: clip;
     text-overflow: clip;
  background-color: #0E8B45;
  color: white;

  border: none;
  cursor: pointer;
  border-radius: 108px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-shadow: rgb(0 0 0 / 20%) 10px 14px 30px;
          box-shadow: rgb(0 0 0 / 20%) 10px 14px 30px;
}
.mainSubscribebtn:hover{
  background-color: #0E8B45 !important;
}
.mainSubscribebtn:active{
  background-color: #383838 !important;
}

.mainSecondarySubscribeBtn{
  left: 43% !important;
  -webkit-transform: translate(-50%,-50%);
      -ms-transform: translate(-50%,-50%);
          transform: translate(-50%,-50%);
  width: 85vw;
  height: 7vh;
  text-align: center;
  font-size: 2.4vh;
}

.mainellipse {
  position: absolute;
  top: 12vh;
  height: 69vh;
  width: 91vw;
}

.mainCircle1 {
  height: 25vmin;
  width: 25vmin;
}
.maincircles {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-grid-columns: auto auto auto;
  grid-template-columns: auto auto auto;
  top: 38vh;
  margin: 0vh 5vw;
}
.mainCircleWithText{
  width: 25vmin;
}
.mainCircle1Text{
  margin: 2vh 1vw !important;
 font-size: 1.6vh !important;
}

.mainWCCS{
  font-size: 3vh;
  margin: 2vh 0vw;
}
.mainwhyChaayos{
  top: 72vh !important
}

.mainAboutUsHeading{
  margin-bottom: 5vh;
  font-size: 3vh;
}
.mainAboutUsDiscription{
  height: auto;
  gap: 0vw;
}
.mainAboutUsText{
  height: auto;
  width: 56vw;
  font-size: 2.8vmin;
  background-color: white;
  position: relative;
  z-index: 1;

}
.mainAboutUsImg{
  width: 40vw;
  position: inherit;
  z-index: 1;
}
.mainAboutUsLeaves{
  position: absolute;
  height: 66vh;
  width: 106vw;
  left: -21vh;
}
.mainfourthimg{ 
  
  display: none;
} 
.mainfourthimg.mobileView{
  display: block;
  width: 90vw;
  margin: 0vh 3vw 8vh;
  height: 70vh;
}
.mainfourthimgText.mobileView{
  display: block;
  font-size: 3vh;
  margin: 3vh 0vw;
  color: #0E8B45;
  text-align: center;
  font-weight: bolder;
  width: 100vw;
}
.mainreviewLeaves {

  height: 44vmin;
 
  left: 65vw;
  top: -5vh;
}
.maingifs{
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  margin: 0vh 10vw;
 
}
.maingifWithText{
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  margin: 2vh 0vw 2vh -2.5vw;
}
.maingif {
  height: 25vmin;
  width: 25vmin;
}
.maingifText{
  margin: 0vh 4vw;
  text-align: left;
}
.maingifheadings{
  font-weight: 700 !important;
 
  margin: 3vh 0vw 0.8vh;
  font-size: 2.4vh;
  width: 65vw;
}
.maingifSubheadings{
  font-size: 1.6vh !important;
  width: 55vw;
}
.mainSecondaryFirstSubscribeBtn{
  top: 66vh;

}
.mainSecondarySecondSubscribeBtn{
  position: unset;
}

.mainfaqQues{
  
  padding: 0vh 2vw;
}
.mainfaqPage{
  font-size: 2.4vh;
  padding: 2vh 1.5vw 2vh;
}
.mainfaqans
{
  font-size: 1.6vh !important;
  
}
.mainOpenModal{
  top: 80vh;
  left:80%
}

.mainreviewtestimonialWrapper{
  /* height: 52vh; */
}
.mainreviewTestimonials {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -webkit-box-flex: 0;
      -ms-flex-positive: 0;
          flex-grow: 0;
  -ms-flex-preferred-size: 60vmin;
      flex-basis: 60vmin;
  border-radius: 25px;
}
.mainreviewName{
  font-size: 5vw;
  padding: 0vh 0vh 1vh 0vh;
}

.mainreviewImg {

  height: 15vmin;
  width: 15vmin;
  top: -10%;
  left: 8%;
}
.mainreviewRating {

  text-align: end;
  /* left: 48%;
  top: 2%; */
}

.mainreviewDiscription{
  width: inherit;
  line-height: 2.4vh;
  font-size: 1.6vh;
}
.mainfaqHeading {

  font-size: 3vh;
  margin-bottom: 3vh;

}
.mainfaqUpArrow,.mainfaqDownArrow{
  
    height: 1.3vh;

}
.mainfooterContainer{
  height: 33vh;
  
  padding: 0vh 2vw;
}
.mainfooterHeading{
  font-size: 3vh;
  margin: 4.5vh 0vw 1vh;
}
.mainfooterHeading img{

    width: 25vw;

}
.mainfooterHeadingText{
    width: 90% !important;
    font-size: 1.5vh;
    display: none;
}
.mainfooterSocialMediaIcons{
  height: 5vh !important;
  margin-top: 0vh !important;
}

.mainfooterImg {
  height: 100% !important;

}
.mainfooterSubHeading {
  font-size: 2vh;
}
.mainfooterSubSubHeading {
  font-size: 1.5vh;

}
.mainfooterSecond{
 
 width:25%;
 overflow-wrap: break-word;
}
.mainfooterThird{
  

 width:15%
}

.mainfooterSocialMediaIconItem{
  width: 7%;
 
}

.mainfooterLeaves{
 
    top: -20vh;
    left: 70vw;

}
.mainfooterAppStores{
  
  height: 11vh;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 55vw;
  -webkit-transform: translateX(21%);
      -ms-transform: translateX(21%);
          transform: translateX(21%);
  
}
.mainfooterCopyright {
  font-size: 1.5vh;
}


.mainfooterAppstoreIcons{
  width: 60% !important;
}


.mainsavingCalcOverlay{
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.mainsavingCalcModalContainer{
  height: initial;
  padding: 2vh 2vw;
  border: 10px solid #D9D9D9;
  position: static;
  margin: 0vh 3vw;
  min-width: 100vw;
  max-width: 100vw;
}
.mainsavingCalcheading{
  width: 38vw
}
.mainsavingCalcheadingDiscription{
  font-size: 2.4vmin;
}
.mainsavingCalcSelectOptionPlans{
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 7vmin;
  margin: 0vh 0vw 0vh 1vw;
  -webkit-box-align: end !important;
      -ms-flex-align: end !important;
          align-items: flex-end !important;
}
.mainsavingCalcSelectOptionPlans p{
  font-size: 3.8vmin !important;
  height: 7vmin;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: end;
}
.mainsavingCalcSelectOptionPlans select{
  font-size: 2.5vmin;
  margin: 0vh 2vw 0vw 0vw !important;
  height: 2.5vh !important;
 

}

.mainSavingCalcSecondRowFirstColFirstBox {
  width: 70%;
}
.mainsavingCalcfirstRow{
  width: 100%;

}
.mainSavingCalcLeftSecondRow {
  width: 100%;
}
.mainsavingCalcRightCol {
  align-items: center;
}
.mainsavingCalcRightColThirdRow{
  width: 60%;
}
.mainsavingCalcSubscribeBtn{
  width: 100%;
  font-size: 3vmin;
}
.mainsavingCalcCrossBtn {

  right: -1vw;
  top: -1vh;
}

.maintooltip {
  height: 7vh;
  width: 70vw;
  top: 79vh;

    left: 4vw;
}
.maintooltip::after {
  border-width: 5vmin;
  top: 47%;
  -webkit-transform: skew(-39deg,21deg);
      -ms-transform: skew(-39deg,21deg);
          transform: skew(-39deg,21deg);
  left: 89%;
}

}