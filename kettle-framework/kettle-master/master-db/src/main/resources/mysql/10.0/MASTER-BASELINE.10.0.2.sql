INSERT INTO KETTLE_MASTER_DEV.STRATEGY_GROUP (STRATEGY_GROUP_ID, STRATEGY_GROUP_NAME, DESCRIPTION, STATUS, CREATED_AT, CREATED_BY)
VALUES
	(1, 'Wallet Group', 'Wallet Suggestion Group', 'ACTIVE', now(), 120057),
    (2, 'Wallet Verbiage Group', 'Wallet Verbiage Suggestion Group', 'ACTIVE', now(), 120057);
INSERT INTO KETTLE_MASTER_DEV.STRATEGY_DEFINITION (STRATEGY_ID, STRATEGY_NAME, STRATEGY_GROUP_ID, DESCRIPTION, STATUS, CREATED_AT, CREATED_BY)
VALUES
	(1, 'Wallet Default Strategy', 1,'Wallet Default Suggestion Strategy', 'ACTIVE', now(), 120057),
    (2, 'Wallet Default Verbiage Strategy', 2,'Wallet Verbiage Default Suggestion Strategy', 'ACTIVE', now(), 120057);

INSERT INTO KETTLE_MASTER_DEV.WALLET_VERBIAGE_STRATEGY_DEFINITION (WALLET_VERBIAGE_STRATEGY_ID, STRATEGY_ID, STRATEGY_GROUP_ID, STATUS, VERBIAGE)
VALUES
	(1,  2, 2, 'ACTIVE', "Purchase wallet worth {value} and get extra {offerValue}");

INSERT INTO KETTLE_MASTER_DEV.WALLET_STRATEGY_DEFINITION (WALLET_STRATEGY_ID, STRATEGY_ID, STRATEGY_GROUP_ID, STATUS,
MIN_AMOUNT, MAX_AMOUNT, VALUE, ALLOTMENT_TYPE, ALLOTMENT_VALUE, OFFER_VALUE)
VALUES
	(1,  1, 1, 'ACTIVE', 1.0, 99.0, 100.0, 'FIXED_AMOUNT', 10.0, 10.0),
    (2,  1, 1, 'ACTIVE', 100.0, 199.0, 200.0, 'FIXED_AMOUNT', 20.0, 20.0),
    (3,  1, 1, 'ACTIVE', 200.0, 299.0, 300.0, 'FIXED_AMOUNT', 30.0, 30.0),
    (4,  1, 1, 'ACTIVE', 300.0, 399.0, 400.0, 'FIXED_AMOUNT', 40.0, 40.0),
    (5,  1, 1, 'ACTIVE', 400.0, 499.0, 500.0, 'FIXED_AMOUNT', 50.0, 50.0),
    (6,  1, 1, 'ACTIVE', 500.0, 599.0, 600.0, 'FIXED_AMOUNT', 60.0, 60.0),
    (7,  1, 1, 'ACTIVE', 600.0, 699.0, 700.0, 'FIXED_AMOUNT', 70.0, 70.0),
    (8,  1, 1, 'ACTIVE', 700.0, 799.0, 800.0, 'FIXED_AMOUNT', 80.0, 80.0);

CREATE INDEX STRATEGY_ID_WALLET_STRATEGY_DEFINITION ON KETTLE_MASTER_DEV.WALLET_STRATEGY_DEFINITION(STRATEGY_ID) USING BTREE;
CREATE INDEX STATUS_WALLET_STRATEGY_DEFINITION ON KETTLE_MASTER_DEV.WALLET_STRATEGY_DEFINITION(STATUS) USING BTREE;
CREATE INDEX MAX_AMOUNT_WALLET_STRATEGY_DEFINITION ON KETTLE_MASTER_DEV.WALLET_STRATEGY_DEFINITION(MAX_AMOUNT) USING BTREE;