package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.Date;

public class MonkCalibrationTime implements Serializable {

    private static final long serialVersionUID = -1742706396572712519L;

    private Integer unitId;
    private Date todayCalibrationTime;
    private Date nextDayCalibrationTime;
    private Date serverTime;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Date getTodayCalibrationTime() {
        return todayCalibrationTime;
    }

    public void setTodayCalibrationTime(Date todayCalibrationTime) {
        this.todayCalibrationTime = todayCalibrationTime;
    }

    public Date getNextDayCalibrationTime() {
        return nextDayCalibrationTime;
    }

    public void setNextDayCalibrationTime(Date nextDayCalibrationTime) {
        this.nextDayCalibrationTime = nextDayCalibrationTime;
    }

    public Date getServerTime() {
        return serverTime;
    }

    public void setServerTime(Date serverTime) {
        this.serverTime = serverTime;
    }

    @Override
    public String toString() {
        return "MonkCalibrationTime{" +
                "unitId=" + unitId +
                ", todayCalibrationTime=" + todayCalibrationTime +
                ", nextDayCalibrationTime=" + nextDayCalibrationTime +
                ", serverTime=" + serverTime +
                '}';
    }
}
