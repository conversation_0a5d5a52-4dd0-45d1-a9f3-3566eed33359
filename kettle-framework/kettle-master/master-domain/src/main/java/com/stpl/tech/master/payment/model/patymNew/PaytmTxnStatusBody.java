package com.stpl.tech.master.payment.model.patymNew;

import java.math.BigDecimal;
import java.util.Date;

public class PaytmTxnStatusBody {

    private PaytmResponseResultInfo resultInfo;

    private String txnId;

    private String bankTxnId;

    private String orderId;

    private BigDecimal txnAmount;

    private String txnType;

    private String gatewayName;

    private String bankName;

    private String mid;

    private String paymentMode;

    private BigDecimal refundAmt;

    private String txnDate;

    public PaytmTxnStatusBody() {
    }

    public PaytmResponseResultInfo getResultInfo() {
        return resultInfo;
    }

    public void setResultInfo(PaytmResponseResultInfo resultInfo) {
        this.resultInfo = resultInfo;
    }

    public String getTxnId() {
        return txnId;
    }

    public void setTxnId(String txnId) {
        this.txnId = txnId;
    }

    public String getBankTxnId() {
        return bankTxnId;
    }

    public void setBankTxnId(String bankTxnId) {
        this.bankTxnId = bankTxnId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getTxnAmount() {
        return txnAmount;
    }

    public void setTxnAmount(BigDecimal txnAmount) {
        this.txnAmount = txnAmount;
    }

    public String getTxnType() {
        return txnType;
    }

    public void setTxnType(String txnType) {
        this.txnType = txnType;
    }

    public String getGatewayName() {
        return gatewayName;
    }

    public void setGatewayName(String gatewayName) {
        this.gatewayName = gatewayName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    public BigDecimal getRefundAmt() {
        return refundAmt;
    }

    public void setRefundAmt(BigDecimal refundAmt) {
        this.refundAmt = refundAmt;
    }

    public String getTxnDate() {
        return txnDate;
    }

    public void setTxnDate(String txnDate) {
        this.txnDate = txnDate;
    }
}
