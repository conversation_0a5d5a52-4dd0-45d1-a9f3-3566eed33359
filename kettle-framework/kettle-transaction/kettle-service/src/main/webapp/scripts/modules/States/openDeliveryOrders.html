<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<h2 class="text-center" style="margin-bottom: 50px;" data-ng-init="init()">
    <button type="button" class="btn btn-warning pull-left"
          style="margin-left: 50px" ng-click="backToCover()">Back</button>
          
    Open Delivery Orders   
    
    <button type="button"  style="margin-right: 50px" class="btn btn-success pull-right" ng-click="refresh()">Refresh</button>  
</h2>
<accordion close-others="oneAtATime">
    <accordion-group class="panel-info" ng-repeat="(orderStatus, orderData) in openOrderData" changecolors="orderStatus" is-open="isOpen">
        <accordion-heading >
            <span class="status-row">{{orderStatus}}</span>
            <i class="pull-right glyphicon"
                          ng-class="{'glyphicon-chevron-down': isOpen, 'glyphicon-chevron-right': !isOpen}"></i>
        </accordion-heading>
            <table class="table table-striped">

            <thead style="background-color: #FFFFFF">
                <tr>
                    <th>Generated Bill No</th>
                    <th>Brand</th>
                    <th>Customer Name</th>
                    <th>Contact Number</th>
                    <th>Creation Time</th>
                    <th>Elapsed Time (in minutes)</th>
                    <th>SDP Id</th>
                    <th>SDP</th>
                    <th>SDP Contact</th>
                    <th>Paid Amount</th>
                    <th>Settlement Type</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <tr  ng-repeat="order in orderData" >
                    <td><b>{{order.order.generateOrderId}}</b></td>
                    <td><b>{{order.brand.brandName}}</b></td>
                    <td>
                        <span ng-if="order.customer.firstName.length>0">{{order.customer.firstName}} {{order.customer.lastName}}</span>
                        <span data-ng-if="order.customer.firstName.length<1">No name found</span>
                    </td>
                    <td>{{order.customer.contactNumber}}</td>
                    <td>{{order.order.billCreationTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                    <td>{{order.elapsedTime}}</td>
                    <td>{{order.deliveryDetails.deliveryBoyId}}</td>
                    <td>{{order.deliveryDetails.deliveryBoyName}}</td>
                    <td>{{order.deliveryDetails.deliveryBoyPhoneNum}}</td>
                    <td>{{order.order.transactionDetail.paidAmount}}</td>
                    <td>{{getSettlements(order.order)}}</td>
                    <td><button class="btn btn-success "
								data-ng-if="order.order.pendingCash && order.order.source=='COD' && order.order.deliveryPartner==8 && order.order.status == 'READY_TO_DISPATCH' && order.deliveryDetails.deliveryBoyId != null"
								data-ng-click="sendUpdate(order.order.orderId,4,order.order.unitId,order.order.source)">Settle</button></td>
				</tr>
            </tbody>
            </table>
    </accordion-group>
</accordion>
