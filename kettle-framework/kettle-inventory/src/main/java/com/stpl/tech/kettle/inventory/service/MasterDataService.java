package com.stpl.tech.kettle.inventory.service;

import java.util.List;

import com.stpl.tech.kettle.inventory.domain.redis.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.TrimmedProductVO;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.redis.domain.model.UnitBasicDetail;

public interface MasterDataService {

	public List<Product> getAllCafeProducts(int unitId);

	public RecipeDetail getRecipeDetail(String recipeId);

	public List<RecipeDetail> getAllRecipeDetail(List<Integer> excludeIds);

	public List<UnitBasicDetail> getAllUnitBasicDetail();

	public UnitBasicDetail getUnitBasicDetail(int unitId);

	public EmployeeBasicDetail getEmployeeBasicDetail(int empId);

	List<TrimmedProductVO> getAllCafeTrimmedProducts(int unitId);

}
