package com.stpl.tech.report.service.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import mondrian.rolap.BitKey;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SuperUAggregatedData {

    BigDecimal delight = BigDecimal.valueOf(0);
    BigDecimal communication =  BigDecimal.valueOf(0);
    BigDecimal upsell = BigDecimal.valueOf(0);
    BigDecimal delightTotal = BigDecimal.valueOf(0);
    Integer delightCount = 0;
    BigDecimal communicationTotal = BigDecimal.valueOf(0);;
    Integer communicationCount = 0;
    BigDecimal upsellTotal = BigDecimal.valueOf(0);;
    Integer upsellCount = 0;

    Integer totalOrderForUpsell = 0;
    Integer upsellOrderCount = 0;
}
