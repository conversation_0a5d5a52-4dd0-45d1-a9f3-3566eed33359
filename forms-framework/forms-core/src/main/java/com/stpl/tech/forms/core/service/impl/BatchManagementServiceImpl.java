package com.stpl.tech.forms.core.service.impl;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.subtlelib.poi.api.row.RowContext;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import com.google.common.io.Files;
import com.stpl.tech.forms.core.domain.model.BatchCodeResponse;
import com.stpl.tech.forms.core.service.BatchManagementService;
import com.stpl.tech.forms.core.service.EnvProperties;
import com.stpl.tech.forms.data.dao.BatchManagementDao;
import com.stpl.tech.forms.data.model.BatchCode;
import com.stpl.tech.forms.data.model.BatchData;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Service
public class BatchManagementServiceImpl implements BatchManagementService {

	private static final Logger LOG = LoggerFactory.getLogger(BatchManagementServiceImpl.class);

	@Autowired
	private BatchManagementDao batchManagementDao;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private EnvProperties envProperties;

	@Autowired
	private FileArchiveService fileArchiveService;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public BatchCodeResponse generatePackCodes(int unitId, int productId, String productName, int count, int length)
			throws DataUpdationException {
		LOG.info("Enter generatePackCodes where unitId is : " + unitId + " and productId is " + productId);
		BatchCodeResponse response = new BatchCodeResponse();
		Unit unit = masterDataCache.getUnit(unitId);
		if (unit == null) {
			LOG.error("Unit not found! ");
			response.setError("Unit not found!");
			return response;
		}
		String unitCodePrefix = batchManagementDao.getCodePrefix(String.valueOf(unitId),
				AppConstants.BATCH_CODE_KEY_TYPE_UNIT);
		if (StringUtils.isBlank(unitCodePrefix)) {
			LOG.error("Unit Code prefix not found! ");
			response.setError("Unit Code prefix not found!");
			return response;
		}
		LOG.info("unitCodePrefix is : " + unitCodePrefix);

		String productCodePrefix = batchManagementDao.getCodePrefix(String.valueOf(productId),
				AppConstants.BATCH_CODE_KEY_TYPE_SKU);
		if (StringUtils.isBlank(productCodePrefix)) {
			LOG.error("Product Code prefix not found! ");
			response.setError("Product Code prefix not found! ");
			return response;
		}
		LOG.info("productCodePrefix is : " + productCodePrefix);

		BatchData batchData = generateBatchCodes(unitId, unit.getName(), unitCodePrefix, productId, productName,
				productCodePrefix, count, length);
		File batchCodeFile = null;
		if (CollectionUtils.isNotEmpty(batchData.getBatchCodes())) {
			batchCodeFile = createBatchCodesExcel(batchData, unitId, productId, batchData.getCodePrefix());
		}
		if (batchCodeFile != null && batchCodeFile.exists()) {
			String baseS3Dir = "batchManagement" + "/" + "PACK_CODES" + "/" + unitId + "/" + productId;
			FileDetail s3File = fileArchiveService.saveFileToS3(envProperties.getS3AuditBucket(), baseS3Dir,
					batchCodeFile, true);
			response.setCodePrefix(batchData.getCodePrefix());
			response.setUrl(s3File.getUrl());
			return response;
		}
		return null;
	}

	private BatchData generateBatchCodes(int unitId, String unitName, String unitCodePrefix, int productId,
			String productName, String productCodePrefix, int count, int length) throws DataUpdationException {
		String codePrefix = unitCodePrefix.concat(productCodePrefix);
		Date currentDate = AppUtils.getCurrentDate();
		int date = AppUtils.getDay(currentDate);
		String datePrefix = StringUtils.leftPad(String.valueOf(date), 2, "0");
		codePrefix = codePrefix.concat(datePrefix);
		int batchCounterResetLimit = envProperties.getBatchCounterResetLimit();
		LOG.info("codePrefix is : " + codePrefix + " Batch Counter Reset Limit is " + batchCounterResetLimit);
		int batchCounter = batchManagementDao.updateBatchCounter(codePrefix, batchCounterResetLimit);
		if (batchCounter == 0) {
			LOG.error("Failed to update batch count : " + codePrefix);
			throw new DataUpdationException("Failed to update batch count : " + codePrefix);
		}
		String batchCounterString = String.valueOf(batchCounter);
		if (batchCounter < 10) {
			batchCounterString = StringUtils.leftPad(batchCounterString, 2, "0");
		}
		codePrefix = codePrefix.concat(batchCounterString);

		LOG.info("generateBatchCodes where batchCodePrefix is : " + codePrefix);
		if (batchManagementDao.getBatchData(codePrefix) != null) {
			throw new DataUpdationException("Batch Data with code prefix  : " + codePrefix + " already exists!");
		}

		LOG.info("New batch data request: " + codePrefix);
		BatchData batchData = buildBatchData(codePrefix, unitId, unitName, unitCodePrefix, productId, productName,
				productCodePrefix, count, length, currentDate, datePrefix);
		batchData = batchManagementDao.saveBatchData(batchData);

		if (batchData == null || batchData.getId() == 0) {
			LOG.info("Failed to save Batch data");
			return null;
		}

		int batchDataId = batchData.getId();
		LOG.info("Batch data saved successfully where Id is : " + batchDataId);

		LOG.info("Generating batch codes ... ");
		List<String> codes = new ArrayList<String>();
		for (int i = 0; i < count; i++) {
			codes.add(generateBatchCode(length));
		}

		LOG.info("Checking if generated batch codes are unique.");

		if (generateUniqueCodes(codes, length, codePrefix)) {
			LOG.info("Batch codes are unique, adding batch codes");
			List<BatchCode> batchCodes = new ArrayList<BatchCode>();
			for (String code : codes) {
				BatchCode batchCode = new BatchCode();
				batchCode.setBatchData(batchData);
				batchCode.setCode(code);
				batchCode.setStatus("CREATED");
				batchCodes.add(batchCode);
			}
			if (batchManagementDao.saveBatchCodes(batchCodes)) {
				LOG.info("Batch codes saved successfully!");
				batchData.setBatchCodes(batchCodes);
				return batchData;
			}
		}
		return null;
	}

	private File createBatchCodesExcel(BatchData batchData, int unitId, int productId, String codePrefix) {
		LOG.info("Enter createBatchCodesExcel");
		String batchCodesFolder = envProperties.getBasePath() + File.separator + unitId + File.separator + productId;
		LOG.info("batchCodesFolder is ", batchCodesFolder);
		File batchCodeFolder = new File(batchCodesFolder);
		if (!batchCodeFolder.exists()) {
			if (!batchCodeFolder.mkdirs()) {
				LOG.error("Failed to create the batch code folder");
				return null;
			}
		}
		String batchCodeFileName = unitId + "_" + productId + "_" + codePrefix + "_" + batchData.getId() + ".xlsx";
		File batchCodeFile = new File(batchCodeFolder + File.separator + batchCodeFileName);
		if (!batchCodeFile.exists()) {
			try {
				if (!batchCodeFile.createNewFile()) {
					LOG.error("Failed to create the batch code file");
					return null;
				}
				writeBatchCodesInExcel(batchCodeFile, batchData.getBatchCodes());
			} catch (IOException e) {
				LOG.error("Failed to create the batch code file " + e);
				return null;
			}
		}
		return batchCodeFile;
	}

	private void writeBatchCodesInExcel(File batchCodeFile, List<BatchCode> batchCodes) {
		WorkbookContextFactory ctxFactory = WorkbookContextFactory.useXlsx();
		WorkbookContext workbookCtx = ctxFactory.createWorkbook();
		SheetContext sheetCtx = workbookCtx.createSheet("BatchCodes");
		for (BatchCode batchCode : batchCodes) {
			RowContext context = sheetCtx.nextRow();
			context.text(batchCode.getCode()).setColumnWidth(15);
		}
		try {
			Files.write(workbookCtx.toNativeBytes(), batchCodeFile);
		} catch (IOException e) {
			LOG.error("Error in generating batch code files.", e);
		}
	}

	private boolean generateUniqueCodes(List<String> codes, int length, String batchCodePrefix) {
		List<String> existingBatchCodes = new ArrayList<String>();
		existingBatchCodes = batchManagementDao.checkIfUniqueBatchCodes(codes, existingBatchCodes);
		if (CollectionUtils.isEmpty(existingBatchCodes)) {
			LOG.info("No duplicate batch code found in database");
			return true;
		}
		LOG.info("Batch codes not unique, removing duplicate batch codes");
		for (String existingBatchCode : existingBatchCodes) {
			int index = codes.indexOf(existingBatchCode);
			codes.set(index, generateBatchCode(length));
		}
		generateUniqueCodes(codes, length, batchCodePrefix);
		return false;
	}

	private BatchData buildBatchData(String batchCodePrefix, int unitId, String unitName, String unitCodePrefix,
			int productId, String productName, String productCodePrefix, int count, int length, Date generationDate,
			String datePrefix) {
		BatchData batchData = new BatchData();
		batchData.setCodePrefix(batchCodePrefix);
		batchData.setManufactureDate(new Date());
		batchData.setExpiryDate(new Date());
		batchData.setUnitId(unitId);
		batchData.setUnitName(unitName);
		batchData.setUnitBatchCode(unitCodePrefix);
		batchData.setProductId(productId);
		batchData.setProductName(productName);
		batchData.setProductBatchCode(productCodePrefix);
		batchData.setCreatedBy(unitId);
		batchData.setCount(count);
		batchData.setLength(length);
		batchData.setCodeGenerationDate(generationDate);
		batchData.setDateBatchCode(datePrefix);
		return batchData;
	}

	private String generateBatchCode(int length) {
		return RandomStringUtils.randomAlphanumeric(length).toUpperCase();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public String getCodePrefix(String keyId, String keyType) {
		return batchManagementDao.getCodePrefix(keyId, keyType);
	}

}
