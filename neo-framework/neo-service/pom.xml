<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>neo-framework</artifactId>
        <groupId>com.stpl.tech.neo</groupId>
        <version>4.1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>neo-service</artifactId>
    <packaging>war</packaging>
    <name>neo-service</name>
    <url>http://maven.apache.org</url>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.spring.version>4.2.5.RELEASE</project.spring.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <prometheus.version>0.6.0</prometheus.version>
    </properties>
    <!--	<build>-->
    <!--		<plugins>-->
    <!--			<plugin>-->
    <!--				<groupId>org.apache.maven.plugins</groupId>-->
    <!--				<artifactId>maven-compiler-plugin</artifactId>-->
    <!--				<configuration>-->
    <!--					<source>1.8</source>-->
    <!--					<target>1.8</target>-->
    <!--				</configuration>-->
    <!--			</plugin>-->
    <!--			<plugin>-->
    <!--				<groupId>org.apache.maven.plugins</groupId>-->
    <!--				<artifactId>maven-war-plugin</artifactId>-->
    <!--				<version>2.6</version>-->
    <!--				<configuration>-->
    <!--					<webXml>WebContent/WEB-INF/web.xml</webXml>-->
    <!--				</configuration>-->
    <!--			</plugin>-->
    <!--		</plugins>-->
    <!--		<finalName>neo-service</finalName>-->
    <!--	</build>-->
    <build>
        <finalName>${project.artifactId}</finalName>
    </build>
    <dependencies>
        <dependency>
            <groupId>com.stpl.tech.neo</groupId>
            <artifactId>neo-core</artifactId>
            <version>4.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.redis</groupId>
            <artifactId>redis-service</artifactId>
            <version>4.1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>net.bull.javamelody</groupId>
            <artifactId>javamelody-spring-boot-starter</artifactId>
            <version>1.90.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- JSTL for c: tag -->
        <dependency>
            <groupId>jstl</groupId>
            <artifactId>jstl</artifactId>
            <version>1.2</version>
        </dependency>
        <!-- 		<dependency>
                    <groupId>org.mongodb</groupId>
                    <artifactId>mongo-java-driver</artifactId>
                    <version>3.4.1</version>
                </dependency> -->
        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-client</artifactId>
            <version>1.12</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>
