import React, {Component} from 'react';
import {connect} from 'react-redux';
import {
    ActivityIndicator,
    Dimensions,
    Image,
    KeyboardAvoidingView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import * as LoginActions from "./../store/actions/LoginActions";

const bgCover = require("./../assets/img/loginCity.png");
var {deviceWidth} = Dimensions.get('window');

class LoginScreen extends Component {

    componentWillMount = () => {
        this.props.resetLogin();
    };

    render() {
        return (
                <View style={styles.container}>
                    <KeyboardAvoidingView style={styles.keyboardContainer}>
                        <View style={styles.backgroundImageContainer}>
                            <Image source={bgCover} style={styles.backgroundImage}/>
                        </View>
                        <View style={styles.loginContainer}>
                            <Image resizeMode="contain" style={styles.logo}
                                   source={require('../assets/img/loginLogo.png')}/>
                        </View>
                        {this.props.showLoader ? (
                            <View style={styles.loaderContainer}>
                                <ActivityIndicator size="large" color='#fff'/>
                                <Text style={styles.loaderMessage}>{this.props.loadingMessage}</Text>
                            </View>
                        ): null}
                        {!this.props.showLoader ? (
                            <View style={styles.formContainer}>
                                <View style={styles.inputContainer}>
                                    <TextInput
                                        style={styles.inputBox}
                                        placeholder="UserId"
                                        keyboardType="number-pad"
                                        underlineColorAndroid='transparent'
                                        placeholderTextColor='#d4272a'
                                        maxLength={7}
                                        value={this.props.userId}
                                        onChangeText={(userId) => this.props.setUserId(userId)}
                                    />
                                </View>
                                <View style={styles.inputContainer}>
                                    <TextInput
                                        style={styles.inputBox}
                                        placeholder="Password"
                                        underlineColorAndroid="transparent"
                                        placeholderTextColor='#d4272a'
                                        secureTextEntry={true}
                                        value={this.props.password}
                                        onChangeText={(password) => this.props.setPassword(password)}
                                    />
                                </View>
                                <View style={{flex: 1, alignItems: 'stretch'}}>
                                    <TouchableOpacity style={styles.buttonStyle} onPress={() =>
                                        this.props.loginSalesPerson(this.props.userId, this.props.password, this.props)}>
                                        <Text style={styles.buttonTitleStyle}>Sign In</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ):null}
                        {this.props.errorSalesLogin ? (
                            <View style={{flex:1}}>
                                <Text style={[styles.errorMessage]}>{this.props.errorSalesLoginMessage}</Text>
                            </View>
                        ) : null}
                    </KeyboardAvoidingView>
                </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'stretch',
        justifyContent: 'center',
        // position: 'relative'
    },
    keyboardContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#f9cd54',
    },
    loginContainer: {
        flex: 1,
        alignItems: 'center',
        flexGrow: 1,
        justifyContent: 'center',
    },
    logo: {
        height: 100,
    },
    loaderContainer: {
        flex: 1,
        alignItems: 'stretch',
        justifyContent: 'center',
        marginBottom: 20,
    },
    loaderMessage: {
        color: '#fff',
        fontSize: 21,
        textAlign:'center',
        paddingLeft: 30,
        paddingRight: 30
    },
    formContainer: {
        flex: 1,
        alignItems: 'stretch',
        flexGrow: 1,
        justifyContent: 'flex-start',
        margin: 20,
        marginLeft: 30,
        marginRight: 30,
        padding: 20
    },
    inputContainer: {
        flex: 0,
        borderBottomColor: '#d4272a',
        borderBottomWidth: 1,
        height: 45,
        marginBottom: 20,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start'
    },
    inputBox: {
        height: 45,
        marginLeft: 10,
        flex: 1,
        color: '#d4272a',
        fontSize: 21,
        marginBottom: 5
    },
    backgroundImageContainer: {
        position: "absolute",
        right: 0,
        bottom: 0,
        left: 0,
        flex: 0,
        height: 70
    },
    backgroundImage: {
        width: deviceWidth,
        flex: 0,
        height: 70,
        resizeMode: "repeat"
    },
    buttonStyle: {
        flex: 0,
        height: 50,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#d4272a',
        borderRadius: 4,
        padding: 10,
    },
    buttonTitleStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: '#f9cd54'
    },
    errorMessage: {
        color: '#d4272a',
        marginLeft: 20,
        marginRight: 20,
        textDecorationLine: 'underline',
        textAlign: 'center',
        fontSize: 21
    },
});

const mapStateToProps = state => {
    return {
        showLoader: state.loginReducer.showLoader,
        loadingMessage: state.loginReducer.loadingMessage,
        userId: state.loginReducer.userId,
        password: state.loginReducer.password,
        errorSalesLogin: state.loginReducer.errorSalesLogin,
        errorSalesLoginMessage: state.loginReducer.errorSalesLoginMessage,
        authDetail: state.loginReducer.authDetail
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setUserId: (userId) => dispatch({type: "SET_USER_ID", payload: userId}),
        setPassword: (password) => dispatch({type: "SET_PASSWORD", payload: password}),
        loginSalesPerson: (userId, password, props) => dispatch(LoginActions.loginSalesPerson(userId, password, props)),
        resetLogin: () => dispatch(LoginActions.resetLogin())
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(LoginScreen);