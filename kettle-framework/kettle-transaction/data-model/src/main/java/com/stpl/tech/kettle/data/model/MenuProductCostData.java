package com.stpl.tech.kettle.data.model;

import com.stpl.tech.master.domain.model.IdCodeName;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 07-12-2018.
 */
@Entity
@Table(name = "MENU_PRODUCT_COST_DETAIL")
public class MenuProductCostData {

    private Integer detailId;
    private Integer closureId;
    private Integer unitId;
    private Integer menuProductId;
    private String menuProductName;
    private String dimension;
    private Integer recipeId;
    private String source;
    private Integer brandId;
    private String orderType;
    private BigDecimal ingredientCost;
    private BigDecimal addOnCost;
    private BigDecimal totalCost;
    private Integer quantity;
    private Integer taxableQuantity;
    private BigDecimal taxableCost;
    private BigDecimal taxableAmount;
    private BigDecimal totalAmount;
    private Date generationTime;

    private List<MenuProductCogsDrilldown> drilldownList;
    private BigDecimal menuPrice;

    public MenuProductCostData() {
    }

    public MenuProductCostData(Integer closureId, IdCodeName recipeProductKey) {
        this.closureId = closureId;
        this.menuProductId = recipeProductKey.getId();
        this.recipeId = Integer.parseInt(recipeProductKey.getCode());
        this.dimension = recipeProductKey.getName();
    }


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "COST_DETAIL_ID", unique = true, nullable = false)
    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    @Column(name = "CLOSURE_ID", nullable = false)
    public Integer getClosureId() {
        return closureId;
    }

    public void setClosureId(Integer closureId) {
        this.closureId = closureId;
    }

    @Column(name = "MENU_PRODUCT_ID", nullable = false)
    public Integer getMenuProductId() {
        return menuProductId;
    }

    public void setMenuProductId(Integer menuProductId) {
        this.menuProductId = menuProductId;
    }

    @Column(name = "DIMENSION", nullable = false)
    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    @Column(name = "PRODUCT_NAME", nullable = false)
    public String getMenuProductName() {
        return menuProductName;
    }

    public void setMenuProductName(String menuProductName) {
        this.menuProductName = menuProductName;
    }

    @Column(name = "RECIPE_ID", nullable = false)
    public Integer getRecipeId() {
        return recipeId;
    }

    public void setRecipeId(Integer recipeId) {
        this.recipeId = recipeId;
    }

    @Column(name = "COST_SOURCE", nullable = false)
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Column(name = "INGREDIENT_COST", nullable = true)
    public BigDecimal getIngredientCost() {
        return ingredientCost;
    }

    public void setIngredientCost(BigDecimal ingredientCost) {
        this.ingredientCost = ingredientCost;
    }

    @Column(name = "ADDON_COST", nullable = true)
    public BigDecimal getAddOnCost() {
        return addOnCost;
    }

    public void setAddOnCost(BigDecimal addOnCost) {
        this.addOnCost = addOnCost;
    }

    @Column(name = "TOTAL_COST", nullable = true)
    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    @Column(name = "GENERATION_TIME", nullable = false)
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    @Column(name = "QUANTITY", nullable = false)
    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @Column(name = "PRICE", nullable = false)
    public BigDecimal getMenuPrice() {
        return menuPrice;
    }

    public void setMenuPrice(BigDecimal menuPrice) {
        this.menuPrice = menuPrice;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "menuProductCostData")
    public List<MenuProductCogsDrilldown> getDrilldownList() {
        return drilldownList;
    }

    public void setDrilldownList(List<MenuProductCogsDrilldown> drilldownList) {
        this.drilldownList = drilldownList;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "BRAND_ID", nullable = false)
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "ORDER_TYPE", nullable = false)
    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    @Column(name = "TAXABLE_QUANTITY", nullable = false)
    public Integer getTaxableQuantity() {
        return taxableQuantity;
    }

    public void setTaxableQuantity(Integer taxableQuantity) {
        this.taxableQuantity = taxableQuantity;
    }

    @Column(name = "TAXABLE_COST", nullable = false)
    public BigDecimal getTaxableCost() {
        return taxableCost;
    }

    public void setTaxableCost(BigDecimal taxableCost) {
        this.taxableCost = taxableCost;
    }

    @Column(name = "TAXABLE_AMOUNT", nullable = false)
    public BigDecimal getTaxableAmount() {
        return taxableAmount;
    }

    public void setTaxableAmount(BigDecimal taxableAmount) {
        this.taxableAmount = taxableAmount;
    }

    @Column(name = "TOTAL_AMOUNT", nullable = false)
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
}
