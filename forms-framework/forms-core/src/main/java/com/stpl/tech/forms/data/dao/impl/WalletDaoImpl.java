package com.stpl.tech.forms.data.dao.impl;

import com.stpl.tech.expense.domain.model.WalletRequest;
import com.stpl.tech.forms.core.exception.WalletException;
import com.stpl.tech.forms.core.service.EnvProperties;
import com.stpl.tech.forms.data.dao.WalletDao;
import com.stpl.tech.forms.data.model.VoucherRejectionData;
import com.stpl.tech.forms.data.model.WalletApproverMapping;
import com.stpl.tech.forms.data.model.WalletBusinessCostCenterMapping;
import com.stpl.tech.forms.data.model.WalletData;
import com.stpl.tech.forms.domain.model.TransitionType;
import com.stpl.tech.forms.domain.model.VoucherStatus;
import com.stpl.tech.forms.domain.model.WalletAccountType;
import com.stpl.tech.forms.domain.model.WalletApproverMappingVO;
import com.stpl.tech.forms.domain.model.WalletBusinessCostCenterMappingVO;
import com.stpl.tech.forms.domain.model.WalletStatus;
import com.stpl.tech.forms.domain.model.WalletType;
import com.stpl.tech.kettle.data.expense.model.VoucherCostCenterAllocationEntity;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.expense.model.VoucherStatusData;
import com.stpl.tech.kettle.data.model.OrderRefundDetail;
import com.stpl.tech.kettle.data.model.OrderRefundStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@SuppressWarnings("unchecked")
@Repository
@Log4j2
public class WalletDaoImpl extends AbstractDaoImpl implements WalletDao {

	@Autowired
	private EnvProperties env;

	@Override
	public WalletData getWalletDetails(String accountType, String walletType, String accountNo) {
		StringBuilder queryString = new StringBuilder(
				"FROM WalletData w WHERE  w.accountNo = :accountNo and w.status = :status ");
//		if (walletType != null) {
//			queryString.append(" and w.walletType = :walletType ");
//		}
//		if (accountType != null) {
//			queryString.append("and w.accountType = :accountType ");
//		}

		Query query = manager.createQuery(queryString.toString());

//		if (walletType != null) {
//			query.setParameter("walletType", walletType);
//		}
//		if (accountType != null) {
//			query.setParameter("accountType", accountType);
//		}
		query.setParameter("accountNo", accountNo);
		query.setParameter("status", WalletStatus.ACTIVE.name());
		List<WalletData> datas = query.getResultList();
		if(datas.size() > 0) {
			return datas.get(0);
		}
		return null;
	}

	@Override
	public List<WalletData> getWallets(WalletRequest walletRequest) {
		StringBuilder queryString = new StringBuilder(
				"SELECT w FROM WalletData w WHERE  w.accountNo IN (:accountNos) and w.status = :status ");
		if (walletRequest.getWalletType() != null) {
			queryString.append(" and w.walletType = :walletType ");
		}
		if (walletRequest.getAccountType() != null) {
			queryString.append("and w.accountType = :accountType ");
		}

		Query query = manager.createQuery(queryString.toString());

		if (walletRequest.getWalletType() != null) {
			query.setParameter("walletType", walletRequest.getWalletType());
		}
		if (walletRequest.getAccountType() != null) {
			query.setParameter("accountType", walletRequest.getAccountType());
		}
		query.setParameter("accountNos", walletRequest.getAccountNos());
		query.setParameter("status", WalletStatus.ACTIVE.name());
		return query.getResultList();
	}

	@Override
	public List<VoucherData> getVouchers(String generatedVoucherId) {
		Query query = manager.createQuery("SELECT v FROM VoucherData v WHERE v.generatedVoucherId = :generatedVoucherId");
		query.setParameter("generatedVoucherId", generatedVoucherId);
		return query.getResultList();
	}
	@Override
	public VoucherData getVouchersByGeneratedId(String generatedVoucherId) {
		Query query = manager.createQuery("SELECT v FROM VoucherData v WHERE v.generatedVoucherId = :generatedVoucherId");
		query.setParameter("generatedVoucherId", generatedVoucherId);
		return (VoucherData) query.getSingleResult();
	}

	@Override
	public List<VoucherData> getVoucherList(List<String> status, Date startDate, Date endDate, List<String> accountNo,
											Boolean isReimbursed) {
		StringBuilder queryString = new StringBuilder("FROM VoucherData v WHERE  1 = 1 ");
		if (startDate != null) {
			queryString.append(" and v.businessDate >= :startDate  ");
		}
		if (endDate != null) {
			queryString.append(" and v.businessDate <= :endDate ");
		}
		if (status != null) {
			queryString.append(" and v.currentStatus in :status ");
		}
		if (accountNo != null) {
			queryString.append(" and v.accountNo in :accountNo ");
		}
		if (isReimbursed == Boolean.FALSE) {
			queryString.append(" and v.isReimbursed is null ");
		}
		queryString.append(" order by v.issuedTime desc ");
		Query query = manager.createQuery(queryString.toString());

		if (status != null) {
			query.setParameter("status", status);
		}
		if (accountNo != null) {
			query.setParameter("accountNo", accountNo);
		}

		if (startDate != null) {
			query.setParameter("startDate", startDate);
		}

		if (endDate != null) {
			query.setParameter("endDate", endDate);
		}

		return query.getResultList();
	}

	@Override
	public List<VoucherData> getVouchersByFinancePendingDate(List<String> status, Date startDate, Date endDate, List<String> accountNo,
											Boolean isReimbursed) {
		StringBuilder queryString = new StringBuilder("SELECT v.voucherData FROM VoucherStatusData v WHERE  v.voucherData.currentStatus = :status  AND v.toStatus in (:status) AND v.transitionStatus = :txStatus");
		if (startDate != null) {
			queryString.append(" and v.actionTime >= :startDate  ");
		}
		if (endDate != null) {
			queryString.append(" and v.actionTime <= :endDate ");
		}
		if (accountNo != null) {
			queryString.append(" and v.voucherData.accountNo in (:accountNo) ");
		}
		queryString.append(" order by v.actionTime desc ");
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("status", status);
		query.setParameter("txStatus", "SUCCESS");
		if (accountNo != null) {
			query.setParameter("accountNo", accountNo);
		}
		if (startDate != null) {
			query.setParameter("startDate", AppUtils.getStartOfDay(startDate));
		}
		if (endDate != null) {
			query.setParameter("endDate", AppUtils.getEndOfDay(endDate));
		}
		return query.getResultList();
	}

	@Override
	public void settleVoucher(Integer voucherId, Date lastUpdatedTime, int expenseId) throws WalletException {
		VoucherData voucher = manager.find(VoucherData.class, voucherId);
		if (voucher.getIsReimbursed() == null) {
			if(voucher.getExpenseMetadataId().equals(expenseId)) {
				updateOrderRefund(voucher.getId());
			}
			VoucherStatus fromStatus = VoucherStatus.valueOf(voucher.getCurrentStatus());
			voucher.setIsReimbursed(AppConstants.YES);
			voucher.setLastUpdatedTime(lastUpdatedTime);
			voucher.setCurrentStatus(VoucherStatus.SETTLED.name());
			saveVoucherStatus(fromStatus, VoucherStatus.SETTLED, "Auto Settled In Bulk", voucher,
					AppConstants.SYSTEM_EMPLOYEE_ID, lastUpdatedTime);
		}
	}

	private void updateOrderRefund(Integer voucherId) throws WalletException {
		OrderRefundDetail orderRefund = getOrderRefundByVoucherId(voucherId);
		if(Objects.isNull(orderRefund)) {
			throw new WalletException("Order Refund Detail not found for voucher Id : {}", voucherId);
		}
		if(orderRefund.getRefundStatus().equals(OrderRefundStatus.SETTLED.name())) {
			return;
		}
		orderRefund.setRefundStatus(OrderRefundStatus.SETTLED.name());
		update(orderRefund, false);
	}

	@Override
	public void settleVouchers(List<Integer> voucherIds, Date lastUpdatedTime, int expenseId) throws WalletException {
		for (Integer voucherId : voucherIds) {
			settleVoucher(voucherId, lastUpdatedTime, expenseId);
		}
	}

	private void saveVoucherStatus(VoucherStatus from, VoucherStatus to, String comment, VoucherData voucherData,
			Integer updatedBy, Date currentTime) {
		VoucherStatusData statusData = new VoucherStatusData();
		statusData.setVoucherData(voucherData);
		statusData.setFromStatus(from.name());
		statusData.setToStatus(to.name());
		statusData.setGeneratedBy(updatedBy);
		if (comment != null) {
			statusData.setActionComment(comment);
		}
		statusData.setActionTime(currentTime);
		statusData.setTransitionStatus(TransitionType.SUCCESS.name());
		add(statusData, true);
	}

	@Override
	public List<VoucherData> getClaimPendingVouchers(String accountNo) {
		Query query = manager.createQuery("SELECT v FROM VoucherData v WHERE  v.accountNo =:accountNo and v.currentStatus = :status and claimId IS NULL");
		query.setParameter("accountNo", accountNo);
		query.setParameter("status", VoucherStatus.APPROVED.name());
		return query.getResultList();
	}

	@Override
	public List<VoucherData> getVoucherListByIdsAndAccountNo(List<Integer> voucherIds, String accountNo) {
		Query query = manager.createQuery("SELECT v FROM VoucherData v WHERE  v.id IN (:voucherIds) AND v.accountNo = :accountNo");
		query.setParameter("voucherIds", voucherIds);
		query.setParameter("accountNo", accountNo);
		return query.getResultList();
	}

	@Override
	public List<VoucherData> getVoucherListByClaimId(Integer claimId) {
		Query query = manager.createQuery("SELECT v FROM VoucherData v WHERE  v.claimId = :claimId");
		query.setParameter("claimId", claimId);
		return query.getResultList();
	}

	@Override
	public List<Integer> getVoucherIdsByClaimId(Integer claimId) {
		Query query = manager.createQuery("SELECT v.id FROM VoucherData v WHERE  v.claimId = :claimId");
		query.setParameter("claimId", claimId);
		return query.getResultList();
	}

	@Override
	public Long getPendingRejectedVoucherCount(Integer walletId, List<String> status) {
		StringBuilder queryString = new StringBuilder(
				"select count(*) FROM VoucherData v WHERE  v.currentStatus in :status and v.walletId = :walletId ");
		Query query = manager.createQuery(queryString.toString());

		query.setParameter("status", status);
		query.setParameter("walletId", walletId);
		return (Long) query.getResultList().get(0);
	}

	@Override
	public Long getVoucherCount(List<Integer> walletId, List<String> status, Date date) {
		StringBuilder queryString = new StringBuilder(
				"select count(*) FROM VoucherData v WHERE  v.currentStatus in :status and v.walletId in (:walletId) and v.lastUpdatedTime < :date");
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("status", status);
		query.setParameter("walletId", walletId);
		query.setParameter("date", date);
		return (Long) query.getResultList().get(0);
	}

	@Override
	public List<WalletData> getWalletDataWithAssociatedId(Integer associatedId){
		Query query = manager.createQuery("SELECT W FROM WalletData W WHERE W.associatedId = :associatedId");
		query.setParameter("associatedId",associatedId);
		return query.getResultList();
	}

	@Override
	public List<VoucherData> getPendingVouchers(List<Integer> walletIds, List<String> status){
		Query query = manager.createQuery("FROM VoucherData V WHERE V.walletId IN (:walletId) AND V.currentStatus IN (:currentStatus)");
		query.setParameter("walletId",walletIds);
		query.setParameter("currentStatus",status);
		return query.getResultList();
	}

	@Override
	public List<VoucherRejectionData> getVoucherRejections(Integer voucherId) {
		Query query = manager.createQuery("SELECT V FROM VoucherRejectionData V WHERE V.voucherId = :voucherId");

		query.setParameter("voucherId", voucherId);
		return query.getResultList();
	}

	@Override
	public List<WalletBusinessCostCenterMapping> findBusinessCostCenterMappingsByWalletId(int walletId) {
		try{
			Query query = manager.createQuery("FROM WalletBusinessCostCenterMapping W WHERE W.walletId=:walletId AND W.mappingStatus=:status");
			query.setParameter("walletId",walletId);
			query.setParameter("status",AppConstants.ACTIVE);
			return query.getResultList();
		}catch (Exception e){
			log.error("Exception while fetching business cost center mappings for walletId :{}",walletId,e);
			return Collections.emptyList();
		}
	}

	@Override
	public List<WalletData> findWalletByAccountNo(String accountNo) {
		try{
			Query query = manager.createQuery("FROM WalletData W WHERE W.accountNo=:accountNo and W.status =:status");
			query.setParameter("accountNo",accountNo);
			query.setParameter("status",AppConstants.ACTIVE);
			return query.getResultList();
		}catch (Exception e){
			log.error("Exception while fetching wallets for accountNo :{}",accountNo,e);
			return Collections.emptyList();
		}
	}

	@Override
	public void updateWalletBusinessCostCenterMapping(WalletBusinessCostCenterMappingVO walletBusinessCostCenterMapping, String lastUpdatedBy, Integer walletId) {
		WalletBusinessCostCenterMapping mapping;
		if(walletBusinessCostCenterMapping.getWalletBccMappingId()!=null){
			mapping=manager.find(WalletBusinessCostCenterMapping.class,walletBusinessCostCenterMapping.getWalletBccMappingId());
			if(Objects.nonNull(mapping) && Objects.nonNull(mapping.getMappingStatus()) && !mapping.getMappingStatus().equalsIgnoreCase(walletBusinessCostCenterMapping.getMappingStatus())){
				mapping.setMappingStatus(walletBusinessCostCenterMapping.getMappingStatus());
				mapping.setLastUpdatedBy(lastUpdatedBy);
				mapping.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			}
		}else{
			mapping = getWalletMapping(walletBusinessCostCenterMapping,walletId);
			if (Objects.nonNull(mapping) && Objects.nonNull(mapping.getWalletBccMappingId())) {
				mapping.setMappingStatus(walletBusinessCostCenterMapping.getMappingStatus());
				mapping.setLastUpdatedBy(lastUpdatedBy);
				mapping.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			}
			else {
				mapping.setWalletBccMappingId(null);
				mapping.setMappingStatus(walletBusinessCostCenterMapping.getMappingStatus());
				mapping.setWalletId(walletId);
				mapping.setAddTime(AppUtils.getCurrentTimestamp());
				mapping.setBccCode(walletBusinessCostCenterMapping.getBccCode());
				mapping.setBccId(walletBusinessCostCenterMapping.getBccId());
				mapping.setBccType(walletBusinessCostCenterMapping.getBccType());
				mapping.setBccName(walletBusinessCostCenterMapping.getBccName());
				mapping.setAddTime(AppUtils.getCurrentTimestamp());
				mapping.setLastUpdatedBy(lastUpdatedBy);
				mapping.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			}
		}
		try{
			manager.persist(mapping);
		}catch (Exception e ){
			log.error("Exception while persiting wallet business cost center mappings to db ::::::", e);
		}

	}

	private WalletBusinessCostCenterMapping getWalletMapping(WalletBusinessCostCenterMappingVO walletBusinessCostCenterMapping, Integer walletId) {
		try {
			Query query = manager.createQuery("from WalletBusinessCostCenterMapping where walletId = :walletId and bccId = :bccId and mappingStatus= :mappingStatus");
			query.setParameter("walletId", walletId);
			query.setParameter("bccId", walletBusinessCostCenterMapping.getBccId());
			query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
			return (WalletBusinessCostCenterMapping) query.getSingleResult();
		} catch (NoResultException e) {
			return new WalletBusinessCostCenterMapping();
		}
	}

	@Override
	public List<WalletData> getUserWalletData(String accountType, Integer associatedId) {
		Query query = manager.createQuery("FROM WalletData w WHERE  w.accountType = :accountType  and w.status = :status and associatedId = :associatedId");
		query.setParameter("associatedId",associatedId);
		query.setParameter("accountType",accountType);
		query.setParameter("status", WalletStatus.ACTIVE.name());
		return query.getResultList();
	}

	@Override
	public WalletData getWalletDataByType(String accountType, Integer associatedId) {
		try {
			Query query = manager.createQuery("FROM WalletData w WHERE  w.accountType = :accountType  and w.status = :status and w.associatedId = :associatedId and w.walletType = :walletType order by  w.id desc");
			query.setParameter("associatedId",associatedId);
			query.setParameter("accountType",accountType);
			query.setParameter("walletType", WalletType.PETTY_CASH.name());
			query.setParameter("status", WalletStatus.ACTIVE.name());
			return  (WalletData) query.getResultList().get(0);
		} catch (Exception e) {
			log.error("Exception while fetching wallet data for unit :{}",associatedId,e);
			return null;
		}
	}

	@Override
	public List<VoucherCostCenterAllocationEntity> getVoucherCostCenterAllocation(Integer voucherId){
		Query query = manager.createQuery("FROM VoucherCostCenterAllocationEntity where voucherId = :voucherId");
		query.setParameter("voucherId",voucherId);
		return query.getResultList();
	}

	@Override
	public Long getPendingRejectedVoucherCountWithAssociatedId(List<Integer> walletId, List<String> status){
		StringBuilder queryString = new StringBuilder(
				"select count(*) FROM VoucherData v WHERE  v.currentStatus in :status and v.walletId in (:walletId)");
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("status", status);
		query.setParameter("walletId", walletId);
		return (Long) query.getResultList().get(0);
	}

	@Override
	public List<WalletData> getWalletAccounts(WalletAccountType walletAccountType, String employeeCode, boolean byPass) throws WalletException {
		List<Integer> id = getWalletApprovedMapping(Integer.valueOf(employeeCode));
		if (!byPass && id.isEmpty() && WalletAccountType.EMPLOYEE.equals(walletAccountType)) {
			throw new WalletException("No Wallet Mapping Found!!");
		}
		StringBuilder queryString = new StringBuilder();
		queryString.append("From WalletData where accountType = :accountType and status = :status ");
		if (!id.isEmpty() && WalletAccountType.EMPLOYEE.equals(walletAccountType) && !byPass) {
			queryString.append("and id in (:id)");
		}
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("status",AppConstants.ACTIVE);
		if (!id.isEmpty() && WalletAccountType.EMPLOYEE.equals(walletAccountType) && !byPass) {
			query.setParameter("id", id);
		}
		query.setParameter("accountType", walletAccountType.name());
		return query.getResultList();
	}

	private List<Integer> getWalletApprovedMapping(Integer employeeCode) throws WalletException {
		Query query = manager.createQuery("FROM WalletApproverMapping where approverId=:approverId and mappingStatus=:mappingStatus");
		query.setParameter("approverId",employeeCode);
		query.setParameter("mappingStatus",AppConstants.ACTIVE);
		List<WalletApproverMapping> walletData = query.getResultList();
		if (!walletData.isEmpty()) {
			return walletData.stream().map(WalletApproverMapping::getWalletId).collect(Collectors.toList());
		}
		return new ArrayList<>();
	}

	@Override
	public List<WalletApproverMapping> getWalletApproverMappings(int walletId) {
		try{
			Query query=manager.createQuery("From WalletApproverMapping W WHERE W.walletId=:walletId and W.mappingStatus = :status");
			query.setParameter("walletId",walletId);
			query.setParameter("status", AppConstants.ACTIVE);
			return query.getResultList();
		}catch (Exception e){
			log.error("Exception while fetching approver mappings for walletId :{}",walletId,e);
			return Collections.emptyList();
		}
	}

	@Override
	public void updateAllWalletApproverMappings(WalletApproverMappingVO walletApproverMapping, String lastUpdatedBy, Integer walletId) {
		WalletApproverMapping approverMapping = new WalletApproverMapping();
		Query query = manager.createQuery("From WalletApproverMapping where walletId= :walletId and approverId = :approverId");
		query.setParameter("walletId",walletId);
		query.setParameter("approverId",walletApproverMapping.getApproverId());
		Object obj;
		try{
			obj = query.getSingleResult();
			approverMapping = (WalletApproverMapping) obj;
			approverMapping.setMappingStatus(walletApproverMapping.getMappingStatus());
			approverMapping.setUpdatedBy(lastUpdatedBy);
			approverMapping.setUpdatedTime(AppUtils.getCurrentTimestamp());
		}catch (NoResultException e){
			log.error("no result found for wallet id {}", walletId);
			approverMapping.setApproverId(walletApproverMapping.getApproverId());
			approverMapping.setApproverName(walletApproverMapping.getApproverName());
			approverMapping.setWalletId(walletId);
			approverMapping.setMappingStatus(walletApproverMapping.getMappingStatus());
			approverMapping.setAddTime(AppUtils.getCurrentTimestamp());
			approverMapping.setUpdatedBy(lastUpdatedBy);
			approverMapping.setUpdatedTime(AppUtils.getCurrentTimestamp());
		}
		manager.persist(approverMapping);


	}




	@Override
	public List<VoucherData> findVoucherByGrNumber(Integer grNumber) {
		try{
			Query query = manager.createQuery("FROM VoucherData WHERE grNumber = :grNumber");
			query.setParameter("grNumber", grNumber);
			return query.getResultList();
		}
		catch (Exception exp) {
			return new ArrayList<>();
		}
	}

	@Override
	public OrderRefundDetail getOrderRefundByOrderId(int orderId) {
		Query query = manager.createQuery("FROM OrderRefundDetail where orderId = :orderId");
		query.setParameter("orderId", orderId);
		try {
			return !query.getResultList().isEmpty() ? (OrderRefundDetail) query.getResultList().get(0) : null;
		}catch (Exception e){
			return null;
		}
	}

	@Override
	public List<WalletData> findAllWalletsInStatus(String status, String accountType) {
		Query query = manager.createQuery("FROM WalletData WHERE status = :status AND accountType = :accountType");
		query.setParameter("status", status)
				.setParameter("accountType", accountType);
		return query.getResultList();
	}

	@Override
	public Map<Integer, List<VoucherData>> findAllPendingVouchers(List<Integer> walletIds, String status, Integer pastDaysFrom, Integer pastDaysTo) {
		Query query = manager.createQuery("FROM VoucherData WHERE walletId IN (:walletIds) " +
				"AND currentStatus = :status AND lastUpdatedTime >= :fromDate AND lastUpdatedTime < :toDate AND claimId IS NULL");
		query.setParameter("walletIds", walletIds)
				.setParameter("status", status)
				.setParameter("fromDate", AppUtils.getDayBeforeOrAfterCurrentDay(-1 * pastDaysFrom))
				.setParameter("toDate", AppUtils.getDayBeforeOrAfterCurrentDay(-1 * pastDaysTo));
		List<VoucherData> voucherDataList = query.getResultList();
		return voucherDataList.stream().collect(Collectors.groupingBy(VoucherData::getWalletId));
	}

	@Override
	public OrderRefundDetail getOrderRefundByVoucherId(Integer voucherId) {
		Query query = manager.createQuery("FROM OrderRefundDetail where referenceRefundId = :referenceRefundId");
		query.setParameter("referenceRefundId", voucherId);
		List<OrderRefundDetail> orderRefund = query.getResultList();
		return orderRefund.isEmpty() ? null : orderRefund.get(0);
	}


}
