//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.03.26 at 02:35:38 PM IST 
//


package com.stpl.tech.warning.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ReasonOwner.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="ReasonOwner"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="AM"/&gt;
 *     &lt;enumeration value="DGM"/&gt;
 *     &lt;enumeration value="AUDITOR"/&gt;
 *     &lt;enumeration value="HR"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "ReasonOwner")
@XmlEnum
public enum ReasonOwner {

    AM,
    DGM,
    INITIATOR_AUDITOR,
    INITIATOR_AM,
    HR;

    public String value() {
        return name();
    }

    public static ReasonOwner fromValue(String v) {
        return valueOf(v);
    }

}
