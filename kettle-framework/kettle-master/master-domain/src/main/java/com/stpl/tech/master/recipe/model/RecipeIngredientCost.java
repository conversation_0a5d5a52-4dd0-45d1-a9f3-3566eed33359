/**
 * 
 */
package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class RecipeIngredientCost implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5960457393212918703L;

	private IngredientDefinition type;

	private int productId;

	private String productName;

	private String uom;

	private BigDecimal quantity;

	private BigDecimal yield;

	private BigDecimal price;

	private BigDecimal cost;

	private String calculatedFromNegotiatedPrice;

	public RecipeIngredientCost(){
		
	}
	public RecipeIngredientCost(IngredientDefinition type, int productId, String productName, String uom,
			BigDecimal quantity, BigDecimal yield) {
		super();
		this.type = type;
		this.productId = productId;
		this.productName = productName;
		this.uom = uom;
		this.quantity = quantity;
		this.yield = yield;
	}

	/**
	 * @return the type
	 */
	public IngredientDefinition getType() {
		return type;
	}

	/**
	 * @param type
	 *            the type to set
	 */
	public void setType(IngredientDefinition type) {
		this.type = type;
	}

	/**
	 * @return the productId
	 */
	public int getProductId() {
		return productId;
	}

	/**
	 * @param productId
	 *            the productId to set
	 */
	public void setProductId(int productId) {
		this.productId = productId;
	}

	/**
	 * @return the productName
	 */
	public String getProductName() {
		return productName;
	}

	/**
	 * @param productName
	 *            the productName to set
	 */
	public void setProductName(String productName) {
		this.productName = productName;
	}

	/**
	 * @return the uom
	 */
	public String getUom() {
		return uom;
	}

	/**
	 * @param uom
	 *            the uom to set
	 */
	public void setUom(String uom) {
		this.uom = uom;
	}

	/**
	 * @return the quantity
	 */
	public BigDecimal getQuantity() {
		return quantity;
	}

	/**
	 * @param quantity
	 *            the quantity to set
	 */
	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	/**
	 * @return the price
	 */
	public BigDecimal getPrice() {
		return price;
	}

	/**
	 * @param price
	 *            the price to set
	 */
	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	/**
	 * @return the cost
	 */
	public BigDecimal getCost() {
		return cost;
	}

	/**
	 * @param cost
	 *            the cost to set
	 */
	public void setCost(BigDecimal cost) {
		this.cost = cost;
	}
	/**
	 * @return the yield
	 */
	public BigDecimal getYield() {
		return yield;
	}
	/**
	 * @param yield the yield to set
	 */
	public void setYield(BigDecimal yield) {
		this.yield = yield;
	}

	public String getCalculatedFromNegotiatedPrice() {
		return calculatedFromNegotiatedPrice;
	}

	public void setCalculatedFromNegotiatedPrice(String calculatedFromNegotiatedPrice) {
		this.calculatedFromNegotiatedPrice = calculatedFromNegotiatedPrice;
	}
}
