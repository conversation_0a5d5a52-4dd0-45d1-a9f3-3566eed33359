/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core;

public enum ReportParams {

	REPORT_EXECUTION_CLASS(1000), OUTPUT_DIRECTORY(1001), EMAIL_ID(1003), UNIT_ID(1004), START_TIME(1005), END_TIME(
			1006), BUSINESS_DATE(1007), TRIGGERED_BY(1008);

	private final int defId;

	private ReportParams(int defId) {
		this.defId = defId;
	}

	public int getDefId() {
		return defId;
	}

}
