/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.customer.service.impl;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.stpl.tech.kettle.customer.dao.ChaayosBranchMappingDao;
import com.stpl.tech.kettle.data.model.ChaayosBranchUnitMapping;
import com.stpl.tech.kettle.data.model.OrderFeedbackQuestionResponse;
import com.stpl.tech.kettle.domain.model.OrderItemReviewModal;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.util.AppConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.core.FeedbackEventStatus;
import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.kettle.core.FeedbackFrequency;
import com.stpl.tech.kettle.core.FeedbackSource;
import com.stpl.tech.kettle.core.FeedbackStatus;
import com.stpl.tech.kettle.core.data.vo.AuditTokenInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackEventInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackOrderMetadata;
import com.stpl.tech.kettle.core.data.vo.FeedbackRatingData;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.dao.FeedbackManagementDao;
import com.stpl.tech.kettle.customer.service.FeedbackManagementService;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.FeedbackDetail;
import com.stpl.tech.kettle.data.model.FeedbackEvent;
import com.stpl.tech.kettle.data.model.FeedbackOrderItem;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderDetailForFeedback;
import com.stpl.tech.kettle.data.model.OrderFeedbackResponse;
import com.stpl.tech.kettle.data.model.OrderItemFeedbackResponse;
import com.stpl.tech.kettle.domain.model.OrderNPSRatingUpdate;
import com.stpl.tech.kettle.domain.model.external.EventDetail;
import com.stpl.tech.kettle.domain.model.webengage.survey.WebEngageSurveyForm;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;

@Service
public class FeedbackManagementServiceImpl implements FeedbackManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(FeedbackManagementServiceImpl.class);

    @Autowired
    private FeedbackManagementDao dao;
    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private TokenService<FeedbackTokenInfo> tokenService;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private ChaayosBranchMappingDao mappingDao;

    private static String NO_RESPONSE = "NA";
    private static String NEW_LINE_CHARACTER = " \\n ";


    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<FeedbackEventInfo> getPendingFeedbackEvents(FeedbackSource source, Date startTime, Date endTime) {
        return dao.getPendingFeedbackEvents(source, startTime, endTime);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<FeedbackEventInfo> getPendingElaboratedFeedbackEvents(FeedbackSource source, Date startTime,
                                                                      Date endTime) {
        return dao.getPendingElaboratedFeedbackEvents(source, startTime, endTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCustomerInfoInFeedbackData(int feedbackId, int customerId, String emailId) {
        return dao.updateCustomerInfoInFeedbackData(feedbackId, customerId, emailId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Pair<String, String> getFeedbackLinkForSource(int feedbackId, FeedbackSource feedbackSource) {
        FeedbackEventInfo event = getFeedbackEventInfo(feedbackId, feedbackSource);
        ShortUrlData shortUrl = null;
        String feedbackUrl = null;
        if (event == null) {
            return null;
        }
        try {
            String unitName = masterCache.getUnitBasicDetail(event.getUnitId()).getName();
            FeedbackTokenInfo token = new FeedbackTokenInfo(event.getContactNumber(), unitName, event.getCustomerName(),
                    event.getOrderId(), event.getOrderSource(), event.getEventSource(), event.getFeedbackId(),
                    event.getFeedbackEventId());
            String jwtToken = tokenService.createToken(token, -1L);
            // feedback.endpoint.nps.delivery.only.order
            if (FeedbackSource.IN_APP.equals(feedbackSource)) {
                if(props.getOrderFeedbackType().equals("internal")){
                    feedbackUrl = event.getBrand().getInternalOrderFeedbackUrl();
                }else{
                    feedbackUrl = (UnitCategory.COD.name().equals(event.getOrderSource())
                            ? event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointDelivery()
                            : event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointDinein());
                }
                URIBuilder builder = new URIBuilder(feedbackUrl);
                builder.addParameter("name", event.getCustomerName());
                builder.addParameter("number", event.getContactNumber());
                builder.addParameter("unit", unitName);
                builder.addParameter("token", jwtToken);
                feedbackUrl = builder.build().toURL().toString();
                shortUrl = new ShortUrlData(null, null);
            } else {
                if(props.getOrderFeedbackType().equals("internal")){
                    feedbackUrl = event.getBrand().getInternalOrderFeedbackUrl();
                }else{
                    feedbackUrl = event.getBrand().getFeedBackUrl()
                            + event.getBrand().getFeedBackEndpointNPSDeliveryOnlyOrder();
                }
                URIBuilder builder = new URIBuilder(feedbackUrl);
                builder.addParameter("unit", unitName);
                builder.addParameter("token", jwtToken);
                feedbackUrl = builder.build().toURL().toString();
                shortUrl = SolsInfiniWebServiceClient.getTransactionalClient(event.getBrand()).getShortUrl(feedbackUrl);
            }


            event.setEventLongUrl(feedbackUrl);
            event.setEventShortUrl(shortUrl.getUrl());
            updateFeedbackEventStatus(event.getFeedbackId(), event.getFeedbackEventId(), shortUrl, feedbackUrl,
                    FeedbackEventStatus.NOTIFIED, FeedbackStatus.CREATED);
        } catch (IOException | URISyntaxException e) {
            LOG.error("Error while generating the feedback message to " + event.getContactNumber(), e);
            updateFeedbackEventStatus(event.getFeedbackId(), event.getFeedbackEventId(), null, null,
                    FeedbackEventStatus.FAILED, FeedbackStatus.FAILED);
        }
        return new Pair<>(feedbackUrl, shortUrl.getUrl());
    }

    @Override
    public Integer getOrderID(Integer feedbackId) {
        return dao.getOrderId(feedbackId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String getOrderDetailForFeedback(FeedbackTokenInfo tokenInfo) throws IOException {
        String auth= props.getKettleServiceOrderManagementAuth();
        LOG.info("token info == {}", tokenInfo.toString());
        try{
            OrderDetailForFeedback orderDetailForFeedback = WebServiceHelper.postRequestWithAuthInternalWithTimeout(props.getKettleServiceBaseUrl()+"/rest/v1/order-management/order-detail-feedback",tokenInfo,OrderDetailForFeedback.class,auth);
            LOG.info("order feedback detail = {}", orderDetailForFeedback.toString());
            OrderFeedbackResponse orderFeedbackResponse= dao.getResponseForFeedbackId(orderDetailForFeedback.getFid());
            if(orderFeedbackResponse != null){
                orderDetailForFeedback.setOr(orderFeedbackResponse.getOrderRating());
                orderDetailForFeedback.setOnr(orderFeedbackResponse.getOrderNPSRating());
            }
            return JSONSerializer.toJSON(orderDetailForFeedback);
        }catch (Exception e){
			LOG.error("Error occured while fetching order detail from kettle by crm {} ", tokenInfo, e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveFeedback(OrderDetailForFeedback feedbackData){
        if(feedbackData != null){
            CustomerInfo customerInfo = customerDao.getCustomerInfoById(feedbackData.getCid());
            OrderDetail orderDetail = dao.getOrderDetail(getOrderID(feedbackData.getFid()));
            OrderFeedbackResponse orderFeedbackResponse = new OrderFeedbackResponse();
            orderFeedbackResponse.setFeedbackId(feedbackData.getFid());
            orderFeedbackResponse.setOrderId(feedbackData.getOid());
            orderFeedbackResponse.setCustomerId(feedbackData.getCid());
            orderFeedbackResponse.setCustomerName(feedbackData.getCn());
            orderFeedbackResponse.setFeedbackTime(AppUtils.getCurrentTimestamp());
            orderFeedbackResponse.setOrderComment(feedbackData.getOc());
            orderFeedbackResponse.setUserAgent(feedbackData.getUa());
            orderFeedbackResponse.setRedirectUrl(feedbackData.getRu());
            orderFeedbackResponse.setChannelPartnerCode(feedbackData.getCpc());
            orderFeedbackResponse.setChannelPartnerName(feedbackData.getCpn());
            orderFeedbackResponse.setOrderSource(feedbackData.getOs());
            orderFeedbackResponse.setCustomerCallback(feedbackData.getCc());
            orderFeedbackResponse.setFeedbackType(FeedbackEventType.ORDER_FEEDBACK.name());
            orderFeedbackResponse.setMaxRating(5);
            orderFeedbackResponse.setMaxNPSRating(10);
            orderFeedbackResponse.setOrderRating(0);
            orderFeedbackResponse.setOrderNPSRating(0);
            orderFeedbackResponse.setHasOrderRating("N");
            orderFeedbackResponse.setHasOrderNPSRating("N");
            if(feedbackData.isSor()){
                orderFeedbackResponse.setHasOrderRating("Y");
                orderFeedbackResponse.setOrderRating(feedbackData.getOr());
            }
            if(feedbackData.isSonr()){
                orderFeedbackResponse.setHasOrderNPSRating("Y");
                orderFeedbackResponse.setOrderNPSRating(feedbackData.getOnr());
            }
            dao.addNpsDetailForOrderFeedback(feedbackData, customerInfo);
            if(orderFeedbackResponse.getRedirectUrl().isEmpty()){
                orderFeedbackResponse.setRedirectUrl(feedbackData.getRud());
            }
            if(orderFeedbackResponse.getCustomerCallback().equals("Y")){
                dao.sendCallBackNpsSlack(feedbackData,customerInfo,masterCache.getUnit(feedbackData.getUid()).getCafeManager().getName(),orderDetail);
            }
            List<OrderItemFeedbackResponse> orderItemFeedbackResponseList = new ArrayList<>();

            for(FeedbackOrderItem feedbackOrderItem : feedbackData.getFiol()){
                OrderItemFeedbackResponse orderItemFeedbackResponse = new OrderItemFeedbackResponse();
                orderItemFeedbackResponse.setOrderItemId(feedbackOrderItem.getIid());
                orderItemFeedbackResponse.setProductId(feedbackOrderItem.getPid());
                orderItemFeedbackResponse.setProductName(feedbackOrderItem.getIn());
                orderItemFeedbackResponse.setQuantity(feedbackOrderItem.getQt());
                orderItemFeedbackResponse.setIssueTags(feedbackOrderItem.getIt());
                orderItemFeedbackResponse.setDimension(feedbackOrderItem.getD());
                orderItemFeedbackResponse.setCustomisation(feedbackOrderItem.getC());
                orderItemFeedbackResponse.setQuestion(feedbackOrderItem.getQ());
                orderItemFeedbackResponse.setItemRating(feedbackOrderItem.getIr());
                orderItemFeedbackResponse.setItemComment(feedbackOrderItem.getIc());
                orderItemFeedbackResponse.setImageUrl(feedbackOrderItem.getIri());
                orderItemFeedbackResponse.setVideoUrl(feedbackOrderItem.getIrv());
                orderItemFeedbackResponse.setOrderFeedbackResponse(orderFeedbackResponse);
                orderItemFeedbackResponse.setFeedbackType(FeedbackEventType.ORDER_FEEDBACK.name());
                orderItemFeedbackResponse.setMaxRating(5);
                orderItemFeedbackResponseList.add(orderItemFeedbackResponse);
            }
            OrderFeedbackResponse response = dao.add(orderFeedbackResponse);
            for(OrderItemFeedbackResponse orderItemFeedbackResponse : orderItemFeedbackResponseList){
                orderItemFeedbackResponse.setOrderFeedbackResponse(response);
                dao.add(orderItemFeedbackResponse);
                dao.updateOrderFeedbackEvent(feedbackData, response.getOrderRating());
            }
            if(response != null){
                LOG.info("Calling FAMEPILOT FOR FEEDBACK ID : {}",feedbackData.getFid());
                if(AppUtils.isProd(props.getEnvironmentType())) {
                    try {
                        LOG.info("calling famePilot API");
                        List<NameValuePair> famePilotObject = getObjectForFamePilot(feedbackData, customerInfo);
                        String url = props.getFamePilotUrlForFeedbackEvent();
                        WebServiceHelper.postRequestForFamePilot(url, famePilotObject);
                    } catch (Exception e) {
                        LOG.error("Error in posting review on famePilot", e);
                    }
                }
                return true;
            }
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<NameValuePair> getObjectForFamePilot(OrderDetailForFeedback feedbackData,CustomerInfo customerInfo) throws IOException {
        List<NameValuePair> formData = new ArrayList<>();
        Integer branchId = mappingDao.findByUnitId(feedbackData.getUid()).getBranchId();
        formData.add(new BasicNameValuePair("business_uuid",props.getFamePilotBussinessUUID()));
        formData.add(new BasicNameValuePair("branch",branchId.toString()));
        formData.add(new BasicNameValuePair("reviewer_name",feedbackData.getCn()));
        formData.add(new BasicNameValuePair("contact_number","+91" + customerInfo.getContactNumber()));
        formData.add(new BasicNameValuePair("email",customerInfo.getEmailId()));
        formData.add(new BasicNameValuePair("review_time",AppUtils.getCurrentDateTime()));
        formData.add(new BasicNameValuePair("rating",getFeedBackRating(feedbackData)));
        formData.add(new BasicNameValuePair("review_text",getReviewTextForFamePilot(feedbackData,customerInfo)));
        formData.add(new BasicNameValuePair("review_tags",getReviewTags(feedbackData)));
        formData.add(new BasicNameValuePair("order_id",feedbackData.getGoid()));
        formData.add(new BasicNameValuePair("order_items",getOrderItemsText(feedbackData)));
        return formData;
        //2024-01-08T12:11:00
    }

    public String getReviewTags(OrderDetailForFeedback feedback){
        String tags = "";
        if(Objects.nonNull(feedback) && Objects.nonNull(feedback.getFiol()) && !feedback.getFiol().isEmpty()){
            List<FeedbackOrderItem> itemList = feedback.getFiol();
            if (Objects.nonNull(itemList) && !itemList.isEmpty()) {
                for (FeedbackOrderItem item : itemList) {
                    tags = tags + item.getIn() +",";
                }
                tags = tags.substring(0,tags.length()-1);
            }
        }
        return tags;
    }

    public String getOrderItemsText(OrderDetailForFeedback feedbackData){
        List<OrderItemReviewModal> list = new ArrayList<>();
        if(Objects.nonNull(feedbackData.getFiol())) {
            List<FeedbackOrderItem> itemList = feedbackData.getFiol();
            if (Objects.nonNull(itemList) && !itemList.isEmpty()) {
                for (FeedbackOrderItem item : itemList) {
                    OrderItemReviewModal orderItemReviewModal = new OrderItemReviewModal();
                    String itemName = item.getIn();
                    String dimension = "None";
                    if (Objects.nonNull(item.getD()) && !item.getD().isEmpty() && !item.getD().equals("None")) {
                        dimension = item.getD();
                    }
                    orderItemReviewModal.setItemName(itemName);
                    if (Objects.nonNull(item.getQt()) && item.getQt() > 1) {
                        orderItemReviewModal.setQuantity(item.getQt());
                    }else{
                        orderItemReviewModal.setQuantity(1);
                    }
//                    if (Objects.nonNull(item.getIr()) && item.getIr() != 0) {
//                        orderItemReviewModal.setRating(item.getIr());
//                    }
//                    if (!StringUtils.isEmpty(item.getIc())) {
//                        orderItemReviewModal.setItemReview(item.getIc());
//                    }
                    Collection<Product> productList = masterCache.getUnitProductDetails(feedbackData.getUid());
                    for(Product p : productList){
                        if(p.getId()==item.getPid()){
                            for(ProductPrice prices : p.getPrices()){
                                if(dimension.equals(prices.getDimension())){
                                    orderItemReviewModal.setPrice(prices.getPrice());
                                }
                            }
                        }
                    }
                    list.add(orderItemReviewModal);
                }
            }
            if(!list.isEmpty()){
                String res = "[";
                for(OrderItemReviewModal o : list){
                    if(res.equals("[")) {
                        res = res + o.toString();
                    }else{
                        res = res + ","+o.toString();
                    }
                }
                res = res + "]";
                return res;
            }
        }
        return "";
    }

    public String getFeedBackRating(OrderDetailForFeedback feedback){
        if(feedback.isSor()){
            return feedback.getOr().toString();
        }else{
            if(feedback.getOnr()>=1 && feedback.getOnr()<=3){
                return "1";
            } else if (feedback.getOnr()>=4 && feedback.getOnr()<=5) {
                return "2";
            } else if (feedback.getOnr()>=6 && feedback.getOnr()<=7) {
                return "3";
            } else if (feedback.getOnr()>=8 && feedback.getOnr()<=9) {
                return "4";
            } else{
                return "5";
            }
        }
    }
    public String getReviewTextForFamePilot(OrderDetailForFeedback feedbackData,CustomerInfo customerInfo) throws IOException {
        String reviewText = "";
        StringBuilder sb = new StringBuilder();
        if(Objects.nonNull(feedbackData)){
            sb.append(feedbackData.getOs() + NEW_LINE_CHARACTER);
            sb.append(feedbackData.getCpn() + NEW_LINE_CHARACTER);
            sb.append("Customer Name : "+feedbackData.getCn() + NEW_LINE_CHARACTER);
            if(Objects.nonNull(customerInfo.getContactNumber()) && !customerInfo.getContactNumber().isEmpty()){
                sb.append("Contact Number : " + "+91" + customerInfo.getContactNumber() + NEW_LINE_CHARACTER);
            }
            if(Objects.nonNull(customerInfo.getEmailId()) && !customerInfo.getEmailId().isEmpty()){
                sb.append("Email : " + customerInfo.getEmailId() + NEW_LINE_CHARACTER);
            }
            if(Objects.nonNull(feedbackData.getFiol())) {
                List<FeedbackOrderItem> itemList = feedbackData.getFiol();
                if (Objects.nonNull(itemList) && !itemList.isEmpty()) {
                    for (FeedbackOrderItem item : itemList) {
                        sb.append(item.getIn() + " ");
                        if (Objects.nonNull(item.getD()) && !item.getD().isEmpty() && !item.getD().equals("None")) {
                            sb.append("- " + item.getD() + " ");
                        }
                        if (Objects.nonNull(item.getQt()) && item.getQt() > 1) {
                            sb.append("X " + item.getQt() + " ");
                        }
                        if (item.getIr() != 0) {
                            sb.append("| " + item.getIr().toString() + " | ");
                        }
                        if (Objects.nonNull(item.getIt()) && !item.getIt().isEmpty()) {
                            for (String s : item.getIt().split(",")) {
                                sb.append(s + " | ");
                            }
                        }
                        if (!item.getIc().isEmpty()) {
                            sb.append(item.getIc());
                        }
                        sb.append(NEW_LINE_CHARACTER);
                    }
                }
            }
            if(Objects.nonNull(feedbackData.getOc()) && !feedbackData.getOc().isEmpty()) {
                sb.append("Order Comment : " + feedbackData.getOc() + NEW_LINE_CHARACTER);
            }else{
                sb.append("Order Comment : " + NO_RESPONSE + NEW_LINE_CHARACTER);
            }
            if(Objects.nonNull(feedbackData.getYnq()) && !feedbackData.getYnq().isEmpty()){
                for(OrderFeedbackQuestionResponse response : feedbackData.getYnq()){
                    if(Objects.nonNull(response.getR()) && !response.getR().isEmpty()) {
                        sb.append(response.getQ() + " | " + response.getR() + " |" + NEW_LINE_CHARACTER);
                    }else{
                        sb.append(response.getQ() + " | " + NO_RESPONSE + " |" + NEW_LINE_CHARACTER);
                    }
                }
            }
            if(Objects.nonNull(feedbackData.getTrq()) && !feedbackData.getTrq().isEmpty()){
                for(OrderFeedbackQuestionResponse response : feedbackData.getTrq()){
                    if(Objects.nonNull(response.getR()) && !response.getR().isEmpty()) {
                        sb.append(response.getQ() + " | " + response.getR() + " |" + NEW_LINE_CHARACTER);
                    }else {
                        sb.append(response.getQ() + " | " + NO_RESPONSE + " |" + NEW_LINE_CHARACTER);
                    }
                }
            }
            if(Objects.nonNull(feedbackData.getMcq()) && !feedbackData.getMcq().isEmpty()){
                for(OrderFeedbackQuestionResponse response : feedbackData.getMcq()){
                    if(Objects.nonNull(response.getR()) && !response.getR().isEmpty()) {
                        sb.append(response.getQ() + " | " + response.getR() + " |" + NEW_LINE_CHARACTER);
                    }else {
                        sb.append(response.getQ() + " | " + NO_RESPONSE + " |" + NEW_LINE_CHARACTER);
                    }
                }
            }

            if(Objects.nonNull(feedbackData.getErq()) && !feedbackData.getErq().isEmpty()){
                for(OrderFeedbackQuestionResponse response : feedbackData.getErq()){
                    if(Objects.nonNull(response.getR()) && !response.getR().isEmpty()) {
                        sb.append(response.getQ() + " | " + response.getR() + " |" + NEW_LINE_CHARACTER);
                    }else {
                        sb.append(response.getQ() + " | " + NO_RESPONSE + " |" + NEW_LINE_CHARACTER);
                    }
                }
            }

            try {
                String shortUrl = getShortUrlForFeedBack(feedbackData.getFid(), feedbackData.getBid());
                if (Objects.nonNull(shortUrl) && !shortUrl.isEmpty()) {
                    sb.append("Feedback Link : https://" + shortUrl + NEW_LINE_CHARACTER);
                }
            }catch (Exception e){
                LOG.error("Error in adding feedback link : {}",e);
            }
            if(Objects.nonNull(feedbackData.getCc()) && !feedbackData.getCc().isEmpty() && feedbackData.getCc().equals(AppConstants.YES)){
                sb.append("Customer Requested Callback | "+feedbackData.getCc() + " |");
            }
            reviewText = sb.toString();
            return reviewText;
        }
        return reviewText;
    }

    private String getShortUrlForFeedBack(Integer id,Integer brandId) throws IOException {
        String shortUrl = "";
        SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(masterCache.getBrandMetaData().get(brandId));
        FeedbackEvent event = dao.getFeedbackEvent(id);
        if(Objects.nonNull(event)){
            shortUrl = String.valueOf(smsWebServiceClient.getShortUrl(event.getEventLongUrl()).getUrl());
        }
        return shortUrl;
    }


    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateFeedbackEventStatus(List<Integer> eventIds, FeedbackEventStatus status) {
        return dao.updateFeedbackEventStatus(eventIds, status);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public FeedbackRatingData addFeedback(EventDetail event, FeedbackTokenInfo tokenInfo) {

        FeedbackRatingData rating = dao.addFeedback(event, tokenInfo);
        tokenInfo.setRating(rating.getRating());
        if (props.getFeedbackNotoficationSlack()) {
            if (tokenInfo.getOrderSource().equals(UnitCategory.COD.name())) {
                SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Kettle",
                        SlackNotification.FEEDBACK_DELIVERY_LOW_RATING, tokenInfo);

            } else {
                SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Kettle",
                        SlackNotification.FEEDBACK_CAFE_LOW_RATING, tokenInfo);

            }
        }
        //  push ratings to dine in app

        try {
            updateOrderNPSRating(rating);
        } catch (Exception e) {
            LOG.error("Error updating order nps rating ", e);
        }

        return rating;
    }

    private void updateOrderNPSRating(FeedbackRatingData rating) {
        OrderNPSRatingUpdate data = new OrderNPSRatingUpdate();
        data.setOrderId(rating.getOrderId());
        data.setRatings(rating.getRating());
        LOG.info("Updating order NPS Rating " + JSONSerializer.toJSON(data));
        Boolean response = WebServiceHelper.postWithAuth(
                props.getDineInCRMBaseUrl() + "v2/crm/nps/update/rating/1000", props.getDineInToken(), data,
                Boolean.class);
        LOG.info("Updating order NPS Rating Response " + JSONSerializer.toJSON(response));
//		try {
//			OrderNPSRatingUpdate data = new OrderNPSRatingUpdate();
//			data.setOrderId(tokenInfo.getOrderId());
//			data.setRatings(rating.getRating());
//			sqsNotificationService.publishToSQS(props.getEnvironmentType().name(), data, "_APP_ORDER_NPS_RATINGS",
//					Regions.fromName(props.getAppOrderStatusQueueRegion()));
//			LOG.info("Publishing NPS Order ratings " + tokenInfo.getOrderId() + " rating " + rating.getRating());
//		} catch (JMSException ex) {
//			LOG.error(" ERROR Publishing NPS Order ratings " +  tokenInfo.getOrderId() + " rating " + rating.getRating(), ex);
//		}
    }


    public Integer addAudit(EventDetail event, AuditTokenInfo auditInfo) {
        return dao.addAudit(event, auditInfo);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Date updateFeedbackEventStatus(int feedbackId, int eventId, ShortUrlData shortUrl, String longUrl,
                                          FeedbackEventStatus status, FeedbackStatus feedbackStatus) {
        dao.updateFeedbackDetail(feedbackId, feedbackStatus);
        return dao.updateFeedbackEventStatus(eventId, shortUrl, longUrl, status);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public FeedbackEvent createFeedbackEvent(FeedbackSource source, int orderId, String orderSource, int feedbackId,
                                             Date currentTimestamp, FeedbackEventType eventType, Integer rating, int brandId) {

        return dao.createFeedbackEvent(source, orderId, orderSource, feedbackId, currentTimestamp, eventType, rating, brandId);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelFeedback(String feedbackToken) {
        FeedbackTokenInfo tokenInfo = new FeedbackTokenInfo();
        tokenService.parseToken(tokenInfo, feedbackToken);
        FeedbackDetail feedbackDetail = dao.find(FeedbackDetail.class, tokenInfo.getFeedbackId());
        if (feedbackDetail != null) {
            feedbackDetail.setFeedbackStatus(FeedbackStatus.CREATED.name());
            dao.update(feedbackDetail);
            FeedbackEvent feedbackEvent = dao.find(FeedbackEvent.class, tokenInfo.getFeedbackEventId());
            if (feedbackEvent != null) {
                feedbackEvent.setEventStatus(FeedbackEventStatus.CANCELLED.name());
                dao.update(feedbackEvent);
                return true;
            }
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateFeedbackData(FeedbackFrequency frequency) {
        dao.updateFeedbackData(frequency);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public boolean getFeedbackStatus(int customerId) {
        LoyaltyScore loyaltyScore = customerDao.getLoyaltyScore(customerId);
        if (loyaltyScore != null && loyaltyScore.getLastOrderId() != null) {
            FeedbackOrderMetadata feedbackDetail = dao.getOrderFeedbackDetail(loyaltyScore.getLastOrderId(),
                    FeedbackSource.POS);
            if (feedbackDetail != null) {
                return feedbackDetail.getFeedbackStatus().equals(FeedbackStatus.CREATED);
            }
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public FeedbackOrderMetadata getFeedbackData(int customerId) {
        LoyaltyScore loyaltyScore = customerDao.getLoyaltyScore(customerId);
        return dao.getOrderFeedbackDetail(loyaltyScore.getLastOrderId(), FeedbackSource.POS);
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.kettle.customer.service.FeedbackManagementService#addOrderNPSDetail(com.stpl.tech.kettle.domain.model.webengage.survey.WebEngageSurveyForm)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer addOrderNPSDetail(WebEngageSurveyForm form) {
        return dao.addOrderNPSDetail(form);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void runNpsProc() {
        dao.runNpsProc();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<FeedbackEventInfo> getPendingNPSEvents(FeedbackSource source) {
        return dao.getPendingNPSEvents(source);
    }

    @Override
    public List<FeedbackEventInfo> getInAppPendingNPSEvents(FeedbackSource source) {
        return dao.getInAppPendingNPSEvents(source);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateLastNPSTime(Date updateTime, int customerId) {
        customerDao.updateLastNPSTime(updateTime, customerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateFeedbackDetail(Integer feedbackId, FeedbackStatus status) {
        return dao.updateFeedbackDetail(feedbackId, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<FeedbackEventInfo> getNotifiedNPSEventsForLastDay(FeedbackSource source) {
        return dao.getNotifiedNPSEventsForLastDay(source);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public FeedbackEventInfo getFeedbackEventInfo(int feedbackId, FeedbackSource qr) {
        return dao.getFeedbackEventInfo(feedbackId, qr);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public FeedbackDetail getFeedbackDetailForSource(Integer orderId, FeedbackSource source) {
        return dao.getFeedbackForSource(orderId, source);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public FeedbackEventInfo getPendingNPSForCustomer(FeedbackSource source, Integer orderId, Integer customerId) {
        return dao.getPendingNPSEventsForCustomer(source,orderId,customerId);
    }


}
