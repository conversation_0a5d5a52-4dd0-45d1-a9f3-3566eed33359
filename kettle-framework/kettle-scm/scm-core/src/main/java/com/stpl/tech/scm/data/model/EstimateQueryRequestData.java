/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;



import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * OrderItem generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ESTIMATE_QUERY_REQUEST")
public class EstimateQueryRequestData implements java.io.Serializable {

    private Integer id;
    private Date targetDate;
    private Integer dayOfWeek;
    private Integer unitId;
    private Integer brandId;
    private BigDecimal targetSale;
    private String status;
    private String calculationDates;
    private String orderType;
    private Date generationDate;
    private BigDecimal dineInSale;
    private BigDecimal deliverySale;


    public EstimateQueryRequestData() {
    }

    public EstimateQueryRequestData(Integer id, Date targetDate, Integer dayOfWeek, Integer unitId, Integer brandId, BigDecimal targetSale, String status, String calculationDates) {
        this.id = id;
        this.targetDate = targetDate;
        this.dayOfWeek = dayOfWeek;
        this.unitId = unitId;
        this.brandId = brandId;
        this.targetSale = targetSale;
        this.status = status;
        this.calculationDates = calculationDates;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }




    @Column(name = "TARGET_DATE")
    public Date getTargetDate() {
        return targetDate;
    }

    public void setTargetDate(Date targetDate) {
        this.targetDate = targetDate;
    }

    @Column(name = "DAY_OF_WEEK")
    public Integer getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(Integer dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    @Column(name = "UNIT_ID")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }


    @Column(name = "BRAND_ID")
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "TARGET_SALE")
    public BigDecimal getTargetSale() {
        return targetSale;
    }

    public void setTargetSale(BigDecimal targetSale) {
        this.targetSale = targetSale;
    }

    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "CALCULATION_DATES")
    public String getCalculationDates() {
        return calculationDates;
    }

    public void setCalculationDates(String calculationDates) {
        this.calculationDates = calculationDates;
    }

    @Column(name="ORDERING_TYPE")
    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name="GENERATION_DATE")
    public Date getGenerationDate() {
        return generationDate;
    }

    public void setGenerationDate(Date generationDate) {
        this.generationDate = generationDate;
    }

    @Column(name="DINE_IN_SALE")
    public BigDecimal getDineInSale() {
        return dineInSale;
    }

    public void setDineInSale(BigDecimal dineInSale) {
        this.dineInSale = dineInSale;
    }

    @Column(name="DELIVERY_SALE")
    public BigDecimal getDeliverySale() {
        return deliverySale;
    }

    public void setDeliverySale(BigDecimal deliverySale) {
        this.deliverySale = deliverySale;
    }
}
