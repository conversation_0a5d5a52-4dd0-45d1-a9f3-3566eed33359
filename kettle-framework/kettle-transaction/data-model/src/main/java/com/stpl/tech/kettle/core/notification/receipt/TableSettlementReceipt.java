package com.stpl.tech.kettle.core.notification.receipt;

import java.math.BigDecimal;
import java.util.Map;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.RawPrintReceipt;
import com.stpl.tech.kettle.data.model.TableOrderMappingDetail;
import com.stpl.tech.kettle.data.model.UnitTableMappingDetail;
import com.stpl.tech.master.domain.model.PaymentMode;

public class TableSettlementReceipt extends RawPrintReceipt {

	private Map<Integer, BigDecimal> settlementMap;
	private Map<Integer, PaymentMode> paymentModes;
	private String basePath;
	private UnitTableMappingDetail table;

	public TableSettlementReceipt(Map<Integer, BigDecimal> settlementMap, Map<Integer, PaymentMode> paymentModes,
			UnitTableMappingDetail table, String basePath) {
		this.settlementMap = settlementMap;
		this.basePath = basePath;
		this.table = table;
		this.paymentModes = paymentModes;
	}

	@Override
	public StringBuilder processData() {
		reset();
		left(rpad("Table Number", 20) + table.getTableNumber());
		left(rpad("Customer Name", 20) + table.getCustomerName());
		left(rpad("Total Amount", 20) + table.getTotalAmount());
		separator();
		leftBold("Orders");
		for(TableOrderMappingDetail map : table.getOrders()) {
			if (!TransactionUtils.isCancelled(map.getOrder().getOrderStatus())) {
				left(rpad(map.getOrder().getGeneratedOrderId(), 36) + lpad(map.getOrder().getSettledAmount(), 10));
			}
		}
		separator();
		leftBold("Settlements");
		BigDecimal amount = null;
		PaymentMode mode = null;
		for (Integer i : settlementMap.keySet()) {
			amount = settlementMap.get(i);
			mode = paymentModes.get(i);
			left(rpad(mode.getName(), 20) + amount);
		}
		cut();
		return getSb();
	}

	@Override
	public String getFilepath() {
		return getBasePath() + "/" + table.getUnitId() + "/tableSettlements/TableSettlementReceipt-"
				+ table.getTableRequestId() + ".html";
	}

	public Map<Integer, BigDecimal> getSettlementMap() {
		return settlementMap;
	}

	public void setSettlementMap(Map<Integer, BigDecimal> settlementMap) {
		this.settlementMap = settlementMap;
	}

	public String getBasePath() {
		return basePath;
	}

	public void setBasePath(String basePath) {
		this.basePath = basePath;
	}

}
