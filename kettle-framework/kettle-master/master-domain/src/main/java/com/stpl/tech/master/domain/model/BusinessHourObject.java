package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BusinessHourObject implements Serializable {

    private static final long serialVersionUID = -2655594041668174721L;

    Integer outletId;
    List<UnitHours> oldBusinessHours;
    List<UnitHours> newBusinessHours;
    String event;

}
