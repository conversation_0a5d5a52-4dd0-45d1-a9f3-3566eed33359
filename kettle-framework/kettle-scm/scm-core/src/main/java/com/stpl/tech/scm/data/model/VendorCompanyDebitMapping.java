/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.stpl.tech.master.data.model.CompanyDetail;


@Entity
@Table(name = "VENDOR_COMPANY_DEBIT_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = { "VENDOR_ID", "COMPANY_ID" }))
public class VendorCompanyDebitMapping implements java.io.Serializable {

	
	private static final long serialVersionUID = 6056330838231520834L;
	private Integer vendorMappingId;
	private int companyId;
	private String companyName;
	private VendorDetailData detailData;
	private BigDecimal debitBalance;
	private Date lastUpdateTime;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "DEBIT_MAPPING_ID", unique = true, nullable = false)
	public Integer getVendorMappingId() {
		return vendorMappingId;
	}

	public void setVendorMappingId(Integer vendorMappingId) {
		this.vendorMappingId = vendorMappingId;
	}
	
	@Column(name = "COMPANY_ID", nullable = false)
	public int getCompanyId() {
		return companyId;
	}

	public void setCompanyId(int companyId) {
		this.companyId = companyId;
	}
	
	@Column(name = "COMPANY_NAME", nullable = false)
	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "VENDOR_ID", nullable = false)
	public VendorDetailData getDetailData() {
		return detailData;
	}

	public void setDetailData(VendorDetailData detailData) {
		this.detailData = detailData;
	}

	@Column(name = "DEBIT_BALANCE", nullable = false)
	public BigDecimal getDebitBalance() {
		return debitBalance;
	}

	public void setDebitBalance(BigDecimal debitBalance) {
		this.debitBalance = debitBalance;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = false, length = 19)
	public Date getLastUpdateTime() {
		return this.lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
