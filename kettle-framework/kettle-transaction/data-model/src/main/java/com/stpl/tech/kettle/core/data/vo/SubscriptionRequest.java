package com.stpl.tech.kettle.core.data.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.stpl.tech.kettle.core.SubscriptionEventType;

public class SubscriptionRequest {

	private SubscriptionEventType type;
	private String offerDescription;
	private Integer productId;
	private String dimension;
	private BigDecimal price;
	private Integer validityInDays;
	private Date startDate;
	private Date endDate;
	private Integer orderId;
	private Integer orderItemId;
	private Integer customerId;
	private Integer planId;
	private String frequencyStrategy;//TIME_BASED OR QAUNTITY_BASED
	private BigDecimal overAllFrequency;
	private BigDecimal frequencyLimit;

	public String getOfferDescription() {
		return offerDescription;
	}

	public void setOfferDescription(String offerDescription) {
		this.offerDescription = offerDescription;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}


	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public Integer getOrderItemId() {
		return orderItemId;
	}

	public void setOrderItemId(Integer orderItemId) {
		this.orderItemId = orderItemId;
	}

	public Integer getValidityInDays() {
		return validityInDays;
	}

	public void setValidityInDays(Integer validityInDays) {
		this.validityInDays = validityInDays;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public SubscriptionEventType getType() {
		return type;
	}

	public void setType(SubscriptionEventType type) {
		this.type = type;
	}

	public Integer getPlanId() {
		return planId;
	}

	public void setPlanId(Integer planId) {
		this.planId = planId;
	}

	public String getFrequencyStrategy() {
		return frequencyStrategy;
	}

	public void setFrequencyStrategy(String frequencyStrategy) {
		this.frequencyStrategy = frequencyStrategy;
	}

	public BigDecimal getOverAllFrequency() {
		return overAllFrequency;
	}

	public void setOverAllFrequency(BigDecimal overAllFrequency) {
		this.overAllFrequency = overAllFrequency;
	}

	public BigDecimal getFrequencyLimit() {
		return frequencyLimit;
	}

	public void setFrequencyLimit(BigDecimal frequencyLimit) {
		this.frequencyLimit = frequencyLimit;
	}
}