package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.domain.model.PriceProfileDomain;
import com.stpl.tech.master.domain.model.PriceProfileProductMappingDomain;
import com.stpl.tech.master.domain.model.PriceProfileProductMappingRequest;
import com.stpl.tech.master.domain.model.PriceProfileVersionsDomain;
import com.stpl.tech.master.domain.model.ProductPricingMappingBulkUploadResponse;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitPriceProfileMappingBulkUploadResponse;
import com.stpl.tech.master.domain.model.UnitPriceProfileMappingDomain;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

public interface PriceProfileManagementService {
    PriceProfileDomain addNewPriceProfile(PriceProfileDomain priceProfile , Integer createdBy) throws DataUpdationException;
    List<PriceProfileDomain> getAllPriceProfilesByStatus(String status);

    public Boolean togglePriceProfileStatus(PriceProfileDomain priceProfile , Integer updatedBy) throws DataUpdationException;

    public Boolean togglePriceProfileVersionStatus(PriceProfileVersionsDomain priceProfileVersion , Integer updatedBy) throws DataUpdationException;

    public  List<PriceProfileProductMappingDomain> getPriceProfileProductMappings(PriceProfileProductMappingRequest request) throws DataNotFoundException;

    public Boolean savePriceProfileProductMappings(List<PriceProfileProductMappingDomain> priceProfileProductMappingDomains
            , Integer updatedeBy);

    public List<UnitPriceProfileMappingDomain> getUnitPriceProfileMappings(UnitCategory unitCategory, String unitRegion  , Integer partnerId
            , Integer brandId, Boolean onlyActive);

    public List<UnitPriceProfileMappingDomain> saveUnitPriceProfileMappings(List<UnitPriceProfileMappingDomain> mappings ,
                                                                            Integer brandId , Integer partnerId , Integer updatedBy
            , AtomicInteger recordsUpdated , AtomicInteger recordsAdded);

    public View getUnitPriceProfileSheet(List<UnitPriceProfileMappingDomain> mappings);

    public View getProductPriceProfileSheet(List<PriceProfileProductMappingDomain> mappings);

    public ProductPricingMappingBulkUploadResponse parseAndCreateBulkProductPricingUpdateEvent(
            MultipartFile file, Integer updatedBy) throws IOException, DataNotFoundException;

    public PriceProfileDomain addNewPriceProfileVersion(Integer priceProfileId ,Integer clonePriceProfileId
            , Integer clonePriceProfileVersion,Integer createdBy);

    public void cloneUnitPriceProfileMappings(UnitDetail unit , Integer cloneUnitId , Integer updatedBy);

    public Boolean addNewPriceProfileVersions(Integer clonePriceProfileVersion,Integer createdBy);

    public Integer getMaxVersion();

    public UnitPriceProfileMappingBulkUploadResponse parseAndCreateUnitPriceProfileMappings(
            MultipartFile file , Integer updatedBy) throws IOException;

    public Boolean toggleBulkVersionStatus(Integer versionNo , String status , Integer updatedBy) throws DataUpdationException;


}
