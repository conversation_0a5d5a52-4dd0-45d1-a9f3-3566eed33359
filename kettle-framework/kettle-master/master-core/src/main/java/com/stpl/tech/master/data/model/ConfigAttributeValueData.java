/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "CONFIG_ATTRIBUTE_VALUE")
public class ConfigAttributeValueData implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 665766992448462876L;
	private Integer valueId;
	private ConfigAttributeDefinitionData attributeDefinition;
	private String attributeValue;
	private String applicationName;

	public ConfigAttributeValueData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ATTRIBUTE_VALUE_ID", unique = true, nullable = false)
	public Integer getValueId() {
		return valueId;
	}

	public void setValueId(Integer valueId) {
		this.valueId = valueId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ATTRIBUTE_DEF_ID", nullable = false)
	public ConfigAttributeDefinitionData getAttributeDefinition() {
		return attributeDefinition;
	}

	public void setAttributeDefinition(ConfigAttributeDefinitionData attributeDefinition) {
		this.attributeDefinition = attributeDefinition;
	}

	@Column(name = "ATTRIBUTE_VALUE", nullable = false, length = 500)
	public String getAttributeValue() {
		return attributeValue;
	}

	public void setAttributeValue(String attributeValue) {
		this.attributeValue = attributeValue;
	}

	@Column(name = "APPLICATION_NAME", nullable = false, length = 100)
	public String getApplicationName() {
		return applicationName;
	}

	public void setApplicationName(String applicationName) {
		this.applicationName = applicationName;
	}

}
