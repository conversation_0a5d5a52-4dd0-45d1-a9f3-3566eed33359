import React from "react";
import {connect} from "react-redux";
import qrcodel from "qr-code-with-logo";
import DesktopFooterLayout from "./DesktopFooterLayout";
import * as CustomerActions from "./../../actions/CustomerActions";
import * as UtilityActions from "./../../actions/UtilityActions";
import appUtil from "./../../AppUtil";
import trackUtils from "../../utils/TrackUtils";
import StorageUtils from "../../utils/StorageUtils";
import {browserHistory} from "react-router";
import * as CartManagementActions from "../../actions/CartManagementActions";
import DesktopBannerHeader from "./DesktopBannerHeader";
import * as OrderManagementActions from "../../actions/OrderManagementActions";
import IdleTimer from 'react-idle-timer';


@connect((store) => {
    return {
        selectedCity:store.localityReducer.selectedCity,
        unit:store.outletMenuReducer.unit,
        customer: store.customerReducer.customer,
        showContactSection: store.customerReducer.showContactSection,
        isLogin: store.customerReducer.isLogin,
        getName: store.customerReducer.getName,
        getEmail: store.customerReducer.getEmail,
        redirectTo: store.customerReducer.redirectTo,
        otpResendSeconds:store.customerReducer.otpResendSeconds,
        cart: store.cartManagementReducer.cart,
        errorSignUpMessage: store.customerReducer.errorSignUpMessage,
        giftCardOffer: store.paymentReducer.giftCardOffer
        //trueCallerQRString:store.customerReducer.trueCallerQRString,
    };
})
export default class DesktopLoginLayout extends React.Component {

    constructor() {
        super();
        this.state = {
            timeout:1000 * 120,
            userLoggedIn: false,
            isTimedOut: false
        };
        this.loginUser = this.loginUser.bind(this);
        this.verifyUser = this.verifyUser.bind(this);
        this.resendVerification = this.resendVerification.bind(this);
        var cart = StorageUtils.getUnitDetails();
        console.log('cart details from storage');
        console.log(cart);

        this.idleTimer = null;
        this.onAction = this._onAction.bind(this);
        this.onActive = this._onActive.bind(this);
        this.onIdle = this._onIdle.bind(this);
    }

    /*consolePrintCart(e) {
        e.preventDefault();
        console.log('in console print cart where cart is ::');
        console.log(this.prop.cart);
    }*/

    _onAction(e) {
        console.log('user did something', e)
        //this.setState({isTimedOut: false})
    }

    _onActive(e) {
        console.log('user is active', e);
        this.setState({isTimedOut: false})
    }

    _onIdle(e) {
        console.log('user is idle from login', e);
        const isTimedOut = this.state.isTimedOut;
        if (isTimedOut) {
            console.log('timed out idle from login');
            // navigate to cart menu after clearing cart and contact details
            this.props.dispatch(OrderManagementActions.startNewOrder());

        } else {
            this.setState({showModal: true});
            this.idleTimer.reset();
            this.setState({isTimedOut: true});
        }

    }

    loginUser(e) {
        e.preventDefault();
        var contact = document.getElementById("userContactInput").value;
        if (!appUtil.checkEmpty(contact) && appUtil.validContact(contact)) {
            this.props.dispatch(CustomerActions.lookupCustomer(contact, false, null, null));
        } else {
            this.props.dispatch(UtilityActions.showPopup("Please enter valid contact number!", "info"));
        }
    }

    verifyUser(e) {
        e.preventDefault();
        var otp = document.getElementById("otpInput").value;
        var name = null;
        var email = null;
        if (this.props.getName) {
            name = document.getElementById("nameInput").value;
        }
        /*if (this.props.getEmail) {
            email = document.getElementById("emailInput").value;
        }*/
        if (this.props.getName && appUtil.checkEmpty(name) ) {
            /*this.props.dispatch(UtilityActions.showPopup("Please enter name!", "info"));*/
            this.props.dispatch(CustomerActions.setErrorSignUp("Please enter name!"));
        }/* else if (this.props.getEmail && !appUtil.validEmail(email)) {
            /!*this.props.dispatch(UtilityActions.showPopup("Please enter valid email address!", "info"));*!/
            this.props.dispatch(CustomerActions.setErrorSignUp("Please enter valid email address!"));
        }*/ else if (appUtil.checkEmpty(otp)) {
            //this.props.dispatch(UtilityActions.showPopup("Please enter one time password received on your mobile!", "info"));
            this.props.dispatch(CustomerActions.setErrorSignUp("Please enter OTP received on your mobile!"));
        } else {
            if (this.props.isLogin) {
                this.props.dispatch(CustomerActions.loginCustomer(name, email, otp, this.props.getName, this.props.getEmail, this.props.customer.contact, this.props.redirectTo));
            } else {
                this.props.dispatch(CustomerActions.signUpCustomer(name, email, otp, this.props.customer.contact,
                    this.props.redirectTo, false, false, null, null));
            }
        }
    }

    goBack(clearCart) {
        StorageUtils.removeCustomerDetail();
        this.props.dispatch(CustomerActions.removeCustomerDetail());
        this.props.dispatch(CartManagementActions.removeCoupon(false));
        this.props.dispatch({type: "SET_COUPON", payload: null});
        if(clearCart == true) {
            this.props.dispatch(CartManagementActions.clearCart());
        }
        browserHistory.push("/menu");
    }

    resendVerification() {
        this.props.dispatch(CustomerActions.resendVerification(this.props.customer.contact));
    }

    componentWillMount() {
        window.scrollTo(0, 0);
        this.props.dispatch(CustomerActions.resetLogin());
        this.props.dispatch(CustomerActions.loadGiftCards(this.props));
        //this.props.dispatch(CustomerActions.generateTrucallerQRRequest());
    }

    editContact() {
        this.props.dispatch(CustomerActions.resetLogin());
    }

    componentDidMount() {
        if(document.getElementById("userContactInput")!=null){
            document.getElementById("userContactInput").focus();
        }
        trackUtils.trackPageView({page:"login",device:"desktop",custom:true});
        //var data = this.props.trueCallerQRString;
        // var canvas = document.getElementById('truecallerQr');
        // var qrOptions = StorageUtils.getQrConfigOptions();
        // //qrOptions = {qrOptions, canvas:canvas, content:data};
        // qrOptions.canvas = canvas;
        // //qrOptions.content = data;
        // console.log("data fro qr:::::::::::::::::::::::::::::::::::", qrOptions);
        // qrcodel.toCanvas(qrOptions);
    }

    componentDidUpdate() {
        if (!this.props.showContactSection) {
            if (this.props.getName) {
                document.getElementById("nameInput").focus();
                /*document.getElementById("nameInput").value = "";*/
            }
            // else if (this.props.getEmail) {
            //     document.getElementById("emailInput").focus();
            // }
            else {
                document.getElementById("otpInput").focus();
            }
        }
    }

    ifValidPhoneNumber(e) {

        e.preventDefault();
        var contact = document.getElementById("userContactInput").value;
        console.log(contact);
        if (!(contact.match(/^[0-9]*$/))) {
            console.log("Enter numbers only!")
        }
        if(contact.match(/^[6-9]\d{9}$/)){
            if (!appUtil.checkEmpty(contact) && appUtil.validContact(contact)) {
                this.props.dispatch(CustomerActions.lookupCustomer(contact, false, null, null));
            } else {
                this.props.dispatch(UtilityActions.showPopup("Please enter valid contact number!", "info"));
            }
        }
        if(contact.match(/^[0-5]\d{9}$/)){
            console.log("Invalid Phone number")
        }
    }

    render() {

        var resendString = "";
        var min = 0,sec=0;
        if(this.props.otpResendSeconds>0){
            min = parseInt(this.props.otpResendSeconds/60);
            sec = this.props.otpResendSeconds%60;
            min>0?resendString+= min+" min ":" ";
            sec>0?resendString+= sec+" sec":"";
        }

        let cartDetail = this.props.cart;
        let payableAmount = 0;
        if(cartDetail!=null && cartDetail.orderDetail.transactionDetail!=null && cartDetail.orderDetail.transactionDetail.paidAmount>=1) {
            payableAmount = Math.round(cartDetail.orderDetail.transactionDetail.paidAmount);
        }

        return (
            <div style={{backgroundColor: "#f4f4f4", height:"1920px"}}>
                <IdleTimer
                    ref={ref => { this.idleTimer = ref }}
                    element={document}
                    onActive={this.onActive}
                    onIdle={this.onIdle}
                    onAction={this.onAction}
                    debounce={250}
                    timeout={this.state.timeout} />
                <DesktopBannerHeader/>
                {/*<div class="paymentModeSection" style={{background: "none", textAlign: "center", minHeight: "auto", marginTop:"50px", marginBottom:"80px"}}>*/}
                    {/*<div class="payableAmount1">*/}
                        {/*<img style={{width: "51px", marginBottom: "-6px"}} src="../../../img/indian-ruppee-icon-white.png"/>*/}
                        {/*<span style={{fontSize: "60px", margin: "0 0 0 15px", fontWeight: "bold"}}>{payableAmount}</span>*/}
                    {/*</div>*/}
                {/*</div>*/}
                <div class="colouredHead">
                    <div style={{maxWidth:"1024px", margin:"0 auto", marginTop:"100px", textAlign:"center"}}>
                        {/*<div class="loginBlockLeft">*/}
                            {/*<div class="truecallerSignInHead">*/}
                                {/*One Tap Login <br/> via <br/>*/}
                                {/*<img src="../../../img/truecallerlogo.png" style={{width: "150px", marginBottom: "-4px"}}/>*/}
                            {/*</div>*/}
                            {/*<div class="truecallerSignInBlock">*/}
                                {/*<div style={{textAlign: "center"}}>*/}
                                    {/*<canvas id="truecallerQr" style={{marginBottom: "45px"}}></canvas>*/}
                                {/*</div>*/}
                            {/*</div>*/}
                        {/*</div>*/}
                        {/*<div class="loginDividerBlock">*/}
                            {/*<div class="loginDivider">OR</div>*/}
                        {/*</div>*/}
                        <div class="loginBlockRight" style={{width: "35%", height: "550px", marginBottom: "50px"}}>
                            <div class="loginBycontactHead">
                                Login via <br/> Contact No.
                            </div>
                            <div class="loginByContactForm">
                                <form name="lookupForm" action="#" class="loginBlockFormContainer"
                                      onSubmit={this.loginUser.bind(this)}>

                                    <div class="contactContainer fieldContainer">
                                        <input class="inputFont" id="userContactInput" type="tel" placeholder="Enter Mobile No." maxLength="10"
                                               autoComplete="off" autoFocus="" onKeyUp={this.ifValidPhoneNumber.bind(this)}/>
                                    </div>

                                    <div class="signUpSection">

                                        {!this.props.showContactSection ? (
                                            <p style={{fontSize: "12px", color:"#5e7e47"}}>
                                                *We have sent an OTP to entered mobile number, please fill the below details.
                                            </p>
                                        ) :
                                            (<p style={{fontSize: "10px"}}>
                                                *Additional Details for new Customer
                                            </p>)
                                        }

                                        {this.props.getName ? (
                                            <div>
                                                <div className="contactContainer fieldContainer">
                                                    <input class="inputFont" id="nameInput" style={{textTransform: "capitalize"}}
                                                           type="text"
                                                           placeholder="Enter Your Name" maxLength="70"
                                                           autoComplete="off"/>
                                                </div>

                                                {/*<div className="contactContainer fieldContainer">
                                                    <input id="emailInput" type="email"
                                                           placeholder="Enter Your Email Id"
                                                           maxLength="100"
                                                           autoComplete="off"/>
                                                </div>
                                                <p style={{fontSize: "12px", color: "#5e7e47"}}>
                                                    *You get a free desi chai on adding email.
                                                </p>*/}
                                            </div>

                                        ):null}
                                        <div className="contactContainer fieldContainer">
                                            <input class="inputFont" id="otpInput" type="number" placeholder="Enter One Time Password"
                                                   maxLength="10"
                                                   autoComplete="off"/>
                                        </div>

                                        {this.props.errorSignUpMessage!=null?(
                                            <div className="errorSignUpMessage">
                                                {this.props.errorSignUpMessage}
                                            </div>
                                            ):null}

                                        <div class="loginButton"
                                             onClick={this.verifyUser.bind(this)}>
                                            Login/Sign Up
                                        </div>
                                        <div class={this.props.showContactSection ? "signUpSectionOverlay" : ""}></div>
                                    </div>

                                </form>
                            </div>
                        </div>
                        {/*<img src="../../../img/backgrounds/login_screen_bg-crop.png" />*/}
                    </div>
                    <div style={{width:"70%", margin:"0 auto"}}>
                        <div class="loginBackButton" onClick={this.goBack.bind(this)} >Edit Cart</div>
                        <div class="loginBackButton" onClick={this.goBack.bind(this, true)} style={{float:"right"}}>Start New Order</div>
                    </div>

                </div>
                <DesktopFooterLayout/>
            </div>
        )
    }
}
