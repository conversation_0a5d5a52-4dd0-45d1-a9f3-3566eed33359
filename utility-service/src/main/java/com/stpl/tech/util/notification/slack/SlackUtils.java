package com.stpl.tech.util.notification.slack;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gson.Gson;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;

public class SlackUtils {

    public static String INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR";
    private static final Logger LOG = LoggerFactory.getLogger(SlackUtils.class);

    public static void slackIt(EnvType type, String user, Exception ex, String message) {
        try {
            String error = getFormattedMsgWithMessage(message, ex);
            LOG.error("{}\n{}", INTERNAL_SERVER_ERROR, error, ex);
            Slack.getInstance().send(type, user, error);

        } catch (Exception e) {
            LOG.error("Slack is Failing", e);
        }
    }

    public static void slackIt(EnvType type, String user, Exception ex) {
        try {
            String error = getFormattedMsgWithMessage(null, ex);
            LOG.error("{}\n{}", INTERNAL_SERVER_ERROR, error, ex);
            Slack.getInstance().send(type, user, error);
        } catch (Exception e) {
            LOG.error("Slack is Failing", e);
        }
    }

    public static void slackIt(String url, EnvType type, String user, Exception ex) {
        try {
            String error = getFormattedMsg(null, url, ex);
            Slack.getInstance().send(type, user, error);
            LOG.error("{}\n{}", INTERNAL_SERVER_ERROR, error, ex);
        } catch (Exception e) {
            LOG.error("Slack is Failing", e);
        }
    }

    public static String slackIt(String userAgent, String url, EnvType envType, String user, Exception ex) {
        try {
            String error = getFormattedMsg(userAgent, url, ex);
//            SlackNotificationService.getInstance().send(envType, user, error);
            LOG.error("{}\n{}", INTERNAL_SERVER_ERROR, error, ex);
            return error;
        } catch (Exception e) {
            LOG.error("Slack is Failing", e);
        }
        return null;
    }

    private static String getFormattedMsg(String userAgent, String url, Exception ex) {
        StringBuilder sb = new StringBuilder();
        sb.append(SlackConstants.EMOJI_POOP_X_4 + SlackConstants.NEW_LINE);
        sb.append("*Error Token:* " + AppUtils.generateTimeString(AppConstants.ETKN) + SlackConstants.NEW_LINE);
        sb.append("*Error Notification:* " + ex.getMessage() + SlackConstants.NEW_LINE);
        sb.append("*Error Generation Timestamp:* " + AppUtils.getCurrentTimeISTString() + SlackConstants.NEW_LINE);
        sb.append("*Request URL:* " + url + SlackConstants.NEW_LINE);
        if (userAgent != null) {
            sb.append("*User Agent:* " + userAgent + SlackConstants.NEW_LINE);
        }
        int exceptionLength = org.apache.commons.lang.exception.ExceptionUtils.getFullStackTrace(ex).length();
        sb.append("*Stacktrace:* "
            + org.apache.commons.lang.exception.ExceptionUtils.getFullStackTrace(ex).substring(0, exceptionLength > 1000 ? 1000 : exceptionLength));
        return sb.toString();
    }

    private static String getFormattedMsgWithMessage(String message, Exception ex) {
        StringBuilder sb = new StringBuilder();
        sb.append(SlackConstants.EMOJI_POOP_X_4 + SlackConstants.NEW_LINE);
        sb.append("*Error Token:* " + AppUtils.generateTimeString(AppConstants.ETKN) + SlackConstants.NEW_LINE);
        if (message != null) {
            sb.append("*Error Message:* " + AppUtils.generateTimeString(AppConstants.ETKN) + SlackConstants.NEW_LINE);
        }
        sb.append("*Error Notification:* " + ex.getMessage() + SlackConstants.NEW_LINE);
        sb.append("*Error Generation Timestamp:* " + AppUtils.getCurrentTimeISTString() + SlackConstants.NEW_LINE);
        int exceptionLength = org.apache.commons.lang.exception.ExceptionUtils.getFullStackTrace(ex).length();
        sb.append("*Stacktrace:* "
            + org.apache.commons.lang.exception.ExceptionUtils.getFullStackTrace(ex).substring(0, exceptionLength > 1000 ? 1000 : exceptionLength));
        return sb.toString();
    }

    public static <T, E> T clone(E object, Class<T> clazz) {
        Gson gson = new Gson();
        String str = gson.toJson(object);
        return gson.fromJson(str, clazz);
    }

}
