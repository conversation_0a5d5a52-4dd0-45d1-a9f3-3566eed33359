package com.stpl.tech.kettle.webengage.domain.model;

/**
 * Created by Chaayos on 09-05-2017.
 */
public class WebengageOrder {

    //default attributes
    private String orderMode;
    private String city;
    private String locality;
    private String outlet;
    private String orderSrc;
    private String host;
    //private String customerId;
    //private String acquisitionSrc;
    //private String empId;
    //private String deviceId;
    //private String criteria;

    private String orderSource;
    private Float totalAmount;
    private Float taxableAmount;
    private Float paidAmount;
    private Float totalDiscount;
    private Integer unitId;
    private String unitName;
    private String offerCode;
    private String containsSignupOffer;
    private String orderItems;
    private Boolean giftCardOnly;
    private String generatedOrderId;
    private String externalOrderId;
    private String paymentMode;
    private Integer pointsRedeemed;
    //private List<WebengageOrderMatadata> metadata;
    protected String settlements;
    protected String billingServerTime;

    public String getOrderMode() {
        return orderMode;
    }

    public void setOrderMode(String orderMode) {
        this.orderMode = orderMode;
    }

    /*public String getAcquisitionSrc() {
        return acquisitionSrc;
    }

    public void setAcquisitionSrc(String acquisitionSrc) {
        this.acquisitionSrc = acquisitionSrc;
    }*/

    public String getOrderSrc() {
        return orderSrc;
    }

    public void setOrderSrc(String orderSrc) {
        this.orderSrc = orderSrc;
    }

    /*public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }*/

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getLocality() {
        return locality;
    }

    public void setLocality(String locality) {
        this.locality = locality;
    }

    public String getOutlet() {
        return outlet;
    }

    public void setOutlet(String outlet) {
        this.outlet = outlet;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public Float getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Float totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Float getTaxableAmount() {
        return taxableAmount;
    }

    public void setTaxableAmount(Float taxableAmount) {
        this.taxableAmount = taxableAmount;
    }

    public Float getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Float paidAmount) {
        this.paidAmount = paidAmount;
    }

    public Float getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(Float totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getOfferCode() {
        return offerCode;
    }

    public void setOfferCode(String offerCode) {
        this.offerCode = offerCode;
    }

    public String getContainsSignupOffer() {
        return containsSignupOffer;
    }

    public void setContainsSignupOffer(String containsSignupOffer) {
        this.containsSignupOffer = containsSignupOffer;
    }

    public String getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(String orderItems) {
        this.orderItems = orderItems;
    }

    public Boolean getGiftCardOnly() {
        return giftCardOnly;
    }

    public void setGiftCardOnly(Boolean giftCardOnly) {
        this.giftCardOnly = giftCardOnly;
    }

    public String getGeneratedOrderId() {
        return generatedOrderId;
    }

    public void setGeneratedOrderId(String generatedOrderId) {
        this.generatedOrderId = generatedOrderId;
    }

    public String getExternalOrderId() {
        return externalOrderId;
    }

    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    public Integer getPointsRedeemed() {
        return pointsRedeemed;
    }

    public void setPointsRedeemed(Integer pointsRedeemed) {
        this.pointsRedeemed = pointsRedeemed;
    }

    /*public List<WebengageOrderMatadata> getMetadata() {
        return metadata;
    }

    public void setMetadata(List<WebengageOrderMatadata> metadata) {
        this.metadata = metadata;
    }*/

    public String getSettlements() {
        return settlements;
    }

    public void setSettlements(String settlements) {
        this.settlements = settlements;
    }

    /*public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }*/

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    /*public String getCriteria() {
        return criteria;
    }

    public void setCriteria(String criteria) {
        this.criteria = criteria;
    }*/

    public String getBillingServerTime() {
        return billingServerTime;
    }

    public void setBillingServerTime(String billingServerTime) {
        this.billingServerTime = billingServerTime;
    }
}
