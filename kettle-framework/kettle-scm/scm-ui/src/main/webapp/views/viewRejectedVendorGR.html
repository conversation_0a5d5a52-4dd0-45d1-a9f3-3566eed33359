<style>
    #viewGRList .collapsible-header.inverse{
        background-color: #CDDC39;
        color: white;
    }
    #viewGRList .collapsible-header{
        color: #654848;
        background-color: white;
        border: 1px solid #26a69a;
    }
    .scrollabe-table {
        display: block !important;
        overflow-x: auto !important;
        white-space: nowrap !important;
    }
    .scrollabe-table thead tr th{
        min-width: 150px;
    }
    .greyBackground {
        background-color: lightgrey;
    }
</style>
<div class="row" data-ng-init="init()">
    <div style="width=100%" id="printHide">
        <!--<svg class="barcode" jsbarcode-format="CODE128" jsbarcode-value="abc123" jsbarcode-textmargin="0" jsbarcode-fontoptions="bold">-->
        <!--</svg>-->
        <div class="searchingCard col s12">
            <div class="row white z-depth-3 custom-listing-li" style="width: 100%;">
                <div class="row">
                    <div class="col s12">
                        <h4>View Vendor Receivings</h4>
                    </div>
                </div>
                <div class="row">
                    <div class="col s2">
                        <label>Select Start date</label>
                        <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd" />
                    </div>
                    <div class="col s2">
                        <label>Select End date</label>
                        <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd" />
                    </div>
                    <div class="col s2">
                        <label>Good Received Id</label>
                        <input type="number" placeholder="GR ID" name="grId" id="grId" ng-model="grId"/>
                    </div>
                    <div class="col s2">
                        <label>Select Vendor</label>
                        <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}" ng-options="vendor as vendor.entityName for vendor in vendors"
                                data-ng-change="selectVendor(vendorSelected)" data-ng-model="vendorSelected"></select>
                    </div>
                    <div class="col s2">
                        <button class="btn margin-top-20" data-ng-click="getGRs()">Find</button>
                    </div>
                </div>
                <hr>
                <div class="row" style="padding:30px;color:gray;text-align: center;"
                     data-ng-show="grsList==null || grsList.length==0">
                    No Vendor Receivings found for the selected criteria
                </div>
                <div class="row" data-ng-show="grs!=null && grs.length>0">
                    <ul id="viewGRList" class="col s12" data-collapsible="accordion" watch>
                        <li class="row" data-ng-repeat="gr in grs track by gr.id">
                            <div class="col s10 collapsible-header waves-effect waves-light lighten-5"
                                 data-ng-class="{'inverse':gr.id==grId}">
                                <div class="left">
                                    GR No.: <b>{{gr.id}}</b> for {{gr.generatedForVendor.name}}
                                    [{{gr.dispatchLocation.city}}]
                                    <span class="chip" data-ng-if="gr.invalid">INVALID</span>
                                </div>
                                <div class="right">
                                    <span class="chip grey white-text">{{gr.receiptType}}: {{gr.receiptNumber}}</span>
                                    <span data-ng-class="{'chip green white-text':gr.toBePaid == true, 'chip red white-text':gr.toBePaid != true}">To be paid: {{gr.toBePaid?'Yes':'No'}}</span>
                                    <span class="chip grey white-text">
                                    {{gr.generationTime | date:'dd/MM/yyyy @ h:mma'}}
                                </span>
                                    <span class="chip grey white-text">GR Status: {{gr.status}}</span>
                                    <i class="fa fa-caret-down right"></i>
                                </div>
                            </div>
                            <div class="collapsible-body">
                                <table class="bordered striped">
                                    <thead>
                                    <tr>
                                        <th class="center-align">SKU</th>
                                        <th class="center-align">SKU ID</th>
                                        <th class="center-align">POI ID</th>
                                        <th class="center-align">GRI ID</th>
                                        <th class="center-align">Packaging Qty(PKG)</th>
                                        <th class="center-align">Accepted(PKG)</th>
                                        <th class="center-align">RejectedQty(PKG)</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr data-ng-repeat="item in gr.vendorPoGRItems track by $index">
                                        <td class="center-align">{{item.skuName}}</td>
                                        <td class="center-align">{{item.skuId}}</td>
                                        <td class="center-align">{{item.purchaseOrderId}}</td>
                                        <td class="center-align">{{item.grItemId}}</td>
                                        <td class="center-align">{{item.actualPackagingQty}}</td>
                                        <td class="center-align">{{item.grItemAcceptedQuantity}}</td>
                                        <td class="center-align">{{item.rejectedQty}}</td>

                                    </tr>
                                    </tbody>
                                </table>
                            </div>

                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>