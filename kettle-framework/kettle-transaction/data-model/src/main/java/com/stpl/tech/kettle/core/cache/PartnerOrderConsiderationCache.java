/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.cache;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.core.data.vo.PartnerDataWithOrderConsideration;

@Repository
public class PartnerOrderConsiderationCache {

	public PartnerOrderConsiderationCache() {

	}

	private Map<Integer, Map<Integer, PartnerDataWithOrderConsideration>> partnerOrderConsideration = new HashMap<>();
	private boolean isCacheLoaded = false;

	public void clearCache() {
		partnerOrderConsideration.clear();
	}

	public Map<Integer, Map<Integer, PartnerDataWithOrderConsideration>> getMissionGaramChai() {
		return partnerOrderConsideration;
	}

	public Map<Integer, PartnerDataWithOrderConsideration> getPartnerOrderForUnit(Integer unitId) {
		return partnerOrderConsideration.get(unitId);
	}

	public boolean isCacheLoaded() {
		return isCacheLoaded;
	}

	public void setCacheLoaded(boolean cacheLoaded) {
		isCacheLoaded = cacheLoaded;
	}

	public PartnerDataWithOrderConsideration getPartnerOrderForPartnerOnUnit(Integer unitId, Integer partnerId) {
		return partnerOrderConsideration.get(unitId).get(partnerId);
	}
}
