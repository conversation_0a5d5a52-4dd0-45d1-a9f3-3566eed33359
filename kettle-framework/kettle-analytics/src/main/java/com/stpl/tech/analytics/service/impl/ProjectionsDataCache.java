/**
 * 
 */
package com.stpl.tech.analytics.service.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.stpl.tech.analytics.model.SalesSplitData;
import com.stpl.tech.analytics.model.UnitData;
import com.stpl.tech.util.AppUtils;

/**
 * <AUTHOR>
 *
 */
@Service
public class ProjectionsDataCache {

	private Map<Integer, UnitData> salesProjection = new HashMap<>();

	private SalesSplitData aggregatedProjections;
	
	private boolean loaded;

	public Map<Integer, UnitData> getSalesProjection() {
		return salesProjection;
	}

	public void setSalesProjection(Map<Integer, UnitData> salesProjection) {
		this.salesProjection = salesProjection;
	}

	public SalesSplitData getAggregatedProjections() {
		return aggregatedProjections;
	}

	public void setAggregatedProjections(SalesSplitData aggregatedProjections) {
		this.aggregatedProjections = aggregatedProjections;
	}

	/**
	 * @param targets
	 */
	public void addProjections(List<UnitData> targets) {
		aggregatedProjections = new SalesSplitData();
		loaded = false;
		for (UnitData data : targets) {
			salesProjection.put(data.getUnitId(), data);
			aggregatedProjections.getDineIn().setSales(
					AppUtils.add(aggregatedProjections.getDineIn().getSales(), data.getSales().getDineIn().getSales()));
			aggregatedProjections.getDelivery().setSales(AppUtils.add(aggregatedProjections.getDelivery().getSales(),
					data.getSales().getDelivery().getSales()));
			aggregatedProjections.getTotal().setSales(
					AppUtils.add(aggregatedProjections.getTotal().getSales(), data.getSales().getTotal().getSales()));
			aggregatedProjections.getDineIn()
					.setTicket(aggregatedProjections.getDineIn().getTicket() + data.getSales().getDineIn().getTicket());
			aggregatedProjections.getDelivery().setTicket(
					aggregatedProjections.getDelivery().getTicket() + data.getSales().getDelivery().getTicket());
			aggregatedProjections.getTotal()
					.setTicket(aggregatedProjections.getTotal().getTicket() + data.getSales().getTotal().getTicket());

			aggregatedProjections.getTotal().setApc(AppUtils.divide(aggregatedProjections.getTotal().getSales(),
					new BigDecimal(aggregatedProjections.getTotal().getTicket())));
			aggregatedProjections.getDelivery().setApc(AppUtils.divide(aggregatedProjections.getDelivery().getSales(),
					new BigDecimal(aggregatedProjections.getDelivery().getTicket())));
			aggregatedProjections.getDineIn().setApc(AppUtils.divide(aggregatedProjections.getDineIn().getSales(),
					new BigDecimal(aggregatedProjections.getDineIn().getTicket())));
		}
		loaded = true;
	}

	public boolean isLoaded() {
		return loaded;
	}

	public void setLoaded(boolean loaded) {
		this.loaded = loaded;
	}

	
}
