<svg width="40" height="41" viewBox="0 0 40 41" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M30 7H36V13" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M35 8L24.5 18.5L19.5 13.5L5.5 24.5" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5 28L10 24V36H5V28Z" fill="url(#paint0_linear_11102_15742)"/>
<path d="M11 23L16 19V36H11V23Z" fill="url(#paint1_linear_11102_15742)"/>
<path d="M17 18L19.5 16L22 19V36H17V18Z" fill="url(#paint2_linear_11102_15742)"/>
<path d="M23 19.9996L24.5 21.5L28 18V35.9996H23V19.9996Z" fill="url(#paint3_linear_11102_15742)"/>
<path d="M29 17.5L34 12.5V18V35.9996H29V17.5Z" fill="url(#paint4_linear_11102_15742)"/>
<defs>
<linearGradient id="paint0_linear_11102_15742" x1="7.5" y1="24" x2="7.5" y2="36" gradientUnits="userSpaceOnUse">
<stop stop-color="#61DE2B"/>
<stop offset="0.463542" stop-color="#32A301"/>
<stop offset="0.979167" stop-color="#026728"/>
</linearGradient>
<linearGradient id="paint1_linear_11102_15742" x1="13.5" y1="19" x2="13.5" y2="36" gradientUnits="userSpaceOnUse">
<stop stop-color="#61DE2B"/>
<stop offset="0.463542" stop-color="#32A301"/>
<stop offset="0.979167" stop-color="#026728"/>
</linearGradient>
<linearGradient id="paint2_linear_11102_15742" x1="19.5" y1="16" x2="19.5" y2="36" gradientUnits="userSpaceOnUse">
<stop stop-color="#61DE2B"/>
<stop offset="0.463542" stop-color="#32A301"/>
<stop offset="0.979167" stop-color="#026728"/>
</linearGradient>
<linearGradient id="paint3_linear_11102_15742" x1="25.5" y1="18" x2="25.5" y2="35.9996" gradientUnits="userSpaceOnUse">
<stop stop-color="#61DE2B"/>
<stop offset="0.463542" stop-color="#32A301"/>
<stop offset="0.979167" stop-color="#026728"/>
</linearGradient>
<linearGradient id="paint4_linear_11102_15742" x1="31.5" y1="12.5" x2="31.5" y2="35.9996" gradientUnits="userSpaceOnUse">
<stop stop-color="#61DE2B"/>
<stop offset="0.463542" stop-color="#32A301"/>
<stop offset="0.979167" stop-color="#026728"/>
</linearGradient>
</defs>
</svg>
