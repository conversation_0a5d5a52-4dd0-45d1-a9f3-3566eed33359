/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.dao.impl;

import java.util.List;

import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.service.ReportingService;
import com.stpl.tech.kettle.domain.model.ExpenseUpdateEvent;
import com.stpl.tech.kettle.domain.model.UnitExpense;

public class ExpenseReportDataProvider {

	private ReportingService reportingService;
	private MetadataCache cache;
	private List<UnitExpense> expenseList;

	public ExpenseReportDataProvider(MetadataCache cache, ReportingService reportingService) {
		this.reportingService = reportingService;
		this.cache = cache;
	}

	public void process(ExpenseUpdateEvent event) {
		expenseList = reportingService.getUnitExpensesForEvent(event);
	}

	public ReportingService getReportingService() {
		return reportingService;
	}

	public void setReportingService(ReportingService reportingService) {
		this.reportingService = reportingService;
	}

	public MetadataCache getCache() {
		return cache;
	}

	public void setCache(MetadataCache cache) {
		this.cache = cache;
	}

	public List<UnitExpense> getExpenseList() {
		return expenseList;
	}

	public void setExpenseList(List<UnitExpense> expenseList) {
		this.expenseList = expenseList;
	}

}
