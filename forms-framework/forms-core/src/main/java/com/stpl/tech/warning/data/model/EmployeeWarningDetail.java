package com.stpl.tech.warning.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "EMPLOYEE_WARNING_DETAIL")
public class EmployeeWarningDetail {

	private Integer warningId;
	private Integer pqscAuditId;
	private Integer unitId;
	private Integer areaMangerId;
	private Integer managerOnDuty;
	private String gulityPersonName;
	private Integer gulityPersonId;
	private String gulityPersonDesg;
	private String impactType;
	private String warningStage;
	private String warningStatus;
	private Date creationTime;
	private Integer latestStatusId;
	private String hrDGMConflict;
	private Integer warningLetterId;
	private Date dateOfIncidence;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "WARNING_ID")
	public Integer getWarningId() {
		return warningId;
	}

	public void setWarningId(Integer warningId) {
		this.warningId = warningId;
	}

	@Column(name = "PQSC_AUDIT_ID", nullable = true)
	public Integer getPqscAuditId() {
		return pqscAuditId;
	}

	public void setPqscAuditId(Integer pqscAuditId) {
		this.pqscAuditId = pqscAuditId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public Integer getUnitId() {
		return unitId;
	}
	
	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}
	
	@Column(name = "AREA_MANAGER_ID", nullable = false)
	public Integer getAreaMangerId() {
		return areaMangerId;
	}

	public void setAreaMangerId(Integer areaMangerId) {
		this.areaMangerId = areaMangerId;
	}

	@Column(name = "MANAGER_ON_DUTY", nullable = true)
	public Integer getManagerOnDuty() {
		return managerOnDuty;
	}

	public void setManagerOnDuty(Integer managerOnDuty) {
		this.managerOnDuty = managerOnDuty;
	}

	@Column(name = "GUILTY_PERSON_NAME", nullable = false)
	public String getGulityPersonName() {
		return gulityPersonName;
	}

	public void setGulityPersonName(String gulityPersonName) {
		this.gulityPersonName = gulityPersonName;
	}

	@Column(name = "GUILTY_PERSON_ID", nullable = false)
	public Integer getGulityPersonId() {
		return gulityPersonId;
	}

	public void setGulityPersonId(Integer gulityPersonId) {
		this.gulityPersonId = gulityPersonId;
	}

	@Column(name = "GUILTY_PERSON_DESIGNATION", nullable = true)
	public String getGulityPersonDesg() {
		return gulityPersonDesg;
	}

	public void setGulityPersonDesg(String gulityPersonDesg) {
		this.gulityPersonDesg = gulityPersonDesg;
	}

	@Column(name = "IMPACT_TYPE", nullable = false)
	public String getImpactType() {
		return impactType;
	}

	public void setImpactType(String impactType) {
		this.impactType = impactType;
	}

	@Column(name = "WARNING_STAGE", nullable = false)
	public String getWarningStage() {
		return warningStage;
	}

	public void setWarningStage(String warningStage) {
		this.warningStage = warningStage;
	}

	@Column(name = "WARNING_STATUS", nullable = false)
	public String getWarningStatus() {
		return warningStatus;
	}

	public void setWarningStatus(String warningStatus) {
		this.warningStatus = warningStatus;
	}

	@Column(name = "CREATION_TIME", nullable = false)
	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	@Column(name = "LATEST_STATUS_ID", nullable = true)
	public Integer getLatestStatusId() {
		return latestStatusId;
	}

	public void setLatestStatusId(Integer latestStatusId) {
		this.latestStatusId = latestStatusId;
	}

	@Column(name = "HR_DGM_CONFLICT", nullable = true, length = 1)
	public String getHrDGMConflict() {
		return hrDGMConflict;
	}

	public void setHrDGMConflict(String hrDGMConflict) {
		this.hrDGMConflict = hrDGMConflict;
	}

	@Column(name = "WARNING_LETTER_REPORT_ID", nullable = true)
	public Integer getWarningLetterId() {
		return warningLetterId;
	}

	public void setWarningLetterId(Integer warningLetterId) {
		this.warningLetterId = warningLetterId;
	}

	@Column(name = "DATE_OF_INCIDENCE", nullable = false)
	public Date getDateOfIncidence() {
		return dateOfIncidence;
	}

	public void setDateOfIncidence(Date dateOfIncidence) {
		this.dateOfIncidence = dateOfIncidence;
	}
	
}
