/*! angular-flash - v1.0.0 - 2015-03-19
* https://github.com/sachinchoolur/angular-flash
* Copyright (c) 2015 Sachin; Licensed MIT */
!function(){"use strict";var a=angular.module("flash",[]);a.run(["$rootScope",function(a){a.flash={},a.flash.text="",a.flash.type="",a.flash.timeout=5e3,a.hasFlash=!1}]),a.directive("dynamic",["$compile",function(a){return{restrict:"A",replace:!0,link:function(b,c,d){b.$watch(d.dynamic,function(d){c.html(d),a(c.contents())(b)})}}}]),a.directive("closeFlash",["$compile","Flash",function(a,b){return{link:function(a,c){c.on("click",function(){b.dismiss()})}}}]),a.directive("flashMessage",["$compile","$rootScope",function(a,b){return{restrict:"A",template:'<div role="alert" ng-show="hasFlash" class="alert {{flash.addClass}} alert-{{flash.type}} alert-dismissible ng-hide alertIn alertOut "> <span dynamic="flash.text"></span> <button type="button" class="close" close-flash><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button> </div>',link:function(a,c,d){b.flash.timeout=parseInt(d.flashMessage,10)}}}]),a.factory("Flash",["$rootScope","$timeout",function(a,b){var c,d={};return d.create=function(d,e,f){var g=this;b.cancel(c),a.flash.type=d,a.flash.text=e,a.flash.addClass=f,b(function(){a.hasFlash=!0},100),c=b(function(){g.dismiss()},a.flash.timeout)},d.pause=function(){b.cancel(c)},d.dismiss=function(){b.cancel(c),b(function(){a.hasFlash=!1})},d}])}();