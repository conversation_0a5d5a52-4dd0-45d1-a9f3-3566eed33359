package com.stpl.tech.scm.domain.model;



import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class StockTakeInitResponse {

    protected List<StockEventDefinition> stockEventDefinition;
    protected List<String> subTypeList;
    protected Map<String, String> stockTakeStatusSubCategoryMap;
}
