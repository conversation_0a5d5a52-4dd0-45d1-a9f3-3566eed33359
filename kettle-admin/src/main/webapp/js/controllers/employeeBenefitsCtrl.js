adminapp.controller("employeeBenefitsCtrl", function ($scope, $window, $http, AppUtil, $rootScope){
    $scope.init=function (){
        $scope.getEmployeeBenefit();
        $scope.listData=[];
        $scope.deactivateEmpData=[];
        $scope.isBulkDeactivate=0;
    }
    $scope.updateAll = function (){
        if($scope.checkBoxModal.checkAll === true){
            $scope.isBulkDeactivate=$scope.employeeBenefits.length;
            for(var i=0;i < $scope.employeeBenefits.length; i++){
                $scope.employeeBenefits[i].isChecked = true;
            }
        }
        if($scope.checkBoxModal.checkAll === false){
            $scope.isBulkDeactivate=0;
            for(var i=0;i < $scope.employeeBenefits.length; i++){
               $scope.employeeBenefits[i].isChecked =false;

            }
        }

    };
    $scope.getEmployeeBenefit=function (){
        $http({
            method:'GET',
            url: AppUtil.restUrls.customerOfferManagement.isEmpApplicable
        }).then(function success(response) {
            if (response.status == -1) {
                alert("Incorrect Data")
            } $scope.employeeBenefits = response.data;
            for(var index =0 ;index<$scope.employeeBenefits.length;index++){
                $scope.employeeBenefits[index].isChecked=false;
            }
            console.log($scope.employeeBenefits);
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    $scope.deactivateEmployeeBenefits = function (employee){
        var data={
            "customerId" : $scope.id,
            "name" : $scope.name,
            "email":$scope.email,
            "contactNumber":$scope.contact
        }
        $scope.listData.push(employee.id);
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.customerOfferManagement.deactivateEMPBenefits,
            data: $scope.listData
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status === 200 && response.data) {
                alert("Employee Deactivated Successfully");
                window.location.reload();
            }
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
            alert("Unable to deactivate employee");
        });
    }
    $scope.deactivateEmployeeBenefitsinBulk = function (employeeBenefits){
         $scope.dataList=[];
         for(var i=0;i < $scope.employeeBenefits.length;i++){
             if($scope.employeeBenefits[i].isChecked === true){
                 $scope.dataList.push($scope.employeeBenefits[i].id);
             }
         }
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.customerOfferManagement.deactivateEMPBenefits,
            data: $scope.dataList
        }).then(function success(response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status === 200 && response.data) {
                alert("Employee Deactivated Successfully");
                window.location.reload();
            }
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.log("error:" + response);
            alert("Unable to deactivate employee");
        });

    }
    $scope.checkBoxIndex = function (index){
        if($scope.employeeBenefits[index].isChecked === true){
            $scope.isBulkDeactivate+=1;
        }else{
            $scope.isBulkDeactivate-=1;
        }
    }

})