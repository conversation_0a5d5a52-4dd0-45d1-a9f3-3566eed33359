package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.service.FavChaiCustomizationManagementService;
import com.stpl.tech.kettle.data.dao.FavChaiCustomizationDao;
import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiRequest;
import com.stpl.tech.kettle.domain.model.SelectedOrderItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FavChaiCustomizationManagementServiceImpl implements FavChaiCustomizationManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(FavChaiCustomizationManagementServiceImpl.class);

    @Autowired
    private FavChaiCustomizationDao favChaiCustomizationDao;

    @Override
    public boolean saveFavChaiCustomizationDetail(Integer customizationId, int productId, SelectedOrderItem orderItemDetails, int customerId, CustomerFavChaiMapping latestCustomerFavChaiMapping, SaveCustomerFavChaiRequest customerFavChaiRequest) {
        boolean isFavChaiSavedOrUpdated = false;
        try{
            favChaiCustomizationDao.saveFavChaiCustomizationDetail(orderItemDetails, latestCustomerFavChaiMapping,customerFavChaiRequest);
            isFavChaiSavedOrUpdated= true;
        }catch(Exception e ){
            LOG.info("Exception while persisting customization Detail for customizationId ::{} and customerId :::{} and error is ::::{}", customizationId, customerId, e);
        }
        return isFavChaiSavedOrUpdated;
    }
}
