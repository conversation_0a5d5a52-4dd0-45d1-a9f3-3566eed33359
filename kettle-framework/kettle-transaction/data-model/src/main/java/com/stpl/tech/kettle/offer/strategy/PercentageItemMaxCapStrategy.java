package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.util.AppConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j

public class PercentageItemMaxCapStrategy extends PercentageItemStrategy implements OfferActionStrategy {

    @Autowired
    AbstractItemStrategy abstractItemStrategy;

    @Override
    public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, MasterDataCache cache,
                                    Map<String, OrderItem> foundItems) throws OfferValidationException {
        Optional<BigDecimal> maxCappedDiscount = Optional.ofNullable(coupon.getOffer().getMaxDiscountAmount());
        int offerValue = coupon.getOffer().getOfferValue();
        Map<Integer, BigDecimal> productDiscountMap = new HashMap<>();
        AtomicLong calculatedDiscount = new AtomicLong(0);
        AtomicLong totalAmount = new AtomicLong(0);
        Set<Integer> categoryIds = new HashSet<>();
        Set<Integer> subCategoryIds = new HashSet<>();
        Set<String> productIds = new HashSet<>();
        if (maxCappedDiscount.isPresent()) {
            List<OrderItem> orderItems = offerOrder.getOrder().getOrders();
            List<IdCodeName> offerMappings = coupon.getOffer().getMetaDataMappings();
            Collections.sort(orderItems, Comparator.comparing(OrderItem::getTotalAmount));
            if (Objects.nonNull(offerMappings)) {
                for(IdCodeName idCodeName : offerMappings) {
                    if(idCodeName.getName().equalsIgnoreCase("PRODUCT_CATEGORY")){
                        categoryIds.add(Integer.valueOf(idCodeName.getCode()));
                    }
                    else if(idCodeName.getName().equalsIgnoreCase("PRODUCT_SUB_CATEGORY")){
                        subCategoryIds.add(Integer.valueOf(idCodeName.getCode()));
                    } else if (!CollectionUtils.isEmpty(coupon.getMappings().get("PRODUCT"))) {
                        Set<CouponMapping> couponMappings = coupon.getMappings().get("PRODUCT");
                        for (CouponMapping couponMapping : couponMappings) {
                            if (AppConstants.ACTIVE.equalsIgnoreCase(couponMapping.getStatus())) {
                                productIds.add(couponMapping.getValue() + "_" + couponMapping.getDimension().replaceAll(" ", ""));
                            }
                        }
                    }
                }
            }
            for (OrderItem orderItem : orderItems) {
                Product product = cache.getProduct(orderItem.getProductId());
                if (Objects.nonNull(product)) {
                    if (!CollectionUtils.isEmpty(categoryIds) && categoryIds.contains(product.getType()) &&
                            !CollectionUtils.isEmpty(subCategoryIds) && subCategoryIds.contains(product.getSubType())) {
                        getDiscountData(totalAmount, calculatedDiscount, productDiscountMap, orderItem, offerValue);
                    } else if ((!CollectionUtils.isEmpty(categoryIds) && categoryIds.contains(product.getType())) && CollectionUtils.isEmpty(subCategoryIds)) {
                        getDiscountData(totalAmount, calculatedDiscount, productDiscountMap, orderItem, offerValue);
                    }
                    else if (!CollectionUtils.isEmpty(subCategoryIds) && subCategoryIds.contains(product.getType()) && CollectionUtils.isEmpty(categoryIds)) {
                        getDiscountData(totalAmount, calculatedDiscount, productDiscountMap, orderItem, offerValue);
                    }
                    else if (!CollectionUtils.isEmpty(productIds) && productIds.contains(orderItem.getProductId() + "_" + orderItem.getDimension().replaceAll(" ", ""))) {
                        getDiscountData(totalAmount, calculatedDiscount, productDiscountMap, orderItem, offerValue);
                    }
                }
            }
            if(CollectionUtils.isEmpty(productDiscountMap)){
                throw new OfferValidationException("Required Product or product Dimension not found",
                        WebErrorCode.PRODUCT_NOT_FOUND);
            }
            int flag = BigDecimal.valueOf(calculatedDiscount.get()).setScale(0, RoundingMode.HALF_UP).compareTo(maxCappedDiscount.get());
            if (flag <= 0) {
                //apply the discount percentage to the products for this offer
                for (OrderItem orderItem : offerOrder.getOrder().getOrders()) {
                    getDiscountDetail(orderItem, productDiscountMap, offerValue,coupon);
                }
            } else {
                //change the discount according to the max cap and then apply to the desired products
                BigDecimal percentageOffered = maxCappedDiscount.get().multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(totalAmount.get()), 2, BigDecimal.ROUND_HALF_UP);
                if(Objects.nonNull(orderItems)) {
                    for (OrderItem orderItem : orderItems) {
                        if (productDiscountMap.containsKey(orderItem.getProductId())) {
                            orderItem.getDiscountDetail().getDiscount().setPercentage(percentageOffered);
                            BigDecimal value = orderItem.getTotalAmount().multiply(percentageOffered).divide(BigDecimal.valueOf(100));
                            orderItem.getDiscountDetail().getDiscount().setValue(value.setScale(0, RoundingMode.HALF_UP));
                            orderItem.getDiscountDetail().setTotalDiscount(value.setScale(0, RoundingMode.HALF_UP));
                            orderItem.getDiscountDetail().setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
                            orderItem.getDiscountDetail().setDiscountReason(coupon.getCode());
                        }
                    }
                }
            }
        }
        offerOrder.setAppliedOfferMessage(getOfferMessage());
        return offerOrder;
    }

    public void getDiscountDetail(OrderItem orderItem, Map<Integer, BigDecimal> productDiscountMap, int offerValue,CouponDetail coupon) {
        if (productDiscountMap.containsKey(orderItem.getProductId())) {
            BigDecimal calculatedDiscount = productDiscountMap.get(orderItem.getProductId());
            orderItem.getDiscountDetail().getDiscount().setValue(calculatedDiscount.setScale(0, RoundingMode.HALF_UP));
            orderItem.getDiscountDetail().getDiscount().setPercentage(BigDecimal.valueOf(offerValue));
            orderItem.getDiscountDetail().setTotalDiscount(calculatedDiscount.setScale(0, RoundingMode.HALF_UP));
            orderItem.getDiscountDetail().setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
            orderItem.getDiscountDetail().setDiscountReason(coupon.getCode());
        }
    }

    private void getDiscountData(AtomicLong totalAmount,AtomicLong calculatedDiscount,Map<Integer, BigDecimal> productDiscountMap,
                                 OrderItem orderItem ,int offerValue ){
        totalAmount.getAndAdd(orderItem.getTotalAmount().longValue());
        BigDecimal amount = BigDecimal.valueOf(offerValue).divide(BigDecimal.valueOf(100), 10, BigDecimal.ROUND_HALF_UP);
        calculatedDiscount.getAndAdd(orderItem.getTotalAmount().multiply(amount).longValue());
        productDiscountMap.put(orderItem.getProductId(), orderItem.getTotalAmount().multiply(amount));
    }

}
