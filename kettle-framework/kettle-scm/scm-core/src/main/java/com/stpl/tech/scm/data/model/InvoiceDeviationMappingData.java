package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "INVOICE_DEVIATION_MAPPING")
public class InvoiceDeviationMappingData {

    private Integer id;
    private PaymentDeviationData paymentDeviationData;
    private String currentStatus;
    private Integer createdBy;
    private Integer acceptedBy;
    private Integer rejectedBy;
    private Integer removedBy;
    private String deviationRemark;
    private String actionRemark;
    private Integer deviationItemId;
    private String deviationItemType;
    private Date actionTime;
    private Date creationTime;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "INVOICE_DEVIATION_MAPPING_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PAYMENT_DEVIATION_ID", nullable = false)
    public PaymentDeviationData getPaymentDeviationData() {
        return paymentDeviationData;
    }

    public void setPaymentDeviationData(PaymentDeviationData paymentDeviationData) {
        this.paymentDeviationData = paymentDeviationData;
    }

    @Column(name = "CURRENT_STATUS", nullable = false)
    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus;
    }

    @Column(name = "ACTION_REMARK")
    public String getActionRemark() {
        return actionRemark;
    }

    public void setActionRemark(String actionRemark) {
        this.actionRemark = actionRemark;
    }

    @Column(name = "DEVIATION_REMARK")
    public String getDeviationRemark() {
        return deviationRemark;
    }

    public void setDeviationRemark(String deviationRemark) {
        this.deviationRemark = deviationRemark;
    }

    @Column(name = "DEVIATION_ITEM_ID", nullable = false)
    public Integer getDeviationItemId() {
        return deviationItemId;
    }

    public void setDeviationItemId(Integer deviationItemId) {
        this.deviationItemId = deviationItemId;
    }

    @Column(name = "DEVIATION_ITEM_TYPE", nullable = false)
    public String getDeviationItemType() {
        return deviationItemType;
    }

    public void setDeviationItemType(String deviationItemType) {
        this.deviationItemType = deviationItemType;
    }

    @Column(name = "ACCEPTED_BY")
    public Integer getAcceptedBy() {
        return acceptedBy;
    }

    public void setAcceptedBy(Integer acceptedBy) {
        this.acceptedBy = acceptedBy;
    }

    @Column(name = "REJECTED_BY")
    public Integer getRejectedBy() {
        return rejectedBy;
    }

    public void setRejectedBy(Integer rejectedBy) {
        this.rejectedBy = rejectedBy;
    }

    @Column(name = "REMOVED_BY")
    public Integer getRemovedBy() {
        return removedBy;
    }

    public void setRemovedBy(Integer removedBy) {
        this.removedBy = removedBy;
    }

    @Column(name = "ACTION_TIME")
    public Date getActionTime() {
        return actionTime;
    }

    public void setActionTime(Date actionTime) {
        this.actionTime = actionTime;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "CREATION_TIME", nullable = false)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }
}
