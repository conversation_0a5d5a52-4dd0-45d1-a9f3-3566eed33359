package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecomOfferData {
    private Integer resultAPC;
    private String discountType;
    private Integer discountValue;
    private String offerTag;
    private List<Integer> productIds;

}
