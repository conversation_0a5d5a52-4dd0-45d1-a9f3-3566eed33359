/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.data.dao;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import com.stpl.tech.analytics.model.PercentageIntegerData;
import com.stpl.tech.analytics.model.SalesSplitData;
import com.stpl.tech.analytics.model.UnitData;
import com.stpl.tech.kettle.data.model.UploadedTargetData;
import com.stpl.tech.kettle.data.model.UnitTargetDetail;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface DataServiceDao extends AbstractDao {

	public void markTargetAsInactive(Date fromDate, Date toDate, Collection<Integer> unitIds);

	public void addAllUnitTargets(List<UnitTargetDetail> details);

	/**
	 * @param data
	 * @param startOfMonth
	 * @param businessDate
	 */
	public SalesSplitData getTargetData(Date startOfMonth, Date businessDate);

	/**
	 * @param startOfMonth
	 * @param businessDate
	 * @return
	 */
	public SalesSplitData getSalesData(Date startOfMonth, Date businessDate);

	/**
	 * @param percentage
	 * @param startOfMonth
	 * @param lastDayOfMonth
	 * @return
	 */
	public PercentageIntegerData getPercentageAchievement(BigDecimal percentage, Date startOfMonth, Date lastDayOfMonth);

	/**
	 * @param startOfMonth
	 * @param businessDate
	 * @return
	 */
	public List<UnitData> getAllUnitsTargetData(Date startOfMonth, Date businessDate);

	public void markAllTargetForMonthAsInactive(int month, int year);

	public void addMonthlyTargets(List<UploadedTargetData> entityList);

	public UploadedTargetData getCurrentMonthTargetsForUnit(int unitId, int month, int year);

	public UploadedTargetData getCurrentDailyTargetsForUnit(int unitId, Date date);
}
