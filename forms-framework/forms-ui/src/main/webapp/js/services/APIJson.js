/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 29-04-2016.
 */
function getCookie(name) {
    let cookie = {};
    document.cookie.split(';').forEach(function(el) {
      let [k,v] = el.split('=');
      cookie[k.trim()] = v;
    })
    return cookie[name];
  }
(function () {
    'use strict';
    var envType="";
    envType =getCookie("EnvName");
    angular.module('formsApp').factory('APIJson', APIJson);
    APIJson.$inject = ['ConfigService'];
    function APIJson(ConfigService) {
        var masterBaseUrl;
        var baseUrl ;
        var obj=ConfigService.values(envType);
        masterBaseUrl=obj.masterBaseUrl;
        baseUrl=obj.baseUrl
        var KETTLE_CONTEXT = masterBaseUrl + "kettle-service/rest/v1/";
        var SCM_CONTEXT = masterBaseUrl + "scm-service/rest/v1/";
        var MASTER_CONTEXT = masterBaseUrl + "master-service/rest/v1/";
        var AUDIT_CONTEXT = baseUrl + "forms-service/rest/v1/";
        var RECON_CONTEXT = baseUrl + "recon-service/rest/v1/";

        var UNIT_METADATA_ROOT_CONTEXT = MASTER_CONTEXT + "unit-metadata/";
        var USER_SERVICES_ROOT_CONTEXT = MASTER_CONTEXT + "users/";
        var USER_MANAGEMENT_SERVICES_ROOT_CONTEXT = MASTER_CONTEXT + "user-management/";

        var SKU_MAPPING_SERVICES_ROOT_CONTEXT = SCM_CONTEXT + "sku-mapping-management/";
        var  UNIT_MANAGEMENT_ROOT_CONTEXT= SCM_CONTEXT + "unit-management/";
        var GOODS_ORDER_MANAGEMENT_ROOT_CONTEXT= SCM_CONTEXT + "goods-receive-management/";


        var AUDIT_FORM_MANAGEMENT_ROOT_CONTEXT = AUDIT_CONTEXT + "audit-form-management/";
        var AUDIT_MANAGEMENT_ROOT_CONTEXT = AUDIT_CONTEXT + "audit-management/";
        var WARNING_LETTER_MANAGEMENT_ROOT_CONTEXT = AUDIT_CONTEXT + "warning-letter-management/";
        var WALLET_MANAGEMENT_ROOT_CONTEXT = AUDIT_CONTEXT + "wallet-management/";
        var CLAIM_MANAGEMENT_ROOT_CONTEXT = AUDIT_CONTEXT + "claim-management/";
        var BATCH_MANAGEMENT_ROOT_CONTEXT = AUDIT_CONTEXT + "batch-management/";

        var EXPENSE_MANAGEMENT_ROOT_CONTEXT = KETTLE_CONTEXT + "expense-management/";
        var BUDGET_METADATA_ROOT_CONTEXT = MASTER_CONTEXT + "budget-metadata/";
        var MASTER_METADATA_ROOT_CONTEXT = MASTER_CONTEXT+"metadata/";
        var PNL_ADJUSTMENT_ROOT_CONTEXT = KETTLE_CONTEXT +"pnl-adjustment/";

        var service = {};

        service.urls = {
            users: {
                login: USER_SERVICES_ROOT_CONTEXT + "login",
                logout: USER_SERVICES_ROOT_CONTEXT + "logout",
                sessionLogin: USER_SERVICES_ROOT_CONTEXT + "session-login"
            },
            sku: {
                skuForUnit: SKU_MAPPING_SERVICES_ROOT_CONTEXT + "sku-for-unit"
            },
            userManagement: {
                usersForUnit: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "users/unit",
                activeUnitsForUser: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "user/units"
            },
            unitMetaData: {
                activeUnits: UNIT_METADATA_ROOT_CONTEXT + "all-active-units",
                families: UNIT_METADATA_ROOT_CONTEXT + "families",
                activeUnitsBySubCategory: UNIT_METADATA_ROOT_CONTEXT + "all-active-units-sub-category",
                unitProductDataTrimmed: UNIT_METADATA_ROOT_CONTEXT + "unit-product-data/trim",
                areaManagerUnits: UNIT_METADATA_ROOT_CONTEXT + "area-manager-units",
                subCategories: UNIT_METADATA_ROOT_CONTEXT + "sub-categories",
                companies:UNIT_METADATA_ROOT_CONTEXT+"companies",
            },
            unitManagement:{
                businessCostCenters: UNIT_MANAGEMENT_ROOT_CONTEXT+"business-cost-center-by-companyId",
                businessCostCentersForType:UNIT_MANAGEMENT_ROOT_CONTEXT+"business-cost-centers-by-type",
            },
            goodsOrderManagement: {
                isSpecialOrder: GOODS_ORDER_MANAGEMENT_ROOT_CONTEXT + "is-special-order"
            },
            employeeMetadata:{
                activeEmployees:USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "employees/active",
            },
            auditFormManagement: {
                getAllAuditForms: AUDIT_FORM_MANAGEMENT_ROOT_CONTEXT + "audit-forms",
                getActiveAuditForms: AUDIT_FORM_MANAGEMENT_ROOT_CONTEXT + "audit-forms-active",
                getAuditForm: AUDIT_FORM_MANAGEMENT_ROOT_CONTEXT + "audit-form"
            },
            auditManagement: {
                audit: AUDIT_MANAGEMENT_ROOT_CONTEXT + "audit",
                searchAudit: AUDIT_MANAGEMENT_ROOT_CONTEXT + "audit-search",
                downloadAuditReport: AUDIT_MANAGEMENT_ROOT_CONTEXT + "audit-download",
                downloadGenerateAuditReport: AUDIT_MANAGEMENT_ROOT_CONTEXT + "audit-generate-download",
                auditReportEmail: AUDIT_MANAGEMENT_ROOT_CONTEXT + "audit-report-email"
            },
            expenseManagement: {
                addExpense: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "add-expense",
                getExpenseDetail: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "get-expense",
                cancelExpenseDetail: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "cancel-expense",
                addExpenses: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "add-expenses" 
            },
            budgetManagement: {
                getExpenseHeader: MASTER_METADATA_ROOT_CONTEXT + "expense-detail",
                warningReasonDetail : MASTER_METADATA_ROOT_CONTEXT + "warning-reason-list"
            },
            warningManagement : {
            	issueWarningLetter : WARNING_LETTER_MANAGEMENT_ROOT_CONTEXT + "issue-warning-letter",
            	warningLetter :  WARNING_LETTER_MANAGEMENT_ROOT_CONTEXT + "warning-letter",
            	getWarningDetail : WARNING_LETTER_MANAGEMENT_ROOT_CONTEXT + "warning-letter-detail",
            	warningAction : WARNING_LETTER_MANAGEMENT_ROOT_CONTEXT + "warning-action-detail",
            	downloadWarningReport : WARNING_LETTER_MANAGEMENT_ROOT_CONTEXT + "warning-report-download", 
            	uploadWarningImage : WARNING_LETTER_MANAGEMENT_ROOT_CONTEXT + "warning-image-upload", 
            	downloadWarningImage : WARNING_LETTER_MANAGEMENT_ROOT_CONTEXT + "warning-image-download",
            	uploadImage : WARNING_LETTER_MANAGEMENT_ROOT_CONTEXT + "image-upload"
            },
            walletManagement : {
            	sendOTP : WALLET_MANAGEMENT_ROOT_CONTEXT + "send-otp",
            	resendOTP :  WALLET_MANAGEMENT_ROOT_CONTEXT + "resend-otp",
            	verifyOTP : WALLET_MANAGEMENT_ROOT_CONTEXT + "verify-otp",
                getWalletByAccountNo:WALLET_MANAGEMENT_ROOT_CONTEXT+"get-wallet-by-account-no",
                getReportingManagers: WALLET_MANAGEMENT_ROOT_CONTEXT+"get-reporting-managers",
            	walletDetail : WALLET_MANAGEMENT_ROOT_CONTEXT + "wallet-details",
                walletDetailByLoginType :WALLET_MANAGEMENT_ROOT_CONTEXT+"get/wallet-data",
                getWallets : WALLET_MANAGEMENT_ROOT_CONTEXT + "get-wallets",
            	issueVoucher : WALLET_MANAGEMENT_ROOT_CONTEXT + "issue-voucher",
            	voucherDetails : WALLET_MANAGEMENT_ROOT_CONTEXT + "voucher-list",
                voucherListFinance : WALLET_MANAGEMENT_ROOT_CONTEXT + "voucher-list-finance",
            	uploadVoucherDoc : WALLET_MANAGEMENT_ROOT_CONTEXT + "upload-voucher-doc",
            	cancelVoucher : WALLET_MANAGEMENT_ROOT_CONTEXT + "cancel-voucher",
            	settleVoucher : WALLET_MANAGEMENT_ROOT_CONTEXT + "settle-voucher",
            	voucherDetail : WALLET_MANAGEMENT_ROOT_CONTEXT + "voucher-detail",
            	downloadInvoice : WALLET_MANAGEMENT_ROOT_CONTEXT + "invoice-download",
            	amApproveVoucher : WALLET_MANAGEMENT_ROOT_CONTEXT + "am-approve-voucher",
            	amRejectVoucher : WALLET_MANAGEMENT_ROOT_CONTEXT + "am-reject-voucher",
            	financeApproveVoucher : WALLET_MANAGEMENT_ROOT_CONTEXT + "finance-approve-voucher",
            	financeRejectVoucher : WALLET_MANAGEMENT_ROOT_CONTEXT + "finance-reject-voucher",
            	topupApprovedVouchers : WALLET_MANAGEMENT_ROOT_CONTEXT + "topup-approved-vouchers",
            	acknowledgePendingRejectedVoucher : WALLET_MANAGEMENT_ROOT_CONTEXT + "acknowledge-rejected-voucher",
            	pendingRejectedVoucherCount : WALLET_MANAGEMENT_ROOT_CONTEXT + "pending-rejected-count",
            	denominationDetail : WALLET_MANAGEMENT_ROOT_CONTEXT + "cash-denomination",
            	createWallet : WALLET_MANAGEMENT_ROOT_CONTEXT + "create-wallet",
            	updateWallet : WALLET_MANAGEMENT_ROOT_CONTEXT + "update-wallet",
            	downloadVoucherDetail : WALLET_MANAGEMENT_ROOT_CONTEXT + "download-voucher-list",
            	checkPendingVoucher : WALLET_MANAGEMENT_ROOT_CONTEXT + "check-pending-voucher/associatedId",
                getClaimPendingVouchers : WALLET_MANAGEMENT_ROOT_CONTEXT + "get-claim-pending-vouchers",
                getBusinessCostCentersByWalletId : WALLET_MANAGEMENT_ROOT_CONTEXT+"get-wallet-bcc-mappings",
                getApprovalByWalletId : WALLET_MANAGEMENT_ROOT_CONTEXT+"get-wallet-approver-mapping",
                updateApprovalStatusByWalletId : WALLET_MANAGEMENT_ROOT_CONTEXT+"update-approver-mapping",
                updateAllWalletBccMappings:WALLET_MANAGEMENT_ROOT_CONTEXT+"update-all-wallet-bcc-mapping",
                updateWalletBccMapping:WALLET_MANAGEMENT_ROOT_CONTEXT+"update-wallet-bcc-mapping",
                getWalletsByAccountType:WALLET_MANAGEMENT_ROOT_CONTEXT+"wallet-accounts-by-accountType",
                updateAllWalletApproverMappings:WALLET_MANAGEMENT_ROOT_CONTEXT+"update-all-wallet-approver-mapping",
                updateWalletApproverMapping:WALLET_MANAGEMENT_ROOT_CONTEXT+"update-approver-mapping",
            },
            claimManagement : {
                addClaim : CLAIM_MANAGEMENT_ROOT_CONTEXT + "add-claim",
                findClaims : CLAIM_MANAGEMENT_ROOT_CONTEXT + "find-claims",
                downloadClaimById : CLAIM_MANAGEMENT_ROOT_CONTEXT + "download-by-id",
                downloadClaim : CLAIM_MANAGEMENT_ROOT_CONTEXT + "download-claim",
                setHappayToClaim : CLAIM_MANAGEMENT_ROOT_CONTEXT + "set-happay-id-to-claim",
                approveClaim : CLAIM_MANAGEMENT_ROOT_CONTEXT + "approve-claim",
                rejectClaim : CLAIM_MANAGEMENT_ROOT_CONTEXT + "reject-claim",
                acknowledgeClaim : CLAIM_MANAGEMENT_ROOT_CONTEXT + "acknowledge-claim",
                getClaimLogs : CLAIM_MANAGEMENT_ROOT_CONTEXT + "get-claim-logs",
                getClaimVouchers : CLAIM_MANAGEMENT_ROOT_CONTEXT + "get-claim-vouchers",
                bulkDownload : CLAIM_MANAGEMENT_ROOT_CONTEXT + "bulk-download",
                downloadById : CLAIM_MANAGEMENT_ROOT_CONTEXT + "download-by-id",
                downloadTemplate : CLAIM_MANAGEMENT_ROOT_CONTEXT + "download-template",
                bulkApprovals : CLAIM_MANAGEMENT_ROOT_CONTEXT + "upload-approvals",
            },
            batchManagement : {
                generatePackCodes : BATCH_MANAGEMENT_ROOT_CONTEXT + "generate-pack-codes",
                getPrefix : BATCH_MANAGEMENT_ROOT_CONTEXT + "get-prefix"
            },
            PnLManagement : {
                getAdjustmentField: PNL_ADJUSTMENT_ROOT_CONTEXT +"adjustment-fields",
                createAdjustment: PNL_ADJUSTMENT_ROOT_CONTEXT +"create-adjustment",
                getAdjustmentReason: PNL_ADJUSTMENT_ROOT_CONTEXT +"get-adjustment-reasons",
                updateAdjustmentStatus: PNL_ADJUSTMENT_ROOT_CONTEXT + "update-adjustment-status",
                getAdjustment: PNL_ADJUSTMENT_ROOT_CONTEXT + "get-adjustments"
            },
            reconciliation:{
                channelPartnerStartRecon : RECON_CONTEXT + "channel-partner-recon/start",
                cashStartRecon : RECON_CONTEXT + "cash-recon/start",
            }

            
        };

        return service;
    }

})();