package com.stpl.tech.kettle.rekognition.util;

import com.stpl.tech.util.AppConstants;

public class RekognitionConstants extends AppConstants {

	public static final String API_VERSION = "v1";

	public static final String API_VERSION_V2 = "v2";
	public static final String SEPARATOR = "/";
	public static final String REKOGNITION_ROOT_CONTEXT = "rekog";
	public static final String IMAGE_SYNC_ROOT_CONTEXT = "sync";
	public static final String ADMIN_ROOT_CONTEXT = "admin";
	public static final String APP_MANAGEMENT_ROOT_CONTEXT = "app-manager";
	public static final String BULK_ROOT_CONTEXT = "rekog-bulk";
	public static final String HYPHEN = "_";
	public static final String DOT = ".";
	public static final String ENCODING_UTF_8 = "UTF-8";
}
