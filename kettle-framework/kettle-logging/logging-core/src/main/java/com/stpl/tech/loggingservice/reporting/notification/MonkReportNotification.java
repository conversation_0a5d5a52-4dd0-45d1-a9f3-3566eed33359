package com.stpl.tech.loggingservice.reporting.notification;

import com.stpl.tech.loggingservice.reporting.model.MonkReport;
import com.stpl.tech.loggingservice.util.KettleLoggingUtil;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 17-03-2018.
 */
public class MonkReportNotification extends EmailNotification{

    private MonkReport output;
    private String unitName;
    private EnvType env;
    private List<String> toMails;
    private String mimeType = AppConstants.EXCEL_MIME_TYPE;

    public MonkReportNotification(MonkReport output, EnvType env, List<String> toMails, String unitName) {
        this.output = output;
        this.env = env;
        this.unitName = unitName;
        setToMails(toMails);
    }

    @Override
    public String[] getToEmails() {
        toMails.add("<EMAIL>");
        return KettleLoggingUtil.isDev(getEnvironmentType()) ? new String[]{"<EMAIL>"} : toMails.toArray(new String[toMails.size()]);
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        return "Chai Monk Logs" + StringUtils.capitalize(unitName);
    }

    @Override
    public String body() throws EmailGenerationException {
        StringBuilder body =  new StringBuilder("Chai Monk Logs for :: ").append(StringUtils.capitalize(unitName)).append("\n")
                .append("From :: ").append(output.getStartDate()).append("\n")
                .append("To :: ").append(output.getEndDate()).append("\n");
        return body.toString();
    }

    @Override
    public EnvType getEnvironmentType() {
        return this.env;
    }

    public String getMimeType() {
        return this.mimeType;
    }

    public void setToMails(List<String> toMails) {
        toMails.add("<EMAIL>"); // cc to technology as well
        this.toMails = toMails;
    }
}
