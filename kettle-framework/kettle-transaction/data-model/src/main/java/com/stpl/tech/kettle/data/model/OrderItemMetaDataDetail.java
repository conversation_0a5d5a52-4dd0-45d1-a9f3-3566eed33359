package com.stpl.tech.kettle.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "ORDER_ITEM_METADATA_DETAIL")
@Getter
@Setter
public class OrderItemMetaDataDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "METADATA_ID", unique = true, nullable = false)
    private int metadataId;

    @Column(name = "ORDER_ITEM_ID", nullable = false)
    private int orderItemId;

    @Column(name = "ORDER_ID", nullable = false)
    private  Integer orderId;
    @Column(name = "CUSTOMER_ID", nullable = true)
    private Integer customerId;
    @Column(name = "ADDED_BY", nullable = true)
    private String addedBy;
    @Column(name = "IS_RECOMMENDED", nullable = true)
    private String isRecommended;

    @Column(name = "ITEM_NAME" , nullable = true)
    private String itemName;

    @Column(name = "IS_SAVED_CHAI")
    private String isSavedChai;

    @Column(name = "SAVED_CHAI_ID")
    private Integer savedChaiId;

    @Column(name = "SPECIAL_MILK_VARIANT")
    private Integer specialMilkVariant;

}
