package com.stpl.tech.kettle.domain.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Document
public class UnitProductsStockEventData {

	@Id
	private String key;
	private Integer unitId;
	private String unitName;
	private Integer productId;
	private String productName;
	private String status;
	private Date eventTimeStamp;
	private String dimension;
	private Integer brandId;
	private String eventType;

	public UnitProductsStockEventData() {
	}

	public UnitProductsStockEventData(Integer unitId, String unitName, Integer productId, String productName,
									  String status, Date eventTimeStamp, String dimension, Integer brandId, String eventType) {
		this.unitId = unitId;
		this.unitName = unitName;
		this.productId = productId;
		this.productName = productName;
		this.status = status;
		this.eventTimeStamp = eventTimeStamp;
		this.dimension = dimension;
		this.brandId = brandId;
		this.eventType = eventType;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Date getEventTimeStamp() {
		return eventTimeStamp;
	}

	public void setEventTimeStamp(Date eventTimeStamp) {
		this.eventTimeStamp = eventTimeStamp;
	}

	public String getDimension() {
		return dimension;
	}

	public Integer getBrandId() {
		return brandId;
	}

	public String getEventType() {
		return eventType;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}
}
