/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.data.dao.KettleReadDao;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Repository
@Log4j2
public class KettleReadDaoImpl implements KettleReadDao {

    @PersistenceContext(unitName = "TransactionReadDataSourcePUName")
    @Qualifier(value = "TransactionReadDataSourceEMFactory")
    private EntityManager transactionReadEntityManager;


    @Override
    public Map<String, BigDecimal> getMonthlyOrdersOfRecipe(Integer recipeId, List<Integer> regionUnitIds) {
        Map<String, BigDecimal> result = new HashMap<>();
        result.put("kettleOrdersQuantity", BigDecimal.ZERO);
        result.put("kettleOrdersSale", BigDecimal.ZERO);
        try {
            StringBuilder queryBuilder = new StringBuilder("SELECT SUM(oi.QUANTITY),SUM(od.TOTAL_AMOUNT) FROM ORDER_DETAIL od INNER JOIN ORDER_ITEM oi on od.ORDER_ID = oi.ORDER_ID " +
                    "where oi.RECIPE_ID =:recipeId and od.BILLING_SERVER_TIME >=:oneMonthAgoDate");
            if (!regionUnitIds.isEmpty()) {
                queryBuilder.append(" AND od.UNIT_ID IN(:regionUnitIds)");
            }
            queryBuilder.append(" GROUP BY oi.RECIPE_ID");
            Query query = transactionReadEntityManager.createNativeQuery(queryBuilder.toString());
            query.setParameter("recipeId", recipeId);
            query.setParameter("oneMonthAgoDate", AppUtils.getDayBeforeOrAfterDay(AppUtils.getCurrentDate(), -30));
            if (!regionUnitIds.isEmpty()) {
                query.setParameter("regionUnitIds", regionUnitIds);
            }
            List<Object[]> monthlyOrders = (List<Object[]>) query.getResultList();
            if (Objects.nonNull(monthlyOrders) && !monthlyOrders.isEmpty()) {
                monthlyOrders.forEach(monthlyOrderSummary -> {
                    result.put("kettleOrdersQuantity", (BigDecimal) monthlyOrderSummary[0]);
                    result.put("kettleOrdersSale", (BigDecimal) monthlyOrderSummary[1]);
                });
            }
        } catch (Exception e) {
            log.error("Exception Occurred while getting the Monthly Orders Of Recipe :: ", e);
        }
        return result;
    }
}
