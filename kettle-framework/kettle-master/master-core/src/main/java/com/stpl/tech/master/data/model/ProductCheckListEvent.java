package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "PRODUCT_CHECKLIST_EVENT")
public class ProductCheckListEvent implements java.io.Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EVENT_ID")
    private Integer eventId;

    @Column(name = "USER_ID", nullable = false)
    private Integer userId;

    @Column(name = "GENERATION_TIMESTAMP")
    private Date generationTimeStamp;

    @Column(name = "COMPLETION_TIMESTAMP")
    private Date completionTimeStamp;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "FILE_LINK")
    private String fileLink;

}
