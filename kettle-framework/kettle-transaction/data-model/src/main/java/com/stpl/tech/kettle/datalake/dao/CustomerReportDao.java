package com.stpl.tech.kettle.datalake.dao;

import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.datalake.model.CustomerNPSFeedbackStats;
import com.stpl.tech.kettle.datalake.model.CustomerOrderFeedbackStats;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface CustomerReportDao extends AbstractDao {

	void calculateNpsForBusinessDate(Date fromDate, Date tillDate);

	void calculateCustomerOneViewFeedbackStats(Date fromDate, Date tillDate);

	CustomerNPSFeedbackStats getNpsForCustomerDetail(Integer customerId) throws DataNotFoundException;

	CustomerOrderFeedbackStats getFeedbackForCustomerDetail(Integer customerId) throws DataNotFoundException;

	List<Integer> customerRecommendedProduct(Integer customerId);

	void calculateFeedbackForBusinessDate(Date fromDate, Date tillDate);

	void calculateCustomerOneViewOrderFeedbackStats(Date fromDate, Date tillDate);

}
