/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 14 Jul, 2015 1:35:15 AM by Hibernate Tools 4.0.0

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * Home object for domain model class UnitProductPricing.
 * 
 * @see com.stpl.tech.master.data.model.UnitProductPricing
 * <AUTHOR> Tools
 */
@Stateless
public class UnitProductPricingHome {

	private static final Log log = LogFactory.getLog(UnitProductPricingHome.class);

	@PersistenceContext
	private EntityManager entityManager;

	public void persist(UnitProductPricing transientInstance) {
		log.debug("persisting UnitProductPricing instance");
		try {
			entityManager.persist(transientInstance);
			log.debug("persist successful");
		} catch (RuntimeException re) {
			log.error("persist failed", re);
			throw re;
		}
	}

	public void remove(UnitProductPricing persistentInstance) {
		log.debug("removing UnitProductPricing instance");
		try {
			entityManager.remove(persistentInstance);
			log.debug("remove successful");
		} catch (RuntimeException re) {
			log.error("remove failed", re);
			throw re;
		}
	}

	public UnitProductPricing merge(UnitProductPricing detachedInstance) {
		log.debug("merging UnitProductPricing instance");
		try {
			UnitProductPricing result = entityManager.merge(detachedInstance);
			log.debug("merge successful");
			return result;
		} catch (RuntimeException re) {
			log.error("merge failed", re);
			throw re;
		}
	}

	public UnitProductPricing findById(Integer id) {
		log.debug("getting UnitProductPricing instance with productId: " + id);
		try {
			UnitProductPricing instance = entityManager.find(UnitProductPricing.class, id);
			log.debug("get successful");
			return instance;
		} catch (RuntimeException re) {
			log.error("get failed", re);
			throw re;
		}
	}
}
