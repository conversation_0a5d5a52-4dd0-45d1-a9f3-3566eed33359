/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.strategy;

import java.util.Comparator;

import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;

public class UnitDeliveryMappingComparator implements Comparator<UnitToDeliveryPartnerMappings> {

	@Override
	public int compare(UnitToDeliveryPartnerMappings o1, UnitToDeliveryPartnerMappings o2) {
		int priorityComparision = o2.getPriority() - o1.getPriority();
		if (priorityComparision == 0) {
			return o2.getId() - o1.getId();
		} else {
			return priorityComparision;
		}
	}

}
