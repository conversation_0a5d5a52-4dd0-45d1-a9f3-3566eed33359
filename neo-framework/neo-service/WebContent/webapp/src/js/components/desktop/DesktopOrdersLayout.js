/**
 * Created by Chaayos on 11-12-2016.
 */
import React from "react";
import {connect} from "react-redux";
import browserHistory from "react-router/lib/browserHistory";
import appUtil from "../../AppUtil";
import StorageUtils from "../../utils/StorageUtils";
import DesktopHeader from "./DesktopHeader";
import DesktopFooterLayout from "./DesktopFooterLayout";
import * as OrderManagementActions from "../../actions/OrderManagementActions";
import trackUtils from "../../utils/TrackUtils";

@connect((store) => {
    return {
        selectedCity: store.localityReducer.selectedCity,
        unit: store.outletMenuReducer.unit,
        orderList: store.orderManagementReducer.orderList,
        status: store.orderManagementReducer.status,
        error: store.orderManagementReducer.error,
        page: store.orderManagementReducer.page,
        showMore: store.orderManagementReducer.showMore,
        membershipIds: store.membershipReducer.membershipIds,
        membershipData: store.membershipReducer.membershipData
    };
})
export default class DesktopOrdersLayout extends React.Component {

    constructor() {
        super();
        this.state = {};
        this.openOrderDetail = this.openOrderDetail.bind(this);
        this.loadNextOrders = this.loadNextOrders.bind(this);
        this.goToMenu = this.goToMenu.bind(this);
        this.reOrder = this.reOrder.bind(this);
    }

    openOrderDetail(orderId) {
        this.props.dispatch(OrderManagementActions.setCurrentOrderId({id: orderId, type: "GEN"}));
        browserHistory.push("/orderDetail");
    }

    loadNextOrders() {
        this.props.dispatch(OrderManagementActions.getCustomerOrders(this.props.page,()=>{
        }));
    }

    goToMenu() {
        trackUtils.trackGoToMenuClicked("orders");
        browserHistory.push("/menu");
    }

    reOrder(order) {
        this.props.dispatch(OrderManagementActions.setCurrentOrderId({id: order.generateOrderId, type: "GEN"}));
        this.props.dispatch(OrderManagementActions.reOrder("orders", order.generateOrderId, appUtil.isInterstate(this.props.unit, this.props.selectedCity)));
    }

    componentWillMount() {
        window.scrollTo(0, 0);
        if (appUtil.checkEmpty(StorageUtils.getAuthDetail()) || appUtil.checkEmpty(StorageUtils.getAuthDetail().sessionKey)) {
            browserHistory.push("/login");
        } else {
            this.props.dispatch(OrderManagementActions.resetPage());
            this.props.dispatch(OrderManagementActions.getCustomerOrders(1, () => {
            }));
        }
    }

    componentDidMount() {
        trackUtils.trackPageView({page: "orders", device: "desktop", custom: true});
    }

    render() {
        var currentOrders = [];
        var previousOrders = [];
        if (this.props.orderList != null && this.props.membershipData != null) {
            this.props.orderList.orders.map((order) => {
                var orderProducts = [];
                order.orders.map((item) => {
                    if (item.productId != 1044 && item.productId != 1043) {
                        orderProducts.push(item);
                    }
                });
                let styles = [];
                if (orderProducts.length > 0) {
                    if (this.props.membershipIds.data.includes(order.orders[0].productId)) {
                        this.props.membershipData.data.subscriptionProducts.map((data) => {
                            if (data.id === order.orders[0].productId) {
                                if (!appUtil.checkEmpty(data.marketingImage[0])) {
                                    styles.push({backgroundImage: "url(https://d3pjt1af33nqn0.cloudfront.net/product_image/" + data.marketingImage[0].url + ")"});
                                } else {
                                    styles.push({backgroundImage: "url(../../../img/products/" + orderProducts[0].productId + ".jpg)"});
                                }
                            }
                        })
                    } else {
                        styles.push({backgroundImage: "url(../../../img/products/" + orderProducts[0].productId + ".jpg)"});
                    }
                    if (orderProducts.length > 1) {
                        styles.push({backgroundImage: "url(../../../img/products/" + orderProducts[1].productId + ".jpg)"});
                    }
                }
                var productName = "";
                order.orders.map((item) => {
                    if (item.productId != 1044 && item.productId != 1043) {
                        productName += item.quantity + " " + item.productName + ", ";
                    }
                });
                productName = productName.substr(0, productName.length - 2);
                var d = new Date(order.billingServerTime);
                d.setHours(d.getUTCHours());
                d.setMinutes(d.getUTCMinutes());
                var c = new Date();
                var mlist = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
                var time;
                if (d.getDate() == c.getDate()) {
                    var secs = parseInt((c.getTime() - d.getTime()) / 1000);
                    var min = parseInt(secs / 60) > 0 ? parseInt(secs / 60) + " min" : "";
                    var sec = parseInt(secs % 60) > 0 ? parseInt(secs % 60) + " sec" : "";
                    if (min.length > 0 && sec.length > 0) {
                        time = min + " " + sec + " ago";
                    } else if (min.length > 0) {
                        time = min + " ago";
                    } else {
                        time = sec + " ago";
                    }
                } else {
                    time = "on " + d.getDate() + " " + mlist[d.getMonth()];
                }
                var orderData = (
                    <div key={order.generateOrderId} class="orderContainer">
                        {appUtil.isOrderCompleted(order.status, order.source) && !this.props.membershipIds.data.includes(order.orders[0].productId) ? (
                            <div class="reOrder" onClick={this.reOrder.bind(this, order)}>Reorder</div>
                        ) : null}
                        <div class="orderStatus" onClick={this.openOrderDetail.bind(this, order.generateOrderId)}>
                            {appUtil.getOrderDisplayStatus(order.status, order.source)}
                        </div>
                        <div class="orderPics" onClick={this.openOrderDetail.bind(this, order.generateOrderId)}>
                            {styles.length > 0 ? (
                                <div class="pic" style={styles[0]}></div>
                            ) : null}
                            {styles.length > 1 ? (
                                <div class="pic" style={styles[1]}></div>
                            ) : null}
                        </div>
                        <div class="orderContent" onClick={this.openOrderDetail.bind(this, order.generateOrderId)}>
                            <div class="orderId">{order.generateOrderId}</div>
                            <div class="orderTime">Placed {time}</div>
                            <div class="productName">{productName}</div>
                            {order.source == "COD" ? (
                                <div class="address">
                                    <b>Delivery:</b> {order.address.line1}, {order.address.locality}, {order.address.city}
                                </div>
                            ) : (null)}
                            {order.source == "TAKE_AWAY" ? (
                                <div class="address"><b>Take Away:</b> {order.unitName}</div>
                            ) : (null)}
                        </div>
                        <div class="clear"></div>
                    </div>
                );
                if (appUtil.isOrderCompleted(order.status, order.source)) {
                    previousOrders.push(orderData);
                } else {
                    currentOrders.push(orderData);
                }
            });
        }

        return (
            <div>
                <div class="colouredHead">
                    <DesktopHeader menu={false} showLocationMetadata={false} showCartBtn={false}
                                   props={this.props.props}/>
                </div>
                <div class="desktopPageContainer" style={{background: "#f7f7f7"}}>
                    <div class="desktopPageHead">My Orders</div>

                    {(this.props.status == "REJECTED") ? (
                        <div class="alert error">{this.props.error.errorMessage}</div>
                    ) : (
                        this.props.orderList == null || this.props.orderList.length > 0 ? (
                            (this.props.status == null || this.props.status == "PENDING") ? (
                                <div>
                                    <div class="ptop50">
                                        <div class="loader load8"></div>
                                    </div>
                                    <p class="text-center">Fetching order history.</p>
                                </div>
                            ) : (
                                <div class="alert info">You have no orders.</div>
                            )
                        ) : (
                            <div>
                                {currentOrders.length > 0 ? (
                                    <div>
                                        <div class="orderListSubHead">Current</div>
                                        {currentOrders}
                                    </div>
                                ) : (null)}
                                {previousOrders.length > 0 ? (
                                    <div>
                                        <div class="orderListSubHead">Previous</div>
                                        {previousOrders}
                                    </div>
                                ) : (null)}
                                {(this.props.status == "PENDING") ? (
                                    <div class="ptop50">
                                        <div class="loader load8"></div>
                                    </div>
                                ) : (null)}
                                {this.props.showMore ? (
                                    <div class="pairBtnWrapper">
                                        <div class="btn btn-default leftBtn" onClick={this.goToMenu.bind(this)}>Go To
                                            Menu
                                        </div>
                                        <div class="btn btn-primary mainBtn"
                                             onClick={this.loadNextOrders.bind(this)}>Show More
                                        </div>
                                    </div>
                                ) : (
                                    <div class="btn btn-primary" style={{marginTop: "20px"}}
                                         onClick={this.goToMenu.bind(this)}>Go To Menu</div>
                                )}
                            </div>
                        )
                    )}
                </div>
                <DesktopFooterLayout/>
            </div>
        )
    }
}
