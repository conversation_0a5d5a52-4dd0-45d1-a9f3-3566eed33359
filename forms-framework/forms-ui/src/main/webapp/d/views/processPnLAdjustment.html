<style>
    body, html {
        overflow-x: hidden;
    }

    .reimbursed {
        background: grey;
    }
</style>
<div data-ng-init="init()" class="wallet-container">
    <div class="row">
        <div class="col-xs-12">
            <h2 class="text-center page-heading">
                Approve/Reject PnL Adjustment
            </h2>
        </div>
    </div>
    <div class="row-spacing">
        <div class="row">
            <div class="col-xs-12">
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="control-label">Select Unit Account </label>
                        <div ng-dropdown-multiselect="" options="units"
                             selected-model="unitMultiSelect"
                             extra-settings="multiSelectSettings"></div>
                    </div>
                </div>
                <div class="col-xs-4 form-group">
                    <label class="control-label">Status</label>
                    <select data-ng-model="status" class='form-control'
                            data-ng-options="status for status in statusList" data-ng-click="resetResponse()">
                    </select>
                </div>
                <div class="col-xs-2">
                    <label class="control-label">Month
                    </label>
                    <select class="form-control"
                            select class="form-control"
                            data-ng-options="month for month in months"
                            required="required" data-ng-model="selectedStartMonth">
                    </select>
                </div>
                <div class="col-xs-2">
                    <label class="control-label">Year
                    </label>
                    <select class="form-control"
                            select class="form-control"
                            data-ng-options="year for year in years"
                            required="required" data-ng-model="selectedStartYear">
                    </select>
                </div>
            </div>
        </div>
        <div class="row">

        </div>
        <br>
        <div class="row">
            <div class="col-xs-12 form-group">
                <div class="col-xs-4">
                    <button class="btn btn-danger" data-ng-click="resetSearchDetails()">Reset</button>
                </div>
                <div class="col-xs-4">
                </div>
                <div class="col-xs-4">
                    <button class="btn btn-primary pull-right"
                            data-ng-click="getDetails(true)">Search
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<!--<div data-ng-if="activeEntity == 'pnlValue'">-->
    <div class="row-spacing">
        <div class="col-xs-12">
            <div data-ng-repeat="voucher in voucherList">
                <div class="panel panel-default"
                     style="border: 5px solid; margin-bottom: 5px;" id="voucher_{{voucher.id}}">
                    <!-- View of Voucher Card -->
                    <div>
                        <div class="panel-heading reimbursed" style="font-size: 17px;">
                            <div class="row row-spacing">
                                <div class="col-xs-6">
                                    Request Id : <label>{{voucher.adjustmentId}}</label>
                                </div>
                                <div class="col-xs-6">
                                    Current Status : {{voucher.status}}
                                    <span data-ng-if="voucher.isReimbursed == true">(Reimbursed)</span>
                                </div>
                            </div>
                            <div class="row row-spacing">
                                <div class="col-xs-6">Unit Name: {{voucher.unitId}}</div>
                            </div>
                            <div class="row row-spacing">
                                <div class="col-xs-6">PnL Header: {{voucher.pnlHeaderName}}</div>
                            </div>
                            <div class="row row-spacing">
                                <div class="col-xs-6">
                                    Adjustment Amount : {{voucher.adjustmentValue}}
                                </div>
                                <div class="col-xs-6">Issue Time :
                                    {{voucher.creationTime | date:'dd/MM/yyyy hh:mm:ss a'}}
                                </div>
                            </div>
                            <div class="row row-spacing">
                                <div class="col-xs-6">Adjustment Type: {{voucher.createComment}}</div>
                            </div>
                            <div class="row row-spacing">
                                <div class="col-xs-6">Adjustment Reason: {{voucher.createComment}}</div>
                            </div>
                            <div class="row row-spacing">
                                <div class="col-xs-6">
                                    Issued By : {{voucher.createdBy}}
                                </div>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div>
                                    <div class="col-xs-4" data-ng-if="status=='CREATED'">
                                        <button class="btn  btn-warning" data-toggle="modal" data-target="#adjustmentModal"
                                                data-ng-click="searchAction('REJECTED',voucher)">
                                            Reject
                                        </button>
                                    </div>
                                    <div class="col-xs-4" data-ng-if="status=='CREATED'">
                                        <button class="btn btn-success" data-toggle="modal" data-target="#adjustmentModal"
                                                data-ng-click="searchAction('APPROVED', voucher)">
                                            Approve
                                        </button>
                                    </div>
                                    <div class="col-xs-4" data-ng-if="status=='CREATED'">
                                        <button class="btn  btn-info " data-toggle="modal" data-target="#adjustmentModal"
                                            data-ng-click="searchAction('CANCELLED', voucher)">Cancel
                                    </button>
                                    </div>
                                    <div class="col-xs-4">
                                        <button class="btn  btn-info" data-toggle="modal" data-target="#adjustmentFinal"
                                                data-ng-click="searchAction('VIEW', voucher)">View
                                        </button>
                                    </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<div id="adjustmentModal" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">PnL Adjustment Confirmation for {{type}}</h4>
            </div>
            <div class="modal-body">
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Adjustment Id :</b> {{adjustmentFinalObject.adjustmentId}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>PnL Header :</b> {{adjustmentFinalObject.pnlHeaderDetail}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Adjustment Type: </b>{{adjustmentFinalObject.adjustmentType}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Adjustment Value: </b>{{adjustmentFinalObject.adjustmentValue}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Unit Id: </b>{{adjustmentFinalObject.unitId}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Created By : </b>{{adjustmentFinalObject.createdBy}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Create Comment : </b>{{adjustmentFinalObject.createComment}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Create Comment Text : </b>{{adjustmentFinalObject.createCommentText}}</div>
                </div>
                <div class="form-group">
                    <label class="control-label">
                        {{type}} Reason
                    </label> <select class="form-control" data-ng-model="adjustmentObject.Comment"
                                     data-ng-options="response for response in adjustmentResponse"
                                     required="required"
                                     data-ng-change="adjustmentObject.Comment">
                </select>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <label class="control-label">{{type}} Comment </label>
                        <textarea style="width: 100%;" rows="5" data-ng-model="adjustmentObject.CommentText"
                                  required="required"
                                  data-ng-change="adjustmentObject.CommentText">
					</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                <button type="button" class="btn btn-danger pull-left" data-dismiss="modal" data-ng-click="resetValue()">Close</button>
                <button type="button" class="btn btn-primary" data-toggle="modal"
                        data-ng-disabled="adjustmentObject.CommentText==null || adjustmentObject.Comment==null"
                        data-target="#adjustmentFinal" data-dismiss="modal">{{type}}</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="adjustmentFinal" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><b>{{type}}</b></h4>
            </div>
            <div class="modal-body">
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Adjustment Id : </b>{{adjustmentFinalObject.adjustmentId}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>PnL Header : </b> {{adjustmentFinalObject.pnlHeaderDetail}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Adjustment Type : </b>{{adjustmentFinalObject.adjustmentType}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Adjustment Value : </b>{{adjustmentFinalObject.adjustmentValue}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Unit Id : </b>{{adjustmentFinalObject.unitId}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Status : </b>{{adjustmentFinalObject.status}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Created By : </b>{{adjustmentFinalObject.createdBy}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Create Comment : </b>{{adjustmentFinalObject.createComment}}</div>
                </div>
                <div class="row row-spacing">
                    <div class="col-xs-6"><b>Create Comment Text : </b>{{adjustmentFinalObject.createCommentText}}</div>
                </div>
                <div class="row row-spacing" data-ng-if="type!='VIEW'">
                    <div class="col-xs-6"><b>{{type}} Comment : </b>{{adjustmentObject.Comment}}</div>
                </div>
                <div class="row row-spacing" data-ng-if="type!='VIEW'">
                    <div class="col-xs-6"><b>{{type}} Comment Text : </b>{{adjustmentObject.CommentText}}</div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary pull-left" data-toggle="modal" data-ng-if="type!='VIEW'"
                            data-target="#adjustmentModal" data-dismiss="modal" data-dismiss="modal" >BACK</button>
                    <button type="button" class="btn btn-primary" data-ng-if="type!='VIEW'" data-dismiss="modal" data-ng-click="updatePnLStatus()">{{type}}</button>
                </div>
            </div>
        </div>
    </div>
</div>
