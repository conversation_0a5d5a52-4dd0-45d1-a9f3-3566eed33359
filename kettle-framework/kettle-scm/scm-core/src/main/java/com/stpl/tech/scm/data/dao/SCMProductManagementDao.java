package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.PackagingDefinitionData;
import com.stpl.tech.scm.data.model.ProductFulfillmentTypeData;
import com.stpl.tech.scm.data.model.ProductPackagingMappingData;
import com.stpl.tech.scm.data.model.SkuAttributeValueData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.SkuPackagingMappingData;
import com.stpl.tech.scm.data.model.UnitSkuVendorMapping;
import com.stpl.tech.scm.domain.model.PackagingType;
import com.stpl.tech.scm.domain.model.UnitProductPackagingMapping;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 07-05-2016.
 */
public interface SCMProductManagementDao extends SCMAbstractDao {

    public List<ProductFulfillmentTypeData> getProductFulfillmentTypes(int productId);

    public List<ProductPackagingMappingData> getPackagingMappingsForProduct(int productId);

    public List<SkuDefinitionData> getSkuAgainstProduct(int productId);

    public List<SkuPackagingMappingData> getPackagingMappingsForSku(int skuId);

	public SkuAttributeValueData fetchSkuAttributeByType(int skuId, int attributeId);

    public List<UnitSkuVendorMapping> getUnitSkuVendorMappingsByUnitId(int unitId);

    public void updateIsDefaultFlag(Integer skuId, Integer productId);

    PackagingDefinitionData findByPackagingTypeAndPackagingCode(PackagingType packagingType, String packagingCode);

    public List<UnitProductPackagingMapping> findPackagingByUnitId(int unitId);

    public List<Object[]> getUnitSkuPackagingMappings(int unitId);
}
