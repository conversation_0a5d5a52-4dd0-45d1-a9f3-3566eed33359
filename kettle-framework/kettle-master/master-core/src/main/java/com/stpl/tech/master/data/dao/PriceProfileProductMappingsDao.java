package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.PriceProfileProductMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface PriceProfileProductMappingsDao extends JpaRepository<PriceProfileProductMapping,Integer> {
    @Query("SELECT p FROM PriceProfileProductMapping p " +
            "WHERE (:versionNo IS NULL OR p.version = :versionNo) " +
            "AND (:profileId IS NULL OR p.priceProfileData.priceProfileDataId = :profileId) " +
            "AND (:productId IS NULL OR p.productId = :productId) " +
            "AND (:dimensionId IS NULL OR p.dimensionCode.rlId = :dimensionId)" +
            " and p.status  =  'ACTIVE' ")
    List<PriceProfileProductMapping> filterMappings(
            @Param("versionNo") Integer versionNo,
            @Param("profileId") Integer profileId,
            @Param("productId") Integer productId,
            @Param("dimensionId") Integer dimensionId);


    @Query("SELECT p FROM PriceProfileProductMapping p \n" +
            "WHERE  p.version IN :versionNos \n" +
            "  AND p.priceProfileData.priceProfileDataId IN :profileIds ")
    List<PriceProfileProductMapping> filterMappingsByProfileAndVersion(
            @Param("versionNos") List<Integer> versionNos,
            @Param("profileIds") List<Integer> profileIds);
}
