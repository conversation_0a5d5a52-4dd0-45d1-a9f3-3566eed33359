package com.stpl.tech.neo.core.service.impl;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.neo.core.dao.CartDao;
import com.stpl.tech.neo.core.dao.CustomerDao;
import com.stpl.tech.neo.core.dao.DeviceDao;
import com.stpl.tech.neo.core.service.BlankCartService;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.domain.model.mongo.CartDetail;
import com.stpl.tech.neo.domain.model.mongo.CartStatus;
import com.stpl.tech.neo.domain.model.mongo.Customer;
import com.stpl.tech.neo.domain.model.mongo.DeviceDetail;
import com.stpl.tech.neo.domain.model.mongo.Order;
import com.stpl.tech.neo.domain.model.mongo.SettlementType;

@Service
public class BlankCartServiceImpl implements BlankCartService {

	@Autowired
	private CartDao cartDao;
	@Autowired
	private DeviceDao deviceDao;
	@Autowired
	private CustomerDao customerDao;

	@Override
	public CartDetail createBlankCart(String customerId, String deviceId, String sessionId) {
		CartDetail cart = new CartDetail();
		cart.setCreationTime(NeoUtil.getCurrentTimestamp());
		cart.setCustomerId(customerId);
		cart.setDeviceId(deviceId);
		cart.setSessionId(sessionId);
		cart.setOrderDetail(new Order());
		cart.getOrderDetail().setWebCustomerId(customerId);
		cart.getOrderDetail().setSettlementType(SettlementType.DEBIT);
		cart.setCartStatus(CartStatus.CREATED);
		cart = cartDao.save(cart);
		// set to device
		if (deviceId != null) {
			Optional<DeviceDetail> device = deviceDao.findById(deviceId);
			if (device.isPresent()) {
				device.get().setLastCartId(cart.getCartId());
				deviceDao.save(device.get());
			}
		}
		// set to customer
		if (customerId != null) {
			Optional<Customer> c = customerDao.findById(customerId);
			if (c.isPresent()) {
				c.get().setCartId(cart.getCartId());
				customerDao.save(c.get());
			}
		}
		return cart;
	}

}