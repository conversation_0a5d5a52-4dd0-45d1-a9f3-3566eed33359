/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * MigrationLookupData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "MIGRATION_LOOKUP_DATA")
public class MigrationLookupData implements java.io.Serializable {

	private int lookupDataId;
	private String lookupType;
	private String lookupText;
	private Integer cumulativePoints;
	private Integer acquiredPoints;
	private Integer registrationUnitId;
	private String firstName;
	private String isActive;

	public MigrationLookupData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "LOOKUP_DATA_ID", unique = true, nullable = false)
	public int getLookupDataId() {
		return lookupDataId;
	}

	public void setLookupDataId(int lookupDataId) {
		this.lookupDataId = lookupDataId;
	}

	@Column(name = "LOOKUP_TYPE", length = 10, nullable = false)
	public String getLookupType() {
		return lookupType;
	}

	public void setLookupType(String lookupType) {
		this.lookupType = lookupType;
	}

	@Column(name = "LOOKUP_TEXT", length = 50, nullable = false)
	public String getLookupText() {
		return lookupText;
	}

	public void setLookupText(String lookupText) {
		this.lookupText = lookupText;
	}

	@Column(name = "CUMULATIVE_POINTS", nullable = true)
	public Integer getCumulativePoints() {
		return cumulativePoints;
	}

	public void setCumulativePoints(Integer cumulativePoints) {
		this.cumulativePoints = cumulativePoints;
	}

	@Column(name = "ACQUIRED_POINTS", nullable = true)
	public Integer getAcquiredPoints() {
		return acquiredPoints;
	}

	public void setAcquiredPoints(Integer acquiredPoints) {
		this.acquiredPoints = acquiredPoints;
	}

	@Column(name = "IS_ACTIVE", length = 1, nullable = false)
	public String getIsActive() {
		return isActive;
	}

	public void setIsActive(String isActive) {
		this.isActive = isActive;
	}

	@Column(name = "FIRST_NAME", length = 50, nullable = false)
	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	@Column(name = "REGISTRATION_UNIT_ID", nullable = true)
	public Integer getRegistrationUnitId() {
		return registrationUnitId;
	}

	public void setRegistrationUnitId(Integer registrationUnitId) {
		this.registrationUnitId = registrationUnitId;
	}
}
