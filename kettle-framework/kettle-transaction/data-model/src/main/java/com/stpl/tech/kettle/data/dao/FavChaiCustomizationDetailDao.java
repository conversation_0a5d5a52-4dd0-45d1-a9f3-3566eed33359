package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import com.stpl.tech.kettle.data.model.FavChaiCustomizationDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface FavChaiCustomizationDetailDao extends JpaRepository<FavChaiCustomizationDetail, Integer> {

    @Query("SELECT fccd FROM FavChaiCustomizationDetail fccd " +
            "LEFT JOIN FETCH fccd.customerFavChaiMapping cfcm " +
            "WHERE fccd.source = :source " +
            "AND fccd.uom = :uom " +
            "AND fccd.type = :type " +
            "AND fccd.dimension IS NULL " +
            "AND cfcm IN :customerFavChaiMapping")
    List<FavChaiCustomizationDetail> findAllBySourceAndUomAndTypeAndDimensionIsNullAndCustomerFavChaiMapping(
            @Param("source") String source,
            @Param("uom") String uom,
            @Param("type") String type,
            @Param("customerFavChaiMapping") List<CustomerFavChaiMapping> customerFavChaiMapping);

}

