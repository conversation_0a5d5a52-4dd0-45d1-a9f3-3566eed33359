/**
 * Created by Chaayos on 07-12-2016.
 */
import React from "react";
import { browserHistory } from "react-router";
import { connect } from "react-redux";
import MobileHeader from "./MobileHeader";
import * as CustomerActions from "../../actions/CustomerActions";
import * as UtilityActions from "../../actions/UtilityActions";
import * as LocalityActions from "../../actions/LocalityActions";
import appUtil from "../../AppUtil";
import trackUtils from "../../utils/TrackUtils";

@connect((store) => {
    return {
        redirectTo:store.customerReducer.redirectTo,
        criteria: store.localityReducer.criteria,
        selectedCity: store.localityReducer.selectedCity,
        selectedLocality: store.localityReducer.selectedLocality,
        sessionKey: store.customerReducer.sessionKey,
        campaignDetail:store.campaignReducer.campaignDetail
    };
})
export default class MobileNewAddressLayout extends React.Component {
    constructor(){
        super();
        this.state = {
        };
        this.saveAddress = this.saveAddress.bind(this);
        this.changeLocality = this.changeLocality.bind(this);
    }

    saveAddress(){
        var landmark = document.getElementById("addressLandmark").value;
        var addressLine1 = document.getElementById("addressLine1").value;
        var addrtype = null;
        var addressInputs = document.getElementsByClassName("addrTypeInput");
        for(var element in addressInputs){
            if(addressInputs[element].checked){
                addrtype = addressInputs[element].value;
            }
        };
        if(appUtil.checkEmpty(addressLine1)){
            this.props.dispatch(UtilityActions.showPopup("Please enter address details.","info"));
        }else if(addrtype==null){
            this.props.dispatch(UtilityActions.showPopup("Please select address type.","info"));
        }else{
            //var locationMetadata  = appUtil.getLocalityMetadata();
            this.props.dispatch(CustomerActions.addAddress(landmark, addressLine1, this.props.selectedLocality.label, this.props.selectedCity, addrtype, this.props.redirectTo));
        }
    }

    changeLocality(){
        browserHistory.push("/");
    }

    componentWillMount(){
        window.scrollTo(0, 0);
        if(appUtil.checkEmpty(this.props.sessionKey)){
            browserHistory.push("/login");
        }
        if (appUtil.checkEmpty(this.props.criteria)) {
            this.props.dispatch(LocalityActions.setCriteria("DELIVERY"));
            this.props.dispatch(UtilityActions.showPopup("Please select locality.","info", 2000));
            browserHistory.push("/");
        } else if (appUtil.checkEmpty(this.props.selectedCity) || appUtil.checkEmpty(this.props.selectedLocality)) {
            this.props.dispatch(UtilityActions.showPopup("Please select locality.","info", 2000));
            browserHistory.push("/");
        }
    }

    componentDidMount(){
        trackUtils.trackPageView({page:"addAddress",device:"mobile",custom:true});
    }

    render (){
        return(
            !appUtil.checkEmpty(this.props.selectedCity) && !appUtil.checkEmpty(this.props.selectedLocality)?(
                <div>
                    <div class="colouredHead">
                        <MobileHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                    </div>
                    <div class="mobilePageContainer">
                        <div class="mobilePageHead">Add a New Address</div>
                        <div class="newAddressInputContainer">
                            <input id="addressLine1" placeholder="Flat/House No." type="text" maxLength="200" />
                        </div>
                        <div class="newAddressInputContainer">
                            <div class="selectedLocWrapper ellipsis">
                                {!appUtil.checkEmpty(this.props.selectedLocality)?this.props.selectedLocality.label:""}, {this.props.selectedCity.city}
                            </div>
                            <div class="changeLocBtn" onClick={this.changeLocality.bind(this)}>Change</div>
                        </div>
                        {this.props.campaignDetail!=null && this.props.campaignDetail.city!=null?(
                            <div style={{padding:"10px"}}>Default locality has been selected already.
                                You can change it by clicking 'Change' link above.</div>
                        ):null}
                        <div class="newAddressInputContainer">
                            <input id="addressLandmark" placeholder="Landmark" type="text" maxLength="100" />
                        </div>
                        <div class="newAddressSubHead">Mark this as</div>
                        <div class="newAddressInputContainer text-center">
                            <div class="radioLabels">
                                <input type="radio" id="home" name="addressType" value="HOME" class="addrTypeInput" />
                                <label for="home"><span><span></span></span>Home</label>
                            </div>
                            <div class="radioLabels">
                                <input type="radio" id="office" name="addressType" value="OFFICE" class="addrTypeInput" />
                                <label for="office"><span><span></span></span>Office</label>
                            </div>
                            <div class="radioLabels">
                                <input type="radio" id="other" name="addressType" value="OTHER" class="addrTypeInput" />
                                <label for="other"><span><span></span></span>Other</label>
                            </div>
                        </div>
                        <div class="btn btn-primary" style={{marginTop:'30px'}} onClick={this.saveAddress.bind(this)}>Save Address</div>
                    </div>
                </div>
            ):null
        )
    }
}

