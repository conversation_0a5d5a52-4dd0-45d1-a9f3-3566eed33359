
html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,dl,dt,dd,ol,nav ul,nav li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}
article, aside, details, figcaption, figure,footer, header, hgroup, menu, nav, section {display: block;}
ol,ul{list-style:none;margin:0;padding:0;}
blockquote,q{quotes:none;}
blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}
table{border-collapse:collapse;border-spacing:0;}
/* start editing from here */
a{text-decoration:none;}
.txt-rt{text-align:right;}/* text align right */
.txt-lt{text-align:left;}/* text align left */
.txt-center{text-align:center;}/* text align center */
.float-rt{float:right;}/* float right */
.float-lt{float:left;}/* float left */
.clear{clear:both;}/* clear float */
.pos-relative{position:relative;}/* Position Relative */
.pos-absolute{position:absolute;}/* Position Absolute */
.vertical-base{	vertical-align:baseline;}/* vertical align baseline */
.vertical-top{	vertical-align:top;}/* vertical align top */
.underline{	padding-bottom:5px;	border-bottom: 1px solid #eee; margin:0 0 20px 0;}/* Add 5px bottom padding and a underline */
nav.vertical ul li{	display:block;}/* vertical menu */
nav.horizontal ul li{	display: inline-block;}/* horizontal menu */
img{max-width:100%;}
/*end reset*/
/*--login form start here--*/ 
body {
    background: rgba(0, 0, 0, 0) linear-gradient(to bottom, #50773e, #50773e, #50773e, #50773e,#50773e, #50773e) repeat scroll 0 0;
    font-family: "Open Sans",sans-serif;
    font-size: 100%;
    min-height: 880px;
    padding: 50px 0 30px;
}
.login {
  width: 28%;
  margin: 0 auto;
  background: #fff;
  padding: 25px 30px 35px 29px;
  border-radius: 4px;
}
h1 {
  font-size: 32px;
  font-weight: 600;
  color: #fff;
  text-align: center;
  margin: 0em 0em 1.5em 0em;
}
.login h2 {
  font-size: 25px;
  font-weight: 700;
  color: #000;
  display: inline-block;
}
.lock {
  display: inline-block;
  float: right;
  padding: 7px 7px 0px 0px;
}
.login p {
  font-size: 16px;
  font-weight: 400;
  color: black;
  margin: 10px 0px 0px 0px;
}
.button a {
  font-size: 15px;
  font-weight: 600;
  color: #fff;
  margin: 0px 13px 0px 0px;
  border-radius: 4px;
}
.button a.tw {
  background: #1DAEE3;
  padding: 10px 25px 10px 55px;
  position: relative;
}
.button a.fa {
  background:#3B5998;
  padding: 10px 25px 10px 53px;
  position: relative;
}
.button a.go {
  background:#D34836;
  padding: 10px 25px 10px 50px;
  position: relative;
  margin: 0px;
}
.button a.tw:hover{
  background:#1CA4D6;
}
.button a.fa:hover{
  background:#37528C;
}
.button a.go:hover{
  background:#C74534;
}
span.anc-tw {
  background: url(../images/twitter.png)no-repeat 10px 9px #1CA4D6;
  width: 26px;
  height: 19px;
  display: inline-block;
  padding: 10px 6px;
  position: absolute;
  top: 0%;
  left: 0%;
  border-radius: 4px 0px 0px 4px;
}
span.anc-fa {
  background: url(../images/facebook.png)no-repeat 6px 9px #37528C;
  width: 26px;
  height: 19px;
  display: inline-block;
  padding: 10px 6px;
  position: absolute;
  top: 0%;
  left: 0%;
  border-radius: 4px 0px 0px 4px;
}
span.anc-go {
  background: url(../images/google.png)no-repeat 9px 9px #C74534;
  width: 26px;
  height: 19px;
  display: inline-block;
  padding: 10px 6px;
  position: absolute;
  top: 0%;
  left: 0%;
  border-radius: 4px 0px 0px 4px;
}
.button {
  padding: 23px 0px 30px 0px;
  border-bottom: 1px solid #ccc;
  margin: 0px 0px 16px 0px;
}
.login input[type="text"] {
  outline: none;
  font-size: 14px;
  font-weight: 400;
  color: #ccc;
  padding: 10px 10px 10px 35px;
  margin: 15px 0px 0px 0px;
  width: 89%;
  border: 1px solid #ccc;                                                                                              
  -webkit-appearance: none;
}
.login input[type="text"]:hover,.login input.active {
    border:1px solid #2CA1CC;
    box-shadow: 0px 0px 1px 0px rgba(44, 161, 204, 0.7);
}
input.user{
  background: url(../images/user.png)no-repeat 7px 7px;
}
input.email{
  background: url(../images/email.png)no-repeat 7px 10px;
    }
input.phone{
  background: url(../images/phone.jpg)no-repeat 7px 10px;
  
  
}
.login input[type="password"] {
  background: url(../images/key.png)no-repeat 8px 7px;
  outline: none;
  font-size: 14px;
  font-weight: 400;
  color: #999;
  padding: 10px 10px 10px 35px;
  margin: 10px 0px 25px 0px;
  width: 89%;
  border: 1px solid #ccc;
  -webkit-appearance: none;
}
.login input[type="password"]:hover {
    border:1px solid #2CA1CC;
    box-shadow: 0px 0px 1px 0px rgba(44, 161, 204, 0.7);
}
a.acc {
   font-size: 14px;
  font-weight: 500;
  color: #fff;
  background: #50773e;
  padding: 8px 60px 8px 17px;
  border-radius: 4px;
  position:relative;
}
a.acc:hover {
  background:#50773e;
}
span.arrow {
  background: url(../images/arrow.png)no-repeat 10px 9px #50773e;
  width: 25px;
  height: 15px;
  display: inline-block;
  text-align: right;
  padding: 10px;
  position: absolute;
  top: 0%;
  right: 0%;
  border-radius: 0px 6px 6px 0px;
}
.copyright {
  padding: 150px 0px 0px 0px;
  text-align: center;
}
.copyright p {
  font-size: 15px;
  font-weight: 400;
  color: #fff;
}
.copyright p a{
  font-size: 15px;
  font-weight: 400;
  color: #D34836;;
}
.copyright p a:hover{
	color: #fff;
	 transition: 0.5s all;
  -webkit-transition: 0.5s all;
  -moz-transition: 0.5s all;
  -o-transition: 0.5s all;
}
/*--meadia quiries start here--*/
@media(max-width:1440px){
.login {
  width: 31%;
}
.button a {
  margin: 0px 7px 0px 0px;
}
.button a.go {
  padding: 10px 25px 10px 52px;
}
}
@media(max-width:1366px){
.button a {
  margin: 0px 3px 0px 0px;
}
.button a.fa {
  padding: 10px 20px 10px 50px;
}
.button a.go {
  padding: 10px 25px 10px 50px;
}
}
@media(max-width:1280px){
.login {
  width: 33.5%;
}
.copyright {
  padding: 100px 0px 0px 0px;
}
.button a.fa {
  padding: 10px 20px 10px 52px;
}
}
@media(max-width:1024px){
.login {
  width: 41.5%;
}
h1 {
  margin: 0em 0em 1em 0em;
}
.copyright {
  padding: 70px 0px 0px 0px;
}
.button a.go {
  padding: 10px 25px 10px 47px;
}
}
@media(max-width:768px){
.login {
  width: 55.5%;
}
body {
  padding: 130px 0px 75px 0px;
}
.copyright {
  padding: 200px 0px 0px 0px;
}
.login input[type="text"] {
  width: 88.4%;
}
.login input[type="password"] {
  width: 88.4%;
}
.button a.go {
  padding: 10px 25px 10px 47px;
}
}
@media(max-width:736px){
.button a.go {
  padding: 10px 15px 10px 45px;	
}
.button a.tw {
  padding: 10px 25px 10px 52px;
}
}
@media(max-width:667px){
.button a.go {
  padding: 10px 9px 10px 45px;
}
.button a.tw {
  padding: 10px 15px 10px 50px;
}
.button a.fa {
  padding: 10px 10px 10px 47px;
}
.button a {
  font-size: 14px;	
}
.login input[type="text"] {
  width: 87%;
}
.login input[type="password"] {
  width: 87%;
}
}
@media(max-width:640px){
.login h2 {
  font-size: 21px;	
}
.login {
  width: 63.5%;
}
span.anc-fa {
  padding: 9px 5px;
}
span.anc-go {
  padding: 9px 6px;
}
.button a {
  font-size: 13px;	
}
span.anc-tw {
  padding: 9px 6px;
}
.login input[type="text"] {
  width: 88%;
}
.login input[type="password"] {
  width: 88%;
}
body {
  padding: 75px 0px 75px 0px;
}
.copyright {
  padding: 100px 0px 0px 0px;
}
.button a.go {
  padding: 10px 20px 10px 58px;
}
.button a.fa {
  padding: 10px 20px 10px 55px;
}
.button a.tw {
  padding: 10px 25px 10px 60px;
}
}
@media(max-width:600px){
.button a.go {
  padding: 10px 17px 10px 50px;
}	
.button a.tw {
  padding: 10px 23px 10px 50px;
}
.button a.fa {
  padding: 10px 10px 10px 50px;
}
.login input[type="text"] {
  width: 87%;
}
.login input[type="password"] {
  width: 87%;
}
}
@media(max-width:568px){
.login input[type="text"] {
  width: 86%;
}
.login input[type="password"] {
  width: 86%;
}
.button a.go {
  padding: 10px 11px 10px 48px;
}	
.button a.tw {
  padding: 10px 15px 10px 50px;
}
.button a.fa {
  padding: 10px 10px 10px 46px;
}
}
@media(max-width:480px){
.button a.tw {
  padding: 10px 20px 10px 47px;
}	
.button a.fa {
  padding: 10px 20px 10px 55px;
}
.button a.go {
  background: #D34836;
  padding: 10px 20px 10px 50px;
}
.login {
  width: 79.5%;
}
.button {
  padding: 20px 0px 25px 0px;
  margin: 0px 0px 12px 0px;
}
span.arrow {
  padding: 9px;
}
.copyright {
  padding: 100px 0px 0px 0px;
}
h1 {
  font-size: 25px;
}
body {
  padding: 50px 0px 50px 0px;
}
.login input[type="text"] {
  width: 87%;
}
.login input[type="password"] {
  width: 87%;
}
}
@media(max-width:414px){
.button a {
  font-size: 12px;
}
span.anc-tw {
  padding: 9px 3px;
  background: url(../images/twitter.png)no-repeat 6px 9px #1CA4D6;
}
span.anc-fa {
  padding: 9px 3px;
}	
span.anc-go {
  background: url(../images/google.png)no-repeat 7px 9px #C74534;
  padding: 9px 3px;
}
.button a.tw {
  padding: 10px 10px 10px 41px;
}
.button a.fa {
  padding: 10px 10px 10px 40px;
}
.button a.go {
  padding: 10px 13px 10px 43px;
}
.login input[type="text"] {
  width: 85%;
}
.login input[type="password"] {
  width: 85%;
}
}
@media(max-width:384px){
.login {
  width: 81.5%;
}
.button a.go {
  padding: 10px 7px 10px 38px;
}	
.button a.fa {
  padding: 10px 8px 10px 38px;
}
.login input[type="text"] {
  width: 84%;
}
.login input[type="password"] {
  width: 84%;
}
}
@media(max-width:375px){
.button a.tw {
  padding: 10px 7px 10px 38px;
}
.button a.fa {
  padding: 10px 7px 10px 38px;
}
.button a.go {
  padding: 10px 6px 10px 37px;
}	
}
@media(max-width:320px){
.login {
  width: 84%;
  padding: 15px 18px 30px 18px;
}	
.login p {
  font-size: 12px;
  margin: 2px 0px 0px 0px;
}
.button a {
  margin: 7px 0px 7px 0px;
  display: block;
}
span.anc-tw {
  padding: 7px 6px;
}
span.anc-fa {
  padding: 7px 5px;
}
span.anc-go {
  padding: 7px 6px;
}
.button {
  padding: 10px 0px 15px 0px;
  margin: 0px 0px 10px 0px;
}
.button a.tw {
  padding: 8px 20px 8px 100px;
}
.button a.fa {
  padding: 8px 18px 8px 100px;
}
.button a.go {
  padding: 8px 20px 8px 100px;
}
.login h2 {
  font-size: 18px;
}
.lock {
  padding: 2px 2px 0px 0px;
}
.login input[type="text"] {
  background: url(../images/user.png)no-repeat 7px 5px;
  width: 81%;
  padding: 8px 10px 8px 35px;
  margin: 10px 0px 0px 0px;
  font-size: 12px;
}
.login input[type="password"] {
  width: 81%;
  padding: 8px 10px 8px 35px;
  margin: 10px 0px 19px 0px;
}
span.arrow {
  padding: 9px 6px;
}
a.acc {
  font-size: 12px;
  padding: 8px 45px 8px 10px;
  border-radius: 4px;
}
body {
  padding: 20px 0px 30px 0px;
}
.copyright p {
  font-size: 13px;
}
.copyright p a{
  font-size: 13px;
}
.copyright {
  padding: 25px 0px 0px 0px;
}
h1 {
  font-size: 22px;
}
body {
  min-height: 550px;
}
}