package com.stpl.tech.master.data.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "PRODUCT_GROUP_IMAGE_DATA")
public class ProductGroupImageData {

    private Integer productGroupImageId;
    private String iconName;
    private String iconDescription;
    private String iconUrl;
    private String status;
    private Integer createdBy;
    private Date creationTime;


    public ProductGroupImageData() {
    }

    public ProductGroupImageData(Integer productGroupImageId, String iconName, String iconDescription, String iconUrl, String status, Integer createdBy, Date creationTime) {
        this.productGroupImageId = productGroupImageId;
        this.iconName = iconName;
        this.iconDescription = iconDescription;
        this.iconUrl = iconUrl;
        this.status = status;
        this.createdBy = createdBy;
        this.creationTime = creationTime;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PRODUCT_GROUP_IMAGE_ID", unique = true, nullable = false)
    public Integer getProductGroupImageId() {
        return productGroupImageId;
    }

    public void setProductGroupImageId(Integer productGroupImageId) {
        this.productGroupImageId = productGroupImageId;
    }


    @Column(name = "ICON_NAME", nullable = false)
    public String getIconName() {
        return iconName;
    }

    public void setIconName(String iconName) {
        this.iconName = iconName;
    }

    @Column(name = "ICON_DESCRIPTION", nullable = false)
    public String getIconDescription() {
        return iconDescription;
    }

    public void setIconDescription(String iconDescription) {
        this.iconDescription = iconDescription;
    }

    @Column(name = "ICON_URL", nullable = false)
    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "CREATION_TIME", nullable = false)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }


}
