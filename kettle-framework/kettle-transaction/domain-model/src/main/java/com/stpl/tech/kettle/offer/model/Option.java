package com.stpl.tech.kettle.offer.model;

public class Option {
	private int optionId;
	private Integer option1;
	private Integer option2;
	private OptionType type;
	private boolean noDiscount = false;

	public Option() {
		super();
	}

	public Option(int optionId, Integer option1) {
		super();
		this.type = OptionType.REGULAR;
		this.optionId = optionId;
		this.option1 = option1;
	}

	public Option(OptionType type, int optionId, Integer option1) {
		super();
		this.type = type;
		this.optionId = optionId;
		this.option1 = option1;
	}

	public Option(int optionId, Integer option1, boolean noDiscount) {
		super();
		this.type = OptionType.REGULAR;
		this.optionId = optionId;
		this.option1 = option1;
		this.noDiscount = noDiscount;
	}

	public Option(int optionId, Integer option1, Integer option2) {
		super();
		this.type = OptionType.REGULAR;
		this.optionId = optionId;
		this.option1 = option1;
		this.option2 = option2;
	}

	public int getOptionId() {
		return optionId;
	}

	public void setOptionId(int optionId) {
		this.optionId = optionId;
	}

	public Integer getOption1() {
		return option1;
	}

	public void setOption1(Integer option1) {
		this.option1 = option1;
	}

	public Integer getOption2() {
		return option2;
	}

	public void setOption2(Integer option2) {
		this.option2 = option2;
	}

	public boolean isNoDiscount() {
		return noDiscount;
	}

	public void setNoDiscount(boolean noDiscount) {
		this.noDiscount = noDiscount;
	}

	public OptionType getType() {
		return type;
	}

	public void setType(OptionType type) {
		this.type = type;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + optionId;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Option other = (Option) obj;
		if (optionId != other.optionId)
			return false;
		return true;
	}

}
