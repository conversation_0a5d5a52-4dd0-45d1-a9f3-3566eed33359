package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DataEncryptionRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	private Integer startCustomerId;
	private Integer endCustomerId;
	private Integer batchSize;
	private Boolean refresh;
	private String type;
	private String salt;
	private String passCode;
	private String source;
	private String idAttr;
	private String emailAttr;
	private String contactAttr;

}
