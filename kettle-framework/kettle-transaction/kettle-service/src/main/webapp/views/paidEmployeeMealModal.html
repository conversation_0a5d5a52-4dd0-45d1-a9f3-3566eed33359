<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div
	class="modal-header"
	data-ng-init="init()">
	<div class="row">
		<div class="col-xs-6">
			<h3 style="margin: 0;">Employee Meal Order</h3>
		</div>
		<div class="col-xs-6">
			<button
				class='btn btn-danger pull-right'
				type="button"
				data-ng-click="goBack()">Cancel</button>
		</div>
	</div>
</div>
<div class="modal-body">
	<div class="container-fluid">
		<div class="row">
			<div class="col-xs-12">
				<select
					class="form-control"
					id="employeeData"
					name="employeeData"
					data-ng-model="emp"
					data-ng-change="getEmployeeData(emp)"
					data-ng-disabled="otherEmployeeIdFlag"
					data-ng-options="data as data.name + ' - ' + data.id for data in employeeDetails">
				</select>
			</div>
		</div>
		<div
			data-ng-if="selectedEmployee != null"
			style="padding-top: 10px">
			<div class="row">
				<div class="col-xs-12">
					<table class="table table-bordered table-responsive">
						<tbody>
							<tr>
								<td>Employee Name</td>
								<td>{{selectedEmployee.name}}</td>
							</tr>
							<tr>
								<td>Contact</td>
								<td>{{selectedEmployee.contactNumber}}</td>
							</tr>
							<tr>
								<td>Allowance Limit</td>
								<td>{{selectedEmployee.mealAllowanceLimit}}</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div
				class="row"
				data-ng-if="!otpRequested">
				<div class="col-xs-12 text-center">
					<button
						class='btn btn-default'
						data-ng-if="isValidContact()"
						type="button"
						data-ng-click="requestOTP()">Request OTP</button>
					<div
						class="alert alert-danger"
						data-ng-if="!isValidContact()">
						<strong>Contact Not Available!</strong> Please update contact at P&C portal or consult HR Team.
					</div>
				</div>
			</div>
			<div
				class="row"
				data-ng-if="otpRequested">
				<div class="col-xs-3"></div>
				<div class="col-xs-3">
					<input
						type="text"
						maxlength="4"
						class="form-control pull-right"
						data-ng-model="mealOTP"
						onkeypress='return event.charCode >= 48 && event.charCode <= 57'>
				</div>
				<div class="col-xs-6">
					<button
						class='btn btn-default'
						data-ng-class="{'disabled':mealOTP == null || mealOTP.length < 4}"
						data-ng-disabled="mealOTP == null || mealOTP.length < 4"
						type="button"
						data-ng-click="verifyOTP(mealOTP)">Verify OTP</button>
				</div>
			</div>
		</div>
		<div
			data-ng-if="selectedEmployee == undefined || selectedEmployee == null"
			class="text-center">
			<h4>Please select Employee.</h4>
		</div>
		<div
			class="alert alert-danger"
			style="margin-top: 10px;"
			data-ng-if="errorMessage != null">
			<b>{{errorMessage}}</b>
		</div>
	</div>
</div>
<div class="modal-footer"></div>