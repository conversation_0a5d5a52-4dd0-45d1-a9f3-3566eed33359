/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * OrderItem generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "SUBSCRIPTION_EVENT_ITEM")
public class SubscriptionEventItem implements java.io.Serializable {

	private Integer subscriptionEventItemId;
	private SubscriptionDetail subscriptionDetail;
	private String eventItemType;
	private int eventItemValue;
	private String eventItemStatus;

	public SubscriptionEventItem() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SUBSCRIPTION_EVENT_ITEM_ID", unique = true, nullable = false)
	public Integer getSubscriptionEventItemId() {
		return subscriptionEventItemId;
	}

	public void setSubscriptionEventItemId(Integer subscriptionEventItemId) {
		this.subscriptionEventItemId = subscriptionEventItemId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ID", nullable = false)
	public SubscriptionDetail getSubscriptionDetail() {
		return this.subscriptionDetail;
	}

	public void setSubscriptionDetail(SubscriptionDetail subscriptionDetail) {
		this.subscriptionDetail = subscriptionDetail;
	}

	@Column(name = "EVENT_ITEM_TYPE", nullable = false, length = 30)
	public String getEventItemType() {
		return this.eventItemType;
	}

	public void setEventItemType(String eventItemType) {
		this.eventItemType = eventItemType;
	}

	@Column(name = "EVENT_ITEM_VALUE", nullable = false)
	public int getEventItemValue() {
		return this.eventItemValue;
	}

	public void setEventItemValue(int eventItemValue) {
		this.eventItemValue = eventItemValue;
	}

	@Column(name = "EVENT_ITEM_STATUS", nullable = false, length = 15)
	public String getEventItemStatus() {
		return this.eventItemStatus;
	}

	public void setEventItemStatus(String eventItemStatus) {
		this.eventItemStatus = eventItemStatus;
	}

}
