/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.CASH_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import javax.xml.datatype.DatatypeConfigurationException;

import com.stpl.tech.kettle.core.service.CashManagementService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.PullPacket;
import com.stpl.tech.kettle.domain.model.PullPacketDenomination;
import com.stpl.tech.kettle.domain.model.PullSettlementDenomination;
import com.stpl.tech.kettle.domain.model.PullSettlementDetail;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + CASH_MANAGEMENT_ROOT_CONTEXT) // v1/cash-management
public class CashManagementResources extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(CashManagementResources.class);

	@Autowired
	private CashManagementService cashManagementService;

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private MasterDataCache masterCache;

	public static final String UNDERSCORE = "_";

	@RequestMapping(method = RequestMethod.POST, value = "pull/open/get", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<PullPacket> createPull(@RequestBody final Map map)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException {
		LOG.info("Geting open pulls for unitId :" + (int)map.get("unitId"));
		int unitId = (int)map.get("unitId");
		Integer paymentMode = map.get("paymentModeId") != null ? (int)map.get("paymentModeId") : null;
		Integer resultCount = map.get("resultCount") != null ? (int)map.get("resultCount") : null;
		List<String> statusList = (ArrayList<String>)map.get("statusList");
		List<PullPacket> pull = cashManagementService.getOpenPullsForUnit(unitId, paymentMode, resultCount, statusList);
		return pull;
	}

	@RequestMapping(method = RequestMethod.POST, value = "pull/submit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public PullPacket submitPull(@RequestBody final PullPacket pullPacket)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException {
		LOG.info("Submitting pull for pullId :" + pullPacket.getPullPacketId());
		return cashManagementService.submitPull(pullPacket);
	}

	@RequestMapping(method = RequestMethod.POST, value = "pull/transfer/get", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<PullPacket> getUnitPullsForTransfer(@RequestBody final Integer unitId)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException {
		return cashManagementService.getUnitPullsForTransfer(unitId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "pull/transfer", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public PullSettlementDetail transferPull(@RequestBody final PullSettlementDetail pullSettlementDetail)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException {
		if (!cashManagementService.validatePullTransferDate(pullSettlementDetail.getSettlementDate(), pullSettlementDetail.getSettlementUnit().getId(), pullSettlementDetail.getSettlementType().getId())) {
			LOG.info("Invalid settlement Date for unit with id {} ", pullSettlementDetail.getSettlementUnit().getId());
			return null;
		}
		return cashManagementService.transferPull(pullSettlementDetail);
	}

	@RequestMapping(method = RequestMethod.POST, value = "paymentMode/denominations", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<PullSettlementDenomination> getDenominationsForPaymentMode(@RequestBody final PaymentMode paymentMode)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException {
		return cashManagementService.getPullSettlementDenominationsForPaymentMode(paymentMode);
	}

	@RequestMapping(method = RequestMethod.POST, value = "pullSettlements/open/get", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<PullSettlementDetail> getOpenPullSettlements(
			@SuppressWarnings("rawtypes") @RequestBody final Map request) throws AuthenticationFailureException,
			DataUpdationException, DataNotFoundException, DatatypeConfigurationException {
		int unitId = Integer.parseInt(request.get("unitId").toString());
        int settlementId = 0;
		if(request.containsKey("settlementId")){
            settlementId = Integer.parseInt(request.get("settlementId").toString());
        }
		return cashManagementService.getOpenPullSettlements(unitId,settlementId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "pullSettlements/open/getByType", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<PullSettlementDetail> getOpenPullSettlementsByType(
			@SuppressWarnings("rawtypes") @RequestBody final Map request) throws AuthenticationFailureException,
			DataUpdationException, DataNotFoundException, DatatypeConfigurationException {
		int settlementTypeId = Integer.parseInt(request.get("settlementTypeId").toString());
		return cashManagementService.getOpenPullSettlementsByType(settlementTypeId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "pullSettlements/get", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<PullSettlementDetail> getPullSettlements(@SuppressWarnings("rawtypes") @RequestBody final Map request)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException,
			DatatypeConfigurationException {
		Date startDate = AppUtils.parseDate(request.get("startDate").toString());
		Date endDate = AppUtils.parseDate(request.get("endDate").toString());
		int unitId = Integer.parseInt(request.get("unitId").toString());
		int start = Integer.parseInt(request.get("start").toString());
		int batchSize = Integer.parseInt(request.get("batchSize").toString());
		return cashManagementService.getPullSettlements(startDate, endDate, unitId, false, false,start,batchSize);
	}

	@RequestMapping(method = RequestMethod.POST, value = "pullSettlements/enrichPull", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public PullSettlementDetail getPullPackets(@SuppressWarnings("rawtypes") @RequestBody final int settlementId)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException,
			DatatypeConfigurationException {
		return cashManagementService.getPullSettlement(settlementId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "pullSettlements/getByType", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<PullSettlementDetail> getPullSettlementsByType(@SuppressWarnings("rawtypes") @RequestBody final Map request)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException,
			DatatypeConfigurationException {
		Date startDate = AppUtils.parseDate(request.get("startDate").toString());
		Date endDate = AppUtils.parseDate(request.get("endDate").toString());
		int settlementTypeId = Integer.parseInt(request.get("settlementTypeId").toString());
		int start = Integer.parseInt(request.get("start").toString());
		int batchSize = Integer.parseInt(request.get("batchSize").toString());
		return cashManagementService.getPullSettlementsByType(startDate, endDate, settlementTypeId,start,batchSize);
	}
	@RequestMapping(method = RequestMethod.POST, value = "pullSettlement/close", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public PullSettlementDetail closePullSettlement(@RequestBody final PullSettlementDetail request)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException, DatatypeConfigurationException {
		return cashManagementService.closePullSettlement(request);
	}

	@RequestMapping(method = RequestMethod.POST, value = "couponDenominations/get", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<PullPacketDenomination> getCouponDenominations(@RequestBody final Map map)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException,
			DatatypeConfigurationException {
		int pullId = (int)map.get("pullId");
		return cashManagementService.getCouponDenominations(pullId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "transferCouponDenominations/get", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<PullPacketDenomination> transferCouponDenominations(@RequestBody final Map map)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException,
			DatatypeConfigurationException {
		List<Integer> pullList = (ArrayList<Integer>)map.get("pullId");
		int paymentModeId = (int)map.get("paymentModeId");
		return cashManagementService.getCouponDenominations(pullList, paymentModeId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "upload/slip", consumes = MediaType.MULTIPART_FORM_DATA, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Map<String, String> uploadSlip(@RequestParam(value = "file") MultipartFile file,
										  @RequestParam(value = "slipNumber") String slipNumber,
										  @RequestParam(value = "slipNumber") String serialNumber,
										  @RequestParam(value = "slipNumber") String ticketNumber,
										  @RequestParam(value = "unitId") String unitId,
										  @RequestParam(value = "slipDate") @DateTimeFormat(pattern = "MM/dd/yyyy") Date slipDate,
										  @RequestParam(value = "paymentMode") Integer paymentMode) {
		Map<String, String> path = new HashMap<>();
		if (!file.isEmpty() && cashManagementService.validatePullTransferDate(slipDate, Integer.parseInt(unitId), paymentMode)) {
			LOG.info("File Size is ::: {}", file.getSize());
			String filePath = unitId + File.separator + "settlements";
			try {
				String filename = masterCache.getUnitBasicDetail(Integer.parseInt(unitId)).getName() + UNDERSCORE + DateFormatUtils.format(slipDate, "dd-MM-yyyy") + UNDERSCORE + slipNumber + UNDERSCORE + serialNumber + UNDERSCORE + ticketNumber + ".jpg";
				path.put("receiptPath", AppUtils.write(file.getBytes(), props.getBasePath(), filePath, filename, LOG));
			} catch (IOException e) {
				LOG.error("Could not read file ::::", e);
			}
		}
		return path;
	}

	@RequestMapping(value = "download/slip", method = RequestMethod.GET)
	public void doDownload(@RequestParam(value = "id") String settlementId, HttpServletResponse response)
			throws IOException, AuthenticationFailureException {

		LOG.info("Downloading image for settlement id :::::: {}",settlementId);

		File downloadFile = new File(cashManagementService.getImagePath(settlementId));
		FileInputStream inputStream = new FileInputStream(downloadFile);
		response.setContentType(AppConstants.EXCEL_MIME_TYPE);
		response.setContentLength((int) downloadFile.length());

		// set headers for the response
		String headerKey = "Content-Disposition";
		String headerValue = String.format("attachment; filename=\"%s\"", downloadFile.getName());
		response.setHeader(headerKey, headerValue);

		// get output stream of the response
		OutputStream outStream = response.getOutputStream();

		byte[] buffer = new byte[4096];
		int bytesRead = -1;

		// write bytes read from the input stream into the output stream
		while ((bytesRead = inputStream.read(buffer)) != -1) {
			outStream.write(buffer, 0, bytesRead);
		}
		inputStream.close();
		outStream.close();
	}

	@RequestMapping(method = RequestMethod.POST, value = "pull/validDates/get", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Map<String, Object> getValidPullDates(@RequestBody final Map map) {
		return cashManagementService.getValidPullTransferDatesAsString((int)map.get("unitId"), (int)map.get("paymentMode"));
	}

	@RequestMapping(method = RequestMethod.POST, value = "pullSettlements/getTotalCount", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Long getPullSettlementsTotalCount(@SuppressWarnings("rawtypes") @RequestBody final Map request)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException,
			DatatypeConfigurationException {
		Date startDate = AppUtils.parseDate(request.get("startDate").toString());
		Date endDate = AppUtils.parseDate(request.get("endDate").toString());
		int unitId = Integer.parseInt(request.get("unitId").toString());
		return cashManagementService.getPullSettlementsTotalCount(startDate, endDate, unitId, false, false);
	}

	@RequestMapping(method = RequestMethod.POST, value = "pullSettlements/getByTypeTotalCount", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Long getByTypePullSettlementsTotalCount(@SuppressWarnings("rawtypes") @RequestBody final Map request)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException,
			DatatypeConfigurationException {
		Date startDate = AppUtils.parseDate(request.get("startDate").toString());
		Date endDate = AppUtils.parseDate(request.get("endDate").toString());
		int settlementTypeId = Integer.parseInt(request.get("settlementTypeId").toString());
		return cashManagementService.getByTypePullSettlementsTotalCount(startDate, endDate, settlementTypeId);
	}

}
