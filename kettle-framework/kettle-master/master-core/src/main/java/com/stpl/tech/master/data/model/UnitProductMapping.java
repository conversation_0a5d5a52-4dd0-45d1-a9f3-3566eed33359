/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

/**
 * UnitProductMapping generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "UNIT_PRODUCT_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = { "UNIT_ID", "PRODUCT_ID" }))
public class UnitProductMapping implements java.io.Serializable {

	private Integer unitProdRefId;
	private ProductDetail productDetail;
	private UnitDetail unitDetail;
	private String productStatus;
	private Date lastUpdateTmstmp;
	private Date productStartDate;
	private Date productEndDate;
	private List<UnitProductPricing> unitProductPricings = new ArrayList<>(0);

	public UnitProductMapping() {
	}

	public UnitProductMapping(ProductDetail productDetail, UnitDetail unitDetail, String productStatus,
			Date lastUpdateTmstmp, Date productStartDate, Date productEndDate) {
		this.productDetail = productDetail;
		this.unitDetail = unitDetail;
		this.productStatus = productStatus;
		this.lastUpdateTmstmp = lastUpdateTmstmp;
		this.productStartDate = productStartDate;
		this.productEndDate = productEndDate;
	}

	public UnitProductMapping(ProductDetail productDetail, UnitDetail unitDetail, String productStatus,
			Date lastUpdateTmstmp, Date productStartDate, Date productEndDate,
			List<UnitProductPricing> unitProductPricings) {
		this.productDetail = productDetail;
		this.unitDetail = unitDetail;
		this.productStatus = productStatus;
		this.lastUpdateTmstmp = lastUpdateTmstmp;
		this.productStartDate = productStartDate;
		this.productEndDate = productEndDate;
		this.unitProductPricings = unitProductPricings;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_PROD_REF_ID", unique = true, nullable = false)
	public Integer getUnitProdRefId() {
		return this.unitProdRefId;
	}

	public void setUnitProdRefId(Integer unitProdRefId) {
		this.unitProdRefId = unitProdRefId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODUCT_ID", nullable = false)
	public ProductDetail getProductDetail() {
		return this.productDetail;
	}

	public void setProductDetail(ProductDetail productDetail) {
		this.productDetail = productDetail;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ID", nullable = false)
	public UnitDetail getUnitDetail() {
		return this.unitDetail;
	}

	public void setUnitDetail(UnitDetail unitDetail) {
		this.unitDetail = unitDetail;
	}

	@Column(name = "PRODUCT_STATUS", nullable = false, length = 15)
	public String getProductStatus() {
		return this.productStatus;
	}

	public void setProductStatus(String productStatus) {
		this.productStatus = productStatus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TMSTMP", nullable = false, length = 19)
	public Date getLastUpdateTmstmp() {
		return this.lastUpdateTmstmp;
	}

	public void setLastUpdateTmstmp(Date lastUpdateTmstmp) {
		this.lastUpdateTmstmp = lastUpdateTmstmp;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "PRODUCT_START_DATE", nullable = false, length = 10)
	public Date getProductStartDate() {
		return this.productStartDate;
	}

	public void setProductStartDate(Date productStartDate) {
		this.productStartDate = productStartDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "PRODUCT_END_DATE", nullable = false, length = 10)
	public Date getProductEndDate() {
		return this.productEndDate;
	}

	public void setProductEndDate(Date productEndDate) {
		this.productEndDate = productEndDate;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitProductMapping")
	@OrderBy("refLookup.rlId asc")
	public List<UnitProductPricing> getUnitProductPricings() {
		return this.unitProductPricings;
	}

	public void setUnitProductPricings(List<UnitProductPricing> unitProductPricings) {
		this.unitProductPricings = unitProductPricings;
	}

}
