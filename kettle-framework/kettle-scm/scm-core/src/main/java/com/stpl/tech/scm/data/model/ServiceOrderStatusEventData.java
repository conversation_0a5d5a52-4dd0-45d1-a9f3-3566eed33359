package com.stpl.tech.scm.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "SERVICE_ORDER_STATUS_EVENT")
public class ServiceOrderStatusEventData {

	private Integer statusEventId;
	private String fromStatus;
	private String toStatus;
	private Integer serviceOrderId;
	private String transitionStatus;
	private Date updateTime;
	private Integer updatedBy;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SERVICE_ORDER_STATUS_EVENT_ID", nullable = false)
	public Integer getStatusEventId() {
		return statusEventId;
	}

	public void setStatusEventId(Integer statusEventId) {
		this.statusEventId = statusEventId;
	}

	@Column(name = "FROM_STATUS", nullable = false)
	public String getFromStatus() {
		return fromStatus;
	}

	public void setFromStatus(String fromStatus) {
		this.fromStatus = fromStatus;
	}

	@Column(name = "TO_STATUS", nullable = false)
	public String getToStatus() {
		return toStatus;
	}

	public void setToStatus(String toStatus) {
		this.toStatus = toStatus;
	}

	@Column(name = "SERVICE_ORDER_ID", nullable = false)
	public Integer getServiceOrderId() {
		return serviceOrderId;
	}

	public void setServiceOrderId(Integer serviceOrderId) {
		this.serviceOrderId = serviceOrderId;
	}

	@Column(name = "TRANSITION_STATUS", nullable = false)
	public String getTransitionStatus() {
		return transitionStatus;
	}

	public void setTransitionStatus(String transitionStatus) {
		this.transitionStatus = transitionStatus;
	}

	@Column(name = "UPDATE_TIME", nullable = false)
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Column(name = "UPDATED_BY", nullable = false)
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}
}
