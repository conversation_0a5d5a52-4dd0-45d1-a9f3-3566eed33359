/**
 * Created by Chaayos on 14-12-2016.
 */
import React from "react";
import appUtil from "../AppUtil";
import DesktopStoreLocatorLayout from "./desktop/DesktopStoreLocatorLayout";
import MobileStoreLocatorLayout from "./mobile/MobileStoreLocatorLayout";

export default class StoreLocatorLayout extends React.Component {
    render (){
        if (appUtil.isMobile()) {
            return (
                <MobileStoreLocatorLayout props={this.props} />
            )
        } else {
            return (
                <DesktopStoreLocatorLayout props={this.props} />
            )
        }
    }
}