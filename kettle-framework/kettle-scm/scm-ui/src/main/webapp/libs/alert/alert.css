.modal-overlay{
    position: fixed;
    left:0;
    right:0;
    top:0;
    bottom:0;
    z-index:10099;
    background-color: #0a0a0a;
    opacity:0.8;
}

#materialModalCentered, #fileUploadModalCentered {
    position: fixed;
    top: 50%;
    left: 50%;
    width: 30em;
	min-height: 18em;
	display: table-cell;
    vertical-align: middle;
    z-index: 10400;
    margin-top: -9em;
    margin-left: -15em;
    border: 1px solid #ccc;
    background-color: #f3f3f3;
}

#materialModalContent, #fileUploadModalContent {
    padding: 10px;
    position: relative;
    width: 30em;
	min-height: 18em;
    margin: auto;
    box-sizing: border-box;
    overflow-y: auto;
}

#materialModalTitle {
    margin: 10px;
    font-weight: bold;
    font-size: 1.2em;
}

#materialModalText {
    margin: 20px 10px 40px;
    line-height:24px;
    height: 200px;
    overflow: auto;
}

#fileUploadButtons .btn{
    float:right;
    margin-right:3px;
}

#materialModalButtons,#fileUploadButtons {
    width:calc(100% - 20px);
    bottom:0px;
    position: absolute;
}

.materialModalButton {
    margin: 10px;
    font-weight: bold;
    cursor: pointer;
    text-align: center;
    float: right;
    text-transform: uppercase;
    padding: 10px;
}

#materialModal.hide {
    opacity: 0;
    transition: opacity 0.2s ease-out;
    pointer-events: none;
}

#materialModal.hide #materialModalCentered {
    transform: scale(0);
    transition: transform 0.2s ease-out;
}

#materialModal.show {
    opacity: 1;
    transition: opacity 0.2s ease-in;
}

#materialModal.show #materialModalCentered {
    transform: scale(1);
    transition: transform 0.2s ease-in;
}
