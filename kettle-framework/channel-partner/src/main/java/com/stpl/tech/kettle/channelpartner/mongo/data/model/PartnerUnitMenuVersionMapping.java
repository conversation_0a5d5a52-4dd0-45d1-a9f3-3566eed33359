package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@Document
@XmlRootElement(name = "PartnerUnitMenuVersionMapping")
public class PartnerUnitMenuVersionMapping {

	@Id
	private String id;
	private String version;
	private String partnerName;
	private Integer partnerId;
	private Integer unitId;
	private Date addTime;
	private String addTimeIST;
	private Integer employeeId;
	private String employeeName;
	private Integer brandId;
	private MenuType menuType;
	private String status;
	private Integer menuSequenceId;
	private String menuSequenceName;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getPartnerName() {
		return partnerName;
	}

	public void setPartnerName(String partnerName) {
		this.partnerName = partnerName;
	}

	public Integer getPartnerId() {
		return partnerId;
	}

	public void setPartnerId(Integer partnerId) {
		this.partnerId = partnerId;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	public Integer getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(Integer employeeId) {
		this.employeeId = employeeId;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public MenuType getMenuType() {
		return menuType;
	}

	public void setMenuType(MenuType menuType) {
		this.menuType = menuType;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getMenuSequenceId() {
		return menuSequenceId;
	}

	public void setMenuSequenceId(Integer menuSequenceId) {
		this.menuSequenceId = menuSequenceId;
	}

	public String getMenuSequenceName() {
		return menuSequenceName;
	}

	public void setMenuSequenceName(String menuSequenceName) {
		this.menuSequenceName = menuSequenceName;
	}

	public String getAddTimeIST() {
		return addTimeIST;
	}

	public void setAddTimeIST(String addTimeIST) {
		this.addTimeIST = addTimeIST;
	}
}
