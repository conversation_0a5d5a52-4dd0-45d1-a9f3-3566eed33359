package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "EMPLOYEE_COST_CENTER_MAPPING")
public class EmployeeCostCenterMappingData {

	private Integer mappingId;
	private Integer employeeId;
	private Integer costCenterId;
	private String mappingStatus;

	private String addedBy;

	private Date addTime;

	private String removedBy;

	private Date removedTime;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "MAPPING_ID")
	public Integer getMappingId() {
		return mappingId;
	}

	public void setMappingId(Integer mappingId) {
		this.mappingId = mappingId;
	}

	@Column(name = "EMPLOYEE_ID")
	public Integer getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(Integer employeeId) {
		this.employeeId = employeeId;
	}

	@Column(name = "COST_CENTER_ID")
	public Integer getCostCenterId() {
		return costCenterId;
	}

	public void setCostCenterId(Integer costCenterId) {
		this.costCenterId = costCenterId;
	}

	@Column(name = "MAPPING_STATUS")
	public String getMappingStatus() {
		return mappingStatus;
	}

	public void setMappingStatus(String mappIngStatus) {
		this.mappingStatus = mappIngStatus;
	}

	@Column(name = "ADDED_BY")
	public String getAddedBy() {
		return addedBy;
	}

	public void setAddedBy(String addedBy) {
		this.addedBy = addedBy;
	}


	@Column(name = "ADD_TIME")
	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}


	@Column(name = "REMOVED_BY")
	public String getRemovedBy() {
		return removedBy;
	}



	public void setRemovedBy(String removedBy) {
		this.removedBy = removedBy;
	}

	@Column(name = "REMOVED_TIME")
	public Date getRemovedTime() {
		return removedTime;
	}

	public void setRemovedTime(Date removedTime) {
		this.removedTime = removedTime;
	}
}
