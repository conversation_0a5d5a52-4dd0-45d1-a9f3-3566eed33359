/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('fullOrderComplimentary',
    ['$scope', '$modalInstance', function ($scope, $modalInstance) {

		$scope.orderComplimentaryDetail = $scope.fullComplimentaryDetail;
		var defaultComplimentaryObj = {
	            isOrderComplimentary:false,
	            reasonCode:null,
	            reason:null
	        };
	
		if($scope.orderComplimentaryDetail.reasonCode == null){
			$scope.orderComplimentaryDetail.reasonCode = $scope.transactionMetadata.complimentaryCodes.content.filter(function(reasonCode){
				return reasonCode.code == "UnsatisfiedCustomer";
			})[0].id;
		}
		
        $scope.submit = function(){
        	var isAccountable = false;
        	var isDefault = false;
            if($scope.orderComplimentaryDetail.isOrderComplimentary){
            	$scope.transactionMetadata.complimentaryCodes.content.forEach(function(complimentaryCode){
            		if($scope.orderComplimentaryDetail.reasonCode == complimentaryCode.id){
            			if(complimentaryCode.type == "ACCOUNTABLE"){
            				isAccountable = true; 
            			}
            		}else{
            			isDefault = true;
            		}
            	});                
            }else{
            	$scope.orderComplimentaryDetail = defaultComplimentaryObj; // resetting the complimentary object
            	isDefault = true;                                
            }
            setOrderComplimentary(isAccountable, isDefault);
            $scope.$parent.calculatePaidAmount();
            $modalInstance.close($scope.orderComplimentaryDetail);
        };

        function setOrderComplimentary(isAccountable,isDefault){
        	$scope.orderItemArray.forEach(function(orderItem){
            	orderItem.orderDetails.complimentaryDetail.isComplimentary = $scope.orderComplimentaryDetail.isOrderComplimentary;
            	orderItem.orderDetails.complimentaryDetail.reasonCode = $scope.orderComplimentaryDetail.reasonCode;
            	orderItem.orderDetails.complimentaryDetail.reason = $scope.orderComplimentaryDetail.reason;
            	orderItem.orderDetails.amount = isDefault ? 0 : getAmount(orderItem,isAccountable);
            	orderItem.orderDetails.discountDetail.promotionalOffer = orderItem.orderDetails.amount;
            	//console.log(orderItem);
            });
        	
        }
        
        function getAmount(orderItem,isAccountable){
        	var amount = isAccountable ? orderItem.orderDetails.price * orderItem.orderDetails.quantity : 0;
        	return amount;
        }
        
        $scope.cancel = function(){
            $modalInstance.dismiss();
        };

    }]);
