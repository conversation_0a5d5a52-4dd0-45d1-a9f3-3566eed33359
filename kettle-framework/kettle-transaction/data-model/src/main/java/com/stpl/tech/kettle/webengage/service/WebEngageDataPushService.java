package com.stpl.tech.kettle.webengage.service;

import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderMetadataDetail;
import com.stpl.tech.kettle.webengage.data.model.CustomerDataPushTrack;
import com.stpl.tech.kettle.webengage.data.model.OrderDataPushTrack;
import com.stpl.tech.kettle.webengage.domain.model.WebengageEvent;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * Created by Chaayos on 02-05-2017.
 */
public interface WebEngageDataPushService {

    public List<CustomerInfo> getCustomerBatch(Integer startUserId, Integer batchSize);

    public List<Integer> getSettledKettleOrderBatch(Integer startOrderId, Integer batchSize);

    public List<OrderMetadataDetail> getOrderMetadataDetail(Integer orderId);

    public WebengageEvent getOrderBatch(Integer orderId);

    public CustomerInfo getCustomerInfo(Integer id);

    public CustomerAddressInfo getCustomerAddressInfo(Integer id);

    public void persistTrackOrder(OrderDataPushTrack entity);

    public void persistTrackCustomer(CustomerDataPushTrack entity);

    public OrderDataPushTrack getStartingBizDateAndOrder();

    public List<OrderDetail> getOrdersByBizDate(Date bizDate);

    public List<LoyaltyScore> getCustomerLoyaltyScores(Collection<Integer> customerIds);

}
