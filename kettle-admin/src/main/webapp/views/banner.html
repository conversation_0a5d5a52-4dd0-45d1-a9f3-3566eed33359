<div id="notificationDiv" ng-init="init()" xmlns:70vh xmlns:70vh xmlns:70vh
     xmlns:max-height="http://www.w3.org/1999/xhtml">
    <div id="notificationDivDetail">
        <div class="row">
            <div class="col s8">
                <br>
                <h1 class="page-header">
                    Banner Management
                </h1>
            </div>
            <div class="col-lg-2">
                <button class="btn btn-primary" data-toggle="modal"
                        data-ng-click="openNewBannerModal()">
                    <i class="fa fa-plus fw"></i> Add Banner
                </button>
            </div>
        </div>
        <br>


        <br><br>

        <div class="form-group ">
            <button class="btn btn-primary"
                    data-ng-click="getBannerList()">
                <i class="fa fa-search fw"></i> Search
            </button>

        </div>
    </div>

    <br>
    <div class="row ">
        <div class="col-xs-12">
            <div class="row ">
                <div class="col-xs-12 overflow">
                    <table class="table table-striped table-bordered">
                        <thead style="background-color: #e7e7e7">
                        <th>Banner Type</th>
                        <th>Banner Title</th>
                        <th>Banner SubTitle</th>
                        <th>Banner Description</th>
                        <th>Banner Url</th>
                        <th>Image</th>
                        <th>Banner Activation Date</th>
                        <th>Banner Expiry Date</th>
                        <th>Banner Code</th>
                        <th>Status</th>
                        <th colspan="2" style="text-align:center">Action</th>
                        </thead>
                        <tbody>
                        <tr ng-repeat=" banners in bannerList">
                            <td>{{banners.type}}</td>
                            <td>{{banners.title}}</td>
                            <td>{{banners.subTitle}}</td>
                            <td width="12%">{{banners.desc}}</td>
                            <td>{{banners.imageUrl}}</td>
                            <td><img height="50px" width="50px"
                                     data-ng-click="openProductImageModal(banners.imageUrl)"
                                     ng-src="{{banners.imageUrl}}"></td>
                            <td>{{banners.start|date:'yyyy-MM-dd'}}</td>
                            <td>{{banners.exp|date:'yyyy-MM-dd'}}</td>
                            <td>{{banners.code}}</td>
                            <td>
                                <h4>
                                        <span
                                                data-ng-if="banners.status == 'ACTIVE'"
                                                class="label label-success">{{banners.status}}</span>
                                    <span
                                            data-ng-if="banners.status != 'ACTIVE'"
                                            class="label label-danger">{{banners.status}}</span>
                                </h4>

                            </td>
                            <td>
                                <button
                                        class="btn btn-primary"
                                        ng-click="editBanner(banners)">Edit
                                </button>
                            </td>
                            <td>
                                <button
                                        data-ng-class="{'btn btn-success':banners.status=='IN_ACTIVE', 'btn btn-danger':banners.status=='ACTIVE'}"
                                        data-ng-click="updateStatus(banners)">
                                    <span data-ng-if="banners.status == 'ACTIVE'">DEACTIVATE</span>
                                    <span data-ng-if="banners.status != 'ACTIVE'">ACTIVATE</span>
                                </button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!--add new notification-->
<div aria-labelledby="AddNewBannerModalLabel" class="modal fade" id="AddNewBannerModalData" role="dialog"
     data-keyboard="false" data-backdrop="static"
     tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button aria-label="Close" class="close" data-dismiss="modal" ng-click="reset()" type="button"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel_add"> Add New Banner</h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Banner Type</label>
                    <div class="form-group">
                        <select class="form-control" ng-model="banner.type"
                                ng-options="type for type in bannerType"
                                data-ng-change="selectingBannerType(banner.type)"
                        >
                        </select>
                    </div>
                </div>
                <div ng-show="section" class="form-group">
                    <label>Section Type</label>
                    <div class="form-group">
                        <select class="form-control" ng-model="banner.sectionType"
                                ng-options="type.fullName for type in bannerSectionType"

                        >
                        </select>
                    </div>
                </div>
                <div ng-show="banner.sectionType.shortName=='REG' && section">
                    <div class="form-group ">
                        <label>Category Id</label>
                        <input class="form-control" ng-model="banner.categoryId"
                               maxlength="10" type="number"/>
                    </div>
                    <div class="form-group ">
                        <label>Product Id</label>
                        <input class="form-control" ng-model="banner.productId"
                               maxlength="10" type="number"/>
                    </div>
                </div>

                <div class="form-group ">
                    <label>Banner Title</label>
                    <input class="form-control" ng-model="banner.title"
                           maxlength="500" type="text"/>
                </div>
                <div class="form-group ">
                    <label>Banner SubTitle</label>
                    <input class="form-control" ng-model="banner.subTitle"
                           maxlength="500" type="text"/>
                </div>
                <div class="form-group ">
                    <label>Banner Description</label>
                    <input class="form-control" ng-model="banner.desc"
                           maxlength="500" type="text"/>
                </div>


                <div class="form-group">
                    <label>Upload Image</label>
                    <input class="btn btn-default" file-model="fileToUpload" style="width: 100%;"
                           type="file">

                    <button class="btn btn-primary "
                            ng-click="uploadBanner()">Upload
                    </button>
                    <span ng-if="banner.imageUrl!=null" style="color: #1ca62c">Uploaded!</span>

                </div>

                <!--<div class="row divInnerRow">-->
                <!--<div class="col-xs-4">-->
                <!--<label style="color: #4f964f">Want Action Button</label>-->
                <!--</div>-->
                <!--<div class="col-xs-8">-->
                <!--<input type="checkbox" data-ng-model="setAction"-->
                <!--class="center-block"/>-->
                <!--</div>-->
                <!--</div>-->
                <!--<div ng-show="banner.type.needsActionType">-->
                <div  class="form-group">
                    <label>Action Type</label>
                    <div class="form-group">
                        <select class="form-control" ng-model="banner.buttonAction"
                                ng-options="type as type.actionType +'('+type.detail+')' for type in actionType"
                        >
                        </select>
                    </div>
                </div>
                <div  class="form-group ">
                    <label>Banner Button Text</label>
                    <input class="form-control" ng-model="banner.buttonText"
                           maxlength="500" type="text"/>
                </div>
                <!--</div>-->
                <div class="form-group ">
                    <label>Banner Code</label>
                    <input class="form-control" ng-model="banner.code"
                           maxlength="500" type="text"/>
                </div>
                <div class="form-group">
                    <label>Start Date</label>
                    <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}">
                        <input class="form-control" data-ng-model="banner.start" type="text"
                               placeholder="yyyy-MM-dd"/>
                    </div>
                </div>

                <div class="form-group">
                    <label>End Date</label>
                    <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{today}}">
                        <input class="form-control" data-ng-model="banner.exp" type="text"
                               placeholder="yyyy-MM-dd"
                        />
                    </div>
                </div>

                <div class="row ">
                    <div class="form-group" align="center">
                        <button class="btn btn-primary"
                                ng-click="saveBanner()">
                            Submit
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>


<!--image view modal-->
<div class="modal fade" id="displayImageModal" style="z-index: 9999;" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document" style="width: 700px;">
        <div class="modal-content">
            <div class="frame" style="margin-top: 40px;margin: auto;">
                <img style="    max-height: 70vh;max-width: 70vw;"
                     data-ng-src="{{imageSrc}}"/>
            </div>
        </div>
    </div>
</div>

