package com.stpl.tech.kettle.core.service.impl;

import com.google.common.hash.Hashing;
import com.google.gson.Gson;
import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.service.IngenicoPaymentService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.PaymentGatewayDao;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.kettle.delivery.model.PaymentPartner;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.payment.config.IngenicoConfig;
import com.stpl.tech.master.core.payment.config.PaytmConfig;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentResponse;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.payment.model.ingenico.*;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateQRResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import com.stpl.tech.master.payment.model.paytm.ResultInfo;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.notification.pubnub.PubnubService;
import com.stpl.tech.util.notification.pubnub.PushNotification;
import com.tp.pg.util.TransactionRequestBean;
import com.tp.pg.util.TransactionResponseBean;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.apache.http.util.EntityUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Hex;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by shikhar on 15/7/19.
 */

@Service
public class IngenicoPaymentServiceImpl implements IngenicoPaymentService {

    private static final Logger LOG = LoggerFactory.getLogger(IngenicoPaymentServiceImpl.class);

    @Autowired
    private IngenicoConfig props;

    @Autowired
    private EnvironmentProperties env;

    @Autowired
    private PaymentGatewayDao paymentGatewayDao;

    @Autowired
    private PubnubService pubnubService;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public IngenicoQrResponse createRequest(OrderPaymentRequest order) throws PaymentFailureException {
        IngenicoQrResponse ingenicoQrResponse = new IngenicoQrResponse();
        IngenicoQrRequest request = createRequestObject(order);
        LOG.info("Created request for ingenico payment: {}", JSONSerializer.toJSON(request));
        ingenicoQrResponse = encryptAndGenerateQr(request, order);
        if (paymentGatewayDao.checkIfIngenicoExist(order.getGenerateOrderId())) {
            paymentGatewayDao.updateIngenicoPaymentMode(order);
        } else {
            paymentGatewayDao.createRequest(request, order);
        }
        return ingenicoQrResponse;
    }

    /*
     * mvn install:install-file
     * -Dfile=/home/<USER>/builds/application/chaayos/kettle-framework/kettle-transaction/data-model/src/main/resources/ingenico-checksum-1.0.12.jar
     * -DgroupId=com.tp.pg -DartifactId=util -Dversion=1.0.12 -Dpackaging=jar
     *
     * */

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updatePaymentModeForOrder(OrderPaymentRequest order) throws Exception {
        paymentGatewayDao.updateIngenicoPaymentMode(order);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public IngenicoQrResponse checkIngenicoPaymentStatus(OrderPaymentRequest order) throws Exception {
        IngenicoQrResponse ingenicoQrResponse = new IngenicoQrResponse();
        IngenicoQrRequest request = createRequestObject(order);
        ingenicoQrResponse = encryptAndGenerateQr(request, order);
        PaymentStatus status = ingenicoQrResponse.getStatus().equals("SUCCESS") ? PaymentStatus.SUCCESSFUL : PaymentStatus.FAILED;
        paymentGatewayDao.updateResponse(status, ingenicoQrResponse);

        return ingenicoQrResponse;
    }

    @Override
    public Boolean checkIngenicoPaymentStatus(OrderPaymentDetail paymentDetail) {
        try {
            PaymentStatus status = getPaymentStatus(env.getEnvironmentType(), paymentDetail);
            return status.equals(PaymentStatus.SUCCESSFUL);
        } catch (PaymentFailureException pe) {
            return false;
        }
    }

    @Override
    public IngenicoQrResponse validateIngenicoCallback(String response) throws PaymentFailureException {
        try {
            IngenicoQrResponse ingenicoQrResponse = getValidatedResponse(response);
            Map map = paymentGatewayDao.updateAndRedirect( ingenicoQrResponse, ingenicoQrResponse.getStatus().equals("SUCCESS"));
            if(map.containsKey("orderId")){
                PushNotification<Map<String, PaymentResponse>> notification =
                        new IngenicoResponseNotification(env.getEnvironmentType(), ingenicoQrResponse, ingenicoQrResponse);
                pubnubService.sendNotification(notification);
            }
            return ingenicoQrResponse;
        } catch (PaymentFailureException e) {
            LOG.error("Error while validating response from ingenico :::::::: {}", e.getMessage());
            throw e;
        }
    }

    private IngenicoQrResponse encryptAndGenerateQr(IngenicoQrRequest request, OrderPaymentRequest order) {
        String encryptedRequest = null;
        try {
            encryptedRequest = encryptHx(JSONSerializer.toJSON(request), props.getIV().getBytes(), props.getSALT().getBytes());
            LOG.info("Encrypted request: {}", encryptedRequest);
        } catch (Exception e) {
            LOG.error("Exception caught while encrypting request");
        }
        if (encryptedRequest != null) {
            try {
                return createIngenicoQRForCRM(encryptedRequest, request.getMerchant().getIdentifier());
            } catch (Exception e) {
                LOG.error("Exception while generating QR for payment through Ingenico: ", e);
            }
        }
        return null;
    }

    @Override
    public IngenicoQrResponse getValidatedResponse(String encryptedResponse) throws PaymentFailureException {
        String decryptedResponse = null;
        IngenicoQrResponse ingenicoQrResponse = null;
        try {
            decryptedResponse = decryptHxT(encryptedResponse, props.getIV().getBytes(), props.getSALT().getBytes());
        } catch (Exception e) {
            LOG.error("Exception while decypting response from Ingenico: ", e);
        }
        if (decryptedResponse != null) {
            Gson gson = new Gson();
            ingenicoQrResponse = gson.fromJson(decryptedResponse, IngenicoQrResponse.class);
        }
        return ingenicoQrResponse;
    }

    @Override
    public OrderPayment refundRequest(EnvType envType, OrderPayment request) throws PaymentFailureException {
        IngenicoQrResponse ingenicoQrResponse = new IngenicoQrResponse();
        IngenicoQrRequest ingenicoQrRequest = createRefundRequest(request);
        ingenicoQrResponse = encryptAndGenerateQr(ingenicoQrRequest, null);
        LOG.info("ingenicoQrResponse refundRequest : {}", JSONSerializer.toJSON(ingenicoQrResponse));
        if (ingenicoQrResponse != null) {
            if (ingenicoQrResponse.getStatusCode().equals("0400")) {
                request.setPaymentStatus(PaymentStatus.REFUND_PROCESSED);
                request.setRefundStatus(PaymentStatus.CREATED);
                if (ingenicoQrResponse.getPaymentMethod() != null && ingenicoQrResponse.getPaymentMethod().getPaymentTransaction() != null) {
                    request.setRefundId(ingenicoQrResponse.getPaymentMethod().getPaymentTransaction().getRefundIdentifier());
                }
            } else {
                LOG.info("ingenico qr response status is not 0400");
                request.setPaymentStatus(PaymentStatus.REFUND_FAILED);
                request.setRefundStatus(PaymentStatus.REFUND_FAILED);
                if (ingenicoQrResponse.getPaymentMethod() != null && ingenicoQrResponse.getPaymentMethod().getPaymentTransaction() != null) {
                    request.setFailureReason(ingenicoQrResponse.getPaymentMethod().getPaymentTransaction().getErrorMessage());
                }
            }
        } else {
            request.setPaymentStatus(PaymentStatus.REFUND_FAILED);
        }
        return request;
    }

    @Override
    public PaymentStatus getPaymentStatus(EnvType type, OrderPaymentDetail request) throws PaymentFailureException {
        TransactionRequestBean requestBean = new TransactionRequestBean();
        requestBean.setStrRequestType("O");
        requestBean.setMerchantTxnRefNumber(request.getExternalOrderId());
        String txnAmt = request.getTransactionAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        requestBean.setStrAmount(txnAmt);
        requestBean.setStrTPSLTxnID(request.getPartnerTransactionId());
        requestBean.setStrShoppingCartDetails("FIRST_" + txnAmt + "_0.0");

        requestBean.setWebServiceLocator(props.getIngenicoUrl());
        requestBean.setStrMerchantCode(props.getMerchantId());
        requestBean.setKey(props.getSALT().getBytes());
        requestBean.setIv(props.getIV().getBytes());
        requestBean.setStrCurrencyCode(props.getCurrency());

        String token = requestBean.getTransactionToken();

        if (token.startsWith("ERROR")) {
            String message = "Error while get payment status for payment with id " + request.getPartnerTransactionId();
            PaymentFailureException e = new PaymentFailureException(message);
            LOG.error(message, token);
            SlackNotificationService.getInstance()
                    .sendNotification(type, "Kettle", null,
                            SlackNotification.REFUNDS.getChannel(type), message);
            new ErrorNotification("Payment Refund Failure", message, e, type).sendEmail();
            throw e;
        } else {
            IngenicoResponse response = new IngenicoResponse(token);
            String txnId = response.getTpslTxnId();
            return txnId != null ? PaymentStatus.SUCCESSFUL : PaymentStatus.FAILED;
        }
    }

    @Override
    public IngenicoQrResponse createIngenicoQRForCRM(String ingenicoEncryptedRequest, String merchantCode) throws Exception {
        StringBuilder endPoint = new StringBuilder(props.getIngenicoApiLinkUrl() + merchantCode);
        try {
            HttpPost httpPost = new HttpPost(endPoint.toString());
            httpPost.setHeader("content-type", "application/json");
            HttpEntity httpEntity = new StringEntity(ingenicoEncryptedRequest);
            httpPost.setEntity(httpEntity);
            LOG.info("Hitting ingenico API to generate QR");
            HttpResponse response = WebServiceHelper.postRequestWithNoTimeout(httpPost);
            LOG.info("response status code from ingenico after generating qr {}", response.getStatusLine().getStatusCode());
            if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
                HttpEntity entity = response.getEntity();
                String responseString = EntityUtils.toString(entity, "UTF-8");
                String decryptedResponse = decryptHxT(responseString, props.getIV().getBytes(), props.getSALT().getBytes());
                LOG.info("decypted response from ingenico: {}", decryptedResponse);
                IngenicoQrResponse qRResponse = JSONSerializer.toJSON(decryptedResponse, IngenicoQrResponse.class);
                LOG.info("Ingenico QR response createIngenicoQRForCRM {} ", JSONSerializer.toJSON(qRResponse));
                return qRResponse;
            } else {
                LOG.info("Response is other than 200 from ingenico ");
            }
        } catch (Exception ex) {
            LOG.error("Exception occured while generating Ingenico QR :: ", ex);
        }
        return null;
    }

    private IngenicoQrRequest createRefundRequest(OrderPayment order) {
        IngenicoQrRequest createRequest = new IngenicoQrRequest();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        LocalDate localDate = LocalDate.now();

        IngenicoTransaction ingenicoTransaction = new IngenicoTransaction();
        CustomerInfo customerInfo = new CustomerInfo();
        createRequest.setConsumer(customerInfo);
        createRequest.setTransaction(ingenicoTransaction);
        Cart cart = new Cart();
        CartItem cartItem = new CartItem();
        cartItem.setAmount(env.getEnvironmentType().equals("SPROD") ? order.getTransactionAmount() : new BigDecimal(1));
        cartItem.setIdentifier(props.getIdentifier());
        cart.setItem(cartItem);
        cart.setDescription("");
        createRequest.setCart(cart);

        IngenicoTransaction transaction = new IngenicoTransaction();
        transaction.setAmount(env.getEnvironmentType().equals("SPROD") ? order.getTransactionAmount() : new BigDecimal(1));
        transaction.setIdentifier(order.getExternalOrderId());
        transaction.setReference(order.getPartnerTransactionId());
        transaction.setDateTime(dtf.format(localDate));
        transaction.setRequestType("R");
        transaction.setCurrency("INR");
        createRequest.setTransaction(transaction);

        IngenicoPaymentRequestMethod paymentMethod = new IngenicoPaymentRequestMethod();
        paymentMethod.setToken(order.getPaymentModeName().equals("BHARAT_QR") ? props.getBharatqr() : props.getUpiQr());
        IngenicoPayment ingenicoPayment = new IngenicoPayment();
        ingenicoPayment.setMethod(paymentMethod);
        IngenicoPaymentInstrument instrument = new IngenicoPaymentInstrument();
        Expiry expiry = new Expiry();
        instrument.setExpiry(expiry);
        IngenicoPaymentHolderInfo ingenicoPaymentHolderInfo = new IngenicoPaymentHolderInfo();
        CustomerAddress address = new CustomerAddress();
        ingenicoPaymentHolderInfo.setAddress(address);
        instrument.setHolder(ingenicoPaymentHolderInfo);
        Issuance issuance = new Issuance();
        instrument.setIssuance(issuance);
        PaymentAuthentication paymentAuthentication = new PaymentAuthentication();
        instrument.setAuthentication(paymentAuthentication);
        ingenicoPayment.setInstrument(instrument);
        IngenicoPaymentInstruction instruction = new IngenicoPaymentInstruction();
        ingenicoPayment.setInstruction(instruction);
        createRequest.setPayment(ingenicoPayment);

        IngenicoMerchant ingenicoMerchant = new IngenicoMerchant();
        ingenicoMerchant.setIdentifier(props.getMerchantId());
        ingenicoMerchant.setResponseType("Server");
        ingenicoMerchant.setResponseEndpointURL(props.getCallbackUrl());
        ingenicoMerchant.setWebhookEndpointURL(props.getCallbackUrl());
        createRequest.setMerchant(ingenicoMerchant);
        return createRequest;
    }

    private IngenicoQrRequest createRequestObject(OrderPaymentRequest order) {
        IngenicoQrRequest createRequest = new IngenicoQrRequest();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        LocalDate localDate = LocalDate.now();

        IngenicoTransaction ingenicoTransaction = new IngenicoTransaction();
        CustomerInfo customerInfo = new CustomerInfo();
        createRequest.setConsumer(customerInfo);
        createRequest.setTransaction(ingenicoTransaction);
        Cart cart = new Cart();
        CartItem cartItem = new CartItem();
        cartItem.setAmount(env.getEnvironmentType().equals("SPROD") ? order.getPaidAmount() : new BigDecimal(1));
        cartItem.setIdentifier(props.getIdentifier());
        cart.setItem(cartItem);
        cart.setDescription(order.getPosId());
        createRequest.setCart(cart);

        IngenicoTransaction transaction = new IngenicoTransaction();
        transaction.setAmount(env.getEnvironmentType().equals("SPROD") ? order.getPaidAmount() : new BigDecimal(1));
        transaction.setIdentifier(order.getGenerateOrderId());
        transaction.setDateTime(dtf.format(localDate));
        transaction.setRequestType(order.getIngenicoRequestType());
        transaction.setCurrency("INR");
        createRequest.setTransaction(transaction);

        IngenicoPaymentRequestMethod paymentMethod = new IngenicoPaymentRequestMethod();
        paymentMethod.setToken(order.getPaymentModeName().equals("BHARAT_QR") ? props.getBharatqr() : props.getUpiQr());
        IngenicoPayment ingenicoPayment = new IngenicoPayment();
        ingenicoPayment.setMethod(paymentMethod);
        IngenicoPaymentInstrument instrument = new IngenicoPaymentInstrument();
        Expiry expiry = new Expiry();
        instrument.setExpiry(expiry);
        IngenicoPaymentHolderInfo ingenicoPaymentHolderInfo = new IngenicoPaymentHolderInfo();
        CustomerAddress address = new CustomerAddress();
        ingenicoPaymentHolderInfo.setAddress(address);
        instrument.setHolder(ingenicoPaymentHolderInfo);
        Issuance issuance = new Issuance();
        instrument.setIssuance(issuance);
        PaymentAuthentication paymentAuthentication = new PaymentAuthentication();
        instrument.setAuthentication(paymentAuthentication);
        ingenicoPayment.setInstrument(instrument);
        IngenicoPaymentInstruction instruction = new IngenicoPaymentInstruction();
        ingenicoPayment.setInstruction(instruction);
        createRequest.setPayment(ingenicoPayment);

        IngenicoMerchant ingenicoMerchant = new IngenicoMerchant();
        ingenicoMerchant.setIdentifier(props.getMerchantId());
        ingenicoMerchant.setResponseType("Server");
        ingenicoMerchant.setResponseEndpointURL(props.getCallbackUrl());
        ingenicoMerchant.setWebhookEndpointURL(props.getCallbackUrl());
        createRequest.setMerchant(ingenicoMerchant);
        return createRequest;
    }


    // Use for Hex output of Encrypted Data - Generic Function
    public static String encryptHx(String text, byte[] iv, byte[] key) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] results = cipher.doFinal(text.getBytes("UTF-8"));
        String hxencstr = ToHexStr(results);
        return hxencstr;
    }

    public static String ToHexStr(byte[] dataBuf) {
        String hxstr = null;
        Hex hx = new Hex();
        hxstr = new String(Hex.encodeHex(dataBuf));
        return hxstr;
    }

    // Use for Hex output Data - Generic Function, with Trimmed data
    public static String decryptHxT(String hxstrr, byte[] iv, byte[] key) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
        SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

        byte[] decbytes = FromHexStr(hxstrr);
        byte[] results = cipher.doFinal(decbytes);
        return new String(results, "UTF-8").trim();
    }


    public static byte[] FromHexStr(String hxStr) {
        byte[] charArray = hxStr.getBytes();
        byte[] bufstr = null;
        Hex hxx = new Hex();
        try {
            bufstr = hxx.decode(charArray);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bufstr;
    }


    private String generateHashToken(IngenicoCreateRequest request) {
        String data = request.getData();
        return getHash(data);
    }

    private String getHash(String requestString) {
        String hashToken = Hashing.sha256()
                .hashString(requestString, StandardCharsets.UTF_8)
                .toString();
        hashToken = hashToken.toUpperCase();
        return hashToken;
    }


}
