ALTER TABLE KETTLE_DEV.ORDER_DETAIL
ADD COLUMN TABLE_NUMBER INTEGER NULL;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN HAS_TABLE_SERVICE VARCHAR(1) NOT NULL DEFAULT 'N';

update <PERSON><PERSON><PERSON><PERSON>_MASTER_DEV.UNIT_DETAIL
set HAS_TABLE_SERVICE = 'Y'
where UNIT_ID = 10005;

update KET<PERSON>E_MASTER_DEV.UNIT_DETAIL
set NO_OF_TABLES = 24
where UNIT_ID = 10005;



#CLM TABLES AND CLM PROC


DROP TABLE IF EXISTS KETTLE_DUMP.CLM_DATA;

CREATE TABLE KETTLE_DUMP.CLM_DATA (
    CUSTOMER_ID INTEGER PRIMARY KEY,
    CONTACT_NUMBER VARCHAR(10),
    EMAIL_ID VARCHAR(100),
    FIRST_ORDER_ID INTEGER,
    LAST_ORDER_ID INTEGER,
    CURRENT_STATUS VARCHAR(15) NOT NULL DEFAULT 'IN_ACTIVE',
    OLD_STATUS VARCHAR(15) NOT NULL DEFAULT 'IN_ACTIVE',
    ACTIVITY_DAYS INTEGER,
    COUNT_OF_VISITS INTEGER,
    CURRENT_SEGMENT VARCHAR(10),
    DAYS_SINCE_LAST_VISIT INTEGER,
    COUNT_OF_UNIQUE_CAFE_VISITED INTEGER,
    FNB_RATIO DECIMAL(10 , 2 ),
    PEOPLE_PER_TICKET DECIMAL(10 , 2 ),
    AMOUNT_PER_PERSON DECIMAL(10 , 2 ),
    MAX_FOOD_NAME VARCHAR(100),
    MAX_FOOD_ID INTEGER,
    MAX_HOT_NAME VARCHAR(100),
    MAX_HOT_ID INTEGER,
    MAX_COLD_NAME VARCHAR(100),
    MAX_COLD_ID INTEGER,
    HOT_PER_TICKET DECIMAL(10 , 2 ),
    COLD_PER_TICKET DECIMAL(10 , 2 ),
    FOOD_PER_TICKET DECIMAL(10 , 2 ),
    BAKERY_PER_TICKET DECIMAL(10 , 2 ),
    COMBO_PER_TICKET DECIMAL(10 , 2 ),
    ITEMS_PER_TICKET DECIMAL(10 , 2 ),
    DELIVERY_ORDERS INTEGER,
    FIRST_UNIT_ID INTEGER,
    FIRST_UNIT_NAME VARCHAR(50),
    LAST_UNIT_ID INTEGER,
    LAST_UNIT_NAME VARCHAR(50),
    MAX_UNIT_ID INTEGER,
    MAX_UNIT_NAME VARCHAR(50),
    MAX_UNIT_VISITS INTEGER,
    MAX_TICKET_HOUR INTEGER,
    MAX_TICKET_HOUR_COUNT INTEGER,
    TOTAL_SPEND DECIMAL(10,2),
    LAST_UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


DROP TABLE IF EXISTS KETTLE_DUMP.CLM_SEGMENT_TREND;
CREATE TABLE KETTLE_DUMP.CLM_SEGMENT_TREND (
    CUSTOMER_ID INTEGER PRIMARY KEY,
    WEEK_1 VARCHAR(10),
    WEEK_2 VARCHAR(10),
    WEEK_3 VARCHAR(10),
    WEEK_4 VARCHAR(10),
    WEEK_5 VARCHAR(10),
    WEEK_6 VARCHAR(10),
    WEEK_7 VARCHAR(10),
    WEEK_8 VARCHAR(10),
    WEEK_9 VARCHAR(10),
    WEEK_10 VARCHAR(10),
    WEEK_11 VARCHAR(10),
    WEEK_12 VARCHAR(10),
    WEEK_13 VARCHAR(10),
    LAST_UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


USE `KETTLE_DUMP`;

DROP procedure IF EXISTS `debug_msg`;

DROP procedure IF EXISTS `SP_UPDATE_CLM_DATA`;

DELIMITER $$
USE `KETTLE_DUMP`$$

CREATE PROCEDURE debug_msg(enabled INTEGER, msg VARCHAR(255))
BEGIN
  IF enabled THEN BEGIN
    select concat("** ", msg) AS '** DEBUG:';
  END; END IF;
END $$


CREATE PROCEDURE `SP_UPDATE_CLM_DATA` ()
BEGIN

-- PROC START 

UPDATE KETTLE_DUMP.CLM_DATA SET OLD_STATUS = CURRENT_STATUS;




-- STEP 1
-- Update Customers

INSERT INTO KETTLE_DUMP.CLM_DATA (CUSTOMER_ID, CONTACT_NUMBER, EMAIL_ID, CURRENT_STATUS, OLD_STATUS)
SELECT CUSTOMER_ID, CONTACT_NUMBER, EMAIL_ID,'IN_ACTIVE','IN_ACTIVE' 
FROM KETTLE_DUMP.CUSTOMER_INFO ci 
WHERE ci.CUSTOMER_ID NOT IN (SELECT CUSTOMER_ID FROM KETTLE_DUMP.CLM_DATA) AND CUSTOMER_ID > 5;





-- STEP 2
-- Order Calculations

UPDATE KETTLE_DUMP.CLM_DATA cd
        LEFT JOIN
    (SELECT 
			od.CUSTOMER_ID,
            MAX(od.ORDER_ID) MAX_ORDER,
            MIN(od.ORDER_ID) MIN_ORDER,
            COUNT(od.ORDER_ID) VISITS,
            COUNT(DISTINCT od.UNIT_ID) UNIQUE_CAFE_COUNT,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN 1
                ELSE 0
            END) DELIVERY_ORDERS,
            SUM(od.TAXABLE_AMOUNT) TOTAL_SPEND
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        BILLING_SERVER_TIME > DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 90), INTERVAL 5 HOUR)
            AND CUSTOMER_ID > 5
			AND od.ORDER_STATUS <> 'CANCELLED'
			AND od.TOTAL_AMOUNT > 0.0
    GROUP BY od.CUSTOMER_ID) a ON a.CUSTOMER_ID = cd.CUSTOMER_ID 
SET 
    cd.LAST_ORDER_ID = a.MAX_ORDER,
    cd.FIRST_ORDER_ID = a.MIN_ORDER,
    cd.COUNT_OF_VISITS = a.VISITS,
    cd.COUNT_OF_UNIQUE_CAFE_VISITED = a.UNIQUE_CAFE_COUNT,
    cd.DELIVERY_ORDERS = a.DELIVERY_ORDERS,
    cd.TOTAL_SPEND = a.TOTAL_SPEND,
    cd.CURRENT_STATUS = CASE
        WHEN a.CUSTOMER_ID IS NULL THEN 'IN_ACTIVE'
        ELSE 'ACTIVE'
    END
WHERE
    a.CUSTOMER_ID = cd.CUSTOMER_ID;




-- STEP 3
-- Days Calculation


UPDATE KETTLE_DUMP.CLM_DATA cd
        LEFT JOIN
    KETTLE_DUMP.ORDER_DETAIL od1 ON cd.FIRST_ORDER_ID = od1.ORDER_ID
        LEFT JOIN
    KETTLE_DUMP.ORDER_DETAIL od2 ON cd.LAST_ORDER_ID = od2.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud1 ON ud1.UNIT_ID = od1.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud2 ON ud2.UNIT_ID = od2.UNIT_ID 
SET 
    cd.FIRST_UNIT_ID = ud1.UNIT_ID,
    cd.FIRST_UNIT_NAME = ud1.UNIT_NAME,
    cd.LAST_UNIT_ID = ud2.UNIT_ID,
    cd.LAST_UNIT_NAME = ud2.UNIT_NAME,
    cd.ACTIVITY_DAYS = TIMESTAMPDIFF(DAY,od1.BILLING_SERVER_TIME,od2.BILLING_SERVER_TIME),
    cd.DAYS_SINCE_LAST_VISIT = TIMESTAMPDIFF(DAY,od2.BILLING_SERVER_TIME,CURRENT_TIMESTAMP())
WHERE
    cd.CURRENT_STATUS = 'ACTIVE';





-- STEP 4
-- MAX Unit and Max Unit Visits

UPDATE KETTLE_DUMP.CLM_DATA cd,
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud,
    (SELECT 
        *
    FROM
        (SELECT 
        a.ORDER_COUNT,
            a.UNIT_ID,
            @unitRank:=CASE
                WHEN @cust <> a.CUSTOMER_ID THEN 0
                ELSE @unitRank + 1
            END AS RANK,
            @cust:=CUSTOMER_ID AS CUSTOMER_ID
    FROM
        (SELECT @unitRank:=- 1) s, (SELECT @cust:=- 1) c, (SELECT 
        @unitRank:=- 1,
            COUNT(ORDER_ID) ORDER_COUNT,
            UNIT_ID,
            CUSTOMER_ID
    FROM
        KETTLE_DUMP.ORDER_DETAIL
    WHERE
        BILLING_SERVER_TIME > DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 90), INTERVAL 5 HOUR)
            AND CUSTOMER_ID > 5
            AND ORDER_STATUS <> 'CANCELLED'
            AND TOTAL_AMOUNT > 0.0
    GROUP BY CUSTOMER_ID , UNIT_ID
    ORDER BY CUSTOMER_ID , ORDER_COUNT DESC) a) m
    WHERE
        m.RANK = 0) b 
SET 
    cd.MAX_UNIT_ID = ud.UNIT_ID,
    cd.MAX_UNIT_NAME = ud.UNIT_NAME,
    cd.MAX_UNIT_VISITS = b.ORDER_COUNT
WHERE
    b.CUSTOMER_ID = cd.CUSTOMER_ID
        AND ud.UNIT_ID = b.UNIT_ID
        AND cd.CURRENT_STATUS = 'ACTIVE';



-- STEP 5
-- FNB Calculations

UPDATE KETTLE_DUMP.CLM_DATA cd,
    (SELECT 
        z.CUSTOMER_ID,
            TRUNCATE(z.PEOPLE_PER_ORDER_COMPARED_TO_BEVERAGE / z.TKT, 2) PEOPLE_PER_TKT,
            TRUNCATE(z.HOT_BEVERAGE_QUANTITY / z.TKT, 2) HOT_BEVERAGE_QUANTITY_PER_TKT,
            TRUNCATE(z.COLD_BEVERAGE_QUANTITY / z.TKT, 2) COLD_BEVERAGE_QUANTITY_PER_TKT,
            TRUNCATE(z.FOOD_QUANTITY / z.TKT, 2) FOOD_QUANTITY_PER_TKT,
            TRUNCATE(z.COMBOS_QUANTITY / z.TKT, 2) COMBOS_QUANTITY_PER_TKT,
            TRUNCATE(z.BAKERY_QUANTITY / z.TKT, 2) BAKERY_QUANTITY_PER_TKT,
            TRUNCATE(z.TOTAL_ITEM_QUANTITY / z.TKT, 2) TOTAL_ITEM_QUANTITY_PER_TKT
    FROM
        (SELECT 
        b.CUSTOMER_ID,
            COUNT(*) TKT,
            AVG(b.PEOPLE_PER_ORDER_COMPARED_TO_BEVERAGE) AS PEOPLE_PER_ORDER_COMPARED_TO_BEVERAGE,
            SUM(b.HOT_BEVERAGE_QUANTITY) AS HOT_BEVERAGE_QUANTITY,
            SUM(b.COLD_BEVERAGE_QUANTITY) AS COLD_BEVERAGE_QUANTITY,
            SUM(b.FOOD_QUANTITY) AS FOOD_QUANTITY,
            SUM(b.COMBOS_QUANTITY) AS COMBOS_QUANTITY,
            SUM(b.BAKERY_QUANTITY) AS BAKERY_QUANTITY,
            SUM(b.TOTAL_ITEM_QUANTITY) AS TOTAL_ITEM_QUANTITY
    FROM
        (SELECT 
        a.*,
            GREATEST(a.TOTAL_BEVERAGE_QUANTITY, a.FOOD_QUANTITY) PEOPLE_PER_ORDER_COMPARED_TO_BEVERAGE
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            oi.ORDER_ID,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE IN (5 , 6)
                        AND pd.PRODUCT_SUB_TYPE NOT IN (501 , 502, 503)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) OTHER_BEVERAGE_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_BEVERAGE_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_BEVERAGE_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 8 THEN oi.QUANTITY
                ELSE 0
            END) COMBOS_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 10 THEN oi.QUANTITY
                ELSE 0
            END) BAKERY_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE IN (9 , 10, 12) THEN oi.QUANTITY
                ELSE 0
            END) OTHERS_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6) THEN oi.QUANTITY
                ELSE 0
            END) TOTAL_BEVERAGE_QUANTITY,
			SUM(oi.QUANTITY) TOTAL_ITEM_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.BILLING_SERVER_TIME > DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 90), INTERVAL 5 HOUR)
            AND od.CUSTOMER_ID > 5
            AND od.TOTAL_AMOUNT > 0.0
    GROUP BY od.CUSTOMER_ID , oi.ORDER_ID) AS a) AS b
    GROUP BY b.CUSTOMER_ID) z) x 
SET 
    cd.HOT_PER_TICKET = x.HOT_BEVERAGE_QUANTITY_PER_TKT,
    cd.COLD_PER_TICKET = x.COLD_BEVERAGE_QUANTITY_PER_TKT,
    cd.FOOD_PER_TICKET = x.FOOD_QUANTITY_PER_TKT,
    cd.COMBO_PER_TICKET = x.COMBOS_QUANTITY_PER_TKT,
    cd.BAKERY_PER_TICKET = x.BAKERY_QUANTITY_PER_TKT,
    cd.ITEMS_PER_TICKET = x.TOTAL_ITEM_QUANTITY_PER_TKT,
    cd.PEOPLE_PER_TICKET = x.PEOPLE_PER_TKT
WHERE
    cd.CUSTOMER_ID = x.CUSTOMER_ID;

	/*
-- STEP
-- MAX FOOD and MAX BEV
UPDATE KETTLE_DUMP.CLM_DATA cd
LEFT JOIN (
SELECT
z.CUSTOMER_ID,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 5 THEN z.PRODUCT_ID
		ELSE NULL
	END ) MAX_HOT_ID,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 5 THEN z.PRODUCT_NAME
		ELSE NULL
	END ) MAX_HOT_NAME,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 5 THEN z.COUNTER
		ELSE NULL
	END ) MAX_HOT_COUNT,
MAX(CASE
		WHEN z.PRODUCT_TYPE = 6 THEN z.PRODUCT_ID
		ELSE NULL
	END) MAX_COLD_ID,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 6 THEN z.PRODUCT_NAME
		ELSE NULL
	END ) MAX_COLD_NAME,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 6 THEN z.COUNTER
		ELSE NULL
	END ) MAX_COLD_COUNT,
MAX(CASE
		WHEN z.PRODUCT_TYPE = 7 THEN z.PRODUCT_ID
		ELSE NULL
	END) MAX_FOOD_ID,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 7 THEN z.PRODUCT_NAME
		ELSE NULL
	END ) MAX_FOOD_NAME,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 7 THEN z.COUNTER
		ELSE NULL
	END ) MAX_FOOD_COUNT,
MAX(CASE
		WHEN z.PRODUCT_TYPE = 8 THEN z.PRODUCT_ID
		ELSE NULL
	END) MAX_COMBO_ID,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 8 THEN z.PRODUCT_NAME
		ELSE NULL
	END ) MAX_COMBO_NAME,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 8 THEN z.COUNTER
		ELSE NULL
	END ) MAX_COMBO_COUNT,
MAX(CASE
		WHEN z.PRODUCT_TYPE = 9 THEN z.PRODUCT_ID
		ELSE NULL
	END) MAX_MER_ID,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 9 THEN z.PRODUCT_NAME
		ELSE NULL
	END ) MAX_MER_NAME,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 9 THEN z.COUNTER
		ELSE NULL
	END ) MAX_MER_COUNT,
MAX(CASE
		WHEN z.PRODUCT_TYPE = 10 THEN z.PRODUCT_ID
		ELSE NULL
	END) MAX_BAKERY_ID,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 10 THEN z.PRODUCT_NAME
		ELSE NULL
	END ) MAX_BAKERY_NAME,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 10 THEN z.COUNTER
		ELSE NULL
	END ) MAX_BAKERY_COUNT,
MAX(CASE
		WHEN z.PRODUCT_TYPE = 12 THEN z.PRODUCT_ID
		ELSE NULL
	END) MAX_OTHERS_ID,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 12 THEN z.PRODUCT_NAME
		ELSE NULL
	END ) MAX_OTHERS_NAME,
MAX(
	CASE
		WHEN z.PRODUCT_TYPE = 12 THEN z.COUNTER
		ELSE NULL
	END ) MAX_OTHER_COUNT
FROM
		(SELECT 
			a.CUSTOMER_ID,
			a.PRODUCT_ID,
			a.PRODUCT_NAME,
			a.PRODUCT_TYPE,
			rlt.RTL_NAME,
			MAX(COUNTER) COUNTER 
			FROM
				(SELECT 
					od.CUSTOMER_ID,
					oi.PRODUCT_ID,
					oi.PRODUCT_NAME,
					pd.PRODUCT_TYPE,
					SUM(oi.QUANTITY) COUNTER
				FROM
					KETTLE_DUMP.ORDER_ITEM oi
						INNER JOIN
					KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
						INNER JOIN 
					KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
				WHERE
					od.ORDER_STATUS <> 'CANCELLED'
						AND od.BILLING_SERVER_TIME > DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),90),INTERVAL 5 HOUR)
						AND od.CUSTOMER_ID > 5
						AND od.TOTAL_AMOUNT > 0.0
				GROUP BY od.CUSTOMER_ID , oi.PRODUCT_ID ) a 
				LEFT JOIN
				KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rlt ON rlt.RTL_ID = a.PRODUCT_TYPE
		GROUP BY a.CUSTOMER_ID, a.PRODUCT_TYPE ) z
GROUP BY z.CUSTOMER_ID) y ON cd.CUSTOMER_ID = y.CUSTOMER_ID
SET 
cd.MAX_HOT_ID = y.MAX_HOT_ID,
cd.MAX_HOT_NAME = y.MAX_HOT_NAME,
cd.MAX_COLD_ID = y.MAX_COLD_ID,
cd.MAX_COLD_NAME = y.MAX_COLD_NAME,
cd.MAX_FOOD_ID = y.MAX_FOOD_ID,
cd.MAX_FOOD_NAME = y.MAX_FOOD_NAME;
*/

-- STEP 6
-- MAX Hour Calculation

UPDATE KETTLE_DUMP.CLM_DATA cd,
    (SELECT 
        *
    FROM
        (SELECT 
        a.ORDER_COUNT,
            a.HOUR_OF_DAY,
            @unitRank:=CASE
                WHEN @cust <> a.CUSTOMER_ID THEN 0
                ELSE @hourRank + 1
            END AS RANK,
            @cust:=CUSTOMER_ID AS CUSTOMER_ID
    FROM
        (SELECT @hourRank:=- 1) s, (SELECT @cust:=- 1) c, (SELECT 
        @hourRank:=- 1,
            COUNT(ORDER_ID) ORDER_COUNT,
            HOUR(BILLING_SERVER_TIME) HOUR_OF_DAY,
            CUSTOMER_ID
    FROM
        KETTLE_DUMP.ORDER_DETAIL
    WHERE
        BILLING_SERVER_TIME > DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 90), INTERVAL 5 HOUR)
            AND CUSTOMER_ID > 5
            AND ORDER_STATUS <> 'CANCELLED'
            AND TOTAL_AMOUNT > 0.0
    GROUP BY CUSTOMER_ID , HOUR_OF_DAY
    ORDER BY CUSTOMER_ID , ORDER_COUNT DESC) a) m
    WHERE
        m.RANK = 0) b 
SET 
    cd.MAX_TICKET_HOUR = b.HOUR_OF_DAY,
    cd.MAX_TICKET_HOUR_COUNT = b.ORDER_COUNT
WHERE
    b.CUSTOMER_ID = cd.CUSTOMER_ID
        AND cd.CURRENT_STATUS = 'ACTIVE';

-- STEP 7
-- Amount per person

UPDATE KETTLE_DUMP.CLM_DATA cd 
SET cd.AMOUNT_PER_PERSON = cd.AMOUNT_PER_PERSON / (cd.PEOPLE_PER_TICKET * cd.COUNT_OF_VISITS);

-- STEP 8
-- Segment calculation on ACTVITY and VISITS

UPDATE KETTLE_DUMP.CLM_DATA cd 
SET 
    CURRENT_SEGMENT = CASE
        WHEN
            ACTIVITY_DAYS = 0
                && COUNT_OF_VISITS >= 1
                && COUNT_OF_VISITS <= 2
        THEN
            'NEW_BORN'
        WHEN
            ACTIVITY_DAYS = 0
                && COUNT_OF_VISITS >= 3
        THEN
            'SPIKE'
        WHEN
            ACTIVITY_DAYS >= 1
                && COUNT_OF_VISITS >= 1
                && COUNT_OF_VISITS <= 2
        THEN
            'CASUAL'
        WHEN
            ACTIVITY_DAYS >= 1
                && ACTIVITY_DAYS <= 30
                && COUNT_OF_VISITS >= 3
                && COUNT_OF_VISITS <= 6
        THEN
            'TODDLER'
        WHEN
            ACTIVITY_DAYS >= 1
                && ACTIVITY_DAYS <= 30
                && COUNT_OF_VISITS > 6
        THEN
            'LOYAL'
        WHEN
            ACTIVITY_DAYS >= 31
                && ACTIVITY_DAYS <= 89
                && COUNT_OF_VISITS >= 3
                && COUNT_OF_VISITS <= 6
        THEN
            'REGULAR'
        WHEN
            ACTIVITY_DAYS > 31
                && COUNT_OF_VISITS > 6
        THEN
            'FAN'
		ELSE 'IN_ACTIVE'
    END;




-- STEP 7
-- Segment Trend shift
Set @weekOfYear= 0;
SELECT WEEKOFYEAR(MAX(LAST_UPDATE_TIME)) INTO @weekOfYear FROM KETTLE_DUMP.CLM_SEGMENT_TREND LIMIT 1;

IF @FileRef is NULL || @weekOfYear <> WEEKOFYEAR(NOW()) THEN
UPDATE KETTLE_DUMP.CLM_SEGMENT_TREND 
SET 
    WEEK_13 = WEEK_12,
    WEEK_12 = WEEK_11,
    WEEK_11 = WEEK_10,
    WEEK_10 = WEEK_9,
    WEEK_9 = WEEK_8,
    WEEK_8 = WEEK_7,
    WEEK_7 = WEEK_6,
    WEEK_6 = WEEK_5,
    WEEK_5 = WEEK_4,
    WEEK_4 = WEEK_3,
    WEEK_3 = WEEK_2,
    WEEK_2 = WEEK_1,
    WEEK_1 = NULL;
END IF;


-- SEGMENT TREND UPDATES
-- Add New Customers

INSERT INTO KETTLE_DUMP.CLM_SEGMENT_TREND 
SELECT 	CUSTOMER_ID,
		'IN_ACTIVE',
        'IN_ACTIVE', 
        'IN_ACTIVE', 
        'IN_ACTIVE',
        'IN_ACTIVE',
        'IN_ACTIVE',
        'IN_ACTIVE',
        'IN_ACTIVE',
        'IN_ACTIVE',
		'IN_ACTIVE',
		'IN_ACTIVE',
		'IN_ACTIVE',
		'IN_ACTIVE' 
FROM KETTLE_DUMP.CLM_DATA WHERE CUSTOMER_ID NOT IN (SELECT CUSTOMER_ID FROM KETTLE_DUMP.CLM_SEGMENT_TREND);

-- Update Trend for all Customers

UPDATE KETTLE_DUMP.CLM_SEGMENT_TREND cst
        LEFT JOIN
    KETTLE_DUMP.CLM_DATA cd ON cst.CUSTOMER_ID = cd.CUSTOMER_ID 
SET 
    cst.WEEK_1 = cd.CURRENT_SEGMENT;
        
END
$$

DELIMITER ;

