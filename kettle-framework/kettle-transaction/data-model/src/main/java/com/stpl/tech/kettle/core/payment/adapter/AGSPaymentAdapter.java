package com.stpl.tech.kettle.core.payment.adapter;

import com.stpl.tech.kettle.core.payment.PaymentAdapter;
import com.stpl.tech.kettle.core.service.AGSPaymentService;
import com.stpl.tech.kettle.core.service.impl.AGSPaymentServiceImpl;
import com.stpl.tech.master.payment.model.AGS.AGSCreateRequest;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMStatus;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class AGSPaymentAdapter extends PaymentAdapter<OrderPaymentRequest,
        AGSCreateRequest> {

    @Autowired
    private AGSPaymentService agsPaymentService;

    @Override
    public AGSCreateRequest createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception {
        return agsPaymentService.createAGSRequest(orderPaymentRequest);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification,Integer brandId) {
        AGSPaymentCMStatus agsPaymentCMStatus = (AGSPaymentCMStatus)object;
        return agsPaymentService.updateAGSPaymentCMStatus(agsPaymentCMStatus);
    }
}
