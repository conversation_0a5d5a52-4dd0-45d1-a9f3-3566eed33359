package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.Map;

public class MetaInfo implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6266981106456968822L;

	private String type;
	private Map<Integer, IdCodeName> data;

	public MetaInfo() {
		super();
	}

	public MetaInfo(String type, Map<Integer, IdCodeName> data) {
		super();
		this.type = type;
		this.data = data;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Map<Integer, IdCodeName> getData() {
		return data;
	}

	public void setData(Map<Integer, IdCodeName> data) {
		this.data = data;
	}

}
