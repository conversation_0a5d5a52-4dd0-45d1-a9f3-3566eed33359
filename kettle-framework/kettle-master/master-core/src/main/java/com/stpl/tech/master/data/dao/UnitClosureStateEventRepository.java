package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.UnitClosureStateEvent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitClosureStateEventRepository extends JpaRepository<UnitClosureStateEvent, Long> {
    UnitClosureStateEvent findByRequestIdAndStateId(Long requestId, Long stateId);
    List<UnitClosureStateEvent> findByRequestId(Long requestId);
}