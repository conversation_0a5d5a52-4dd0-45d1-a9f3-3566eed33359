package com.example.chaayosStockTake;
/**
 * Created by <PERSON><PERSON><PERSON> in Jan 2020.
 */
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/*"eventId",
        "unitId",
        "status",
        "stockEventAssetMappingDefinitions",*/

public class StockTakingEvent implements Serializable {
    private Integer eventId;
    private Integer unitId;
    private EventAssetMappingStatus status;
    @SerializedName("stockEventAssetMappingDefinitions")
    private List<StockTakingScannedItem> stockTakingScannedItemsList;

    public StockTakingEvent(Integer eventId, Integer unitId, EventAssetMappingStatus status, List<StockTakingScannedItem> stockTakingScannedItemsList) {
        this.eventId = eventId;
        this.unitId = unitId;
        this.status = status;
        this.stockTakingScannedItemsList = stockTakingScannedItemsList;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public EventAssetMappingStatus getStatus() {
        return status;
    }

    public void setStatus(EventAssetMappingStatus status) {
        this.status = status;
    }

    public List<StockTakingScannedItem> getStockTakingScannedItemsList() {
        return stockTakingScannedItemsList;
    }

    public void setStockTakingScannedItemsList(List<StockTakingScannedItem> stockTakingScannedItemsList) {
        this.stockTakingScannedItemsList = stockTakingScannedItemsList;
    }
}
