package com.stpl.tech.master.core.external.partner.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.data.model.ChannelPartnerCommission;
import com.stpl.tech.master.data.model.MenuRecommendationMappingData;
import com.stpl.tech.master.data.model.MenuSequenceMappingData;
import com.stpl.tech.master.data.model.ProductGroupData;
import com.stpl.tech.master.data.model.ProductSequenceData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMappingData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.PriceProfileDetail;

public interface ChannelPartnerExternalService{

	public List<ChannelPartnerDetail> getAllChannelPartner(Date businessDate) throws DataNotFoundException;

	public ChannelPartnerCommission getChannelPartnerCommission(int partnerId, Date businessDate) throws DataNotFoundException;

	public List<UnitChannelPartnerMappingData> getUnitChannelPartnerMappings();

    UnitChannelPartnerMappingData findMappingByUnitAndPartnerId(Integer unitId, Integer partnerId);

    List<UnitChannelPartnerMenuMappingData> findMenuMappingByUnitPartnerMappingId(Integer unitChannelPartnerMappingId);

	List<UnitChannelPartnerMenuMappingData> getUnitPartnerMenuMappings();

    List<UnitChannelPartnerMenuMappingData> findMenuMappingByUnitPartnerBrandMappingId(Integer unitChannelPartnerMappingId, Integer brandId);

    List<ProductGroupData> getProductGroupByTagAndType(String tag, String type);

    List<ProductGroupData> getProductGroupByNameAndType(String name, String type,String tag);

    List<ProductSequenceData> getProductSequenceByGroupId(Integer groupId);

    List<ProductSequenceData> getProductSequenceByGroupIds(List<Integer> groupIds);

    List<ProductSequenceData> getAllProductSequenceByGroupId(Integer groupId);

    ProductSequenceData getProductSequenceByGroupIdAndProductId(Integer groupId, Integer productId);

    List<MenuSequenceMappingData> getAllMenuSequenceMappingByMenuSequenceId(Integer menuSequenceId);

    List<ProductGroupData> getAllProductGroupByGroupIds(List<Integer> groupIds);

    List<ProductSequenceData> getAllSequencedProductInGroupForCloning(Integer cloneId);

    List<MenuSequenceMappingData> getAllSequencedProductInMenuForCloning(Integer cloneId);

    List<MenuRecommendationMappingData> getAllRecommendationMapping(Integer recommendationId);

	public List<Integer> getChannelPartnerIdsForMenuSequence(Integer menuSequenceId);

	public List<Integer> getUnitIdsForMenu(List<Integer> channelPartnerIds, Integer kettlePartnerId);

    List<Integer> getMenuSequenceIdsAsPerMenuApp(MenuApp menuApp);

    List<ProductGroupData> getProductGroupDataAsPerIdAndMenuApp(List<Integer> ids, MenuApp menuApp);

     List<Integer> getCategoryIdsMenuSequenceMapping(List<Integer> menuSequenceIds);

    PriceProfileDetail getUnitPartnerProfilePrice(Integer priceProfileId);

    List<PriceProfileDetail> getUnitPartnerProfilePrice();

    boolean addProfilePriceMapping(PriceProfileDetail detail);

    List<PriceProfileDetail> getProfilePrice(String priceProfileStrategy);

    boolean updateProfilePriceMapping(IdCodeName detail);

	public Map<Integer, PriceProfileDetail> getAllActivePriceProfiles();

	public Map<Integer, PriceProfileDetail> getAllActivePriceProfiles(Integer priceProfileId);

    List<UnitChannelPartnerMenuMappingData> getUnitPartnerMenuMappings(Integer unitChannelPartnerMappingId,Integer brandId);
}
