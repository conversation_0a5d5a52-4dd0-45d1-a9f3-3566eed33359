
'use strict';

angular.module('posApp').controller('orderRefundSearchCtrl',
    ['$scope', 'AuthenticationService', 'posAPI', 'AppUtil', '$location', '$rootScope', '$window', '$http',
    function ($scope, AuthenticationService, posAPI, AppUtil, $location, $rootScope, $window, $http) {

        $scope.initOrderRefund = function() {
            $scope.refundOrdersList = [];
        }

        $scope.getServiceChargeRefundDetails = function() {
            $scope.refundOrdersList = [];
            var formattedDate = formatDateForBackend($scope.startDate);
            $http({
                method: 'GET',
                url: AppUtil.restUrls.order.getServiceChargeRefundDetails,
                params: {unitId : AppUtil.getUnitDetails().id, startDate: formattedDate}
            }).then(function success(response) {
                if(response != null) {
                     $scope.refundOrdersList = response.data;
                }
            }, function error(response) {
                   AppUtil.myAlert(response.data.errorMessage);
            })
        }

        function formatDateForBackend(date) {
            var day = String(date.getDate()).padStart(2, '0');
            var month = String(date.getMonth() + 1).padStart(2, '0');
            var year = date.getFullYear();

            return year + "-" + month + "-" + day;
        }

        $scope.backToCover = function() {
            if (!AppUtil.isCOD()) {
                $location.url('/cover');
            } else {
                $location.url('/CODCover');
            }
        }

    }
  ]
);
