/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limit`ed
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('scmApp')
    .controller('vehicleModalCtrl', ['$scope', 'apiJson', '$http','items',  'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye','$timeout','$window',
        function ($scope, apiJson, $http, items, appUtil, $location, $toastService, metaDataService,
                  $fileUploadService, $alertService, previewModalService, Popeye, $timeout, $window) {
    	
    	 $scope.init = function (){
             $scope.periods = ["ACTIVE","IN_ACTIVE"];
             $scope.multiDispatchs = ["Y","N"];
             $scope.transportModes = ["ROAD","RAIL","SHIP","AIR"];
             $scope.vehicleName = items.vehicleName;
             $scope.registrationNumber = items.registrationNumber;
             $scope.model = items.model == "NA" ? "" : items.model;
             $scope.make = items.make == "NA" ? "" : items.make;
             $scope.transportMode = items.transportMode;
             $scope.vehicleStatus = items.vehicleStatus;
             $scope.multiDispatch = "N";
             $scope.mode = items.mode;
             if(items.mode == "add"){
            	 $scope.vehicleId = null;
             }
             else{
            	 $scope.vehicleId = items.vehicleId;
             }
    	 }
    	 $scope.submitVehicleData = function(){
    		 if($scope.vehicleId == null){
    			 $scope.addVehicle();
    		 }
    		 else{
    			 $scope.updateVehicle($scope.vehicleId);
    		 }
    	 }
    	 
    	 $scope.updateVehicle = function(vehicleId){
         	if($scope.vehicleName=="" || $scope.vehicleName==null){
     			alert("Please input the Vehicle Name.");
     			return;
     		}
         	if($scope.registrationNumber=="" || $scope.registrationNumber==null){
     			alert("Please input the Registration Number.");
     			return;
     		}
         	if($scope.model=="" || $scope.model==null){
      			alert("Please input the Model.");
      			return;
      		}
          	if($scope.make=="" || $scope.make==null){
      			alert("Please input the Vehicle Brand.");
      			return;
      		}
          	if($scope.transportMode=="" || $scope.transportMode==null){
      			alert("Please input the Transport Mode.");
      			return;
      		}
        	if($scope.vehicleStatus=="" || $scope.vehicleStatus==null){
     			alert("Please input the Status.");
     			return;
     		}
        	if($scope.multiDispatch=="" || $scope.multiDispatch==null){
     			alert("Please input the Multi Dispatch.");
     			return;
     		}
        	if($scope.multiDispatch!=null){
        		$scope.multiDispatch = $scope.multiDispatch == 'Y'? true : false;
     		}
         	var vehicleDetail = {
         			registrationNumber:$scope.registrationNumber,
         			 name:$scope.vehicleName,
         			 model:$scope.model,
         			 make:$scope.make,
         			 transportMode:$scope.transportMode,
         			 status:$scope.vehicleStatus,
         			 vehicleId:$scope.vehicleId,
         			 multiDispatch:$scope.multiDispatch
         	}
         	
         	$http({
                 method: "POST",
                 url: apiJson.urls.vehicleData.saveUpdatedVehicle,
                 data: vehicleDetail,
             }).then(function success(response) {
            	 if(response.data){
     				$toastService.create("Vehicle Data is updated successfully");
     				closeModal(true);
     			}else{
     				$toastService.create("Something went wrong.");
     			}
             }, function error(response) {
                 console.log("error:" + response);
             });
      		
          }
    	 
    	 
    	 
    	 $scope.addVehicle = function(){
         	if($scope.vehicleName=="" || $scope.vehicleName==null){
     			alert("Please input the Vehicle Name.");
     			return;
     		}
         	if($scope.registrationNumber=="" || $scope.registrationNumber==null){
     			alert("Please input the Registration Number.");
     			return;
     		}
         	if($scope.model=="" || $scope.model==null){
      			alert("Please input the Model.");
      			return;
      		}
          	if($scope.make=="" || $scope.make==null){
      			alert("Please input the Vehicle Brand.");
      			return;
      		}
          	if($scope.transportMode=="" || $scope.transportMode==null){
      			alert("Please input the Transport Mode.");
      			return;
      		}
        	if($scope.vehicleStatus=="" || $scope.vehicleStatus==null){
     			alert("Please input the Status.");
     			return;
     		}
        	if($scope.multiDispatch=="" || $scope.multiDispatch==null){
     			alert("Please input the Multi Dispatch.");
     			return;
     		}
        	if($scope.multiDispatch!=null){
        		$scope.multiDispatch = $scope.multiDispatch == 'Y'? true : false;
     		}
        	var vehicleDetail = {
        			registrationNumber:$scope.registrationNumber,
        			 name:$scope.vehicleName,
        			 model:$scope.model,
        			 make:$scope.make,
        			 transportMode:$scope.transportMode,
        			 status:$scope.vehicleStatus,
        			 multiDispatch:$scope.multiDispatch
        	}
        	
        	$http({
                method: "POST",
                url: apiJson.urls.vehicleData.saveVehicle,
                data: vehicleDetail,
            }).then(function success(response) {
            	if(response.data){
    				$toastService.create("Vehicle Data is saved successfully");
    				closeModal(true);
    			}else{
    				$toastService.create("Something went wrong.");
    			}
            }, function error(response) {
                console.log("error:" + response);
            });
     		
         }
    	 
    	 $scope.cancel=function(){
         	  closeModal(false);
           };
           
           function closeModal(data){
            	  Popeye.closeCurrentModal(data);
              }
           
           
    	
    }]);
    
