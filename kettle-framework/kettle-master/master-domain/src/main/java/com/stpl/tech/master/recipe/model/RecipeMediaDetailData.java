package com.stpl.tech.master.recipe.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

@ExcelSheet(value = "Recipe Media Detail Data")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class RecipeMediaDetailData {

    @ExcelField(headerName = "dimension.code")
    private String dimensionCode;
    @ExcelField(headerName = "name")
    private String name;
    @ExcelField(headerName = "product.name")
    private String productName;
    @ExcelField(headerName = "product.productId")
    private Integer productId;
    @ExcelField(headerName = "product.type")
    private Integer productType;
    @ExcelField(headerName = "profile")
    private String profile;
    @ExcelField(headerName = "recipeId")
    private Integer recipeId;
    @ExcelField(headerName = "Image 1")
    private String image1;
    @ExcelField(headerName = "Image 2")
    private String image2;
    @ExcelField(headerName = "Image 3")
    private String image3;

    public String getDimensionCode() {
        return dimensionCode;
    }

    public void setDimensionCode(String dimensionCode) {
        this.dimensionCode = dimensionCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public Integer getRecipeId() {
        return recipeId;
    }

    public void setRecipeId(Integer recipeId) {
        this.recipeId = recipeId;
    }

    public String getImage1() {
        return image1;
    }

    public void setImage1(String image1) {
        this.image1 = image1;
    }

    public String getImage2() {
        return image2;
    }

    public void setImage2(String image2) {
        this.image2 = image2;
    }

    public String getImage3() {
        return image3;
    }

    public void setImage3(String image3) {
        this.image3 = image3;
    }

    @Override
    public String toString() {
        return "RecipeMediaDetailData{" +
                "dimensionCode='" + dimensionCode + '\'' +
                ", name='" + name + '\'' +
                ", productName='" + productName + '\'' +
                ", productId=" + productId +
                ", productType=" + productType +
                ", profile='" + profile + '\'' +
                ", recipeId=" + recipeId +
                ", image1='" + image1 + '\'' +
                ", image2='" + image2 + '\'' +
                ", image3='" + image3 + '\'' +
                '}';
    }
}
