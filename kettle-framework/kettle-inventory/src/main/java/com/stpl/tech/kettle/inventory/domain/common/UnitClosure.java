package com.stpl.tech.kettle.inventory.domain.common;

import java.util.Date;

public class UnitClosure {

	protected int id;
	protected int unitId;
	protected Date businessDate;
	protected Date startTime;
	protected int lastOrderId;
	protected int startOrderId;
	protected String currentStatus;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public int getLastOrderId() {
		return lastOrderId;
	}

	public void setLastOrderId(int lastOrderId) {
		this.lastOrderId = lastOrderId;
	}

	public int getStartOrderId() {
		return startOrderId;
	}

	public void setStartOrderId(int startOrderId) {
		this.startOrderId = startOrderId;
	}

	public String getCurrentStatus() {
		return currentStatus;
	}

	public void setCurrentStatus(String currentStatus) {
		this.currentStatus = currentStatus;
	}

}
