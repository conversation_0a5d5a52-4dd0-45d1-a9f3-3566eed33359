//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.25 at 12:25:34 AM IST 
//

package com.stpl.tech.master.core.external.acl.service.impl;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.master.core.external.acl.service.TokenDao;

import io.jsonwebtoken.Claims;

public class JWTToken implements TokenDao {

	protected String sessionKey;
	protected int unitId;
	protected String issuer;
	protected int userId;
	protected int terminalId;
	protected String macAddress;
	protected String geoLocation;

	public JWTToken() {

	}

	public JWTToken(String sessionKey, int unitId, String issuer, int userId, int terminalId) {
		this.sessionKey = sessionKey;
		this.unitId = unitId;
		this.issuer = issuer;
		this.userId = userId;
		this.terminalId = terminalId;
	}

	public JWTToken(String sessionKey, int unitId, String issuer, int userId, int terminalId , String macAddress
	, String geoLocation) {
		this.sessionKey = sessionKey;
		this.unitId = unitId;
		this.issuer = issuer;
		this.userId = userId;
		this.terminalId = terminalId;
		this.macAddress = macAddress;
		this.geoLocation = geoLocation;
	}

	/**
	 * Gets the value of the sessionKey property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSessionKey() {
		return sessionKey;
	}

	/**
	 * Sets the value of the sessionKey property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSessionKey(String value) {
		this.sessionKey = value;
	}

	/**
	 * Gets the value of the unitId property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public int getUnitId() {
		return unitId;
	}

	/**
	 * Sets the value of the unitId property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUnitId(int value) {
		this.unitId = value;
	}

	/**
	 * Gets the value of the issuer property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getIssuer() {
		return issuer;
	}

	/**
	 * Sets the value of the issuer property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setIssuer(String value) {
		this.issuer = value;
	}

	/**
	 * Gets the value of the userId property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public int getUserId() {
		return userId;
	}

	/**
	 * Sets the value of the userId property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUserId(int value) {
		this.userId = value;
	}

	/**
	 * Gets the value of the terminalId property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public int getTerminalId() {
		return terminalId;
	}

	public String getMacAddress() {
		return macAddress;
	}

	public void setMacAddress(String macAddress) {
		this.macAddress = macAddress;
	}

	public String getGeoLocation() {
		return geoLocation;
	}

	public void setGeoLocation(String geoLocation) {
		this.geoLocation = geoLocation;
	}

	/**
	 * Sets the value of the terminalId property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setTerminalId(int value) {
		this.terminalId = value;
	}

	public Map<String, Object> createClaims() {
		// Setting JWT Claims
		Map<String, Object> authClaims = new HashMap<String, Object>();
		authClaims.put("sessionKey", this.getSessionKey());
		authClaims.put("unitId", this.getUnitId());
		authClaims.put("issuer", this.getIssuer());
		authClaims.put("userId", this.getUserId());
		authClaims.put("terminalId", this.getTerminalId());
		authClaims.put("macAddress", this.getMacAddress());
		authClaims.put("geoLocation", this.getGeoLocation());
		return authClaims;
	}

	public void parseClaims(Claims claims) {
		this.sessionKey = claims.get("sessionKey", String.class);
		this.unitId = claims.get("unitId", Integer.class);
		this.userId = claims.get("userId", Integer.class);
		this.issuer = claims.get("issuer", String.class);
		this.terminalId = claims.get("terminalId", Integer.class);
	}

}
