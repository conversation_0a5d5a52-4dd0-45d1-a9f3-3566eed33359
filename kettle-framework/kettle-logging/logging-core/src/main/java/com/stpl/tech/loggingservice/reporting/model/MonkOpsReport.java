package com.stpl.tech.loggingservice.reporting.model;

import com.stpl.tech.loggingservice.model.chaimonk.log.FatalErrorCode;
import com.stpl.tech.loggingservice.reporting.mapper.MonkLogDataHelper;
import com.stpl.tech.loggingservice.reporting.mapper.ShiftViewForUnit;
import com.stpl.tech.loggingservice.util.KettleLoggingUtil;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.ExcelWriter;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 17-03-2018.
 */

public class MonkOpsReport extends MonkReport {

    private WorkbookContext workbookCtx;
    private Unit unit;
    private String filePath;
    private Date startDate;
    private Date endDate;
    private boolean generated = false;
    private String fileName;
    private ExcelWriter writer;

    private List<MonkTaskOpsView> opsTaskView;

    public MonkOpsReport(WorkbookContextFactory factory, List<MonkTaskOpsView> opsTaskView, Date start, Date end, String baseDir, Unit unit) {
        this.workbookCtx = factory.createWorkbook();
        this.opsTaskView = opsTaskView;
        this.startDate = start;
        this.endDate = end;
        this.unit = unit;
        this.fileName = "CHAI_MONK_LOGS_" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xslx";
        this.filePath = baseDir + File.separator + "MONK" + File.separator + "OPS_LOGS";
        this.writer = new ExcelWriter(getWorkbookCtx().toNativeWorkbook());
    }

    public WorkbookContext getWorkbookCtx() {
        return workbookCtx;
    }

    public Unit getUnit() {
        return unit;
    }

    public String getFilePath() {
        return filePath;
    }

    public Date getStartDate() {
        return startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public boolean isGenerated() {
        return generated;
    }

    public String getFileName() {
        return fileName;
    }

    public String getName(){ return unit.getName();}


    public void renderTaskView() {
        writer.writeSheet(opsTaskView, MonkTaskOpsView.class);
    }

    public void renderSummaryView(MonkTaskMtdView mtd, MonkTaskMtdView today) {
        SheetContext sheetCtx = workbookCtx.createSheet("Unit Summary Report");
        Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);

        sheetCtx.nextRow().setTextStyle(headerStyle).text("").setColumnWidth(KettleLoggingUtil.COLUMN_WIDTH)
                .text("Today").setColumnWidth(KettleLoggingUtil.COLUMN_WIDTH)
                .text("MTD").setColumnWidth(KettleLoggingUtil.COLUMN_WIDTH);


        sheetCtx.nextRow().text("Number of Tasks").number(today.getNumberOfOrders()).number(mtd.getNumberOfOrders())
                .nextRow().text("Average TAT").text(KettleLoggingUtil.toTimeString(today.getAverageTAT()))
                    .text(KettleLoggingUtil.toTimeString(mtd.getAverageTAT()))
                .nextRow().text(FatalErrorCode.NO_WATER.name()).number(today.getWaterFatals()).number(mtd.getWaterFatals())
                .nextRow().text(FatalErrorCode.NO_MILK.name()).number(today.getMilkFatals()).number(mtd.getMilkFatals())
                .nextRow().text(FatalErrorCode.NO_POWER.name()).number(today.getPowerFatals()).number(mtd.getPowerFatals())
                .nextRow().text(FatalErrorCode.PATTI_DELAY.name()).number(today.getPattiFatals()).number(mtd.getPattiFatals())
                .nextRow().text("Avg. Waiting time").text(KettleLoggingUtil.toTimeString(today.getWaitingTime()))
                    .text(KettleLoggingUtil.toTimeString(mtd.getWaitingTime()))
                .nextRow().text("Avg. Pickup time").text(KettleLoggingUtil.toTimeString(today.getPickupTime()))
                    .text(KettleLoggingUtil.toTimeString(mtd.getPickupTime()))
                .nextRow().text("Avg. Add Patti Time").text(KettleLoggingUtil.toTimeString(today.getAddPattiTime()))
                    .text(KettleLoggingUtil.toTimeString(mtd.getAddPattiTime()))
                .nextRow().text("Total No Pan Delays").number(today.getNoPanOrderDelays()).number(mtd.getNoPanOrderDelays())
                .nextRow().text("Avg. Time: No Pan Delay").text(KettleLoggingUtil.toTimeString(today.getAverageNoPanDelay()))
                    .text(KettleLoggingUtil.toTimeString(mtd.getAverageNoPanDelay()))
                .nextRow().text("Avg Deviation from Ideal (in sec)").number(today.getDeviationFromIdeal())
                    .number(mtd.getDeviationFromIdeal());
    }

    public void renderShiftSummaries(List<ShiftViewForUnit> shifts){
        List<MonkSummaryView> shiftSummaries = new ArrayList<>();
        for(ShiftViewForUnit shift : shifts){
            MonkLogDataHelper.generateShiftSummaryView(shiftSummaries, shift, this.opsTaskView);
        }
        writer.writeSheet(shiftSummaries,MonkShiftSummaryView.class);
    }

    @Override
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Override
    public void setGenerated(boolean value) {
        this.generated = value;
    }


}
