import React from "react";
import browserHistory from "react-router/lib/browserHistory";
import { connect } from "react-redux";
import MobileHeader from "./MobileHeader";
import * as CustomerActions from "./../../actions/CustomerActions";
import * as UtilityActions from "./../../actions/UtilityActions";
import trackUtils from "../../utils/TrackUtils";
import appUtil from "./../../AppUtil";

@connect((store) => {
    return {
        selectedCity:store.localityReducer.selectedCity,
        unit:store.outletMenuReducer.unit,
        customer: store.customerReducer.customer,
        showContactSection: store.customerReducer.showContactSection,
        isLogin: store.customerReducer.isLogin,
        getName: store.customerReducer.getName,
        getEmail: store.customerReducer.getEmail,
        redirectTo: store.customerReducer.redirectTo,
        otpResendSeconds:store.customerReducer.otpResendSeconds
    };
})
export default class MobileLoginLayout extends React.Component {

    constructor(){
        super();
        this.state = {
            otpValue: ""
        };
        this.loginUser = this.loginUser.bind(this);
        this.verifyUser = this.verifyUser.bind(this);
        this.resendVerification = this.resendVerification.bind(this);
    }

    loginUser(e){
        e.preventDefault();
        var contact = document.getElementById("userContactInput").value;
        if(!appUtil.checkEmpty(contact) && appUtil.validContact(contact)){
            this.props.dispatch(CustomerActions.lookupCustomer(contact));
        }else{
            this.props.dispatch(UtilityActions.showPopup("Please enter valid contact number!","info"));
        }
    }

    verifyUser(e){
        e.preventDefault();
        var otp = document.getElementById("otpInput").value;
        var name = null;
        var email = null;
        if(this.props.getName) {
            name = document.getElementById("nameInput").value;
        }
        if(this.props.getEmail) {
            email = document.getElementById("emailInput").value;
        }
        if(appUtil.checkEmpty(otp)){
            this.props.dispatch(UtilityActions.showPopup("Please enter one time password received on your mobile!","info"));
        }else if(this.props.getName && appUtil.checkEmpty(name)) {
            this.props.dispatch(UtilityActions.showPopup("Please enter name!","info"));
        }else if(this.props.getEmail && !appUtil.validEmail(email)) {
            this.props.dispatch(UtilityActions.showPopup("Please enter valid email address!","info"));
        }else {
            if(this.props.isLogin){
                this.props.dispatch(CustomerActions.loginCustomer(name, email, otp, this.props.getName, this.props.getEmail, this.props.customer.contact, this.props.redirectTo));
            }else {
                this.props.dispatch(CustomerActions.signUpCustomer(name, email, otp, this.props.customer.contact, this.props.redirectTo,
                appUtil.isInterstate(this.props.unit,this.props.selectedCity)));
            }
        }
    }

    resendVerification(){
        this.props.dispatch(CustomerActions.resendVerification(this.props.customer.contact));
    }

    componentWillMount(){
        window.scrollTo(0, 0);
        this.props.dispatch(CustomerActions.resetLogin());
    }

    componentDidMount(){
        if(document.getElementById("userContactInput")!=null){
            document.getElementById("userContactInput").focus();
        }
        trackUtils.trackPageView({page:"login",device:"mobile",custom:true});
    }

    componentDidUpdate(){
        if(!this.props.showContactSection && !this.state.otpValue){
            if(this.props.getName){
                document.getElementById("nameInput").focus();
                document.getElementById("nameInput").value = "";
            }else if(this.props.getEmail){
                document.getElementById("emailInput").focus();
            }else{
                document.getElementById("otpInput").focus();
            }
        }
    }

    handleChange(event){
        let targetValue = event.target.value;
        if(targetValue.length<=4 && !isNaN(targetValue))
            this.setState({otpValue:  event.target.value})

    }

    render() {

        var resendString = "";
        var min = 0,sec=0;
        if(this.props.otpResendSeconds>0){
            min = parseInt(this.props.otpResendSeconds/60);
            sec = this.props.otpResendSeconds%60;
            min>0?resendString+= min+" min ":" ";
            sec>0?resendString+= sec+" sec":"";
        }

        return (
            <div>
                <div class="colouredHead">
                    <MobileHeader menu={false} showLocationMetadata={false} showCartBtn={false} props={this.props.props} />
                </div>
                {this.props.showContactSection?(
                    <div class="mobilePageContainer">
                        <form name="lookupForm" action="#" onSubmit={this.loginUser.bind(this)}>
                            <div class="mobilePageHead">Login</div>
                            <div class="loginSectionTagline">Login with your Mobile No.</div>
                            <div class="contactContainer">
                                <input id="userContactInput" type="tel" placeholder="Enter Mobile No." maxLength="10" autoComplete="off" />
                            </div>
                            <div class="btn btn-primary" style={{marginTop:'16px'}} onClick={this.loginUser.bind(this)}>Login</div>
                        </form>
                    </div>
                ):(
                    <div class="mobilePageContainer">
                        <form name="verificationForm" action="#" onSubmit={this.verifyUser.bind(this)}>
                            <div class="mobilePageHead">Verification</div>
                            <div class="loginSectionTagline">An OTP was sent to {this.props.customer!=null?this.props.customer.contact:""}</div>
                            {this.props.getName?(
                                <div class="contactContainer">
                                    <input id="nameInput" style={{textTransform:"capitalize"}} type="text" placeholder="Enter your name" maxLength="70" autoComplete="off" />
                                </div>
                            ):null}
                            {this.props.getEmail?(
                                <div class="contactContainer">
                                    <input id="emailInput" type="email" placeholder="Enter your email" maxLength="100" autoComplete="off" />
                                </div>
                            ):null}
                            <div class="contactContainer">
                                <input id="otpInput" type="text" value={this.state.otpValue} onChange={this.handleChange.bind(this)} placeholder="Enter One Time Password" maxLength="4"
                                       autoComplete="off"/>
                            </div>
                            <div class="btn btn-primary" style={{marginTop:'16px'}} onClick={this.verifyUser.bind(this)}>Verify</div>
                            <div class="resendText">
                                {this.props.otpResendSeconds<=0?(
                                    <div>
                                        Didn't receive?
                                        <span class="resendLink" onClick={this.resendVerification.bind(this)}>Resend</span>
                                    </div>
                                ):(
                                    <div>
                                        Try again in {resendString}.
                                    </div>
                                )}
                            </div>
                        </form>
                    </div>
                )}
            </div>
        )
    }
}
