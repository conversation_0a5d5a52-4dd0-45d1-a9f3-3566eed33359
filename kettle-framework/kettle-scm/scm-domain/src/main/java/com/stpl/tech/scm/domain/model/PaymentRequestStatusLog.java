//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.09.11 at 11:20:21 AM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;


/**
 * <p>Java class for PaymentRequestStatusLog complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PaymentRequestStatusLog"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="fromStatus" type="{http://www.w3schools.com}PaymentRequestStatus"/&gt;
 *         &lt;element name="toStatus" type="{http://www.w3schools.com}PaymentRequestStatus"/&gt;
 *         &lt;element name="updatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="updateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="remarks" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PaymentRequestStatusLog", propOrder = {
    "id",
    "fromStatus",
    "toStatus",
    "updatedBy",
    "updateTime",
    "remarks"
})
public class PaymentRequestStatusLog {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PaymentRequestStatus fromStatus;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PaymentRequestStatus toStatus;
    @XmlElement(required = true)
    protected IdCodeName updatedBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date updateTime;
    @XmlElement(required = true)
    protected String remarks;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the fromStatus property.
     * 
     * @return
     *     possible object is
     *     {@link PaymentRequestStatus }
     *     
     */
    public PaymentRequestStatus getFromStatus() {
        return fromStatus;
    }

    /**
     * Sets the value of the fromStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link PaymentRequestStatus }
     *     
     */
    public void setFromStatus(PaymentRequestStatus value) {
        this.fromStatus = value;
    }

    /**
     * Gets the value of the toStatus property.
     * 
     * @return
     *     possible object is
     *     {@link PaymentRequestStatus }
     *     
     */
    public PaymentRequestStatus getToStatus() {
        return toStatus;
    }

    /**
     * Sets the value of the toStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link PaymentRequestStatus }
     *     
     */
    public void setToStatus(PaymentRequestStatus value) {
        this.toStatus = value;
    }

    /**
     * Gets the value of the updatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    /**
     * Sets the value of the updatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setUpdatedBy(IdCodeName value) {
        this.updatedBy = value;
    }

    /**
     * Gets the value of the updateTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * Sets the value of the updateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdateTime(Date value) {
        this.updateTime = value;
    }

    /**
     * Gets the value of the remarks property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getRemarks() {
        return remarks;
    }

    /**
     * Sets the value of the remarks property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setRemarks(String value) {
        this.remarks = value;
    }

}
