package com.stpl.tech.master.monk.configuration.model;

import com.stpl.tech.master.domain.model.IdCodeName;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 28-05-2018.
 */
public class AppBuildData {

    private String appName;
    private String name;
    private IdCodeName uploadDetails;
    private List<IdCodeName> units;

    public AppBuildData() {
    }

    public AppBuildData(String appName, IdCodeName uploadDetails) {
        this.appName = appName;
        String[] appStr = appName.split("_");
        this.name = appStr[0] + "_" + appStr[1];
        this.uploadDetails = uploadDetails;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public IdCodeName getUploadDetails() {
        return uploadDetails;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setUploadDetails(IdCodeName uploadUrl) {
        this.uploadDetails = uploadUrl;
    }

    public List<IdCodeName> getUnits() {
        if (units == null) {
            units = new ArrayList<>();
        }
        return units;
    }
}
