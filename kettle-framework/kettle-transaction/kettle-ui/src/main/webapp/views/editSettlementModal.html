<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header" data-ng-init="init()">
	<h3 style="margin: 0;">Update Settlement</h3>
</div>

<div class="modal-body">
	<div class="container-fluid">
		<div class="row">
			<div class="col-xs-12">
				<select class="form-control" id="paymentModeData" name="paymentModeData"
					data-ng-model="selectedMode" data-ng-options="data as data.name for data in paymentModes">
				</select>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-6" style="margin-top: 10px;">
				<button class='btn btn-default btn-lg' type="button"
					ng-click="setPaymentMode()">Save</button>
			</div>
			<div class="col-xs-6" style="margin-top: 10px;">
				<button class='btn btn-default btn-lg pull-right' type="button"
					ng-click="goBack()">Cancel</button>
			</div>
		</div>
	</div>
	<div class="modal-footer"></div>