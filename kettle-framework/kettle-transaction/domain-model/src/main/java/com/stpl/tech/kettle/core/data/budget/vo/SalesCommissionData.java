/**
 * 
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class SalesCommissionData {

	private BigDecimal creditCardTransactionCharges = new BigDecimal(0d);
	private BigDecimal voucherTransactionCharges = new BigDecimal(0d);
	private BigDecimal walletsTransactionCharges = new BigDecimal(0d);
	private BigDecimal commissionChannelPartners = new BigDecimal(0d);
	private BigDecimal commissionOthers = new BigDecimal(0d);

	public BigDecimal getCreditCardTransactionCharges() {
		return creditCardTransactionCharges;
	}

	public void setCreditCardTransactionCharges(BigDecimal creditCardTransactionCharges) {
		this.creditCardTransactionCharges = creditCardTransactionCharges;
	}

	public BigDecimal getVoucherTransactionCharges() {
		return voucherTransactionCharges;
	}

	public void setVoucherTransactionCharges(BigDecimal voucherTransactionCharges) {
		this.voucherTransactionCharges = voucherTransactionCharges;
	}

	public BigDecimal getWalletsTransactionCharges() {
		return walletsTransactionCharges;
	}

	public void setWalletsTransactionCharges(BigDecimal walletsTransactionCharges) {
		this.walletsTransactionCharges = walletsTransactionCharges;
	}

	public BigDecimal getCommissionChannelPartners() {
		return commissionChannelPartners;
	}

	public void setCommissionChannelPartners(BigDecimal commissionChannelPartners) {
		this.commissionChannelPartners = commissionChannelPartners;
	}

	public BigDecimal getCommissionOthers() {
		return commissionOthers;
	}

	public void setCommissionOthers(BigDecimal commissionOthers) {
		this.commissionOthers = commissionOthers;
	}

}
