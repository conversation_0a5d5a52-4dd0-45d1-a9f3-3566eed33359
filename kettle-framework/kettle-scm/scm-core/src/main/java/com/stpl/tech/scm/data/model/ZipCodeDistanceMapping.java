package com.stpl.tech.scm.data.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Table(name = "ZIP_CODE_DISTANCE_MAPPING")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ZipCodeDistanceMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="ID")
  private  Integer id;
    @Column(name = "SOURCE_ZIP_CODE")
    private String sourceZipCode;
    @Column(name="DESTINATION_ZIP_CODE")
    private String destinationZipCode;
    @Column(name="DISTANCE")
    private BigDecimal distance;

}
