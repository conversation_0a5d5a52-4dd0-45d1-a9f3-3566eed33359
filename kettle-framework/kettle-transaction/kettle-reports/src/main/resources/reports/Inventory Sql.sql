SELECT 
    oi.UNIT_ID,
    ud.UNIT_NAME,
    pd.PRODUCT_ID,
    pd.PRODUCT_NAME,
    oi.DAY_OF_THE_WEEK,
    CASE
        WHEN oi.DAY_OF_THE_WEEK = 0 THEN 'MONDAY'
        WHEN oi.DAY_OF_THE_WEEK = 1 THEN 'TUESDAY'
        WHEN oi.DAY_OF_THE_WEEK = 2 THEN 'WEDNESDAY'
        WHEN oi.DAY_OF_THE_WEEK = 3 THEN 'THURSDAY'
        WHEN oi.DAY_OF_THE_WEEK = 4 THEN 'FRIDAY'
        WHEN oi.DAY_OF_THE_WEEK = 5 THEN 'SATURDAY'
        WHEN oi.DAY_OF_THE_WEEK = 6 THEN 'SUNDAY'
    END WEEK_DAY,
    oi.TOTAL_DAYS,
    oi.TOTAL_QUANTITY,
    oi.MAX_QUANTITY,
    oi.MIN_QUANTITY,
    oi.AVG_QUANTITY
FROM
    PRODUCT_DETAIL pd
        LEFT OUTER JOIN
    (SELECT 
        b.UNIT_ID UNIT_ID,
            b.PRODUCT_ID PRODUCT_ID,
            b.DAY_OF_THE_WEEK DAY_OF_THE_WEEK,
            COUNT(DISTINCT b.BUSINESS_DATE) TOTAL_DAYS,
            SUM(b.TOTAL_QUANTITY) TOTAL_QUANTITY,
            MAX(b.TOTAL_QUANTITY) MAX_QUANTITY,
            MIN(b.TOTAL_QUANTITY) MIN_QUANTITY,
            CEIL(SUM(b.TOTAL_QUANTITY) / COUNT(DISTINCT b.BUSINESS_DATE)) AVG_QUANTITY
    FROM
        (SELECT 
        a.UNIT_ID,
            a.PRODUCT_ID,
            a.DAY_OF_THE_WEEK,
            a.BUSINESS_DATE,
            SUM(a.QUANTITY) TOTAL_QUANTITY
    FROM
        (SELECT 
        CASE
                WHEN HOUR(od.BILL_GENERATION_TIME) <= 5 THEN WEEKDAY(DATE_ADD(BILL_GENERATION_TIME, INTERVAL - 6 HOUR))
                ELSE WEEKDAY(od.BILL_GENERATION_TIME)
            END DAY_OF_THE_WEEK,
            CASE
                WHEN HOUR(od.BILL_GENERATION_TIME) <= 5 THEN DATE(DATE_ADD(BILL_GENERATION_TIME, INTERVAL - 6 HOUR))
                ELSE DATE(od.BILL_GENERATION_TIME)
            END BUSINESS_DATE,
            od.UNIT_ID,
            oi.PRODUCT_ID,
            oi.QUANTITY
    FROM
        ORDER_DETAIL od, ORDER_ITEM oi
    WHERE
        od.ORDER_ID = oi.ORDER_ID
            AND od.ORDER_STATUS = 'SETTLED') a
    GROUP BY a.UNIT_ID , a.PRODUCT_ID , a.DAY_OF_THE_WEEK , a.BUSINESS_DATE) b
    GROUP BY b.UNIT_ID , b.PRODUCT_ID , b.DAY_OF_THE_WEEK) oi ON pd.PRODUCT_ID = oi.PRODUCT_ID
        LEFT OUTER JOIN
    UNIT_DETAIL ud ON oi.UNIT_ID = ud.UNIT_ID;