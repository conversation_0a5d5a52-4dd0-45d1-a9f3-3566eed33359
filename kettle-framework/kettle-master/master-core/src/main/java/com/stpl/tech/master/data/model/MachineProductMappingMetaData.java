package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "MACHINE_PRODUCT_MAPPING_METADATA")
public class MachineProductMappingMetaData {

    @Id
    @Column(name = "MACHINE_ID", nullable = false)
    private Integer machineId;

    @Column(name = "MACHINE_NAME", nullable = false)
    private String machineName;

    @Column(name = "PRODUCT_IDS", nullable = false)
    private String productIds;

}
