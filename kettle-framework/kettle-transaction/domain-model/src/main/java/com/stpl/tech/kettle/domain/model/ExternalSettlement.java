/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.02.29 at 04:02:18 PM IST
//

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * Java class for Settlement complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="ExternalSettlement"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="externalTransactionId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ExternalSettlement", propOrder = {
	"externalSettlementId",
    "amount",
    "externalTransactionId"
})
@Document
public class ExternalSettlement implements Serializable{

	/**
	 *
	 */
	private static final long serialVersionUID = 7597118806842459583L;

	@Id
	private String _id;
/*
	@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;*/

	@Field
	protected int externalSettlementId;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal amount;
    @XmlElement(required = true)
    protected String externalTransactionId;

    public ExternalSettlement() {
		// TODO Auto-generated constructor stub
	}

	public ExternalSettlement(int externalSettlementId, BigDecimal amount, String externalTransactionId) {
		super();
		this.externalSettlementId = externalSettlementId;
		this.amount = amount;
		this.externalTransactionId = externalTransactionId;
	}

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/*public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}*/

	/**
	 * Gets the value of the settlementId property.
	 *
	 */
	public int getExternalSettlementId() {
		return externalSettlementId;
	}

	/**
	 * Sets the value of the settlementId property.
	 *
	 */
	public void setExternalSettlementId(int value) {
		this.externalSettlementId = value;
	}

	/**
	 * Gets the value of the amount property.
	 *
	 * @return possible object is {@link BigDecimal }
	 *
	 */
	public BigDecimal getAmount() {
		return amount;
	}

	/**
	 * Sets the value of the amount property.
	 *
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 *
	 */
	public void setAmount(BigDecimal value) {
		this.amount = value;
	}

    /**
     * Gets the value of the externalTransactionId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getExternalTransactionId() {
        return externalTransactionId;
    }

    /**
     * Sets the value of the externalTransactionId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setExternalTransactionId(String value) {
        this.externalTransactionId = value;
    }

}
