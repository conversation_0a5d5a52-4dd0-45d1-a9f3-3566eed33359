/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 25-04-2016.
 */
(function () {
    'use strict';

    angular.module('scmApp').factory('appUtil', AppUtil);
    AppUtil.$inject = ['$rootScope', '$alertService'];
    function AppUtil($rootScope, $alertService) {
        var service = {};

        var a = ['','One ','Two ','Three ','Four ', 'Five ','Six ','Seven ','Eight ','Nine ','Ten ','Eleven ','Twelve ','Thirteen ','Fourteen ','Fifteen ','Sixteen ','Seventeen ','Eighteen ','Nineteen '];
        var b = ['', '', 'Twenty','Thirty','Forty','Fifty', 'Sixty','Seventy','Eighty','Ninety'];

        service.mimeTypes =  {
            PDF:"application/pdf",
            XLSX:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            XLS:"application/vnd.ms-excel",
            JPG:"image/jpg",
            JPEG:"image/jpeg",
            PNG:"image/png",
            CSV:"text/plain",
            TXT:"text/plain",
            JSON:"application/json"
        };
        service.metadata = {};
        service.getMetadata = getMetadata;
        service.setMetadata = setMetadata;
        service.createRequestUnit = createRequestUnit;
        service.createGeneratedBy = createGeneratedBy;
        service.formatDate = formatDate;
        service.isImage = isImage;
        service.getIdCodeName = getIdCodeName;
        service.getAdvanceBufferAmount = getAdvanceBufferAmount;

        function getAdvanceBufferAmount (amount) {
            return parseFloat((amount * 0.05).toFixed(0));
        }

        service.isFloat = function(n){
            return Number(n) === n && n % 1 !== 0;
        };

        service.getDate = function(days){
            var time = new Date();
            time.setDate(time.getDate() + days);
            return time.toISOString();
        };

        service.getDateWithoutTime = function(){
            var date = new Date(service.getCurrentBusinessDate());
            return formatDate(date,'yyyy-MM-dd');
        };

        service.convertToDateWithoutTime = function(date){
            var date = new Date(date);
            return formatDate(date,'yyyy-MM-dd');
        };

        service.calculatedDate = function(days, date){
        	var time = null;
        	if(angular.isUndefined(date) || date == null){
        		time = new Date();
        	}else{
        		time = new Date(date);
        	}
           time.setDate(time.getDate() + days);
           return time;
        };

        service.getFormattedDate = function(milliseconds){
            var time = new Date(milliseconds);
            var year = time.getFullYear();
            var month = time.getMonth()+1;
            var date = time.getDate();
            return date+"-"+ month + "-" + year;
        };


        service.getTimeInPast = function(days){
            var time = new Date();
            time.setDate(time.getDate() - days);
            return time.toISOString();
        };

        service.getTimeInFuture = function(days){
            var time = new Date();
            time.setDate(time.getDate() + days);
            return time.toISOString();
        };

        service.datediff = datediff;
        service.datediffRO = datediffRO;

        service.getCurrentBusinessDate = getCurrentBusinessDate;
        service.getRegularOrderingDate = getRegularOrderingDate;

        service.getUnitList = getUnitList;
        service.setUnitList = setUnitList;
        service.unitList = [];
        service.filterCurrentUnit = filterCurrentUnit;
        service.setPermissions = setPermissions;
        service.permissions = [];
        service.getPermissions = getPermissions;
        service.setCurrentUser = setCurrentUser;
        service.getCurrentUser = getCurrentUser;
        service.currentUser = null;
        service.refOrderListObj = {
            startDate: null,
            endDate: null,
            referenceOrderId: null,
            status: null,
            referenceOrderList: []
        };
        service.getRefOrderListObj = getRefOrderListObj;
        service.setRefOrderListObj = setRefOrderListObj;
        service.reqOrderListObj = {
            startDate: null,
            endDate: null,
            requestOrderId: null,
            fulfillingUnit: null,
            status: null,
            requestOrderList: []
        };
        service.getReqOrderListObj = getReqOrderListObj;
        service.setReqOrderListObj = setReqOrderListObj;
        service.trOrderListObj = {
            startDate: null,
            endDate: null,
            transferOrderId: null,
            generatedForUnit: null,
            status: null,
            transferOrderList: [],
            selectedTOs : [],
            allSelected : false
        };
        service.getTrOrderListObj = getTrOrderListObj;
        service.setTrOrderListObj = setTrOrderListObj;

        service.bulkTrOrderListObj = {
            startDate : null,
            endDate : null,
            bulkEventList : [],
            selectedEventId : null,
            selectedEventTOs : [],
            selectedTOs : [],
            allSelected : false
        }

        service.getBulkTrOrderListObj = getBulkTrOrderListObj;
        service.setBulkTrOrderListObj = setBulkTrOrderListObj;

        service.grOrderListObj = {
            startDate: null,
            endDate: null,
            goodsReceiveOrderId: null,
            generationUnit: null,
            status: null,
            goodsReceiveOrderList: []
        };
        service.getGrOrderListObj = getGrOrderListObj;
        service.setGrOrderListObj = setGrOrderListObj;
        service.menuProductCategories = [];
        service.setTaxProfiles = setTaxProfiles;
        service.getTaxProfiles = getTaxProfiles;
        service.getTaxProfile = getTaxProfile;
        service.setMenuProductCategories = setMenuProductCategories;
        service.getMenuProductCategories = getMenuProductCategories;
        service.unitData = null;
        service.getUnitData = getUnitData;
        service.setUnitData = setUnitData;
        service.scmProductDetails = [];
        service.getScmProductDetails = getScmProductDetails;
        service.setScmProductDetails = setScmProductDetails;
        service.skuProductMap = {};
        service.getSkuProductMap = getSkuProductMap;
        service.setSkuProductMap = setSkuProductMap;
        service.packagingMap = {};
        service.getPackagingMap = getPackagingMap;
        service.setPackagingMap = setPackagingMap;
        service.checkPermission = checkPermission;
        service.isWarehouseOrKitchen = isWarehouseOrKitchen;
        service.isWarehouseOrKitchenByCategory = isWarehouseOrKitchenByCategory;
        service.isWarehouseByCategory = isWarehouseByCategory;
        service.isWarehouse = isWarehouse;
        service.isKitchen = isKitchen;
        service.setCategoryAttributeValues = setCategoryAttributeValues;
        service.getCategoryAttributeValues = getCategoryAttributeValues;
        service.getSavedInventoryList = getSavedInventoryList;
        service.setSavedInventoryList = setSavedInventoryList;
        service.updateSkuMappingsInCache = updateSkuMappingsInCache;
        service.inWords = inWords;
        service.checkExactPermission = checkExactPermission;
        service.findUnitDetail = findUnitDetail;
        service.addProductToCache = addProductToCache;
        service.updateProductInCache = updateProductInCache;
        service.removeProductFromCache = removeProductFromCache;
        service.checkAttributeType = checkAttributeType;
        service.addSkuToCache = addSkuToCache;
        service.updateSkuInCache = updateSkuInCache;
        service.getActiveScmProducts = getActiveScmProducts;
        service.getActiveScmOrderingProducts = getActiveScmOrderingProducts;
        service.filterUnitList = filterUnitList;
        service.showFilteredUnitList = showFilteredUnitList;
        service.getModule = getModule;
        service.getCompanyList = getCompanyList;
        service.setCompanyList = setCompanyList;
        service.companyList = [];
        service.companyMap = {};
        service.getCompanyMap = getCompanyMap;
        service.getDefaultCompany = getDefaultCompany;
        service.brandList=[];
        service.getBrandList=getBrandList;
        service.setBrandList=setBrandList;
        service.getGridOptions = function  getGridOptions(scope){
            return {
                enableGridMenu: true,
                exporterExcelFilename: 'download.xlsx',
                exporterExcelSheetName: 'Sheet1',
                enableColumnMenus: true,
                saveFocus: false,
                enableRowSelection: true,
                enableFiltering: true,
                saveScroll: true,
                enableSelectAll: true,
                multiSelect: true,
                enableColumnResizing: true,
                exporterMenuPdf : false,
                exporterMenuExcel : true,
                fastWatch: true,
                onRegisterApi: function (gridApi) {
                    scope.gridApi = gridApi;
                }
            }
        }

        service.removeSavedInventoryList = function(){
            if(!service.isEmptyObject(localStorage.getItem("inventoryDataList"))){
                this.inventoryDataList = null;
                localStorage.removeItem("inventoryDataList");
            }
        };

        service.isNumberInteger = function(value){
        	return !isNaN(value) && (function(x) { return (x | 0) === x; })(parseFloat(value));
        };

        service.isCafe = function(){
            return (!service.isEmptyObject(service.getUnitData()) && service.getUnitData().family == "CAFE");
        };

        service.checkPermissions = function (permission, userPermissions, type) {
            console.log("inside checkPermissions function", permission, userPermissions);
            var flag = false;
            var permissionObj = {view: 1, add: 2, update: 4, delete: 8};
            for (var permissionKey in userPermissions) {
                var result = permissionObj.view & userPermissions[permissionKey];
                if (permissionKey == permission && result == permissionObj[type]) {
                    flag = true;
                    break;
                }
            }
            return flag;
        };
        service.stripPermissions = function (permission) {
            console.log("inside stripPermissions function", permission);
            if (permission.lastIndexOf("*") == permission.length - 1) {
                for (var i = 0; i < 2; i++) {
                    permission = permission.substr(0, permission.lastIndexOf("."));
                }
            } else {
                permission = permission.substr(0, permission.lastIndexOf("."));
            }
            if (permission.indexOf(".") != -1) {
                permission += ".*";
            }
            console.log("stripped permission is ::: ", permission);
            console.log("result permission is ::", permission);
            return permission;
        };

        service.isEmptyObject = function (obj) {
            if (obj != undefined && obj != null) {
                if (typeof obj == 'string' || typeof obj == 'number')
                    return obj.toString().length == 0;
                else
                    return Object.keys(obj).length == 0;
            }
            return true;
        };

        service.getCategory = function (id) {
            if (!service.isEmptyObject(service.metadata.categoryDefinitions)) {
                var categories = service.metadata.categoryDefinitions;
                for (var index in categories) {
                    if (categories[index].categoryId == id) {
                        return categories[index];
                    }
                }
            }
            return null;
        };

        service.getSubCategory = function (id) {
            var ret = null;
            if (id != undefined && id != null && !service.isEmptyObject(service.getMetadata().subCategoryDefinitions)) {
                service.getMetadata().subCategoryDefinitions.forEach(function (cat) {
                    if(cat.subCategoryId==id){
                        ret = cat;
                    }
                })
            }
            return ret;
        };

        service.getAttribute = function (id) {
            if (!service.isEmptyObject(service.metadata.attributeDefinitions)) {
                for (var type in service.metadata.attributeDefinitions) {
                    for (var index in service.metadata.attributeDefinitions[type]) {
                        if (service.metadata.attributeDefinitions[type][index].attributeId == id) {
                            return service.metadata.attributeDefinitions[type][index];
                        }
                    }
                }
            }
            return null;
        };

        service.checkEmpty = function (str) {
            var type = typeof str;
            if (type == 'string') {
                return str.trim().length == 0;
            } else if (type == "number") {
                return str == -1;
            } else if (type == null || type == undefined) {
                return true;
            } else {
                return service.isEmptyObject(str);
            }
        };

        function setHMS(date) {
            var result = new Date(date);
            result.setHours(0);
            result.setMinutes(0);
            result.setSeconds(0);
            return result;
        }

        function datediffRO(from, to) {
            var fromDateRo = setHMS(from);
            var toDateRo = setHMS(to);
            var timeDiffRo = Math.abs(fromDateRo.getTime() - toDateRo.getTime());
            var diffDaysRo = Math.ceil(timeDiffRo / (1000 * 3600 * 24));
            return diffDaysRo;
        }

        function datediff(from, to) {
            var fromDate = new Date(from);
            var toDate = new Date(to);
            var timeDiff = Math.abs(fromDate.getTime() - toDate.getTime());
            var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
            return diffDays;
        }

        function isWarehouseOrKitchen(unit){
            var flag = false;
            var unitData = unit!=undefined ? unit : service.getUnitData();
            if(!service.isEmptyObject(unitData)){
                flag = (unitData.family == "WAREHOUSE" || unitData.family=="KITCHEN");
            }
            return flag;
        }

        function isWarehouseOrKitchenByCategory(unit){
            var flag = false;
            var unitData = unit!=undefined ? unit : service.getUnitData();
            if(!service.isEmptyObject(unitData)){
                flag = (unitData.category == "WAREHOUSE" || unitData.category=="KITCHEN");
            }
            return flag;
        }

        function isWarehouseByCategory(unit){
            var flag = false;
            var unitData = unit!=undefined ? unit : service.getUnitData();
            if(!service.isEmptyObject(unitData)){
                flag = unitData.category == "WAREHOUSE" ;
            }
            return flag;
        }

        function isWarehouse(unit){
            var flag = false;
            var unitData = unit!=undefined ? unit : service.getUnitData();
            if(!service.isEmptyObject(unitData)){
                flag = (unitData.family == "WAREHOUSE");
            }
            return flag;
        }

        function isKitchen(unit){
            var flag = false;
            var unitData = unit!=undefined ? unit : service.getUnitData();
            if(!service.isEmptyObject(unitData)){
                flag = (unitData.family == "KITCHEN");
            }
            return flag;
        }

        function getCurrentBusinessDate() {
            var time = new Date();
            if(time.getHours() < 5){
                time.setDate(time.getDate() - 1);
            }
            console.log(time.toDateString());
            return formatDate(time,"yyyy-MM-dd");
        }

        function getRegularOrderingDate() {
            var time = new Date();
            if(time.getHours() < 10){
                time.setDate(time.getDate() - 1);
            }
            // time.setDate(time.getDate() - 1);
            // console.log(time.toDateString());
            time.setHours(0);
            time.setMinutes(0);
            time.setSeconds(0);
            console.log("Regular Ordering events time check is : ",time);
            return time;
        }

        function createRequestUnit() {
            var currentUser = this.getCurrentUser();
            return {
                id: currentUser.unitId,
                code: null,
                name: "Good Earth City Center"
            }
        }

        function createGeneratedBy() {
            var currentUser = this.getCurrentUser();
            return {
                id: currentUser.user.id,
                code: null,
                name: currentUser.user.name
            }
        }

        function getMetadata(){
            if(this.metadata==null || service.isEmptyObject(this.metadata)){
                this.metadata = JSON.parse(localStorage.getItem("scmMetadata"));
            }
            return this.metadata;
        }

        function setMetadata(metadata){
            localStorage.setItem("scmMetadata", JSON.stringify(metadata));
            this.metadata = metadata;
        }

        function setCompanyList(list) {
            this.companyList = list;
            localStorage.setItem("allCompanyList", JSON.stringify(list));
            for(var i = 0;i< this.companyList.length;i++){
            	this.companyMap[this.companyList[i].id] = this.companyList[i];
            }
        }

        function getDefaultCompany(){
        	for(var i=0;i<this.companyList.length;i++){
            	if( this.companyList[i].id == 1000){
            		return this.companyList[i];
            	}
            }
        }

        function getCompanyMap() {
            return this.companyMap;
        }

        function getCompanyList() {
            if (this.companyList == null || this.companyList.length == 0) {
                this.companyList = JSON.parse(localStorage.getItem("allCompanyList"));
            }
            return this.companyList;
        }

        function setBrandList(list) {
            this.brandList = list;

        }
        function getBrandList() {

            return this.brandList;
        }



        function setUnitList(list) {
            this.unitList = list;
            localStorage.setItem("allUnitList", JSON.stringify(list));
        }

        function getUnitList() {
            if (this.unitList == null || this.unitList.length == 0) {
                this.unitList = JSON.parse(localStorage.getItem("allUnitList"));
            }
            return this.unitList;
        }

        function filterCurrentUnit(unitList) {
            var list = [];
            var currentUnitId = this.getCurrentUser().unitId;
            unitList.map(function (unit) {
                if(unit.id!==currentUnitId){
                    list.push(unit);
                }
            });
            return list;
        }

        function getPermissions() {
            if (this.permissions == null) {
                this.permissions = JSON.parse(localStorage.getItem("scmPermissions"));
            }
            return this.permissions;
        }

        function setPermissions(permissions) {
            this.permissions = permissions;
            localStorage.setItem("scmPermissions", JSON.stringify(permissions));
        }

        function setCurrentUser(user) {
            this.currentUser = user;
            if(!service.isEmptyObject(this.currentUser.user)){
                removeUserAttributes(this.currentUser.user);
            }
            localStorage.setItem("scmCurrentUser", JSON.stringify(user));
        }

        function removeUserAttributes(user){
            try{
                delete user.reportingManager;
                delete user.units;
                delete user.applications;
                delete user.currentAddress;
                delete user.permanentAddress;
                delete user.department.designations;
            }catch (e){
                console.log("Could not delete user attributes",e);
            }
        }

        function getCurrentUser() {
            if (this.currentUser == null) {
                this.currentUser = JSON.parse(localStorage.getItem("scmCurrentUser"));
            }
            return this.currentUser;
        }

        function setRefOrderListObj(obj){
            this.refOrderListObj = obj;
        }

        function getRefOrderListObj(){
            return this.refOrderListObj;
        }

        function setReqOrderListObj(obj){
            this.reqOrderListObj = obj;
        }

        function getReqOrderListObj(){
            return this.reqOrderListObj;
        }

        function setTrOrderListObj(obj){
            this.trOrderListObj = obj;
        }

        function getTrOrderListObj(){
            return this.trOrderListObj;
        }

        function setGrOrderListObj(obj){
            this.grOrderListObj = obj;
        }

        function getBulkTrOrderListObj() {
            return this.bulkTrOrderListObj;
        }

        function setBulkTrOrderListObj(obj){
            this.bulkTrOrderListObj = obj;
        }

        function getGrOrderListObj(){
            return this.grOrderListObj;
        }

        function getCategoryAttributeValues(values){
            if(this.categoryAttributeValues==null || this.categoryAttributeValues.length==0){
                this.categoryAttributeValues = JSON.parse(localStorage.getItem("categoryAttributeValues"));
            }
            return this.categoryAttributeValues;
        }

        function setCategoryAttributeValues(values){
            localStorage.setItem("categoryAttributeValues",JSON.stringify(values));
            this.categoryAttributeValues = values;
        }

        function setMenuProductCategories(categories){
            localStorage.setItem("menuProductCategories", JSON.stringify(categories));
            this.menuProductCategories = categories;
        }

        function setTaxHsnList(taxCodesHsn){
            localStorage.setItem("taxHsnCodes", JSON.stringify(taxCodesHsn));
            this.taxHsnCodes = taxCodesHsn;
        }

        function getTaxHsnList(){
            var taxHsnCodes = JSON.parse(localStorage.getItem("taxHsnCodes"));
            for(var i in taxHsnCodes){
        	    return taxHsnCodes[i];
            }
            return null;
        }

        function setTaxProfiles(taxes){
            localStorage.setItem("taxCodes", JSON.stringify(taxes));
            this.taxCodes = taxes;
        }

        function getTaxProfiles(){
            if(this.taxCodes==null || this.taxCodes.length==0){
                this.taxCodes = JSON.parse(localStorage.getItem("taxCodes"));
            }
            return angular.copy(this.taxCodes);
        }

        function getTaxProfile(code){
            var taxCodes = JSON.parse(localStorage.getItem("taxCodes"));
            for(var i in taxCodes){
        	if(taxCodes[i].code == code){
        	    return taxCodes[i];
        	}
            }
            return null;
        }
        function getMenuProductCategories(){
            if(this.menuProductCategories==null || this.menuProductCategories.length==0){
                this.menuProductCategories = JSON.parse(localStorage.getItem("menuProductCategories"));
            }
            return angular.copy(this.menuProductCategories);
        }

        function setUnitData(data){
            this.unitData = data;
            if(!service.isEmptyObject(this.unitData.unitManager)){
                removeUserAttributes(this.unitData.unitManager);
            }
            localStorage.setItem("unitData", JSON.stringify(this.unitData));
        }

        function getUnitData(){
            if(this.unitData==null){
                this.unitData = JSON.parse(localStorage.getItem("unitData"));
            }
            return this.unitData;
        }

        function getScmProductDetails(){
            // if(this.scmProductDetails==null || this.scmProductDetails.length==0){
            //     this.scmProductDetails = JSON.parse(localStorage.getItem("scmProductDetails"));
            // }
            return this.scmProductDetails;
        }

        function getSavedInventoryList(){
            if(this.inventoryDataList==null || this.inventoryDataList.length==0){
                this.inventoryDataList = JSON.parse(localStorage.getItem("inventoryDataList"));
            }
            return this.inventoryDataList;
        }

        function setSavedInventoryList(data){
            localStorage.setItem("inventoryDataList", JSON.stringify(data));
            this.inventoryDataList = data;
        }


        function setScmProductDetails(data){
            // localStorage.setItem("scmProductDetails", JSON.stringify(data));
            this.scmProductDetails = data;
        }

        function getSkuProductMap(force){
            if(force || this.skuProductMap==null || service.isEmptyObject(this.skuProductMap)){
                this.skuProductMap = JSON.parse(localStorage.getItem("skuProductMap"));
            }
            return this.skuProductMap;
        }

        function setSkuProductMap(data){
            this.skuProductMap = data;
            //localStorage.setItem("skuProductMap", JSON.stringify(data));
        }

        function getPackagingMap(){
            if(this.packagingMap==null || service.isEmptyObject(this.packagingMap)){
                this.packagingMap = JSON.parse(localStorage.getItem("packagingMap"));
            }
            return this.packagingMap;
        }

        function setPackagingMap(data){
            this.packagingMap = data;
            localStorage.setItem("packagingMap", JSON.stringify(data));
        }

        function checkExactPermission(permission){
            var ret = false;
            if(JSON.parse(localStorage.getItem("scmCurrentUser"))!=null){
                var sessionKey = JSON.parse(localStorage.getItem("scmCurrentUser")).sessionKeyId;
                var map = JSON.parse(localStorage.getItem("scmPermissions"));
                map = map[sessionKey];
                // console.log(permission);
                if(permission in map){
                    ret = true;
                }
            }
            return ret;
        }

        function checkPermission(permission){
            var ret = false;
            if(JSON.parse(localStorage.getItem("scmCurrentUser"))!=null){
                var sessionKey = JSON.parse(localStorage.getItem("scmCurrentUser")).sessionKeyId;
                var map = JSON.parse(localStorage.getItem("scmPermissions"));
                map = map[sessionKey];
                while(permission.length>0){
                    // console.log(permission);
                    if(permission in map){
                        ret = true;
                        break;
                    }else{
                        if(permission.indexOf(".*")!=-1){
                            permission = permission.substring(0, permission.lastIndexOf('.'));
                        }
                        if (permission.length!=0 && permission.indexOf(".")!=-1) {
                            permission = permission.substring(0, permission.lastIndexOf('.'));
                            permission = permission+".*";
                        } else {
                            break;
                        }
                    }
                }
            }
            return ret;
        }

        function formatDate(date, format){
            var time = new Date(date);
            var yyyy = time.getFullYear();
            var M = time.getMonth()+1;
            var d = time.getDate();
            var MM = M;
            var dd = d;
            var hh = time.getHours();
            var mm = time.getMinutes();
            var ss = time.getSeconds();
            if(M<10){
                MM = "0"+M;
            }
            if(d<10){
                dd = "0"+d;
            }
            if(hh<10){
                hh = "0"+hh;
            }
            if(mm<10){
                mm = "0"+mm;
            }
            if(ss<10){
                ss = "0"+ss;
            }
            if(format=="yyyy-MM-dd"){
                return yyyy+"-"+MM+"-"+dd;
            }
            if(format=="dd/MM/yyyy"){
                return dd+"/"+MM+"/"+yyyy;
            }
            if(format=="dd-MM-yyyy-hh-mm-ss"){
                return dd+"-"+MM+"-"+yyyy+"-"+hh+"-"+mm+"-"+ss;
            }
            if(format=="yyyy-MM-dd hh-mm-ss"){
                return yyyy+"-"+MM+"-"+dd+" "+hh+":"+mm+":"+ss;
            }
            if(format=="dd-MM-yyyy"){
                return dd+"-"+MM+"-"+yyyy;
            }
            var monthNames = ["Jan", "Feb", "Mar", "Apr",
                                "May", "Jun", "Jul", "Aug",
                                "Sep", "Oct", "Nov", "Dec"];
            if(format=="dd-MMM-yyyy")
            {
                return dd+ " " + monthNames[M-1] + " " + yyyy;
            }
        }

        function findUnitDetail(unitId){
            var ret = null;
            this.getUnitList().forEach(function (unit) {
                if(unit.id==unitId){
                    console.log("returning unit ", unit);
                    ret = unit;
                }
            });
            return ret;
        }

        function addProductToCache(product){
            var products = service.getScmProductDetails();
            var skuProductMap = service.getSkuProductMap();
            products.push(product);
            service.setScmProductDetails(products);
            service.setSkuProductMap(skuProductMap);
        }

        function updateProductInCache(product){
            var products = service.getScmProductDetails();
            console.log(product);
            products.forEach(function (item, index) {
                if(item.productId==product.productId){
                    products.splice(index, 1, product);
                }
            });
            service.setScmProductDetails(products);
        }

        function removeProductFromCache(product) {
            var products = service.getScmProductDetails();
            console.log(product);
            products.forEach(function (item, index) {
                if(item.productId==product.productId){
                    products.splice(index, 1);
                }
            });
            service.setScmProductDetails(products);
        }

        function checkAttributeType(id, type){
            var ret = false;
            service.getMetadata().attributeDefinitions[type].forEach(function (attr) {
                if(attr.attributeId==id){
                    ret = true;
                }
            });
            return ret;
        }

        function addSkuToCache(sku){
            var skuProductMap = service.getSkuProductMap();
            var skuList = skuProductMap[sku.linkedProduct.id];
            if(skuList==undefined || skuList==null){
                skuList = [];
            }
            skuList.push(sku);
            skuProductMap[sku.linkedProduct.id] = skuList;
            service.setSkuProductMap(skuProductMap);
        }

        function updateSkuInCache(sku){
            var skuProductMap = service.getSkuProductMap();
            var skuList = skuProductMap[sku.linkedProduct.id];
            skuList.forEach(function (item,index) {
                if(item.skuId==sku.skuId){
                    skuList.splice(index, 1, sku);
                }else{
                    if(sku.isDefault){
                        item.isDefault = false;
                    }
                }
            });
            skuProductMap[sku.linkedProduct.id] = skuList;
            service.setSkuProductMap(skuProductMap);
        }

        function updateSkuMappingsInCache(productId,skuId,packagingMappingList){
            var skuProductMap = service.getSkuProductMap();
            var skuList = skuProductMap[productId];
            skuList.forEach(function (item) {
                if(item.skuId == skuId){
                    item.skuPackagings = packagingMappingList;
                }
            });
            skuProductMap[productId] = skuList;
            service.setSkuProductMap(skuProductMap);
        }

        function getActiveScmProducts(){
            var prods = [];
            service.getScmProductDetails().filter(function(p){
                return isWarehouseOrKitchen(service.getUnitData()) || p.availableAtCafe;
            }).forEach(function (item) {
                if(item.productStatus=="ACTIVE"){
                    prods.push(item);
                }
            });
            return prods;
        }

        function getActiveScmOrderingProducts(assetOrder){
            var prods = [];
            var unit = service.getUnitData();
            var unitId = unit.unitId;
            service.getScmProductDetails().filter(function(p){
                return isWarehouseOrKitchen(unit) || p.availableAtCafe;
            }).forEach(function (item) {
                if(item.productStatus=="ACTIVE" &&
                    ((!assetOrder && !item.assetOrdering) || (assetOrder && item.assetOrdering))){
                		prods.push(item);
                }
            });
            return prods;
        }

        function inWords (num) {
            if ((num = num.toString()).length > 9) return 'overflow';
            var n = ('000000000' + num).substr(-9).match(/^(\d{2})(\d{2})(\d{2})(\d{1})(\d{2})$/);
            if (!n) return; var str = '';
            str += (n[1] != 0) ? (a[Number(n[1])] || b[n[1][0]] + ' ' + a[n[1][1]]) + 'Crore ' : '';
            str += (n[2] != 0) ? (a[Number(n[2])] || b[n[2][0]] + ' ' + a[n[2][1]]) + 'Lakh ' : '';
            str += (n[3] != 0) ? (a[Number(n[3])] || b[n[3][0]] + ' ' + a[n[3][1]]) + 'Thousand ' : '';
            str += (n[4] != 0) ? (a[Number(n[4])] || b[n[4][0]] + ' ' + a[n[4][1]]) + 'Hundred ' : '';
            str += (n[5] != 0) ? ((str != '') ? 'and ' : '') + (a[Number(n[5])] || b[n[5][0]] + ' ' + a[n[5][1]]) + 'Only ' : '';
            return str;
        }

        function filterUnitList(list) {
            var ret = [];
            if($rootScope.aclData.action.SHFUD!=null){
                list.map(function (unit) {
                    if($rootScope.mappedUnits.indexOf(unit.id)>=0){
                        ret.push(unit);
                    }
                });
            }else{
                ret = list;
            }
            return ret;
        }

        function showFilteredUnitList() {
            return $rootScope.aclData.action.SHFUD!=null;
        }

        function getModule(url) {
            //"http://localhost:8080/master-service/rest/v1/unit-metadata/all-units-list"
            url = url.replace("rest/v1/","");
            url = url.substr(url.lastIndexOf(":"));
            url = url.substr(url.indexOf("/")+1);
            url = url.replace(/\//g,".");
            return url;
        }

        function isImage(fileExt){
            return fileExt=="jpg" || fileExt == "jpeg" || fileExt=="png";
        }

        function getIdCodeName(dataId, name, code){
    		var idCodeName ={
    				id : dataId,
    				code : (code === undefined ? null : code),
    				name : (name === undefined ? null : name)
    		};
    		return idCodeName;
    	}



        return service;
    }


})();
