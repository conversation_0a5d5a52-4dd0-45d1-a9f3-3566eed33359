package com.stpl.tech.neo.controllers;

import com.stpl.tech.neo.core.RateLimiterListType;
import com.stpl.tech.neo.core.exception.WebOrderException;
import com.stpl.tech.neo.core.service.EnvironmentProperties;
import com.stpl.tech.neo.core.service.NeoCustomerService;
import com.stpl.tech.neo.core.service.NeoRekognitionService;
import com.stpl.tech.neo.core.util.NeoUtil;
import com.stpl.tech.neo.domain.model.CustomerCampaignJourney;
import com.stpl.tech.neo.domain.model.CustomerCardInfo;
import com.stpl.tech.neo.domain.model.CustomerInfoResponse;
import com.stpl.tech.neo.domain.model.CustomerLoginRequest;
import com.stpl.tech.neo.domain.model.EmailTemplate;
import com.stpl.tech.neo.domain.model.MyOfferResponse;
import com.stpl.tech.neo.domain.model.OTPResponse;
import com.stpl.tech.neo.domain.model.mongo.Address;
import com.stpl.tech.neo.domain.model.mongo.Customer;
import com.stpl.tech.neo.domain.model.mongo.CustomerAddress;
import com.stpl.tech.neo.domain.model.mongo.CustomerData;
import com.stpl.tech.neo.domain.model.mongo.CustomerInfo;
import com.stpl.tech.neo.domain.model.mongo.CustomerSignupData;
import com.stpl.tech.neo.domain.model.mongo.DeviceVO;
import com.stpl.tech.neo.domain.model.mongo.GameLeaderBoardResponse;
import com.stpl.tech.neo.domain.model.mongo.Pair;
import com.stpl.tech.neo.domain.model.mongo.SMSRequest;
import com.stpl.tech.neo.domain.model.mongo.SignUpTimeSlotCount;
import com.stpl.tech.neo.domain.model.mongo.SpecialOfferResponse;
import com.stpl.tech.redis.domain.model.IdCodeName;
import com.stpl.tech.redis.domain.model.IdName;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.stpl.tech.neo.core.NeoServiceConstants.API_VERSION;
import static com.stpl.tech.neo.core.NeoServiceConstants.SEPARATOR;
import static com.stpl.tech.neo.core.NeoServiceConstants.WEB_CUSTOMER_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
		+ WEB_CUSTOMER_ROOT_CONTEXT, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.POST) // 'v1/c'
public class NeoCustomerResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(NeoCustomerResource.class);

	@Autowired
	private NeoCustomerService customerService;

	@Autowired
	private NeoRekognitionService rekognitionService;

	@Autowired
	private EnvironmentProperties environmentProperties;

	@Autowired
	private RateLimitingInterceptor interceptor;
	/**
	 * -1 = new customer <br>
	 * 0= skipped for safety <br>
	 * 1 = valid customer <br>
	 * 2 =name missing <br>
	 * 3 = email missing<br>
	 * 4 = name and email missing <br>
	 *
	 * @param contact
	 * @return magic number
	 * @throws WebOrderException
	 */
	// lookup
	@RequestMapping(value = "lkp")
	public int lookup(@RequestBody final String contact) throws WebOrderException {
		LOG.info("Request to lookup contact: {}", contact);
		if (NeoUtil.isProd(environmentProperties.getEnvType())
				&& !interceptor.checkForClient(contact, RateLimiterListType.USER_LOOK_UP)) {
			throw new WebOrderException("Access denied due to max attempts reached " + contact);
		}
		return customerService.lookupAndSendOTP(contact, true);
	}

	// lookup without sending OTP
	@RequestMapping(value = "lkpo")
	public int lookupOnly(@RequestBody final String contact) throws WebOrderException {
		LOG.info("Request to lookup contact: {}", contact);
		if (NeoUtil.isProd(environmentProperties.getEnvType())
				&& !interceptor.checkForClient(contact, RateLimiterListType.USER_LOOK_UP)) {
			throw new WebOrderException("Access denied due to max attempts reached " + contact);
		}
		return customerService.lookup(contact);
	}

	@RequestMapping(value = "signup-offer/lko")
	public int signUpLookUp(@RequestBody final String contact){
		CustomerSignupData result= customerService.addCustomerNumber(contact);
		if(customerService.lookup(contact)==-1 && !result.getOfferAvailed()){
			return customerService.lookupAndSendOTP(contact, true);
		}
		else{
			 if(customerService.oldCustomerLookUp(contact)==-1 && !result.getOfferAvailed()){
				 return customerService.lookupAndSendOTP(contact, true);
			 }
		}
		return 0;
	}

	@RequestMapping(value = "signup-offer/lgn")
	public IdCodeName loginSignupOffer(@RequestBody CustomerData data)  throws WebOrderException{
		LOG.info("Request to login for data {}", JSONSerializer.toJSON(data));
		CustomerInfo customerInfo=customerService.login(data);
		if(customerInfo!=null){
			IdCodeName result= customerService.getCustomerUniqueCoupon(data);
			if(result.getShortCode()!=null){
				customerService.findCustomerSignupData(data.getContact());
			}
			return result;
		}
		else{
			return new IdCodeName();
		}
	}

	@RequestMapping(value = "signup-offer/su")
	public IdCodeName signUpOffer(@RequestBody CustomerData data) {
		LOG.info("Request to sign up offer for data {}", JSONSerializer.toJSON(data));
		CustomerInfo customerInfo= customerService.signUp(data, true);
		if(customerInfo!=null){
			IdCodeName result= customerService.getCustomerUniqueCoupon(data);
			if(result.getShortCode()!=null){
				customerService.findCustomerSignupData(data.getContact());
			}
			return result;
		}
		else
			return new IdCodeName();
	}

	// get customer info from contact number
	@RequestMapping(value = "gci")
	public CustomerInfo getCustomerInfo(@RequestBody CustomerData request) throws WebOrderException {
		LOG.info("Request to login for data {}", JSONSerializer.toJSON(request));
		if (NeoUtil.isProd(environmentProperties.getEnvType())
				&& !interceptor.checkForClient(request.getContact(), RateLimiterListType.USER_GET_INFO)) {
			throw new WebOrderException("Access denied due to max attempts reached " + request.getContact());
		}
		return customerService.getCustomerInfo(request);
	}

	// get customer loyality and chaayos cash
	@RequestMapping(value = "glcc")
	public CustomerInfo getLoyality(@RequestBody CustomerInfoResponse request) throws Exception {
		LOG.info("Request to get loyalty and chaayos cash for data {}", JSONSerializer.toJSON(request));
		return customerService.getLoyality(request);
	}

	// get gift card of customer
	@RequestMapping(value = "gc", produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.POST)
	public Object getGiftCard(@RequestBody int customerId) throws Exception {
		LOG.info("Request to get gift card for data {}", customerId);
		return customerService.getGiftCard(customerId);
	}

	// login
	@RequestMapping(value = "lgn")
	public CustomerInfo login(@RequestBody CustomerData request) throws WebOrderException {
		LOG.info("Request to login for data {}", JSONSerializer.toJSON(request));
		return customerService.login(request);
	}

	// login by key
	@RequestMapping(value = "lgn/key", produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public CustomerLoginRequest loginByKey(@RequestParam String key)
			throws WebOrderException {
		LOG.info("Request to login by key {}", key);
		return customerService.loginByKey(key);
	}

	// logout
	@RequestMapping(value = "lgt")
	public DeviceVO logout(@RequestBody CustomerData request) throws WebOrderException {
		LOG.info("Request to logout for data {}", JSONSerializer.toJSON(request));
		return customerService.logout(request);
	}

	// sign up
	@RequestMapping(value = "su")
	public CustomerInfo signUp(@RequestBody CustomerData request) {
		LOG.info("Request to sign up for data {}", JSONSerializer.toJSON(request));
		return customerService.signUp(request, true);
	}

	// sign up for marekting offer
	@RequestMapping(value = "suo")
	public CustomerInfo signUpForOffer(@RequestBody CustomerData request) {
		LOG.info("Request to sign up for data {}", JSONSerializer.toJSON(request));
		return customerService.signUp(request, false);
	}

	// sign up
	@RequestMapping(value = "suv")
	public CustomerInfo signUpVerified(@RequestBody CustomerData request) {
		LOG.info("Request to sign up for data {}", JSONSerializer.toJSON(request));
		if(Objects.isNull(request.getLoginNeo())){
			return customerService.signUpVerified(request, true);
		}else{
			return customerService.signUpVerified(request, request.getLoginNeo());
		}
	}

	// get customer address on customer Id (contact is not used)
	@RequestMapping(value = "as")
	public List<Address> getCustomerAddresses(@RequestBody final String customerId) {
		LOG.info("Request to get addresses for customerId : {}", customerId);
		return customerService.getCustomerAddresses(customerId);
	}

	// add customer address on customer Id (contact is not used)
	@RequestMapping(value = "add/as")
	public Address addCustomerAddress(@RequestBody CustomerAddress request) {
		LOG.info("Request to add address {}", JSONSerializer.toJSON(request));
		return customerService.addCustomerAddress(request);
	}

	// add customer address to cart
	@RequestMapping(value = "up/a/c")
	public boolean addCustomerAddress(@RequestBody IdName request) {
		LOG.info("Request to update address to cart {}", JSONSerializer.toJSON(request));
		return customerService.updateAddressToCart(request);
	}

	// re-send OTP
	@RequestMapping(value = "v/r")
	@Deprecated
	public boolean resendOTP(@RequestBody String contact) throws WebOrderException {
		LOG.info("Request to resend OTP for contact {}", contact);
		return customerService.resendOTP(contact).isSuccess();
	}

	// re-send OTP
	@RequestMapping(value = "v/r2")
	public OTPResponse resendOTP2(@RequestBody String contact) throws WebOrderException {
		LOG.info("Request to resend OTP for contact {}", contact);
		return customerService.resendOTP(contact);
	}

	// generate otp
	@RequestMapping(value = "gotp")
	public boolean generateOTP(@RequestBody String contact) throws WebOrderException {
		LOG.info("Request to generate OTP for contact {}", contact);
		return customerService.generateOtp(contact);
	}

	// get customer Loyalty Score on customer Id (contact is not used)
	@RequestMapping(value = "lyt")
	public CustomerInfo getLoyaltyScore(@RequestBody final String customerId) {
		LOG.info("Request to get addresses for customerId : {}", customerId);
		return customerService.getCustomerLoyaltyScore(customerId);
	}

	// get bought by you products for customer on customer Id
	@RequestMapping(value = "bbu")
	public Set<String> getRecentProducts(@RequestBody final String customerId) {
		LOG.info("Request to get products for customerId : {}", customerId);
		return customerService.getRecentProducts(customerId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "etfs", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Integer submitFeedbackTypeForm(@RequestBody final Object formEvent) {
		String json = JSONSerializer.toJSON(formEvent);
		LOG.info("External form submission : {}", json);
		try {
			customerService.submitNPS(formEvent);
		} catch (Exception e) {
			LOG.error("Error while processing the json " + json, e);
			throw e;
		}
		return -1;
	}

	@RequestMapping(value = "votp")
	public boolean verifyOTP(@RequestBody CustomerData request) throws WebOrderException {
		LOG.info("Request to verify otp for data {}", JSONSerializer.toJSON(request));
		return customerService.verifyOTP(request);
	}

	@RequestMapping(value = "oofiid")
	public boolean optOutOfFaceItById(@RequestBody final String customerId) {
		LOG.info("Request to opt out of Face IT by customer id : {}", customerId);
		return rekognitionService.optOutOfFaceItById(customerId);
	}

	@RequestMapping(value = "oofic")
	public boolean optOutOfFaceItByContact(@RequestBody final String contactNumber) {
		LOG.info("Request to opt out of Face IT by contact number : {}", contactNumber);
		return rekognitionService.optOutOfFaceItByContact(contactNumber);
	}

	@RequestMapping(value = "gagca")
	public CustomerCardInfo getAvailableGiftCardAmount(@RequestBody final String customerId) {
		LOG.info("Request to get available gift card amount for customerId : {}", customerId);
		return customerService.getCustomerCardInfo(customerId);
	}

	@RequestMapping(value = "share/sms")
	public Boolean shareAppViaSMS(@RequestBody SMSRequest input) {
		LOG.info("Request to send SMS to customer" + input);
		input.setCreationTime(AppUtils.getCurrentTimestamp());
		return customerService.sendSMS(input);
	}

	@RequestMapping(value = "share/email")
	public boolean shareAppViaEmail(@RequestBody EmailTemplate emailTemplate) throws EmailGenerationException {
		LOG.info("Request to send Email to customer" + emailTemplate.getCustomerEmail());
		return customerService.sendEmail(emailTemplate);
	}

	@RequestMapping(value = "supo/su")
	public CustomerInfo signUpOfferSignUp(@RequestBody CustomerData data) {
		LOG.info("Request to sign up offer for data supo/su {}", JSONSerializer.toJSON(data));
		return customerService.signUpForOffer(data);
	}
	@RequestMapping(value = "my-offer")
	public List<MyOfferResponse> getMyOffer(@RequestBody CustomerData data) {
		LOG.info("Request to get my offer for data = {}", JSONSerializer.toJSON(data));
		return customerService.getMyOffer(data);
	}

	@RequestMapping(value = "cj")
	public CustomerCampaignJourney saveCustomerJourney(@RequestBody CustomerCampaignJourney data) {
		LOG.info("Request to get my offer for data = {}", JSONSerializer.toJSON(data));
		return customerService.saveCustomerJourney(data);
	}

	@RequestMapping(value = "supo/ts")
	public SignUpTimeSlotCount signUpOfferTimeSlot(@RequestBody IdCodeName dateOfDelivery) {
		LOG.info("Request to get timeslots for sign up offer for "+ dateOfDelivery.getName());
		return customerService.getTimeSlotCounts(dateOfDelivery.getName());
	}

	@RequestMapping(value = "customer-by-contact")
	public Customer getCustomerInfoContact (@RequestBody String contact) {
		try{
			LOG.info("Request to get customer for contact ::::::{}",contact);
			return customerService.getCustomerInfoByContact(contact);
		}catch(Exception e){
			LOG.error("Unable to get data for customer with contact ::{}", contact);
			return null;
		}
	}

	@RequestMapping(value = "lkp-and-offer")
	public Pair<Integer,List<SpecialOfferResponse>> lookupAndCreateOffer(@RequestBody CustomerData request){
		int code = customerService.lookupAndSendOTP(request.getContact(),false);
		if(code == -1){
			return new Pair<>(code,null);
		}
		return new Pair<>(code,customerService.getSpecialOffer(request));
	}

	@RequestMapping(value = "register-and-offer")
	public List<SpecialOfferResponse> getSpecialOffer(@RequestBody CustomerData request){
		return customerService.registerAndOffer(request);
	}

	@RequestMapping(value = "leaderboard")
	public GameLeaderBoardResponse getSpecialOffer(@RequestParam String contact, @RequestParam String token,
														 @RequestParam Boolean getRank, @RequestBody CustomerData customerData){
	return customerService.getLeaderBoard(contact, token, getRank,customerData);
	}
}
