import React from "react";
import { connect } from "react-redux";
import appUtil from "../AppUtil";
import StorageUtils from "../utils/StorageUtils";
import MobileUtilityLayout from "./mobile/MobileUtilityLayout";
import DesktopUtilityLayout from "./desktop/DesktopUtilityLayout";
import * as UtilityActions from "../actions/UtilityActions";
import * as OutletLoginActions from "../actions/OutletLoginActions";
// import * as PubNubActions from "../actions/PubNubActions";
import * as PaymentActions from "../actions/PaymentActions";
import {browserHistory} from "react-router";

///https://github.com/balloob/react-sidebar#installation side bar repo

@connect((store) => {
    return {
        internetError: store.utilityReducer.internetError,
        campaignInitialized:store.campaignReducer.campaignInitialized,
        cardPaymentMode:store.paymentReducer.cardPaymentMode
    };
})
export default class MainRoutingLayout extends React.Component {

    constructor() {
        super();
        this.state = {
        };
        this.tryAgain = this.tryAgain.bind(this);
    }

    tryAgain(){
        window.location.reload();
    }

    componentWillMount(){
        var kettleAuthDetails = StorageUtils.getKettleAuthDetails();
        this.props.dispatch(OutletLoginActions.setKettleAuthDetails(kettleAuthDetails));
        if(kettleAuthDetails != null && kettleAuthDetails.authToken != null) {
            // this.props.dispatch(PubNubActions.setupPubNub());
            //send to outlet menu layout
            browserHistory.push("/menu");
        } else {
            //send to login layout
            browserHistory.push("/outletLogin");
        }
        var authDetail = StorageUtils.getAuthDetail();
        if (!appUtil.checkEmpty(authDetail) && authDetail.deviceKey!=null) {
            this.props.dispatch(UtilityActions.stampDevice(authDetail));
        } else {
            this.props.dispatch(UtilityActions.createDevice());
        }
        this.props.dispatch(UtilityActions.setDefaultData());
        if(this.props.cardPaymentMode === 'EZETAP'){
            this.props.dispatch(PaymentActions.cancelEzetapPayment());
            this.props.dispatch(PaymentActions.sendKeepAliveToEzetap());
        }
        this.props.dispatch(PaymentActions.updateSelectivePaymentMode());
        this.props.dispatch(PaymentActions.updateCardPaymentMode());
        /*window.oncontextmenu = function(){return false};
        window.ondragstart = function(){return false};*/
    }

    render() {

        return (
            <div>
                {this.props.internetError?(
                    <div class="internetErrorContainer">
                        <div class="dummyHead"><img src="img/logo.svg" /></div>
                        {appUtil.isMobile() ? (
                            <img src="../../img/internetErrorPhone.png" />
                        ) : (
                            <img src="../../img/internetErrorLaptop.png" />
                        )}
                        <p class="msg">Something went wrong.</p>
                        <div class="btn btn-primary" style={{width:"200px", display:"inline-block"}}
                             onClick={this.tryAgain.bind(this)}>Try again</div>
                    </div>
                ):(
                    <div id="container">
                        {appUtil.isMobile() ? (
                            <MobileUtilityLayout />
                        ) : (
                            <DesktopUtilityLayout />
                        )}
                        {this.props.children}
                    </div>
                )}
            </div>
        )
    }
}