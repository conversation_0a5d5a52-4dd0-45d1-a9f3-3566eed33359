/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.config;

import com.google.common.base.Preconditions;
import com.stpl.tech.master.util.DataSourceConstants;
import com.stpl.tech.master.util.ServiceContextFactory;
import com.stpl.tech.spring.config.SpringUtilityServiceConfig;
import com.stpl.tech.util.ExecutionEnvironment;
import org.apache.commons.lang.NotImplementedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import javax.sql.DataSource;

@Configuration
//@PropertySource({ "classpath:connection.properties" })
@Import(value = {SpringUtilityServiceConfig.class})
public class ServiceConfig {

	@Autowired
	private Environment connectionProperties;

	public ServiceConfig() {
		super();
	}

	@Bean(name = DataSourceConstants.UAT_DATA_SOURCE)
	public DataSource dataUATSource() {
		return getDataSource(ExecutionEnvironment.UAT.name());
	}

	@Bean(name = DataSourceConstants.DUMP_DATA_SOURCE)
	public DataSource dataDUMPSource() {
		return getDataSource(ExecutionEnvironment.DUMP.name());
	}

	@Bean(name = DataSourceConstants.DEV_DATA_SOURCE)
	public DataSource dataDEVSource() {
		return getDataSource(ExecutionEnvironment.DEV.name());
	}

	@ConditionalOnProperty(value = "env.type", havingValue = "sprod", matchIfMissing = false)
	@Bean(name = DataSourceConstants.PROD_DATA_SOURCE)
	public DataSource dataProdSource() {
		return getDataSource(ExecutionEnvironment.PROD.name());
	}

	@ConditionalOnProperty(value = "env.type", havingValue = "sprod", matchIfMissing = false)
	@Bean(name = DataSourceConstants.PROD_WRITE_DATA_SOURCE)
	public DataSource dataProdWriteSource() {
		return getDataSource("PROD_WRITE");
	}

	public DataSource getCreateDataSourceBean(ExecutionEnvironment env, Boolean write) {
		switch (env) {
			case DEV:
				return dataDEVSource();
			case UAT:
				return dataUATSource();
			case DUMP:
				return dataDUMPSource();
			case PROD:
			case SPROD:
				return write ? dataProdWriteSource() : dataProdSource();
			default:
				throw new NotImplementedException("Do not have Data Source setup for " + env.name());
		}
	}

	private DataSource getDataSource(String executionEnv) {
		final DriverManagerDataSource dataSource = new DriverManagerDataSource();
		dataSource.setDriverClassName(
				Preconditions.checkNotNull(connectionProperties.getProperty(executionEnv + ".jdbc.driverClassName")));
		dataSource.setUrl(Preconditions.checkNotNull(connectionProperties.getProperty(executionEnv + ".jdbc.url")));
		dataSource
				.setUsername(Preconditions.checkNotNull(connectionProperties.getProperty(executionEnv + ".jdbc.user")));
		dataSource
				.setPassword(Preconditions.checkNotNull(connectionProperties.getProperty(executionEnv + ".jdbc.pass")));
		return dataSource;
	}
}