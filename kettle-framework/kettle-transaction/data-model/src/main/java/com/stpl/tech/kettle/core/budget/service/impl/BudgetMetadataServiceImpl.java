package com.stpl.tech.kettle.core.budget.service.impl;

import com.stpl.tech.kettle.core.CalculationType;
import com.stpl.tech.kettle.core.budget.dao.BudgetMetadataDao;
import com.stpl.tech.kettle.core.budget.service.BudgetMetadataService;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.CalculationStatus;
import com.stpl.tech.kettle.core.data.budget.vo.ConsumableBudgetRequest;
import com.stpl.tech.kettle.core.data.budget.vo.ConsumablesAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField;
import com.stpl.tech.kettle.core.data.budget.vo.ServiceAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.ScoreCardCategory;
import com.stpl.tech.kettle.core.service.PnLAdjustmentService;
import com.stpl.tech.kettle.core.service.UnitBudgetService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.UnitBudgetDao;
import com.stpl.tech.kettle.data.dao.impl.BudgetUtils;
import com.stpl.tech.kettle.data.model.PnlAdjustmentDetail;
import com.stpl.tech.kettle.data.model.UnitBankChargesBudgetDetail;
import com.stpl.tech.kettle.data.model.UnitBudgetExceededData;
import com.stpl.tech.kettle.data.model.UnitBudgetoryDetail;
import com.stpl.tech.kettle.data.model.UnitChannelPartnerChargesBudgetDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.data.model.UnitFacilityChargesBudgetDetail;
import com.stpl.tech.kettle.data.model.UnitManpowerBudgetDetail;
import com.stpl.tech.kettle.domain.model.AdjustmentFieldData;
import com.stpl.tech.kettle.domain.model.UnitExpenditure;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.PnlAdjustmentStatus;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.ExcelWriter;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.exception.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BudgetMetadataServiceImpl implements BudgetMetadataService {

    private static final Logger LOG = LoggerFactory.getLogger(BudgetMetadataServiceImpl.class);

    @Autowired
    private BudgetMetadataDao budgetMetadataDao;

    @Autowired
    private UnitBudgetDao unitBudgetDao;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    PnLAdjustmentService pnLAdjustmentService;

    @Autowired
    UnitBudgetService unitBudgetService;

    @Autowired
    EnvironmentProperties props;

    @Override
    public View getBudgetView() {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "BudgetSheet_" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<UnitBudgetoryDetail> l = new ArrayList<UnitBudgetoryDetail>();
                masterCache.getAllUnits().stream().filter(u -> MasterUtil.isActiveCafe(u)).sorted().forEach(u -> {
                    l.add(new UnitBudgetoryDetail(u.getId(), u.getName()));
                });
                writer.writeSheet(l, UnitBudgetoryDetail.class);
            }
        };
    }

    @Override
    public View getBudgetView(int year, int month) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "BudgetSheet_" + year + "-" + month + "_"
                    + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                List<UnitBudgetoryDetail> existingBudgets = budgetMetadataDao.getAllActiveBudgets(year, month);
                Map<Integer, UnitBudgetoryDetail> map = existingBudgets.stream()
                    .collect(Collectors.toMap(UnitBudgetoryDetail::getUnitId, Function.identity()));
                ExcelWriter writer = new ExcelWriter(workbook);
                List<UnitBudgetoryDetail> all = new ArrayList<UnitBudgetoryDetail>();
                List<UnitBudgetoryDetail> l = new ArrayList<UnitBudgetoryDetail>();
                masterCache.getAllUnits().stream().filter(u -> MasterUtil.isActiveCafe(u)).sorted().forEach(u -> {
                    if (map == null || (map != null && !map.containsKey(u.getId()))) {
                        l.add(new UnitBudgetoryDetail(u.getId(), u.getName(), year, month, AppConstants.ACTIVE));
                    } else {
                        all.add(map.get(u.getId()));
                    }
                });
                if (l != null && l.size() > 0) {
                    all.addAll(l);
                }
                writer.writeSheet(all, UnitBudgetoryDetail.class);
            }
        };
    }

    @Override
    public View getManpowerBudgetView(int year, int month) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "ManpowerBudgetSheet_" + year + "-" + month + "_"
                    + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                List<UnitBudgetoryDetail> existingBudgets = budgetMetadataDao.getAllActiveBudgets(year, month);
                Map<Integer, UnitBudgetoryDetail> map = existingBudgets.stream()
                    .collect(Collectors.toMap(UnitBudgetoryDetail::getUnitId, Function.identity()));
                ExcelWriter writer = new ExcelWriter(workbook);
                List<UnitManpowerBudgetDetail> all = new ArrayList<UnitManpowerBudgetDetail>();
                List<UnitManpowerBudgetDetail> l = new ArrayList<UnitManpowerBudgetDetail>();
                masterCache.getAllUnits().stream().filter(u -> MasterUtil.isActiveCafe(u)).sorted().forEach(u -> {
                    if (map == null || (map != null && !map.containsKey(u.getId()))) {
                        l.add(new UnitManpowerBudgetDetail(u.getId(), u.getName(), year, month, AppConstants.ACTIVE));
                    } else {
                        all.add(convert(map.get(u.getId())));
                    }
                });
                if (l != null && l.size() > 0) {
                    all.addAll(l);
                }
                writer.writeSheet(all, UnitManpowerBudgetDetail.class);
            }

            private UnitManpowerBudgetDetail convert(UnitBudgetoryDetail d) {
                UnitManpowerBudgetDetail m = new UnitManpowerBudgetDetail(d.getUnitId(), d.getUnitName(), d.getYear(),
                    d.getMonth(), AppConstants.ACTIVE);
                m.copyToOld(d);
                return m;
            }
        };
    }

    @Override
    public View getChannelPartnerChargesBudgetView(int year, int month) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "ChannelPartnerCharges_" + year + "-" + month + "_"
                    + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                List<UnitBudgetoryDetail> existingBudgets = budgetMetadataDao.getAllActiveBudgets(year, month);
                Map<Integer, UnitBudgetoryDetail> map = existingBudgets.stream()
                    .collect(Collectors.toMap(UnitBudgetoryDetail::getUnitId, Function.identity()));
                ExcelWriter writer = new ExcelWriter(workbook);
                List<UnitChannelPartnerChargesBudgetDetail> all = new ArrayList<UnitChannelPartnerChargesBudgetDetail>();
                List<UnitChannelPartnerChargesBudgetDetail> l = new ArrayList<UnitChannelPartnerChargesBudgetDetail>();
                masterCache.getAllUnits().stream().filter(u -> MasterUtil.isActiveCafe(u)).sorted().forEach(u -> {
                    if (map == null || (map != null && !map.containsKey(u.getId()))) {
                        l.add(new UnitChannelPartnerChargesBudgetDetail(u.getId(), u.getName(), year, month, AppConstants.ACTIVE));
                    } else {
                        all.add(convert(map.get(u.getId())));
                    }
                });
                if (l != null && l.size() > 0) {
                    all.addAll(l);
                }
                writer.writeSheet(all, UnitChannelPartnerChargesBudgetDetail.class);
            }

            private UnitChannelPartnerChargesBudgetDetail convert(UnitBudgetoryDetail d) {
                UnitChannelPartnerChargesBudgetDetail m = new UnitChannelPartnerChargesBudgetDetail(d.getUnitId(), d.getUnitName(), d.getYear(),
                    d.getMonth(), AppConstants.ACTIVE);
                m.copyToOld(d);
                return m;
            }
        };
    }

    @Override
    public View getBankChargesBudgetView(int year, int month) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "BankCharges_" + year + "-" + month + "_"
                    + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                List<UnitBudgetoryDetail> existingBudgets = budgetMetadataDao.getAllActiveBudgets(year, month);
                Map<Integer, UnitBudgetoryDetail> map = existingBudgets.stream()
                    .collect(Collectors.toMap(UnitBudgetoryDetail::getUnitId, Function.identity()));
                ExcelWriter writer = new ExcelWriter(workbook);
                List<UnitBankChargesBudgetDetail> all = new ArrayList<UnitBankChargesBudgetDetail>();
                List<UnitBankChargesBudgetDetail> l = new ArrayList<UnitBankChargesBudgetDetail>();
                masterCache.getAllUnits().stream().filter(u -> MasterUtil.isActiveCafe(u)).sorted().forEach(u -> {
                    if (map == null || (map != null && !map.containsKey(u.getId()))) {
                        l.add(new UnitBankChargesBudgetDetail(u.getId(), u.getName(), year, month, AppConstants.ACTIVE));
                    } else {
                        all.add(convert(map.get(u.getId())));
                    }
                });
                if (l != null && l.size() > 0) {
                    all.addAll(l);
                }
                writer.writeSheet(all, UnitBankChargesBudgetDetail.class);
            }

            private UnitBankChargesBudgetDetail convert(UnitBudgetoryDetail d) {
                UnitBankChargesBudgetDetail m = new UnitBankChargesBudgetDetail(d.getUnitId(), d.getUnitName(), d.getYear(),
                    d.getMonth(), AppConstants.ACTIVE);
                m.copyToOld(d);
                return m;
            }
        };
    }

    @Override
    public View getFacilityChargesBudgetView(int year, int month) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "FacilityCharges_" + year + "-" + month + "_"
                    + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                List<UnitBudgetoryDetail> existingBudgets = budgetMetadataDao.getAllActiveBudgets(year, month);
                Map<Integer, UnitBudgetoryDetail> map = existingBudgets.stream()
                    .collect(Collectors.toMap(UnitBudgetoryDetail::getUnitId, Function.identity()));
                ExcelWriter writer = new ExcelWriter(workbook);
                List<UnitFacilityChargesBudgetDetail> all = new ArrayList<UnitFacilityChargesBudgetDetail>();
                List<UnitFacilityChargesBudgetDetail> l = new ArrayList<UnitFacilityChargesBudgetDetail>();
                masterCache.getAllUnits().stream().filter(u -> MasterUtil.isActiveCafe(u)).sorted().forEach(u -> {
                    if (map == null || (map != null && !map.containsKey(u.getId()))) {
                        l.add(new UnitFacilityChargesBudgetDetail(u.getId(), u.getName(), year, month, AppConstants.ACTIVE));
                    } else {
                        all.add(convert(map.get(u.getId())));
                    }
                });
                if (l != null && l.size() > 0) {
                    all.addAll(l);
                }
                writer.writeSheet(all, UnitFacilityChargesBudgetDetail.class);
            }

            private UnitFacilityChargesBudgetDetail convert(UnitBudgetoryDetail d) {
                UnitFacilityChargesBudgetDetail m = new UnitFacilityChargesBudgetDetail(d.getUnitId(), d.getUnitName(), d.getYear(),
                    d.getMonth(), AppConstants.ACTIVE);
                m.copyToOld(d);
                return m;
            }
        };
    }

    @Override
    public View getServiceChargesBudgetView(int year, int month) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "ServiceCharges_" + year + "-" + month + "_"
                    + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                List<UnitBudgetoryDetail> existingBudgets = budgetMetadataDao.getAllActiveBudgets(year, month);
                Map<Integer, UnitBudgetoryDetail> map = existingBudgets.stream()
                    .collect(Collectors.toMap(UnitBudgetoryDetail::getUnitId, Function.identity()));
                ExcelWriter writer = new ExcelWriter(workbook);
                List<ServiceAggregate> all = new ArrayList<ServiceAggregate>();
                List<ServiceAggregate> l = new ArrayList<ServiceAggregate>();
                masterCache.getAllUnits().stream().filter(u -> MasterUtil.isActiveCafe(u)).sorted().forEach(u -> {
                    l.add(new ServiceAggregate(u.getId(), u.getName(), year, month, AppConstants.ACTIVE));
                });
                if (l != null && l.size() > 0) {
                    all.addAll(l);
                }
                writer.writeSheet(all, ServiceAggregate.class);
            }

        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, transactionManager = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean uploadBudgetDocument(MultipartFile file, int userId) throws IOException, ExcelParsingException {
        Workbook workbook;
        if (file.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        Date currentTime = AppUtils.getCurrentTimestamp();
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();
//        try{
        List<UnitBudgetoryDetail> entityList = parser.createEntity(workbook.getSheetAt(0), UnitBudgetoryDetail.class,
            errors::add);
        if (errors.isEmpty()) {
            int year = entityList.get(0).getYear();
            int month = entityList.get(0).getMonth();
            entityList.stream().forEach(a -> {
                a.setUpdatedBy(userId);
                a.setUpdateTime(currentTime);
            });
            budgetMetadataDao.cancelBudgetRecords(month, year);
            budgetMetadataDao.addAll(entityList);
        } else {
            LOG.info("Error Parsing Workbook for Expense, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            LOG.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        }
//        }catch (Exception e){
//            LOG.error("Error Parsing Workbook {}", e.getMessage());
//        }
        workbook.close();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, transactionManager = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean uploadManpowerBudgetDocument(MultipartFile file, int userId) throws IOException, ExcelParsingException {
        Workbook workbook;
        if (file.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();
        List<UnitManpowerBudgetDetail> entityList = parser.createEntity(workbook.getSheetAt(0),
            UnitManpowerBudgetDetail.class, errors::add);
        if (errors.isEmpty()) {
            int year = entityList.get(0).getYear();
            int month = entityList.get(0).getMonth();
            Date currentTime = AppUtils.getCurrentTimestamp();
            entityList.stream().forEach(a -> {
                a.setUpdatedBy(userId);
                a.setUpdateTime(currentTime);
            });
            budgetMetadataDao.cancelManpowerBudgetRecords(month, year);
            budgetMetadataDao.addAll(entityList);
            budgetMetadataDao.updateBudgetRecords(entityList);
        } else {
            LOG.info("Error Parsing Workbook for Manpower Expenses, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            LOG.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        }
        workbook.close();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, transactionManager = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean uploadChannelPartnerChargesBudgetDocument(MultipartFile file, int userId) throws IOException, ExcelParsingException {
        Workbook workbook;
        if (file.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();
        List<UnitChannelPartnerChargesBudgetDetail> entityList = parser.createEntity(workbook.getSheetAt(0),
            UnitChannelPartnerChargesBudgetDetail.class, errors::add);
        if (errors.isEmpty()) {
            int year = entityList.get(0).getYear();
            int month = entityList.get(0).getMonth();
            Date currentTime = AppUtils.getCurrentTimestamp();
            entityList.stream().forEach(a -> {
                a.setUpdatedBy(userId);
                a.setUpdateTime(currentTime);
            });
            budgetMetadataDao.cancelChannelPartnerChargesBudgetRecords(month, year);
            budgetMetadataDao.addAll(entityList);
            budgetMetadataDao.updateChannelPartnerChargesBudgetRecords(entityList);
        } else {
            LOG.info("Error Parsing Workbook for Manpower Expenses, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            LOG.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        }
        workbook.close();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, transactionManager = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean uploadBankChargesBudgetDocument(MultipartFile file, int userId) throws IOException, ExcelParsingException {
        Workbook workbook;
        if (file.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();
        List<UnitBankChargesBudgetDetail> entityList = parser.createEntity(workbook.getSheetAt(0),
            UnitBankChargesBudgetDetail.class, errors::add);
        if (errors.isEmpty()) {
            int year = entityList.get(0).getYear();
            int month = entityList.get(0).getMonth();
            Date currentTime = AppUtils.getCurrentTimestamp();
            entityList.stream().forEach(a -> {
                a.setUpdatedBy(userId);
                a.setUpdateTime(currentTime);
            });
            budgetMetadataDao.cancelBankChargesBudgetRecords(month, year);
            budgetMetadataDao.addAll(entityList);
            budgetMetadataDao.updateBankChargesBudgetRecords(entityList);
        } else {
            LOG.info("Error Parsing Workbook for Bank Expenses, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            LOG.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        }
        workbook.close();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, transactionManager = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean uploadFacilityChargesBudgetDocument(MultipartFile file, int userId) throws IOException, ExcelParsingException {
        Workbook workbook;
        if (file.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();
        List<UnitFacilityChargesBudgetDetail> entityList = parser.createEntity(workbook.getSheetAt(0),
            UnitFacilityChargesBudgetDetail.class, errors::add);
        if (errors.isEmpty()) {
            int year = entityList.get(0).getYear();
            int month = entityList.get(0).getMonth();
            Date currentTime = AppUtils.getCurrentTimestamp();
            entityList.stream().forEach(a -> {
                a.setUpdatedBy(userId);
                a.setUpdateTime(currentTime);
            });
            budgetMetadataDao.cancelFacilityChargesBudgetRecords(month, year);
            budgetMetadataDao.addAll(entityList);
            budgetMetadataDao.updateFacilityChargesBudgetRecords(entityList);
        } else {
            LOG.info("Error Parsing Workbook for Facility Expenses, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            LOG.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        }
        workbook.close();
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, transactionManager = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean uploadServiceChargesBudgetDocument(MultipartFile file, int userId) throws IOException, ExcelParsingException {
        Workbook workbook;
        if (file.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();
        List<ServiceAggregate> entityList = parser.createEntity(workbook.getSheetAt(0),
            ServiceAggregate.class, errors::add);
        Map<String, AdjustmentFieldData> headerMap = pnLAdjustmentService.getAdjustmentMaps(ExpenseField.CalculationType.COST.name());
        if (errors.isEmpty()) {
            int year = entityList.get(0).getYear();
            int month = entityList.get(0).getMonth();
            Date currentTime = AppUtils.getCurrentTimestamp();
            List<PnlAdjustmentDetail> list = new ArrayList<>();
            entityList.stream().forEach(a -> {
                checkIfValueExist(a, year, month, userId, headerMap, list);
            });
            budgetMetadataDao.addAll(list);
        } else {
            LOG.info("Error Parsing Workbook for Service Expenses, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            LOG.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        }
        workbook.close();
        return true;
    }


    private void createAdjustmentObject(int userId, AdjustmentFieldData adjustmentFieldData, int unitId, int year, int month, BigDecimal value, List<PnlAdjustmentDetail> list) {
        PnlAdjustmentDetail pnl = new PnlAdjustmentDetail();
        pnl.setStatus(PnlAdjustmentStatus.APPROVED.value());
        pnl.setAdjustmentType("COST");
        pnl.setPnlHeaderColumnName(adjustmentFieldData.getColumnName());
        pnl.setPnlHeaderDetail(adjustmentFieldData.getDetail());
        pnl.setPnlHeaderName(adjustmentFieldData.getName());
        pnl.setPnlHeaderType(adjustmentFieldData.getType());
        pnl.setAdjustmentValue(value);
        pnl.setUnitId(unitId);
        pnl.setYear(year);
        pnl.setMonth(month);
        pnl.setCreatedBy(userId);
        pnl.setApprovedBy(userId);
        pnl.setCreationTime(AppUtils.getCurrentTimestamp());
        pnl.setApprovalTime(AppUtils.getCurrentTimestamp());
        pnl.setCreateComment(AppConstants.BULK_ADJUSTMENT);
        pnl.setCreateCommentText(AppConstants.BULK_ADJUSTMENT);
        pnl.setApprovedComment(AppConstants.BULK_ADJUSTMENT);
        pnl.setApprovedCommentText(AppConstants.BULK_ADJUSTMENT);
        list.add(pnl);
    }

    private void checkIfValueExist(ServiceAggregate u, int year, int month, int userId, Map<String, AdjustmentFieldData> headerMap, List<PnlAdjustmentDetail> list) {
        if (u.getOtherServiceCharges() != null && u.getOtherServiceCharges().compareTo(BigDecimal.ZERO) != 0) {
            createAdjustmentObject(userId, headerMap.get("OTHER_SERVICE_CHARGES"), u.getUnitId(), year, month, u.getOtherServiceCharges(), list);
        }
        if ((u.getSecurityGuardCharges() != null && u.getSecurityGuardCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("SECURITY_GUARD_CHARGES"), u.getUnitId(), year, month, u.getSecurityGuardCharges(), list);
        }
        if ((u.getFuelCharges() != null && u.getFuelCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("FUEL_CHARGES"), u.getUnitId(), year, month, u.getFuelCharges(), list);
        }
        if ((u.getLogisticCharges() != null && u.getLogisticCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("LOGISTIC_CHARGES"), u.getUnitId(), year, month, u.getLogisticCharges(), list);
        }
        if ((u.getCommunicationInternet() != null && u.getCommunicationInternet().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("COMMUNICATION_INTERNET"), u.getUnitId(), year, month, u.getCommunicationInternet(), list);
        }
        if ((u.getCommunicationTelephone() != null && u.getCommunicationTelephone().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("COMMUNICATION_TELEPHONE"), u.getUnitId(), year, month, u.getCommunicationTelephone(), list);
        }
        if ((u.getCommunicationILL() != null && u.getCommunicationILL().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("COMMUNICATION_ILL"), u.getUnitId(), year, month, u.getCommunicationILL(), list);
        }
        if ((u.getPayrollProcessingFee() != null && u.getPayrollProcessingFee().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("PAYROLL_PROCESSING_FEES"), u.getUnitId(), year, month, u.getPayrollProcessingFee(), list);
        }
        if ((u.getNewsPaper() != null && u.getNewsPaper().compareTo(BigDecimal.ZERO) > 0)) {
            createAdjustmentObject(userId, headerMap.get("NEWSPAPER_CHARGES"), u.getUnitId(), year, month, u.getNewsPaper(), list);
        }
        if ((u.getStaffWelfareExpenses() != null && u.getStaffWelfareExpenses().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("STAFF_WELFARE_EXPENSES"), u.getUnitId(), year, month, u.getStaffWelfareExpenses(), list);
        }
        if ((u.getCourierCharges() != null && u.getCourierCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("COURIER_CHARGES"), u.getUnitId(), year, month, u.getCourierCharges(), list);
        }
        if ((u.getPrintingAndStationary() != null && u.getPrintingAndStationary().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("PRINTING_AND_STATIONARY"), u.getUnitId(), year, month, u.getPrintingAndStationary(), list);
        }
        if ((u.getBusinessPromotion() != null && u.getBusinessPromotion().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("BUSINESS_PROMOTION"), u.getUnitId(), year, month, u.getBusinessPromotion(), list);
        }
        if ((u.getLegalCharges() != null && u.getLegalCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("LEGAL_CHARGES"), u.getUnitId(), year, month, u.getLegalCharges(), list);
        }
        if ((u.getProfessionalCharges() != null && u.getProfessionalCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("PROFESSIONAL_CHARGES"), u.getUnitId(), year, month, u.getProfessionalCharges(), list);
        }
        if ((u.getCleaningCharges() != null && u.getCleaningCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CLEANING_CHARGES"), u.getUnitId(), year, month, u.getCleaningCharges(), list);
        }
        if ((u.getPestControlCharges() != null && u.getPestControlCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("PEST_CONTROL_CHARGES"), u.getUnitId(), year, month, u.getPestControlCharges(), list);
        }
        if ((u.getTechologyTraining() != null && u.getTechologyTraining().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("TECHNOLOGY_TRAINING"), u.getUnitId(), year, month, u.getTechologyTraining(), list);
        }
        if ((u.getCorporateMarketingDigital() != null && u.getCorporateMarketingDigital().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_DIGITAL"), u.getUnitId(), year, month, u.getCorporateMarketingDigital(), list);
        }
        if ((u.getCorporateMarketingAdvOffline() != null && u.getCorporateMarketingAdvOffline().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_AD_OFFLINE"), u.getUnitId(), year, month, u.getCorporateMarketingAdvOffline(), list);
        }
        if ((u.getCorporateMarketingAdvOnline() != null && u.getCorporateMarketingAdvOnline().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_AD_ONLINE"), u.getUnitId(), year, month, u.getCorporateMarketingAdvOnline(), list);
        }
        if ((u.getCorporateMarketingOutdoor() != null && u.getCorporateMarketingOutdoor().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_OUTDOOR"), u.getUnitId(), year, month, u.getCorporateMarketingOutdoor(), list);
        }
        if ((u.getCorporateMarketingAgencyFees() != null && u.getCorporateMarketingAgencyFees().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_AGENCY_FEES"), u.getUnitId(), year, month, u.getCorporateMarketingAgencyFees(), list);
        }
        if ((u.getDeliveryChargesVariable() != null && u.getDeliveryChargesVariable().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("DELIVERY_CHARGES_VARIABLE"), u.getUnitId(), year, month, u.getDeliveryChargesVariable(), list);
        }

//        if ((u.getConveyanceMarketing() != null && u.getConveyanceMarketing().compareTo(BigDecimal.ZERO) != 0)) {
//            createAdjustmentObject(userId, headerMap.get("CONVEYANCE_MARKETING"), u.getUnitId(), year, month, u.getConveyanceMarketing(), list);
//        }
//        if ((u.getConveyanceOperations() != null && u.getConveyanceOperations().compareTo(BigDecimal.ZERO) != 0)) {
//            createAdjustmentObject(userId, headerMap.get("CONVEYANCE_OPERATION"), u.getUnitId(), year, month, u.getConveyanceOperations(), list);
//        }
//        if ((u.getConveyanceOthers() != null && u.getConveyanceOthers().compareTo(BigDecimal.ZERO) != 0)) {
//            createAdjustmentObject(userId, headerMap.get("CONVEYANCE_OTHERS"), u.getUnitId(), year, month, u.getConveyanceOthers(), list);
//        }
        if ((u.getAuditFee() != null && u.getAuditFee().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("AUDIT_FEE"), u.getUnitId(), year, month, u.getAuditFee(), list);
        }
        if ((u.getAuditFeeOutOfPocket() != null && u.getAuditFeeOutOfPocket().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("AUDIT_FEE_OUT_OF_POCKET"), u.getUnitId(), year, month, u.getAuditFeeOutOfPocket(), list);
        }
        if ((u.getBrokerage() != null && u.getBrokerage().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("BROKERAGE"), u.getUnitId(), year, month, u.getBrokerage(), list);
        }
        if ((u.getCharityAndDonations() != null && u.getCharityAndDonations().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CHARITY_AND_DONATIONS"), u.getUnitId(), year, month, u.getCharityAndDonations(), list);
        }
        if ((u.getDomesticTicketsAndHotels() != null && u.getDomesticTicketsAndHotels().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("DOMESTIC_TICKETS_AND_HOTELS"), u.getUnitId(), year, month, u.getDomesticTicketsAndHotels(), list);
        }
        if ((u.getInternationalTicketsAndHotels() != null && u.getInternationalTicketsAndHotels().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("INTERNATIONAL_TICKETS_AND_HOTELS"), u.getUnitId(), year, month, u.getInternationalTicketsAndHotels(), list);
        }
        if ((u.getHouseKeepingCharges() != null && u.getHouseKeepingCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("HOUSEKEEPING_CHARGES"), u.getUnitId(), year, month, u.getHouseKeepingCharges(), list);
        }
        if ((u.getLateFeeCharges() != null && u.getLateFeeCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("LATE_FEE_CHARGES"), u.getUnitId(), year, month, u.getLateFeeCharges(), list);
        }
//        if ((u.getMarketingDataAnalysis() != null && u.getMarketingDataAnalysis().compareTo(BigDecimal.ZERO) != 0)) {
//            createAdjustmentObject(userId, headerMap.get("MARKETING_DATA_ANALYSIS"), u.getUnitId(), year, month, u.getMarketingDataAnalysis(), list);
//        }
        if ((u.getMiscellaneousExpenses() != null && u.getMiscellaneousExpenses().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("MISCELLANEOUS_EXPENSES"), u.getUnitId(), year, month, u.getMiscellaneousExpenses(), list);
        }
        if ((u.getPenalty() != null && u.getPenalty().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("PENALTY"), u.getUnitId(), year, month, u.getPenalty(), list);
        }
        if ((u.getPhotoCopyExpenses() != null && u.getPhotoCopyExpenses().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("PHOTO_COPY_EXPENSES"), u.getUnitId(), year, month, u.getPhotoCopyExpenses(), list);
        }
        if ((u.getQcrExpense() != null && u.getQcrExpense().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("QCR_EXPENSE"), u.getUnitId(), year, month, u.getQcrExpense(), list);
        }
        if ((u.getRecuritmentConsultants() != null && u.getRecuritmentConsultants().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("RECRUITMENT_CONSULTANTS"), u.getUnitId(), year, month, u.getRecuritmentConsultants(), list);
        }
        if ((u.getRocFees() != null && u.getRocFees().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("ROC_FEES"), u.getUnitId(), year, month, u.getRocFees(), list);
        }
        if ((u.getDebitCreditWrittenOff() != null && u.getDebitCreditWrittenOff().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("DEBIT_CREDIT_WRITTEN_OFF"), u.getUnitId(), year, month, u.getOtherServiceCharges(), list);
        }
        if ((u.getDifferenceInExchange() != null && u.getDifferenceInExchange().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("DIFFENECE_IN_EXCHANGE"), u.getUnitId(), year, month, u.getDifferenceInExchange(), list);
        }
        if ((u.getRnDEngineeringExpenses() != null && u.getRnDEngineeringExpenses().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("RND_ENGINEERING_EXPENSE"), u.getUnitId(), year, month, u.getRnDEngineeringExpenses(), list);
        }
        if ((u.getCapitalImprovementExpenses() != null && u.getCapitalImprovementExpenses().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CAPITAL_IMPROVEMENT_EXPENSES"), u.getUnitId(), year, month, u.getCapitalImprovementExpenses(), list);
        }
        if ((u.getLeaseHoldImprovements() != null && u.getLeaseHoldImprovements().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("LEASE_HOLD_IMPROVEMENTS"), u.getUnitId(), year, month, u.getLeaseHoldImprovements(), list);
        }
        if ((u.getMarketingLaunch() != null && u.getMarketingLaunch().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("MARKETING_LAUNCH"), u.getUnitId(), year, month, u.getMarketingLaunch(), list);
        }
        if ((u.getCorporateMarketingPhotography() != null && u.getCorporateMarketingPhotography().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_PHOTO"), u.getUnitId(), year, month, u.getCorporateMarketingPhotography(), list);
        }
        if ((u.getOdcRental() != null && u.getOdcRental().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("ODC_RENTAL"), u.getUnitId(), year, month, u.getOdcRental(), list);
        }
//        if ((u.getCogsOthers() != null && u.getCogsOthers().compareTo(BigDecimal.ZERO) != 0)) {
//            createAdjustmentObject(userId, headerMap.get("COGS_OTHERS"), u.getUnitId(), year, month, u.getCogsOthers(), list);
//        }
        if ((u.getCommissionChange() != null && u.getCommissionChange().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("COMMISSION_CHANGE_CAFE"), u.getUnitId(), year, month, u.getCommissionChange(), list);
        }
        if ((u.getVehicleRegularMaintenanceHq() != null && u.getVehicleRegularMaintenanceHq().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("VEHICLE_REGULAR_MAINTENANCE_HQ"), u.getUnitId(), year, month, u.getVehicleRegularMaintenanceHq(), list);
        }
        if ((u.getBuildingMaintenanceHq() != null && u.getBuildingMaintenanceHq().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("BUILDING_MAINTENANCE_HQ"), u.getUnitId(), year, month, u.getBuildingMaintenanceHq(), list);
        }
        if ((u.getComputerItMaintenanceHq() != null && u.getComputerItMaintenanceHq().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("COMPUTER_IT_MAINTENANCE_HQ"), u.getUnitId(), year, month, u.getComputerItMaintenanceHq(), list);
        }
        if ((u.getEquipmentMaintenanceHq() != null && u.getEquipmentMaintenanceHq().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("EQUIPMENT_MAINTENANCE_HQ"), u.getUnitId(), year, month, u.getEquipmentMaintenanceHq(), list);
        }
        if ((u.getMarketingNpiHq() != null && u.getMarketingNpiHq().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("MARKETING_NPI_HQ"), u.getUnitId(), year, month, u.getMarketingNpiHq(), list);
        }
        if ((u.getLicenseExpenses() != null && u.getLicenseExpenses().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("LICENSE_EXPENSES"), u.getUnitId(), year, month, u.getLicenseExpenses(), list);
        }
        if ((u.getCorporateMarketingAtlRadio() != null && u.getCorporateMarketingAtlRadio().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_ATL_RADIO"), u.getUnitId(), year, month, u.getCorporateMarketingAtlRadio(), list);
        }
        if ((u.getCorporateMarketingAtlTv() != null && u.getCorporateMarketingAtlTv().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_ATL_TV"), u.getUnitId(), year, month, u.getCorporateMarketingAtlTv(), list);
        }
        if ((u.getCorporateMarketingAtlPrintAd() != null && u.getCorporateMarketingAtlPrintAd().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_ATL_PRINT_AD"), u.getUnitId(), year, month, u.getCorporateMarketingAtlPrintAd(), list);
        }
        if ((u.getCorporateMarketingAtlCinema() != null && u.getCorporateMarketingAtlCinema().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_ATL_CINEMA"), u.getUnitId(), year, month, u.getCorporateMarketingAtlCinema(), list);
        }
        if ((u.getCorporateMarketingAtlDigital() != null && u.getCorporateMarketingAtlDigital().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_ATL_DIGITAL"), u.getUnitId(), year, month, u.getCorporateMarketingAtlDigital(), list);
        }
        if ((u.getLogisticInterstateColdVehicle() != null && u.getLogisticInterstateColdVehicle().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("LOGISTIC_INTRASTATE_COLD_VEHICLE"), u.getUnitId(), year, month, u.getLogisticInterstateColdVehicle(), list);
        }
        if ((u.getLogisticInterstateNonColdVehicle() != null && u.getLogisticInterstateNonColdVehicle().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("LOGISTIC_INTRASTATE_NON_COLD_VEHICLE"), u.getUnitId(), year, month, u.getLogisticInterstateNonColdVehicle(), list);
        }
        if ((u.getLogisticInterstateAir() != null && u.getLogisticInterstateAir().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("LOGISTIC_INTERSTATE_AIR"), u.getUnitId(), year, month, u.getLogisticInterstateAir(), list);
        }
        if ((u.getLogisticInterstateRoad() != null && u.getLogisticInterstateRoad().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("LOGISTIC_INTERSTATE_ROAD"), u.getUnitId(), year, month, u.getLogisticInterstateRoad(), list);
        }
        if ((u.getLogisticInterstateTrain() != null && u.getLogisticInterstateTrain().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("LOGISTIC_INTERSTATE_TRAIN"), u.getUnitId(), year, month, u.getLogisticInterstateTrain(), list);
        }
        if ((u.getAirConditionerAmc() != null && u.getAirConditionerAmc().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("AIR_CONDITIONER_AMC"), u.getUnitId(), year, month, u.getAirConditionerAmc(), list);
        }
        if ((u.getCorporateMarketingSms() != null && u.getCorporateMarketingSms().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_SMS_EMAIL"), u.getUnitId(), year, month, u.getCorporateMarketingSms(), list);
        }
        if ((u.getSecurityDepositProperty() != null && u.getSecurityDepositProperty().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("SECURITY_DEPOSIT_PROPERTY"), u.getUnitId(), year, month, u.getSecurityDepositProperty(), list);
        }
        if ((u.getSecurityDepositMVAT() != null && u.getSecurityDepositMVAT().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("SECURITY_DEPOSIT_MVAT"), u.getUnitId(), year, month, u.getSecurityDepositMVAT(), list);
        }
        if ((u.getSecurityDepositElectricity() != null && u.getSecurityDepositElectricity().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("SECURITY_DEPOSIT_ELECTRICITY"), u.getUnitId(), year, month, u.getSecurityDepositElectricity(), list);
        }
        if ((u.getMarketingDiscountEcom() != null && u.getMarketingDiscountEcom().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("MARKETING_DISCOUNT_ECOM"), u.getUnitId(), year, month, u.getMarketingDiscountEcom(), list);
        }
        if ((u.getShippingCharges() != null && u.getShippingCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("SHIPPING_CHARGES"), u.getUnitId(), year, month, u.getShippingCharges(), list);
        }
        if ((u.getOtherTransactionCharges() != null && u.getOtherTransactionCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("OTHER_TRANSACTION_CHARGES"), u.getUnitId(), year, month, u.getOtherTransactionCharges(), list);
        }
        if ((u.getDiscountDealerMargin() != null && u.getDiscountDealerMargin().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("DISCOUNT_DEALER_MARGIN"), u.getUnitId(), year, month, u.getDiscountDealerMargin(), list);
        }
        if ((u.getPerformanceMarketingService() != null && u.getPerformanceMarketingService().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("PERFORMANCE_MARKETING_SERVICE"), u.getUnitId(), year, month, u.getPerformanceMarketingService(), list);
        }
        if ((u.getInsuranceMarine() != null && u.getInsuranceMarine().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("INSURANCE_MARINE"), u.getUnitId(), year, month, u.getInsuranceMarine(), list);
        }
        if ((u.getShareStampingCharges() != null && u.getShareStampingCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("SHARE_STAMPING_CHARGES"), u.getUnitId(), year, month, u.getShareStampingCharges(), list);
        }
        if ((u.getOtherChargesEcom() != null && u.getOtherChargesEcom().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("OTHER_CHARGES_ECOM"), u.getUnitId(), year, month, u.getOtherChargesEcom(), list);
        }
        if ((u.getComissionChannelPartnerFixed() != null && u.getComissionChannelPartnerFixed().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("COMISSION_CHANNEL_PARTNER_FIXED"), u.getUnitId(), year, month, u.getComissionChannelPartnerFixed(), list);
        }
        if ((u.getCogsTradingGoods() != null && u.getCogsTradingGoods().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("COGS_TRADING_GOODS"), u.getUnitId(), year, month, u.getCogsTradingGoods(), list);
        }
        if ((u.getRoyaltyFees() != null && u.getRoyaltyFees().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("ROYALTY_FEES"), u.getUnitId(), year, month, u.getRoyaltyFees(), list);
        }
        if ((u.getFreightCharges() != null && u.getFreightCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("FREIGHT_CHARGES"), u.getUnitId(), year, month, u.getFreightCharges(), list);
        }
        if ((u.getCorporateMarketingChannelPartner() != null && u.getCorporateMarketingChannelPartner().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("CORPORATE_MARKETING_CHANNEL_PARTNER"), u.getUnitId(), year, month, u.getCorporateMarketingChannelPartner(), list);
        }
        if ((u.getEmployeeFacilitationCharges() != null && u.getEmployeeFacilitationCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("EMPLOYEE_FACILITATION_CHARGES"), u.getUnitId(), year, month, u.getEmployeeFacilitationCharges(), list);
        }
        if ((u.getProntoAMC() != null && u.getProntoAMC().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("PRONTO_AMC"), u.getUnitId(), year, month, u.getProntoAMC(), list);
        }
        if ((u.getOthersMaintenance() != null && u.getOthersMaintenance().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("OTHERS_MAINTENANCE"), u.getUnitId(), year, month, u.getOthersMaintenance(), list);
        }
        if ((u.getTechnologyPlatformCharges() != null && u.getTechnologyPlatformCharges().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("TECHNOLOGY_PLATFORM_CHARGES"), u.getUnitId(), year, month, u.getTechnologyPlatformCharges(), list);
        }
        if ((u.getInterestOnTermLoan() != null && u.getInterestOnTermLoan().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("INTEREST_ON_TERM_LOAN"), u.getUnitId(), year, month, u.getInterestOnTermLoan(), list);
        }
        if ((u.getTechnologyOthers() != null && u.getTechnologyOthers().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("TECHNOLOGY_OTHERS"), u.getUnitId(), year, month, u.getTechnologyOthers(), list);
        }
        if ((u.getSystemRental() != null && u.getSystemRental().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("SYSTEM_RENTAL"), u.getUnitId(), year, month, u.getSystemRental(), list);
        }
        if ((u.getRoRental() != null && u.getRoRental().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("RO_RENTAL"), u.getUnitId(), year, month, u.getRoRental(), list);
        }
        if ((u.getInsuranceAccidental() != null && u.getInsuranceAccidental().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("INSURANCE_ACCIDENTAL"), u.getUnitId(), year, month, u.getInsuranceAccidental(), list);
        }
        if ((u.getDgRental() != null && u.getDgRental().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("DG_RENTAL"), u.getUnitId(), year, month, u.getDgRental(), list);
        }
        if ((u.getOthersAMC() != null && u.getOthersAMC().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("OTHERS_AMC"), u.getUnitId(), year, month, u.getOthersAMC(), list);
        }
        if ((u.getMusicRentals() != null && u.getMusicRentals().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("MUSIC_RENTALS"), u.getUnitId(), year, month, u.getMusicRentals(), list);
        }
        if ((u.getInsuranceAssets() != null && u.getInsuranceAssets().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("INSURANCE_ASSETS"), u.getUnitId(), year, month, u.getInsuranceAssets(), list);
        }
        if ((u.getInsuranceCGL() != null && u.getInsuranceCGL().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("INSURANCE_CGL"), u.getUnitId(), year, month, u.getInsuranceCGL(), list);
        }
        if ((u.getInsuranceMedical() != null && u.getInsuranceMedical().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("INSURANCE_MEDICAL"), u.getUnitId(), year, month, u.getInsuranceMedical(), list);
        }
        if ((u.getPettyCashRentals() != null && u.getPettyCashRentals().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("PETTY_CASH_RENTALS"), u.getUnitId(), year, month, u.getPettyCashRentals(), list);
        }
        if ((u.getEdcRental() != null && u.getEdcRental().compareTo(BigDecimal.ZERO) != 0)) {
            createAdjustmentObject(userId, headerMap.get("EDC_RENTAL"), u.getUnitId(), year, month, u.getEdcRental(), list);
        }


    }


    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, transactionManager = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public List<Integer> getPnLUnits(Date d) {
        return unitBudgetDao.getPnlUnitIds(d, CalculationStatus.PENDING_SUMO_CALCULATION, CalculationType.CURRENT);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, transactionManager = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public List<Integer> getFinalizedPnLUnits(Date d) {
        return unitBudgetDao.getPnlUnitIds(d, CalculationStatus.PENDING_FINALIZED_CALCULATION,
            CalculationType.FINALIZED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, transactionManager = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean savePnLData(List<BudgetDetail> budgetDetails, CalculationStatus status, CalculationType type) {
        for (BudgetDetail bd : budgetDetails) {
            UnitExpenditureDetail d = unitBudgetDao.getLatestUnitExpenditureDetail(bd.getKey().getUnitId(),
                    bd.getKey().getMonth(), bd.getKey().getYear(), status, type);
            try {
                unitBudgetDao.saveSCMPnL(d, bd, status, type);
            } catch (Exception e) {
                LOG.error("Error in saving PnL Data from SCM", e);
                unitBudgetService.markAsFailed(d.getDetailId());
                String message="Failed To generate PnL for Unit "+ d.getUnitId()+"{"+d.getUnitName()+"} For Business Date ::"+ d.getBusinessDate();
                SlackNotificationService.getInstance().sendNotification(
                        props.getEnvironmentType(), "Kettle", null,
                        SlackNotification.PNL_NOTIFY
                                .getChannel(props.getEnvironmentType()),
                        message);
            }
            if (CalculationType.FINALIZED.equals(type)) {
                LOG.info("marking finalized pnl as completed");
                unitBudgetService.markAsCompleted(d.getDetailId());
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, transactionManager = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public Map<String, Map<String, BigDecimal>> applyBudgetConstraintRO(ConsumableBudgetRequest budgetRequest) {
        Map<String, Map<String, BigDecimal>> resultMap = new HashMap<>();
        UnitBudgetoryDetail budgetoryDetail = unitBudgetDao.getUnitBudgetDetail(budgetRequest.getUnitId(),
            AppUtils.getCurrentMonth(), AppUtils.getCurrentYear());
        if (budgetoryDetail == null) {
            LOG.info("Budget not provided for this unit " + budgetRequest.getUnitId()
                + ". So Not Applying Budget Check");
            Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
            map.put(AppConstants.BUDGET_UNAVAILABLE, BigDecimal.ZERO);

            resultMap.put(AppConstants.BUDGET_UNAVAILABLE, map);
            return resultMap;
        }

        ConsumablesAggregate currentAggregate = budgetRequest.getCurrentAggregate();
        ConsumablesAggregate requestedAggregate = budgetRequest.getRequestedAggregate();

        BigDecimal budgetAmount = BigDecimal.ZERO;
        BigDecimal currentAmount = BigDecimal.ZERO;
        BigDecimal requestedAmount = BigDecimal.ZERO;

        for (Field field : ConsumablesAggregate.class.getDeclaredFields()) {
            BigDecimal current = (BigDecimal) BudgetUtils.getReadMethodValue(field.getName(), currentAggregate);
            currentAmount = AppUtils.add(currentAmount, current);
            BigDecimal requested = (BigDecimal) BudgetUtils.getReadMethodValue(field.getName(), requestedAggregate);
            requestedAmount = AppUtils.add(requestedAmount, requested);

            if (requested.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            BigDecimal budget = (BigDecimal) BudgetUtils.getReadMethodValue(field.getName(), budgetoryDetail);
            budget = (budget == null ? BigDecimal.ZERO : budget);
            budgetAmount = AppUtils.add(budgetAmount, budget);

            if (budget.compareTo(BigDecimal.ZERO) > 0 && AppUtils.add(current, requested).compareTo(budget) == 1) {
                Map<String, BigDecimal> budgetMap = new HashMap<>();
                budgetMap.put("currentAmount", current);
                budgetMap.put("requestedAmount", requested);
                budgetMap.put("budgetAmount", budget);
                resultMap.put(field.getName(), budgetMap);
            }
        }

        LOG.info("budgetAmount :" + budgetAmount + " currentAmount : " + currentAmount + " requestedAmount : "
            + requestedAmount);

        if (budgetAmount.compareTo(BigDecimal.ZERO) > 0
            && AppUtils.add(currentAmount, requestedAmount).compareTo(budgetAmount) == 1) {

            UnitExpenditure expenditure = null;
            if (budgetRequest.isFixedAsset()) {
                expenditure = BudgetUtils.getAnnotationsValueOfCategory(ExpenseField.ScoreCardCategory.FIXED_ASSETS);
            } else {
                expenditure = BudgetUtils.getAnnotationsValueOfCategory(ExpenseField.ScoreCardCategory.CONSUMABLE);
            }

            UnitBudgetExceededData exceededData = new UnitBudgetExceededData();
            exceededData.setUnitId(budgetRequest.getUnitId());
            exceededData.setExpenseType("Request Order");
            exceededData.setDay(AppUtils.getCurrentDayofMonth());
            exceededData.setExpenseLabel(expenditure.getLabel());
            exceededData.setMonth(AppUtils.getCurrentMonth());
            exceededData.setYear(AppUtils.getCurrentYear());
            exceededData.setBudgetAmount(budgetAmount);
            exceededData.setCurrentAmount(currentAmount);
            exceededData.setRequestedAmount(requestedAmount);
            exceededData.setExpenseSource(ApplicationName.SCM_SERVICE.name());
            exceededData.setBudgetCategory(expenditure.getCategory());
            exceededData.setCreatedBy(budgetRequest.getCreatedBy());
            exceededData.setCreatedOn(AppUtils.getCurrentTimestamp());
            StringBuilder sb = new StringBuilder("You have exceeded your allocated budget : " + budgetAmount
                + " for category " + ScoreCardCategory.CONSUMABLE.name() + " as your current expense is "
                + currentAmount + " .");
            exceededData.setNotificationType(expenditure.getRestrictionType());

            LOG.info(sb.toString() + " : Caused :  " + expenditure.getRestrictionType());

            try {
                List<UnitBudgetExceededData> budgetExceededDatas = unitBudgetDao.getTodayEntry(exceededData,
                    AppUtils.getCurrentDate());
                if (budgetExceededDatas.size() == 0) {
                    unitBudgetDao.add(exceededData);
                }
            } catch (ConstraintViolationException e) {
                LOG.info(e.getMessage());
            }
        }
        return resultMap;
    }

}
