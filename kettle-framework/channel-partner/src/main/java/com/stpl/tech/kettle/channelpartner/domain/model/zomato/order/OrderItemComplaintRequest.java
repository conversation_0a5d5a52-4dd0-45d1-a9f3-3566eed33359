package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "quantity",
        "name",
        "dish_id",
        "total_cost"
})
public class OrderItemComplaintRequest implements Serializable {

    @JsonProperty("quantity")
    private Integer quantity;

    @JsonProperty("name")
    private String name;

    @JsonProperty("dish_id")
    private String dishId;

    @JsonProperty("total_cost")
    private Float totalCost;

    public OrderItemComplaintRequest() {
    }

    public OrderItemComplaintRequest(Integer quantity, String name, String dishId, Float totalCost) {
        this.quantity = quantity;
        this.name = name;
        this.dishId = dishId;
        this.totalCost = totalCost;
    }

    @JsonProperty("quantity")
    public Integer getQuantity() {
        return quantity;
    }

    @JsonProperty("quantity")
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("dish_id")
    public String getDishId() {
        return dishId;
    }

    @JsonProperty("dish_id")
    public void setDishId(String dishId) {
        this.dishId = dishId;
    }

    @JsonProperty("total_cost")
    public Float getTotalCost() {
        return totalCost;
    }

    @JsonProperty("total_cost")
    public void setTotalCost(Float totalCost) {
        this.totalCost = totalCost;
    }

}
