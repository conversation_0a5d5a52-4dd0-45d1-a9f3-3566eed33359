package com.stpl.tech.util.notification.slack;

import com.google.gson.Gson;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

public class GoogleChatApi {

	private static final String TEXT = "text";
	private static final Gson gson = new Gson();

	/**
	 * Prepare Message and send to Slack
	 */
	public void call(SlackMessage message) throws IOException, InterruptedException {
		if (message != null) {
			this.send(message.getText(),message.getChannel());
		}
	}

	private String send(String message,String webHookUrl) throws IOException, InterruptedException {
		HttpURLConnection connection = null;

		try {
			Map<String,String> body = new HashMap<>();
			body.put(TEXT,message);
			String msg = gson.toJson(body);
			final URL url = new URL(webHookUrl);
			connection = (HttpURLConnection) url.openConnection();
			connection.setRequestMethod("POST");
			connection.setRequestProperty("Content-Type", "application/json; UTF-8");
			connection.setDoOutput(true);
			DataOutputStream outputStream = new DataOutputStream(connection.getOutputStream());
			outputStream.writeBytes(msg);
			outputStream.flush();
			outputStream.close();
			final InputStream is = connection.getInputStream();
			final BufferedReader rd = new BufferedReader(new InputStreamReader(is));
			String line;
			StringBuilder response = new StringBuilder();
			while ((line = rd.readLine()) != null) {
				response.append(line);
				response.append('\n');
			}

			rd.close();
			return response.toString();
		} catch (Exception e) {
			throw new SlackException(e);
		} finally {
			if (connection != null) {
				connection.disconnect();
			}
		}
	}
}
