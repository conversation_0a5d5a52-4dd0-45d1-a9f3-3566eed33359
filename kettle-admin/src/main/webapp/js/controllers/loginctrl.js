/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
adminapp.controller("LoginController", function ($scope, $location, $rootScope, $http, AuthService, $cookieStore, AppUtil, $window, toastService) {

    $scope.init = function () {
        $scope.userId = null;
        $scope.passcode = null;
        $scope.brandId = null;
        $scope.companyId = null;
        $scope.showMessage = false;
        $scope.loginText = "Login";
        $scope.getCompanies();
        $rootScope.loggedInBrandId = null;
        $rootScope.loggedInCompanyId = null;
        $rootScope.loggedInBrandName = null;
        $rootScope.loggedInCompanyName = null;
    };

    $scope.getBrands = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: "GET",
            url: AppUtil.restUrls.brandMetaData.getAllBrands,
            headers: { bypassCompanyBrandFilter: true }
        }).then(function success(response) {
            if (response.status === 200 && response.data != null) {
                $scope.brands = response.data;
                $scope.filteredBrands = $scope.brands;
            } else {
                console.log("Unable to Fetch Brands");
            }
            $rootScope.showFullScreenLoader = false;
            $scope.getCompanyBrandsMap();
        }, function error(response) {
            console.log("Unable to Fetch Brands");
            $rootScope.showFullScreenLoader = false;
            $scope.getCompanyBrandsMap();
        });
    }

    $scope.getCompanies = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: "GET",
            url: AppUtil.restUrls.unitMetaData.companies,
            headers: { bypassCompanyBrandFilter: true }
        }).then(function success(response) {
            if (response.status === 200 && response.data != null) {
                $scope.companies = response.data;
                $scope.companies.sort(function (a, b) {
                    return a.id - b.id;
                });
            } else {
                console.log("Unable to Fetch Companies");
            }
            $rootScope.showFullScreenLoader = false;
            $scope.getBrands();
        }, function error(response) {
            console.log("Unable to Fetch Companies");
            $rootScope.showFullScreenLoader = false;
            $scope.getBrands();
        });
    }

    $scope.getCompanyBrandsMap = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: "GET",
            url: AppUtil.restUrls.brandMetaData.getCompanyBrandMapping,
            headers: { bypassCompanyBrandFilter: true }
        }).then(function success(response) {
            if (response.status === 200 && response.data != null) {
                $scope.companyBrandMap = response.data;
            } else {
                console.log("Unable to Fetch Company Brand Mapping");
            }
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            console.log("Unable to Fetch Company Brand Mapping");
            $rootScope.showFullScreenLoader = false;
        });
    }
    
    $scope.login = function () {

        if ($scope.companyId == null) {
            toastService.warning("Kindly Select Company!");
            return;
        }

        var userObj = {
            userId: $scope.userId,
            password: $scope.passcode,
            unitId: 0,
            terminalId: 0,
            macAddress: $location.search().mac,
            application: "KETTLE_ADMIN"
        };

        $scope.loginText = "Logging in...";
        $scope.loginDisabled = true;

        $http({
            method: 'post',
            url: AppUtil.restUrls.users.login,
            data: userObj,
            params: { 
                companyId: $scope.companyId, 
                brandId: $scope.brandId,  
            }
        }).then(function success(response) {
            //console.log("data=",response);
            if (response.data.sessionKeyId == null) {
                $scope.authSuccess = false;
                $scope.authMessage = 'Credentials are not correct!';
                $scope.showMessage = true;
                $scope.loginText = "Login";
                $scope.loginDisabled = false;
            } else {
                $scope.authSuccess = true;
                $scope.authMessage = 'Authentication successful. Redirecting to dashboard!';
                $scope.showMessage = true;
                AppUtil.setUserData(response.data.user);
                $rootScope.userData = AppUtil.getUserData();
                //console.log($scope.userNameData);
                //console.log("tt",response.data.permissions);
                AppUtil.setPermissions(response.data.permissions);
                AppUtil.setUserValues(response.data);
                $rootScope.aclData = response.data.acl;
                AppUtil.setAcl(response.data.acl);
                response.data.permissions = null;
                // $cookieStore.put('adminglobals', response.data);
                //console.log("pp",response.data.jwtToken);
                AuthService.setAuthorization(response.data.jwtToken);

                // Setting Brand
                $rootScope.loggedInBrandId = $scope.brandId;
                if ($scope.brandId) {
                    $rootScope.loggedInBrandName = $scope.brands.find(function (b) {
                        return b.brandId === $scope.brandId;
                    }).brandName;
                }
                $window.localStorage.setItem('loggedInBrandId', $scope.brandId);
                $window.localStorage.setItem('loggedInBrandName', $rootScope.loggedInBrandName);

                // Setting Company
                $rootScope.loggedInCompanyId = $scope.companyId;
                if ($scope.companyId) {
                    $rootScope.loggedInCompanyName = $scope.companies.find(function (c) {
                        return c.id === $scope.companyId;
                    }).name;
                }
                $window.localStorage.setItem('loggedInCompanyId', $scope.companyId);
                $window.localStorage.setItem('loggedInCompanyName', $rootScope.loggedInCompanyName);

                $location.path("/dashboard/reports");
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    };

    $scope.removeAlert = function () {
        $scope.showMessage = false;
    }

    $scope.onCompanyChange = function () {
        if ($scope.companyId == null) {
            return;
        }
        $scope.filteredBrands = $scope.brands.filter(function (brand) {
            if ($scope.companyBrandMap[$scope.companyId] != undefined) {
                return $scope.companyBrandMap[$scope.companyId].some(function (b) {
                    return b.brandId == brand.brandId;
                });
            }
        });
    }
});