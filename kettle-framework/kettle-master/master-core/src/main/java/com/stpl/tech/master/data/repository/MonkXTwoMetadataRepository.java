package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.MonkXTwoMetadata;
import com.stpl.tech.master.data.model.UnitDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface MonkXTwoMetadataRepository extends JpaRepository<MonkXTwoMetadata, Integer> {
    @Query("SELECT m FROM MonkXTwoMetadata m LEFT JOIN m.unitDetail u WHERE u.unitId = :unitId")
    Optional<MonkXTwoMetadata> findByUnitDetail_UnitId(@Param("unitId") Integer unitId);
    
    @Query("SELECT m FROM MonkXTwoMetadata m LEFT JOIN FETCH m.monkUrls WHERE m.unitDetail.unitId = :unitId")
    Optional<MonkXTwoMetadata> findByUnitDetail_UnitIdWithUrls(@Param("unitId") Integer unitId);

}

