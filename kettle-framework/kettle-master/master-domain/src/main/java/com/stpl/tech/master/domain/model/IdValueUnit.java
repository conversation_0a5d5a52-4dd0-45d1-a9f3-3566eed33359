//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2022.07.17 at 08:32:55 PM IST
//

package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.Objects;


/**
 * <p>Java class for IdName complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="IdName"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)

public class IdValueUnit  {

    private static final long serialVersionUID = -9010774607517134157L;

    private int id;

    private Integer value;

    private String unit;

    public IdValueUnit() {

    }
    public IdValueUnit(int id, Integer value, String unit) {
        super();
        this.id = id;
        this.value = value;
        this.unit = unit;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof IdValueUnit)) return false;
        IdValueUnit that = (IdValueUnit) o;
        return getId() == that.getId() &&
                Objects.equals(getValue(), that.getValue()) &&
                Objects.equals(getUnit(), that.getUnit());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getValue(), getUnit());
    }

    @Override

    public String toString() {
        return "IdValueUnit{" +
                "id=" + id +
                ", value=" + value +
                ", unit='" + unit + '\'' +
                '}';
    }
}
