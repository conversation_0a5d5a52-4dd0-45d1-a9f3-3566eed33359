package com.stpl.tech.report.service.model;

import java.util.Date;

public class CLMOrderDataPushRequest {
    private boolean dataPushInRange;
    private Date startBusinessDate;
    private Date endBusinessDate;
    private Integer brandId;
    private Integer startCustomerId;
    private Integer endCustomerId;

    public CLMOrderDataPushRequest() {
    }

    public boolean isDataPushInRange() {
        return dataPushInRange;
    }

    public void setDataPushInRange(boolean dataPushInRange) {
        this.dataPushInRange = dataPushInRange;
    }

    public Date getStartBusinessDate() {
        return startBusinessDate;
    }

    public void setStartBusinessDate(Date startBusinessDate) {
        this.startBusinessDate = startBusinessDate;
    }

    public Date getEndBusinessDate() {
        return endBusinessDate;
    }

    public void setEndBusinessDate(Date endBusinessDate) {
        this.endBusinessDate = endBusinessDate;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }


    public Integer getStartCustomerId() {
        return startCustomerId;
    }

    public void setStartCustomerId(Integer startCustomerId) {
        this.startCustomerId = startCustomerId;
    }

    public Integer getEndCustomerId() {
        return endCustomerId;
    }

    public void setEndCustomerId(Integer endCustomerId) {
        this.endCustomerId = endCustomerId;
    }
}
