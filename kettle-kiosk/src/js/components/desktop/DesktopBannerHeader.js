import React from "react";
import { connect } from "react-redux";
import appUtil from "../../AppUtil";

@connect((store) => {
    return {
    };
})
export default class DesktopBannerHeader extends React.Component {

    constructor() {
        super();
        this.state = {};
    }

    componentWillMount(){
    }

    render() {

        return (
            <div class='headerBannerWrapper'>
                <img src="../../../img/banner/main-banner.gif" />
            </div>
        )
    }
}