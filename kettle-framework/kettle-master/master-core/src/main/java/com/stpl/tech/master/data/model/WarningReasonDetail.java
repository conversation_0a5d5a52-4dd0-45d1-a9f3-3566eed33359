package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "WARNING_REASON_DETAIL")
public class WarningReasonDetail {

	private int reasonId;
	private String reasonName;
	private String category;
	private String reasonResult;
	private String warningType;
	private String status;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "REASON_ID", unique = true, nullable = false)
	public int getReasonId() {
		return reasonId;
	}

	public void setReasonId(int reasonId) {
		this.reasonId = reasonId;
	}

	@Column(name = "REASON_NAME", nullable = false)
	public String getReasonName() {
		return reasonName;
	}

	public void setReasonName(String reasonName) {
		this.reasonName = reasonName;
	}

	@Column(name = "CATEGORY", nullable = false)
	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	@Column(name = "REASON_RESULT", nullable = false)
	public String getReasonResult() {
		return reasonResult;
	}

	public void setReasonResult(String reasonResult) {
		this.reasonResult = reasonResult;
	}

	@Column(name = "WARNING_TYPE", nullable = false)
	public String getWarningType() {
		return warningType;
	}

	public void setWarningType(String warningType) {
		this.warningType = warningType;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}
